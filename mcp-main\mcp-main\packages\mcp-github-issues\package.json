{"name": "@telus/mcp-github-issues", "version": "0.0.8", "description": "MCP server for managing GitHub repository issues", "keywords": ["telus", "mcp", "github-issues"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-github-issues"}, "license": "MIT", "author": "<PERSON> (@pdufault)", "type": "module", "main": "dist/index.js", "bin": {"mcp-github-issues": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "prepare": "pnpm run build", "start": "node dist/index.js", "dev": "pnpm run build && pnpm run start"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "@octokit/rest": "^21.1.0", "dotenv": "~16.4.7", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}