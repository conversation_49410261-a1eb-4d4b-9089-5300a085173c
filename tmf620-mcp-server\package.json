{"name": "tmf620-mcp-server", "version": "1.0.0", "main": "build/index.js", "scripts": {"build": "tsc", "start": "node build/index.js", "dev": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": [], "author": "", "license": "ISC", "description": "MCP server for TMF620 Product Catalog API", "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "axios": "^1.8.4", "dotenv": "^16.4.7", "https-proxy-agent": "^7.0.6"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/plugin-transform-modules-commonjs": "^7.24.0", "@babel/preset-env": "^7.24.0", "@babel/preset-typescript": "^7.23.3", "@types/jest": "^29.5.12", "@types/node": "^22.13.10", "babel-jest": "^29.7.0", "chalk": "^5.3.0", "cross-env": "^7.0.3", "jest": "^29.7.0", "nodemon": "^3.0.1", "ts-jest": "^29.1.2", "typescript": "^5.8.2"}}