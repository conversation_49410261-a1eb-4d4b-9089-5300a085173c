# @telus/mcp-nifi

This package provides a Model Context Protocol (MCP) server for interacting with Apache NiFi. It allows you to manage and monitor NiFi workflows, retrieve system diagnostics, and perform various operations on NiFi components.

## Overview

The server uses NiFi's token-based authentication system. When making requests, it will:
1. First authenticate with your provided credentials to obtain a token
2. Use this token for subsequent API requests
3. Handle token refresh automatically when needed

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-nifi
   ```

2. Add to your Cline/Claude Desktop MCP settings:

   ```json
   {
     "mcpServers": {
       "nifi": {
         "command": "node",
         "args": ["--use-system-ca", "path/to/dist/index.js"],
         "env": {
           "NIFI_URL": "https://your-nifi-instance:9443",
           "NIFI_USERNAME": "your-username",
           "NIFI_PASSWORD": "your-password"
         }
       }
     }
   }
   ```

   Note: The `--use-system-ca` flag is required for proper SSL certificate validation.

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-nifi
   npm install
   ```

2. Create a `.env` file in the root directory:

   ```
   NIFI_URL=https://your-nifi-instance:9443
   NIFI_USERNAME=your-username
   NIFI_PASSWORD=your-password
   ```

3. Build and start the server:

   ```bash
   npm run build
   npm start
   ```

## Features

- Token-based authentication with NiFi API
- System diagnostics retrieval
- Flow management and monitoring
- Process group operations
- Version control capabilities
- Cluster management

## Prerequisites

- Node.js (version 14 or higher recommended)
- Access to an Apache NiFi instance
- Valid NiFi user credentials

## Available Tools

| Tool Name | Description | Input | Output |
|-----------|-------------|-------|--------|
| `get_system_diagnostics` | Retrieve system diagnostics information | `nodeId` (optional string) | System diagnostics data |
| `get_flow` | Retrieve flow information | `id` (string) | Flow data |
| `get_component_status` | Get status of a specific component | `id` (string) | Component status data |
| `create_connection` | Create a new connection between components | `sourceId` (string), `destinationId` (string) | Connection data |
| `get_controller_config` | Get NiFi controller configuration | None | Controller configuration data |
| `manage_version` | Perform version control operations | `action` (string), `versionedFlowId` (string) | Version control operation result |
| `manage_process_group` | Manage process groups | `action` (string), `id` (string) | Process group operation result |
| `manage_cluster` | Manage NiFi cluster | `action` (string) | Cluster operation result |

## Security Notes

- Always use HTTPS URLs for production environments
- Store credentials securely and never commit them to version control
- Use the `--use-system-ca` flag to ensure proper SSL certificate validation

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

MIT © TELUS
