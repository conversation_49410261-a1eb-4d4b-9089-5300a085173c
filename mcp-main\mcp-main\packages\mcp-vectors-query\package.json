{"name": "@telus/mcp-vectors-query", "version": "0.0.8", "description": "MCP server for vector operations", "keywords": ["telus", "mcp", "vectors", "query"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-vectors-query"}, "license": "MIT", "author": "<PERSON> (@kevintelus)", "type": "module", "main": "dist/index.js", "bin": {"vectors-query-server": "./dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node --eval \"import('fs').then(fs => fs.chmodSync('dist/index.js', '755'))\"", "inspector": "pnpm dlx @modelcontextprotocol/inspector dist/index.js", "prepare": "pnpm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "@turbopuffer/turbopuffer": "^0.5.14", "axios": "^1.8.4", "dotenv": "~16.4.7", "openai": "^4.0.0"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}