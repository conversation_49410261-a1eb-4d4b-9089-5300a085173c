# @telus/mcp-jira-server

Welcome to the TELUS Model Context Protocol (MCP) JIRA Server! This MCP server provides tools for interacting with JIRA Server (on-prem) and cloud-hosted JIRA instances (like [https://telus-cio.atlassian.net/](https://telus-cio.atlassian.net/)).
Also this is different than the JIRA Cloud server, which has a separate Public MCP server.

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-jira-server
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for Claude Desktop):

   ```json
   {
     "mcpServers": {
       "jira": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-jira-server"],
         "env": {
           "JIRA_USERNAME": "[username]",
           "JIRA_PASSWORD": "[password-or-api-token]",
           "JIRA_BASE_URL": "https://jira.tsl.telus.com", // On-premise example
           // "JIRA_BASE_URL": "https://telus-cio.atlassian.net", // Cloud-hosted example
           "SPRINT_BOARD_ID": "[optional-board-id]",  // Optional: Default board ID for sprint operations
           
           // Optional proxy configuration
           "PROXY_HOST": "[proxy-host]",
           "PROXY_PORT": "[proxy-port]"
         }
       }
     }
   }
   ```

   Replace `[username]` and `[password-or-api-token]` with your JIRA credentials.

   **Authentication Notes:**
   - For on-premise JIRA instances (like [https://jira.tsl.telus.com](https://jira.tsl.telus.com)), use your regular JIRA username and password
   - For cloud-hosted JIRA instances (like [https://telus-cio.atlassian.net/](https://telus-cio.atlassian.net/)), you can use your email as the username and an API token as the password
     - To create an API token, go to [https://id.atlassian.com/manage-profile/security/api-tokens](https://id.atlassian.com/manage-profile/security/api-tokens)
   - If your network requires a proxy to access JIRA, configure the optional proxy settings

   **Differences between Jira Server and Jira Cloud:**

   The server automatically detects if you're using Jira Server (on-premise) or Jira Cloud based on the base URL:
   - **Jira Server (on-premise)**: URLs like `https://jira.tsl.telus.com`
     - Uses standard username/password authentication
     - Data fields and formats follow the Jira Server model
     - Sprints are formatted differently than Cloud instances

   - **Jira Cloud**: URLs containing `atlassian.net` like `https://telus-cio.atlassian.net`
     - Requires API token authentication (see above)
     - Data fields and formats follow the Jira Cloud model
     - Sprint and user formats are different

   The server automatically handles these differences, including:
   - Different data formats for sprints
   - Differences in user account IDs
   - Variations in API response structures

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-jira-server
   pnpm install
   ```

2. Build the server:

   ```bash
   pnpm build
   ```

3. Start the server:

   ```bash
   pnpm start
   ```

## 🛠 Available Tools

### 1. 🔍 Search Issues (jira_search_issues)

Search for JIRA issues using JQL queries.

```typescript
{
  "jql": string;           // JQL query string
  "maxResults"?: number;   // Maximum number of issues to return
  "expand"?: string;       // Additional fields to include
}
```

Example Cline prompt:

```xml
Find all JIRA issues assigned to me that are in progress

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_search_issues</tool_name>
<arguments>
{
  "jql": "assignee = currentUser() AND status = 'In Progress'",
  "maxResults": 10
}
</arguments>
</use_mcp_tool>
```

### 2. 📅 Get Epic Timeline (jira_get_epic_timeline)

Get a timeline report of EPICs in a project.

```typescript
{
  "projectKey": string;    // Project key
  "labels"?: string;       // Comma-separated list of labels
}
```

Example Cline prompt:

```xml
Show me the timeline for all EPICs in the TPSAPI project

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_get_epic_timeline</tool_name>
<arguments>
{
  "projectKey": "TPSAPI"
}
</arguments>
</use_mcp_tool>
```

### 3. ➕ Create Issue (jira_create_issue)

Create a new JIRA issue.

```typescript
{
  "projectKey": string;              // Project key
  "issueType": string;              // Issue type (Enabler, Story, Spike, EPIC, Initiative)
  "summary": string;                // Issue summary
  "description"?: string;           // Issue description
  "epicLink"?: string;             // EPIC key to link to
  "parentIssueKey"?: string;       // Parent issue key (required for sub-tasks)
  "subTasks"?: Array<{             // Sub-tasks to create
    "summary": string;
    "description": string;
  }>;
  "technicalInformation"?: string;  // Technical documentation
  "acceptanceCriteria"?: string;    // Acceptance criteria
  "labels"?: string[];             // Labels to add
  "storyPoints"?: number;          // Story points estimate for the issue
  "priority"?: string;             // Priority of the issue (e.g., 'Highest', 'High', 'Medium', 'Low', 'Lowest')
  "originalEstimate"?: string;     // Original time estimate (e.g., "3d", "4h 30m")
  "remainingEstimate"?: string;    // Remaining time estimate
}
```

Example Cline prompt:

```xml
Create a new story in TPSAPI project for implementing user authentication

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_create_issue</tool_name>
<arguments>
{
  "projectKey": "TPSAPI",
  "issueType": "Story",
  "summary": "Implement user authentication",
  "description": "Add user authentication using JWT tokens",
  "labels": ["authentication", "security"]
}
</arguments>
</use_mcp_tool>
```

### 4. 🔄 Update Issue (jira_update_issue)

Update an existing JIRA issue.

```typescript
{
  "issueKey": string;              // Issue key (e.g., "PROJ-123")
  "fields": {
    "summary"?: string;            // Updated summary
    "description"?: string;        // Updated description
    "technicalInformation"?: string; // Updated technical info
    "acceptanceCriteria"?: string;  // Updated acceptance criteria
    "labels"?: string[];           // Updated labels
    "status"?: string;            // New status name (e.g., 'In Progress', 'Done')
    "assignee"?: string;          // Account ID or username of the user to assign the issue to
    "storyPoints"?: number;       // Updated story points estimate
    "priority"?: string;          // Updated priority (e.g., 'Highest', 'High', 'Medium', 'Low', 'Lowest', 'Critical')
    "timetracking"?: {            // Time tracking information
      "originalEstimate": string;  // Updated original estimate
      "remainingEstimate": string; // Updated remaining estimate
    }
  }
}
```

> **Note on Status Transitions**: When updating the status field, the server automatically handles the transition process. It first retrieves all available transitions for the issue and then executes the appropriate transition to reach the target status. If the requested status transition is not valid, the server will return an error with available transitions.

Example Cline prompt:

```xml
Update TPSAPI-123 to In Progress status and add a technical documentation note

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_update_issue</tool_name>
<arguments>
{
  "issueKey": "TPSAPI-123",
  "fields": {
    "status": "In Progress",
    "technicalInformation": "Using JWT tokens with RSA-256 encryption"
  }
}
</arguments>
</use_mcp_tool>
```

### 5. 📁 Get Project Issues (jira_get_project_issues)

Get all issues for a specific project.

```typescript
{
  "projectKey": string;    // Project key
  "limit"?: number;       // Results limit (1-50, default: 10)
}
```

Example Cline prompt:

```xml
Show me the latest 20 issues from the TPSAPI project

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_get_project_issues</tool_name>
<arguments>
{
  "projectKey": "TPSAPI",
  "limit": 20
}
</arguments>
</use_mcp_tool>
```

### 6. 📋 Get Issue Details (jira_get_issue)

Get detailed information about a specific issue.

```typescript
{
  "issueKey": string;     // Issue key (e.g., "PROJ-123")
  "expand"?: string;      // Additional fields to include
}
```

Example Cline prompt:

```xml
Show me the details of TPSAPI-123 including components and attachments

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_get_issue</tool_name>
<arguments>
{
  "issueKey": "TPSAPI-123",
  "expand": "components,attachments"
}
</arguments>
</use_mcp_tool>

```

### 7. 💬 Add Comment (jira_add_comment)

Add a comment to a JIRA issue.

```typescript
{
  "issueKey": string;     // Issue key (e.g., "PROJ-123")
  "comment": string;      // Comment text (supports JIRA markup)
}
```

Example Cline prompt:

```xml
Add a comment to TPSAPI-123 about the authentication implementation

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_add_comment</tool_name>
<arguments>
{
  "issueKey": "TPSAPI-123",
  "comment": "Implemented JWT authentication with refresh token support"
}
</arguments>
</use_mcp_tool>
```

### 8. 🏷 Get Fields (jira_get_fields)

Get all available JIRA fields including custom fields.

```typescript
// No parameters required
```

Example Cline prompt:

```xml
Show me all available JIRA fields

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_get_fields</tool_name>
<arguments>{}</arguments>
</use_mcp_tool>
```

### 9. ⏱️ Get Worklogs (jira_get_worklogs)

Get worklogs for a specific JIRA issue.

```typescript
{
  "issueKey": string;     // JIRA issue key (e.g., "PROJ-123")
  "startAt"?: number;     // The index of the first item to return in the page of results (page offset)
  "maxResults"?: number;  // The maximum number of items to return per page
  "startedAfter"?: string; // The start of the period to return worklogs for (ISO 8601 format)
}
```

Example Cline prompt:

```xml
Show me the worklogs for TPSAPI-123

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_get_worklogs</tool_name>
<arguments>
{
  "issueKey": "TPSAPI-123"
}
</arguments>
</use_mcp_tool>
```

### 10. 🏃‍♂️ Get Board Sprints (jira_get_board_sprints)

Get all sprints from a specific board.

```typescript
{
  "boardId": string;     // Board ID (rapidView ID). Required unless SPRINT_BOARD_ID is set
  "state"?: string;       // Filter by state (active,future,closed)
}
```

Example Cline prompts:

```xml
Show me all active sprints (using default board from SPRINT_BOARD_ID)

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_get_board_sprints</tool_name>
<arguments>{}</arguments>
</use_mcp_tool>
```

```xml
Show me all active sprints for board 123

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_get_board_sprints</tool_name>
<arguments>
{
  "boardId": "123",
  "state": "active"
}
</arguments>
</use_mcp_tool>
```

### 11. 🎯 Get Sprint Issues (jira_get_sprint_issues)

Get all issues in the current active sprint using the configured SPRINT_BOARD_ID.

```typescript
{
  // No parameters required if SPRINT_BOARD_ID is set
}
```

Example Cline prompt:

```xml
Show me all issues in the current sprint (using default board from SPRINT_BOARD_ID)

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_get_sprint_issues</tool_name>
<arguments>{}</arguments>
</use_mcp_tool>
```

### 12. ⏱️ Add Worklog (jira_add_worklog)

Add a worklog to a specific JIRA issue.

```typescript
{
  "issueKey": string;     // JIRA issue key (e.g., "PROJ-123")
  "timeSpent": string;    // The time spent working on the issue (e.g., "3h 30m")
  "comment"?: string;     // A comment about the work performed
  "started"?: string;     // The datetime when the worklog was started (ISO 8601 format)
}
```

Example Cline prompt:

```xml
Add a worklog of 2 hours to TPSAPI-123

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_add_worklog</tool_name>
<arguments>
{
  "issueKey": "TPSAPI-123",
  "timeSpent": "2h",
  "comment": "Implemented authentication feature"
}
</arguments>
</use_mcp_tool>
```

### 13. 🔄 Update Worklog (jira_update_worklog)

Update an existing worklog in a JIRA issue.

```typescript
{
  "issueKey": string;     // JIRA issue key (e.g., "PROJ-123")
  "worklogId": string;    // The ID of the worklog to update
  "timeSpent": string;    // The new time spent working on the issue
  "comment"?: string;     // A new comment about the work performed
  "started"?: string;     // The new datetime when the worklog was started
}
```

Example Cline prompt:

```xml
Update the worklog in TPSAPI-123 to 3 hours

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_update_worklog</tool_name>
<arguments>
{
  "issueKey": "TPSAPI-123",
  "worklogId": "12345",
  "timeSpent": "3h",
  "comment": "Updated time spent on authentication implementation"
}
</arguments>
</use_mcp_tool>
```

### 14. 🗑️ Delete Worklog (jira_delete_worklog)

Delete a worklog from a JIRA issue.

```typescript
{
  "issueKey": string;     // JIRA issue key (e.g., "PROJ-123")
  "worklogId": string;    // The ID of the worklog to delete
  "adjustEstimate"?: string; // How to update remaining estimate (auto, leave, new, manual)
  "newEstimate"?: string; // New estimate when adjustEstimate is "new"
  "reduceBy"?: string;    // Amount to reduce by when adjustEstimate is "manual"
}
```

Example Cline prompt:

```xml
Delete worklog from TPSAPI-123

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_delete_worklog</tool_name>
<arguments>
{
  "issueKey": "TPSAPI-123",
  "worklogId": "12345",
  "adjustEstimate": "auto"
}
</arguments>
</use_mcp_tool>
```

### 15. 👤 Get User Info (jira_get_user_info)

Get detailed information about a JIRA user.

```typescript
{
  "username"?: string;     // Username or display name of the JIRA user
  "accountId"?: string;    // Account ID of the JIRA user (alternative to username)
}
```

> **Note**: You must provide either `username` or `accountId`. For Jira Cloud instances, `accountId` is preferred. For Jira Server/Datacenter instances, `username` is typically used.

Example Cline prompts:

```xml
Get information about user johndoe (Jira Server/Datacenter)

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_get_user_info</tool_name>
<arguments>
{
  "username": "johndoe"
}
</arguments>
</use_mcp_tool>
```

```xml
Get information about user by account ID (Jira Cloud)

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_get_user_info</tool_name>
<arguments>
{
  "accountId": "5b10a2844c20165700ede21g"
}
</arguments>
</use_mcp_tool>
```

### 16. 📊 Get User Worklogs (jira_get_user_worklogs)

Get and analyze worklogs for a specific user within a date range.

```typescript
{
  "username": string;     // Username or display name of the user
  "startDate": string;    // Start date in YYYY-MM-DD format
  "endDate": string;      // End date in YYYY-MM-DD format
}
```

Example Cline prompt:

```xml
Get worklogs for user johndoe from last week

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_get_user_worklogs</tool_name>
<arguments>
{
  "username": "johndoe",
  "startDate": "2024-02-26",
  "endDate": "2024-03-01"
}
</arguments>
</use_mcp_tool>
```

### 17. 📚 Get Worklogs Bulk (jira_get_worklogs_bulk)

Get worklogs for multiple issues.

```typescript
{
  "issueKeys": string[];  // Array of issue keys to get worklogs for (max 1000)
  "startedAfter"?: string; // The start of the period to return worklogs for (ISO 8601)
}
```

Example Cline prompt:

```xml
Get worklogs for multiple TPSAPI issues

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_get_worklogs_bulk</tool_name>
<arguments>
{
  "issueKeys": ["TPSAPI-123", "TPSAPI-124", "TPSAPI-125"],
  "startedAfter": "2024-02-01T00:00:00Z"
}
</arguments>
</use_mcp_tool>
```

### 18. 🔍 Search Users (jira_search_users)

Search for users in JIRA.

```typescript
{
  "query": string;        // A query string used to search username, name or e-mail address
  "startAt"?: number;     // The index of the first user to return (0-based)
  "maxResults"?: number;  // The maximum number of users to return (default: 50)
  "includeInactive"?: boolean; // Whether to include inactive users in the search results (default: false)
}
```

Example Cline prompt:

```xml
Search for users with "john" in their name or email

[Cline will use:]
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_search_users</tool_name>
<arguments>
{
  "query": "john",
  "maxResults": 10,
  "includeInactive": false
}
</arguments>
</use_mcp_tool>
```

## 📝 Common JQL Examples

Here are some common JQL queries you can use with the `jira_search_issues` tool:

1. Find my open issues:

    ```sql
    assignee = currentUser() AND status not in (Done, Cancelled)
    ```

2. Find recently updated issues:

    ```sql
    project = TPSAPI AND updated >= -7d ORDER BY updated DESC
    ```

3. Find high-priority bugs:

    ```sql
    project = TPSAPI AND issuetype = Bug AND priority in (Highest, High)
    ```

4. Find issues in the current sprint:

    ```sql
    project = TPSAPI AND sprint in openSprints()
    ```

5. Find issues blocked by other issues:

    ```sql
    project = TPSAPI AND status = Blocked
    ```

## 🔧 Custom Fields Management

The MCP JIRA Server includes robust handling of custom fields, which can vary between different JIRA instances.

### How Custom Fields Are Handled

The server uses several strategies to locate the correct custom field IDs:

1. **Field Mapping**: On startup, the server creates a mapping of field names to their IDs by querying the JIRA API
2. **Multiple Fallbacks**: For common custom fields, the server tries multiple known field IDs if the primary one isn't found
3. **Name-based Search**: If standard IDs fail, the server searches for fields with similar names

### Common Custom Fields

The server has special handling for these common custom fields:

| Field | Primary ID | Fallbacks |
|-------|------------|-----------|
| Technical Information | customfield_17065 | customfield_12850 |
| Acceptance Criteria | customfield_11001 | customfield_10011 |
| Epic Link | customfield_10004 | customfield_10000 |
| Epic Name | customfield_10014 | customfield_10006 |
| Story Points | customfield_17372 | customfield_10016 |
| Sprint | customfield_10003 | customfield_10000 |

### Finding Custom Field IDs

If you need to work with custom fields not automatically handled by the server:

1. Use the `jira_get_fields` tool to list all available fields
2. Look for the field you need in the response
3. Note the field ID (e.g., `customfield_12345`)
4. Use this ID when working with the field

## 🚀 Advanced Usage Examples

### Creating an issue with custom fields, story points, and time tracking

```xml
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_create_issue</tool_name>
<arguments>
{
  "projectKey": "TPSAPI",
  "issueType": "Story",
  "summary": "Implement user authentication",
  "description": "Add user authentication using JWT tokens",
  "labels": ["authentication", "security"],
  "technicalInformation": "Using JWT with RSA-256 encryption and refresh tokens",
  "acceptanceCriteria": "1. Users can log in\n2. Tokens expire after 1 hour\n3. Refresh tokens work properly",
  "originalEstimate": "3d",
  "storyPoints": 8,
  "priority": "High"
}
</arguments>
</use_mcp_tool>
```

### Creating an Epic with linked Stories and Sub-tasks

```xml
<!-- First, create the Epic -->
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_create_issue</tool_name>
<arguments>
{
  "projectKey": "TPSAPI",
  "issueType": "Epic",
  "summary": "Authentication System Overhaul",
  "description": "Implement a new authentication system with improved security",
  "labels": ["security", "authentication", "epic"]
}
</arguments>
</use_mcp_tool>

<!-- Then create a Story linked to the Epic -->
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_create_issue</tool_name>
<arguments>
{
  "projectKey": "TPSAPI",
  "issueType": "Story",
  "summary": "Implement JWT authentication",
  "description": "Add JWT-based authentication with refresh tokens",
  "epicLink": "TPSAPI-123",
  "storyPoints": 5,
  "subTasks": [
    {
      "summary": "Implement token generation",
      "description": "Create JWT token generation service"
    },
    {
      "summary": "Implement token validation",
      "description": "Create middleware for token validation"
    }
  ]
}
</arguments>
</use_mcp_tool>
```

### Transitioning an issue through multiple statuses

```xml
<!-- First transition to In Progress -->
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_update_issue</tool_name>
<arguments>
{
  "issueKey": "TPSAPI-123",
  "fields": {
    "status": "In Progress",
    "comment": "Starting work on this issue"
  }
}
</arguments>
</use_mcp_tool>

<!-- Later, transition to Done -->
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_update_issue</tool_name>
<arguments>
{
  "issueKey": "TPSAPI-123",
  "fields": {
    "status": "Done",
    "comment": "Completed implementation and testing"
  }
}
</arguments>
</use_mcp_tool>
```

### Analyzing user worklog data

```xml
<use_mcp_tool>
<server_name>jira</server_name>
<tool_name>jira_get_user_worklogs</tool_name>
<arguments>
{
  "username": "johndoe",
  "startDate": "2024-10-01",
  "endDate": "2024-10-31"
}
</arguments>
</use_mcp_tool>
```

The result will include:

- Total hours worked
- Breakdown by week
- Breakdown by day
- Details of each worklog entry

## ⚠️ Error Handling

The server provides detailed error messages when something goes wrong. Common errors include:

- Invalid JQL syntax
- Missing required fields
- Invalid status transitions
- Permission issues
- Network connectivity problems

Error responses include:

- Error code
- Detailed message
- API response details when available

## 🌟 Best Practices

1. Always check available transitions before updating status
2. Use labels consistently for better organization
3. Provide clear descriptions and acceptance criteria
4. Link related issues when possible
5. Use appropriate issue types for different work items
6. Keep comments focused and relevant
7. Use the expand parameter to get additional fields only when needed
8. Set reasonable limits when searching to avoid performance issues

## 🔧 Troubleshooting

1. If authentication fails:
   - Verify your credentials in the MCP settings
   - Check if your JIRA account is active
   - Ensure you have the necessary permissions

2. If status transitions fail:
   - Check if the target status is valid for the issue type
   - Verify the current status allows the transition
   - Ensure you have permission to make the transition

3. If search returns unexpected results:
   - Verify your JQL syntax
   - Check field names and values
   - Consider using the jira_get_fields tool to see available fields

4. If the server won't start:
   - Check the MCP settings file syntax
   - Verify the path to the server executable
   - Ensure all environment variables are set correctly

## 🤝 Support

For issues, feature requests, or contributions:

1. Check the existing issues in the repository
2. Create a new issue with detailed information
3. Follow the contribution guidelines when submitting PRs
4. Join the #g-llm-ide channel to get involved and discuss the project

## 📄 License

This project is licensed under the [MIT License](LICENSE).
