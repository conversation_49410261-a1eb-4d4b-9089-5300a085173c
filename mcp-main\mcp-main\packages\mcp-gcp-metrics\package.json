{"name": "@telus/mcp-gcp-metrics", "version": "1.0.0", "description": "MCP server for querying Google Cloud Monitoring metrics", "type": "module", "main": "dist/index.js", "author": "<PERSON> (@jordanblum-telus)", "license": "MIT", "bin": {"mcp-gcp-metrics": "dist/index.js"}, "files": ["dist", "README.md"], "keywords": ["telus", "mcp", "gcp", "monitoring", "metrics", "cloud"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-gcp-metrics"}, "scripts": {"build": "tsc && node --eval \"import('fs').then(fs => fs.chmodSync('dist/index.js', '755'))\"", "prepare": "pnpm run build", "start": "node dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.9.0", "axios": "^1.6.2", "google-auth-library": "^9.0.0"}, "devDependencies": {"@types/node": "^20.10.4", "typescript": "^5.3.3"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}