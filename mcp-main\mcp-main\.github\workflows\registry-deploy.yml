name: Publish MCP Registry

on:
  push:
    branches:
      - main
  workflow_dispatch:

permissions:
  contents: write
  pages: write
  id-token: write

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Cache turbo build setup
        uses: actions/cache@v4
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - name: Cache Next.js build
        uses: actions/cache@v4
        with:
          path: |
            .next/cache
            apps/registry/.next/cache
          key: ${{ runner.os }}-nextjs-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-nextjs-

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: "pnpm"
          registry-url: "https://npm.pkg.github.com"
          scope: "@telus"

      - name: Install Dependencies
        run: pnpm install
        env:
          NODE_AUTH_TOKEN: ${{ secrets.MCP_REGISTRY_TOKEN }}

      - name: Setup Pages
        id: setup_pages
        uses: actions/configure-pages@v5

      - name: Build
        run: pnpm run build -F registry --force
        env:
          PAGES_BASE_PATH: ${{ steps.setup_pages.outputs.base_path }}

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./apps/registry/out

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
