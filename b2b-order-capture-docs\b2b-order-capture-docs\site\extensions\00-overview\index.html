
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/extensions/00-overview/">
      
      
        <link rel="prev" href="../../backend/05-development-guide/">
      
      
        <link rel="next" href="../01-plugin-architecture/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Overview - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#extensions-application-overview" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Overview
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" checked>
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#introduction" class="md-nav__link">
    <span class="md-ellipsis">
      Introduction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Introduction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#purpose-and-scope" class="md-nav__link">
    <span class="md-ellipsis">
      Purpose and Scope
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#qes-plugin-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      QES Plugin Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="QES Plugin Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#high-level-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      High-Level Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-lifecycle" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Lifecycle
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Core Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-framework-dependencies" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Framework Dependencies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-and-testing-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Development and Testing Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Business Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Business Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-modification-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Modification Engine
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bandwidth-validation-system" class="md-nav__link">
    <span class="md-ellipsis">
      Bandwidth Validation System
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#state-machine-extensions" class="md-nav__link">
    <span class="md-ellipsis">
      State Machine Extensions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technical-features" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Technical Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Technical Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-framework-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Framework Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#validation-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Engine
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensibility" class="md-nav__link">
    <span class="md-ellipsis">
      Extensibility
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-project-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Project Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Component Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#available-maven-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Available Maven Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-urls" class="md-nav__link">
    <span class="md-ellipsis">
      Development URLs
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Deployment
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#introduction" class="md-nav__link">
    <span class="md-ellipsis">
      Introduction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Introduction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#purpose-and-scope" class="md-nav__link">
    <span class="md-ellipsis">
      Purpose and Scope
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#qes-plugin-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      QES Plugin Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="QES Plugin Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#high-level-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      High-Level Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-lifecycle" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Lifecycle
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Core Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-framework-dependencies" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Framework Dependencies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-and-testing-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Development and Testing Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Business Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Business Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-modification-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Modification Engine
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bandwidth-validation-system" class="md-nav__link">
    <span class="md-ellipsis">
      Bandwidth Validation System
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#state-machine-extensions" class="md-nav__link">
    <span class="md-ellipsis">
      State Machine Extensions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technical-features" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Technical Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Technical Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-framework-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Framework Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#validation-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Engine
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensibility" class="md-nav__link">
    <span class="md-ellipsis">
      Extensibility
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-project-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Project Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Component Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#available-maven-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Available Maven Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-urls" class="md-nav__link">
    <span class="md-ellipsis">
      Development URLs
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Deployment
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="extensions-application-overview">Extensions Application Overview<a class="headerlink" href="#extensions-application-overview" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#introduction">Introduction</a></li>
<li><a href="#qes-plugin-architecture">QES Plugin Architecture</a></li>
<li><a href="#technology-stack">Technology Stack</a></li>
<li><a href="#key-features">Key Features</a></li>
<li><a href="#project-structure">Project Structure</a></li>
<li><a href="#development-environment">Development Environment</a></li>
</ul>
<h2 id="introduction">Introduction<a class="headerlink" href="#introduction" title="Permanent link">&para;</a></h2>
<p>The <strong>nc-cloud-bss-oc-ui-be-extension-b2b</strong> is a sophisticated QES (Quote Engine Service) and QSS (Quote Submission Service) plugin framework that extends the NetCracker Order Capture Product with TELUS-specific business logic, validation rules, quote modification capabilities, and comprehensive quote submission workflows. This enterprise-grade extension system provides custom bandwidth validators, quote modificators, state machine validators, submission processors, approval workflows, and business rule engines tailored for TELUS B2B telecommunications services.</p>
<h3 id="purpose-and-scope">Purpose and Scope<a class="headerlink" href="#purpose-and-scope" title="Permanent link">&para;</a></h3>
<p>The extensions application is designed to:
- <strong>Extend Order Capture Product</strong>: Custom business logic plugins for TELUS requirements
- <strong>Process Quote Submissions</strong>: Comprehensive quote submission workflows and approval processes
- <strong>Validate Network Services</strong>: Specialized bandwidth validators for WAN L2/L3 services
- <strong>Modify Quote Behavior</strong>: Dynamic quote adjustments and pricing rules
- <strong>Enforce Business Rules</strong>: TELUS-specific validation and compliance logic for quotes and submissions
- <strong>Manage Quote Lifecycle</strong>: Custom expiration periods and state transitions
- <strong>Enable Approval Workflows</strong>: Multi-level approval processes for quote submissions
- <strong>Support Submission Integration</strong>: External system integration for submission processing</p>
<h2 id="qes-plugin-architecture">QES Plugin Architecture<a class="headerlink" href="#qes-plugin-architecture" title="Permanent link">&para;</a></h2>
<h3 id="high-level-architecture">High-Level Architecture<a class="headerlink" href="#high-level-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;QES Plugin Framework&quot;
        A[Order Capture Product] --&gt; B[QES Engine]
        B --&gt; C[Plugin Registry]
        C --&gt; D[TELUS B2B Plugins]
    end

    subgraph &quot;TELUS B2B Extensions&quot;
        D --&gt; E[Quote Modificators]
        D --&gt; F[Bandwidth Validators]
        D --&gt; G[State Validators]
        D --&gt; H[Business Rules]

        E --&gt; I[TelusQuoteDeltaModificatorImpl]
        E --&gt; J[TelusQuoteResultingDeltaModificator]
        E --&gt; K[TelusQuoteExpirationPeriodProvider]

        F --&gt; L[TelusWanL2CirBandwidthValidator]
        F --&gt; M[TelusWanL2EvcBandwidthValidator]
        F --&gt; N[TelusWanL3IpQosBandwidthValidator]

        G --&gt; O[TelusStateMachineExtensionValidator]

        H --&gt; P[Common Utils]
        H --&gt; Q[Bandwidth Utils]
        H --&gt; R[Validation Utils]
    end

    subgraph &quot;Integration Points&quot;
        S[Order Capture APIs]
        T[Quote Engine]
        U[Validation Engine]
        V[State Machine]
    end

    I --&gt; T
    J --&gt; T
    K --&gt; T
    L --&gt; U
    M --&gt; U
    N --&gt; U
    O --&gt; V

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style D fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style E fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="plugin-lifecycle">Plugin Lifecycle<a class="headerlink" href="#plugin-lifecycle" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant OCP as Order Capture Product
    participant QES as QES Engine
    participant Plugin as TELUS Plugin
    participant Validator as Bandwidth Validator
    participant Modificator as Quote Modificator

    OCP-&gt;&gt;QES: Quote Processing Request
    QES-&gt;&gt;Plugin: Load Plugin
    Plugin-&gt;&gt;Plugin: Initialize Components

    QES-&gt;&gt;Validator: Validate Bandwidth
    Validator-&gt;&gt;Validator: Check L2/L3 Rules
    Validator--&gt;&gt;QES: Validation Result

    QES-&gt;&gt;Modificator: Modify Quote
    Modificator-&gt;&gt;Modificator: Apply Business Rules
    Modificator--&gt;&gt;QES: Quote Delta

    QES-&gt;&gt;Plugin: State Validation
    Plugin-&gt;&gt;Plugin: Validate Transition
    Plugin--&gt;&gt;QES: State Result

    QES--&gt;&gt;OCP: Processed Quote
</code></pre></div>
<h2 id="technology-stack">Technology Stack<a class="headerlink" href="#technology-stack" title="Permanent link">&para;</a></h2>
<h3 id="core-technologies">Core Technologies<a class="headerlink" href="#core-technologies" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Technology</th>
<th>Version</th>
<th>Purpose</th>
<th>Key Features</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>QES Plugin Framework</strong></td>
<td>2023.3.2.0</td>
<td>Extension Platform</td>
<td>Plugin lifecycle, dependency injection, event handling</td>
</tr>
<tr>
<td><strong>Java</strong></td>
<td>17</td>
<td>Programming Language</td>
<td>Modern language features, performance, enterprise support</td>
</tr>
<tr>
<td><strong>Maven</strong></td>
<td>3.x</td>
<td>Build Tool</td>
<td>Dependency management, plugin packaging, lifecycle management</td>
</tr>
<tr>
<td><strong>Lombok</strong></td>
<td>1.18.30</td>
<td>Code Generation</td>
<td>Boilerplate reduction, annotations, builder patterns</td>
</tr>
<tr>
<td><strong>SLF4J + Logback</strong></td>
<td>2.x</td>
<td>Logging Framework</td>
<td>Structured logging, performance, configuration</td>
</tr>
</tbody>
</table>
<h3 id="plugin-framework-dependencies">Plugin Framework Dependencies<a class="headerlink" href="#plugin-framework-dependencies" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Dependency</th>
<th>Version</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>qes-plugin-api</strong></td>
<td>2023.3.2.0</td>
<td>Core Plugin API</td>
</tr>
<tr>
<td><strong>order-capture-model</strong></td>
<td>2023.3.2.63</td>
<td>Data Models</td>
</tr>
<tr>
<td><strong>smartplug-annotations</strong></td>
<td>2023.3.2.0</td>
<td>Plugin Registration</td>
</tr>
<tr>
<td><strong>validation-framework</strong></td>
<td>2023.3.2.0</td>
<td>Validation Engine</td>
</tr>
</tbody>
</table>
<h3 id="development-and-testing-tools">Development and Testing Tools<a class="headerlink" href="#development-and-testing-tools" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;buildTool&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Maven 3.x&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;javaVersion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;17&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;qesFrameworkVersion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2023.3.2.0&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;testingFramework&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;JUnit 5.6.3 + Mockito 5.3.1&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;codeGeneration&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Lombok 1.18.30&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;packaging&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;JAR Plugin&quot;</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="key-features">Key Features<a class="headerlink" href="#key-features" title="Permanent link">&para;</a></h2>
<h3 id="business-features">🎯 <strong>Business Features</strong><a class="headerlink" href="#business-features" title="Permanent link">&para;</a></h3>
<h4 id="quote-modification-engine">Quote Modification Engine<a class="headerlink" href="#quote-modification-engine" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Dynamic Quote Adjustments</strong>: Real-time quote modifications based on business rules</li>
<li><strong>Pricing Rule Application</strong>: TELUS-specific pricing logic and discount calculations</li>
<li><strong>Customer-Specific Logic</strong>: Tailored quote behavior based on customer type and tier</li>
<li><strong>Regulatory Compliance</strong>: Automatic application of regulatory requirements</li>
</ul>
<h4 id="bandwidth-validation-system">Bandwidth Validation System<a class="headerlink" href="#bandwidth-validation-system" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>WAN L2 CIR Validation</strong>: Committed Information Rate validation for Layer 2 services</li>
<li><strong>WAN L2 EVC Validation</strong>: Ethernet Virtual Circuit bandwidth validation</li>
<li><strong>WAN L3 IP QoS Validation</strong>: Layer 3 Quality of Service bandwidth validation</li>
<li><strong>Multi-Service Support</strong>: Comprehensive validation across TELUS service portfolio</li>
</ul>
<h4 id="state-machine-extensions">State Machine Extensions<a class="headerlink" href="#state-machine-extensions" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Custom State Transitions</strong>: TELUS-specific workflow validation</li>
<li><strong>Business Rule Enforcement</strong>: State-dependent business logic validation</li>
<li><strong>Approval Workflows</strong>: Multi-level approval process validation</li>
<li><strong>Lifecycle Management</strong>: Quote expiration and extension logic</li>
</ul>
<h3 id="technical-features">🔧 <strong>Technical Features</strong><a class="headerlink" href="#technical-features" title="Permanent link">&para;</a></h3>
<h4 id="plugin-framework-integration">Plugin Framework Integration<a class="headerlink" href="#plugin-framework-integration" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Hot-Pluggable Architecture</strong>: Dynamic plugin loading and unloading</li>
<li><strong>Dependency Injection</strong>: Spring-based component management</li>
<li><strong>Event-Driven Processing</strong>: Reactive plugin execution</li>
<li><strong>Configuration Management</strong>: External configuration support</li>
</ul>
<h4 id="validation-engine">Validation Engine<a class="headerlink" href="#validation-engine" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Rule-Based Validation</strong>: Configurable business rule validation</li>
<li><strong>Multi-Level Validation</strong>: Service, quote, and workflow validation</li>
<li><strong>Error Aggregation</strong>: Comprehensive validation result reporting</li>
<li><strong>Performance Optimization</strong>: Efficient validation processing</li>
</ul>
<h4 id="extensibility">Extensibility<a class="headerlink" href="#extensibility" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Interface-Based Design</strong>: Clean plugin interfaces and contracts</li>
<li><strong>Utility Libraries</strong>: Reusable components for common operations</li>
<li><strong>Configuration Driven</strong>: Externalized business rules and parameters</li>
<li><strong>Testing Framework</strong>: Comprehensive testing utilities</li>
</ul>
<h2 id="project-structure">Project Structure<a class="headerlink" href="#project-structure" title="Permanent link">&para;</a></h2>
<h3 id="maven-project-organization">Maven Project Organization<a class="headerlink" href="#maven-project-organization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>nc-cloud-bss-oc-ui-be-extension-b2b/
├── b2b-qes-plugin/                                    # Main plugin module
│   ├── src/main/java/
│   │   └── com/netcracker/solutions/telus/qes/extension/plugin/
│   │       ├── TelusQuoteDeltaModificatorImpl.java    # Quote modification engine
│   │       ├── TelusQuoteExpirationPeriodProvider.java # Quote lifecycle management
│   │       ├── TelusQuoteResultingDeltaModificator.java # Final quote modifications
│   │       ├── TelusStateMachineExtensionValidator.java # State transition validation
│   │       ├── bandwidth/                             # Bandwidth validators
│   │       │   ├── TelusWanL2CirBandwidthValidator.java # L2 CIR validation
│   │       │   ├── TelusWanL2EvcBandwidthValidator.java # L2 EVC validation
│   │       │   └── TelusWanL3IpQosBandwidthValidator.java # L3 QoS validation
│   │       ├── common/                                # Utility classes
│   │       │   ├── utils/                             # Common utilities
│   │       │   │   ├── BandwidthValidatorUtils.java   # Bandwidth validation utilities
│   │       │   │   ├── CommonUtils.java               # General utilities
│   │       │   │   ├── ValidationUtils.java           # Validation helpers
│   │       │   │   └── QuoteUtils.java                # Quote manipulation utilities
│   │       │   ├── constants/                         # Constants and enums
│   │       │   │   ├── ServiceTypes.java              # Service type definitions
│   │       │   │   ├── ValidationMessages.java        # Validation message constants
│   │       │   │   └── BusinessRules.java             # Business rule constants
│   │       │   └── model/                             # Internal models
│   │       │       ├── ValidationContext.java        # Validation context
│   │       │       ├── QuoteModificationContext.java  # Modification context
│   │       │       └── BandwidthValidationResult.java # Validation results
│   │       └── config/                                # Configuration classes
│   │           ├── PluginConfiguration.java           # Plugin configuration
│   │           ├── ValidationConfiguration.java       # Validation configuration
│   │           └── BusinessRuleConfiguration.java     # Business rule configuration
│   ├── src/main/resources/                            # Configuration resources
│   │   ├── plugin.properties                          # Plugin properties
│   │   ├── validation-rules.yml                       # Validation rule definitions
│   │   ├── business-rules.yml                         # Business rule definitions
│   │   ├── bandwidth-limits.yml                       # Bandwidth limit configurations
│   │   └── logback-spring.xml                         # Logging configuration
│   ├── src/test/java/                                 # Unit and integration tests
│   │   └── com/netcracker/solutions/telus/qes/extension/plugin/
│   │       ├── TelusQuoteDeltaModificatorImplTest.java # Quote modificator tests
│   │       ├── bandwidth/                             # Bandwidth validator tests
│   │       │   ├── TelusWanL2CirBandwidthValidatorTest.java
│   │       │   ├── TelusWanL2EvcBandwidthValidatorTest.java
│   │       │   └── TelusWanL3IpQosBandwidthValidatorTest.java
│   │       ├── common/utils/                          # Utility tests
│   │       └── integration/                           # Integration tests
│   ├── src/test/resources/                            # Test resources
│   │   ├── test-data/                                 # Test data files
│   │   ├── mock-configurations/                       # Mock configurations
│   │   └── application-test.yml                       # Test configuration
│   └── pom.xml                                        # Module POM configuration
├── deployments/                                       # Deployment configurations
│   ├── charts/                                        # Helm charts
│   │   ├── Chart.yaml                                 # Helm chart definition
│   │   ├── values.yaml                                # Default values
│   │   ├── values-dev.yaml                            # Development values
│   │   ├── values-prod.yaml                           # Production values
│   │   └── templates/                                 # Kubernetes templates
│   │       ├── deployment.yaml                        # Plugin deployment
│   │       ├── configmap.yaml                         # Configuration map
│   │       └── service.yaml                           # Service definition
│   └── docker/                                        # Docker configurations
│       ├── Dockerfile                                 # Container definition
│       └── docker-compose.yml                         # Local development setup
├── docs/                                              # Documentation
│   ├── plugin-development-guide.md                    # Development guide
│   ├── validation-rules.md                            # Validation documentation
│   ├── business-rules.md                              # Business rule documentation
│   └── api-reference.md                               # API reference
├── pom.xml                                            # Parent POM configuration
├── README.md                                          # Project documentation
├── CHANGELOG.md                                       # Version history
└── LICENSE                                            # License information
</code></pre></div>
<h3 id="plugin-component-architecture">Plugin Component Architecture<a class="headerlink" href="#plugin-component-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Plugin Components&quot;
        A[Quote Modificators]
        B[Bandwidth Validators]
        C[State Validators]
        D[Utility Libraries]
    end

    subgraph &quot;Quote Modificators&quot;
        E[TelusQuoteDeltaModificatorImpl]
        F[TelusQuoteResultingDeltaModificator]
        G[TelusQuoteExpirationPeriodProvider]
    end

    subgraph &quot;Bandwidth Validators&quot;
        H[TelusWanL2CirBandwidthValidator]
        I[TelusWanL2EvcBandwidthValidator]
        J[TelusWanL3IpQosBandwidthValidator]
    end

    subgraph &quot;State Validators&quot;
        K[TelusStateMachineExtensionValidator]
    end

    subgraph &quot;Utility Libraries&quot;
        L[BandwidthValidatorUtils]
        M[CommonUtils]
        N[ValidationUtils]
        O[QuoteUtils]
    end

    A --&gt; E
    A --&gt; F
    A --&gt; G

    B --&gt; H
    B --&gt; I
    B --&gt; J

    C --&gt; K

    D --&gt; L
    D --&gt; M
    D --&gt; N
    D --&gt; O

    E --&gt; L
    E --&gt; M
    F --&gt; N
    G --&gt; O
    H --&gt; L
    I --&gt; L
    J --&gt; L
    K --&gt; N

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style C fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h2 id="development-environment">Development Environment<a class="headerlink" href="#development-environment" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Required Software Versions</span>
Java:<span class="w"> </span>OpenJDK<span class="w"> </span><span class="m">17</span><span class="w"> </span>or<span class="w"> </span>higher
Maven:<span class="w"> </span><span class="m">3</span>.8.x<span class="w"> </span>or<span class="w"> </span>higher
QES<span class="w"> </span>Plugin<span class="w"> </span>Framework:<span class="w"> </span><span class="m">2023</span>.3.2.0
Order<span class="w"> </span>Capture<span class="w"> </span>Product:<span class="w"> </span><span class="m">2023</span>.3.2.63
Git:<span class="w"> </span><span class="m">2</span>.x<span class="w"> </span>or<span class="w"> </span>higher
IDE:<span class="w"> </span>IntelliJ<span class="w"> </span>IDEA<span class="w"> </span>or<span class="w"> </span>Eclipse<span class="w"> </span><span class="o">(</span>recommended<span class="o">)</span>
</code></pre></div>
<h3 id="environment-setup">Environment Setup<a class="headerlink" href="#environment-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Clone repository</span>
git<span class="w"> </span>clone<span class="w"> </span>&lt;repository-url&gt;
<span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-be-extension-b2b

<span class="c1"># 2. Verify Java version</span>
java<span class="w"> </span>-version
mvn<span class="w"> </span>-version

<span class="c1"># 3. Build the plugin</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install

<span class="c1"># 4. Package the plugin</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package

<span class="c1"># 5. Verify plugin structure</span>
jar<span class="w"> </span>-tf<span class="w"> </span>b2b-qes-plugin/target/b2b-qes-plugin-*.jar
</code></pre></div>
<h3 id="plugin-configuration">Plugin Configuration<a class="headerlink" href="#plugin-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># plugin.properties</span>
<span class="nt">plugin</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-qes-plugin</span>
<span class="w">  </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1.2.0</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">TELUS B2B Order Capture QES Extensions</span>
<span class="w">  </span><span class="nt">vendor</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">TELUS</span>

<span class="w">  </span><span class="nt">components</span><span class="p">:</span>
<span class="w">    </span><span class="nt">quote-modificators</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">class</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">com.netcracker.solutions.telus.qes.extension.plugin.TelusQuoteDeltaModificatorImpl</span>
<span class="w">        </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">priority</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">class</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">com.netcracker.solutions.telus.qes.extension.plugin.TelusQuoteResultingDeltaModificator</span>
<span class="w">        </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">priority</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">200</span>

<span class="w">    </span><span class="nt">bandwidth-validators</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">class</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">com.netcracker.solutions.telus.qes.extension.plugin.bandwidth.TelusWanL2CirBandwidthValidator</span>
<span class="w">        </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">service-types</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">WAN_L2_CIR</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">class</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">com.netcracker.solutions.telus.qes.extension.plugin.bandwidth.TelusWanL2EvcBandwidthValidator</span>
<span class="w">        </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">service-types</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">WAN_L2_EVC</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">class</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">com.netcracker.solutions.telus.qes.extension.plugin.bandwidth.TelusWanL3IpQosBandwidthValidator</span>
<span class="w">        </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">service-types</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">WAN_L3_IP_QOS</span><span class="p p-Indicator">]</span>

<span class="w">    </span><span class="nt">state-validators</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">class</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">com.netcracker.solutions.telus.qes.extension.plugin.TelusStateMachineExtensionValidator</span>
<span class="w">        </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">states</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">DRAFT</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">SUBMITTED</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">APPROVED</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">CANCELLED</span><span class="p p-Indicator">]</span>
</code></pre></div>
<h3 id="available-maven-commands">Available Maven Commands<a class="headerlink" href="#available-maven-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Build commands</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile<span class="w">                    </span><span class="c1"># Compile plugin source code</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w">                    </span><span class="c1"># Package plugin JAR</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w">                    </span><span class="c1"># Install plugin to local repository</span>
mvn<span class="w"> </span>clean<span class="w"> </span>deploy<span class="w">                     </span><span class="c1"># Deploy plugin to remote repository</span>

<span class="c1"># Plugin-specific commands</span>
mvn<span class="w"> </span>qes:validate-plugin<span class="w">              </span><span class="c1"># Validate plugin structure</span>
mvn<span class="w"> </span>qes:generate-metadata<span class="w">            </span><span class="c1"># Generate plugin metadata</span>
mvn<span class="w"> </span>qes:package-plugin<span class="w">               </span><span class="c1"># Package for QES deployment</span>

<span class="c1"># Development commands</span>
mvn<span class="w"> </span><span class="nb">test</span><span class="w">                            </span><span class="c1"># Run unit tests</span>
mvn<span class="w"> </span>integration-test<span class="w">                </span><span class="c1"># Run integration tests</span>
mvn<span class="w"> </span>verify<span class="w">                          </span><span class="c1"># Run all tests and validations</span>

<span class="c1"># Code quality</span>
mvn<span class="w"> </span>checkstyle:check<span class="w">                </span><span class="c1"># Code style validation</span>
mvn<span class="w"> </span>spotbugs:check<span class="w">                  </span><span class="c1"># Static analysis</span>
mvn<span class="w"> </span>jacoco:report<span class="w">                   </span><span class="c1"># Test coverage report</span>
</code></pre></div>
<h3 id="development-urls">Development URLs<a class="headerlink" href="#development-urls" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Environment</th>
<th>URL</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Local QES Engine</strong></td>
<td>http://localhost:8081</td>
<td>QES development server</td>
</tr>
<tr>
<td><strong>Plugin Registry</strong></td>
<td>http://localhost:8081/plugins</td>
<td>Plugin management</td>
</tr>
<tr>
<td><strong>Plugin Metrics</strong></td>
<td>http://localhost:8081/metrics</td>
<td>Plugin performance metrics</td>
</tr>
<tr>
<td><strong>Plugin Health</strong></td>
<td>http://localhost:8081/health</td>
<td>Plugin health status</td>
</tr>
</tbody>
</table>
<h3 id="plugin-deployment">Plugin Deployment<a class="headerlink" href="#plugin-deployment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Deploy to QES Engine</span>
mvn<span class="w"> </span>qes:deploy-plugin<span class="w"> </span>-Dqes.url<span class="o">=</span>http://localhost:8081

<span class="c1"># Deploy to development environment</span>
mvn<span class="w"> </span>qes:deploy-plugin<span class="w"> </span>-Dqes.url<span class="o">=</span>https://qes-dev.telus.com

<span class="c1"># Deploy to production environment</span>
mvn<span class="w"> </span>qes:deploy-plugin<span class="w"> </span>-Dqes.url<span class="o">=</span>https://qes-prod.telus.com<span class="w"> </span>-Dprofile<span class="o">=</span>production
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../01-plugin-architecture/">Plugin Architecture →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>