// Types for assessment-related data structures

// Input parameters for the trigger_assessment tool
export interface TriggerAssessmentParams {
  owner?: string;
  repo: string;
  branch?: string;
  sha: string;
  commit_message?: string;
}

// Category input in assessment result
export interface CategoryInput {
  status: string;
  value: {
    decorator: string;
    risk: number;
    name: string;
    message: string;
  };
}

// Category in assessment result
export interface Category {
  name: string;
  categoryTotalRisk: number;
  processedInputs: CategoryInput[];
}

// Concise assessment result structure
export interface ConciseResult {
  metadata: {
    owner: string;
    repo: string;
    branch: string;
    gitCommitId: string;
    riskThreshold: number;
  };
  assessmentDbRecord: {
    id: number;
    createdAt: string;
  };
  categories: Record<string, {
    categoryTotalRisk: number;
    plugins: Record<string, number>;
  }>;
  assessmentSummary: {
    totalRisk: number;
    callToAction: {
      infoMessage: string;
      isPassing: boolean;
      actionMessage: string;
      isApprovedToProceed: boolean;
    };
  };
}

// Payload structure for the risk engine assessment request
export interface AssessmentPayload {
  data: {
    metadata: {
      installationId: number;
      owner: string;
      repo: string;
      branch: string;
      gitCommitId: string;
      gitCommitMsg: string;
      gitHubEventName: string;
      gitHubEventAction: string;
      triggerSource: string;
      project: string;
    };
    risk_inputs: any[];
  };
}
