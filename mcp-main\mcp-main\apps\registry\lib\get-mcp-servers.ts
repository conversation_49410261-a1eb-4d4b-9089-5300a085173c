import { MCPServer } from "@/types/servers";
import fs from "fs";
import path from "path";
import { URL } from "url";

export async function getMCPServers(): Promise<MCPServer[]> {
  // Get the root directory of the monorepo
  const rootDir = path.resolve(process.cwd(), "../../");
  const packagesDir = path.join(rootDir, "packages");

  try {
    const packageDirs = fs.readdirSync(packagesDir);

    const servers: MCPServer[] = [];

    for (const dir of packageDirs) {
      const packagePath = path.join(packagesDir, dir);
      const stat = fs.statSync(packagePath);

      if (stat.isDirectory()) {
        const packageJsonPath = path.join(packagePath, "package.json");
        const readmePath = path.join(packagePath, "README.md");

        if (fs.existsSync(packageJsonPath)) {
          try {
            const packageData = JSON.parse(
              fs.readFileSync(packageJsonPath, "utf8"),
            );
            
            // Skip packages that don't follow the MCP naming convention
            if (!packageData.name || !packageData.name.includes("mcp")) {
              continue;
            }
            
            let readme = "";
            if (fs.existsSync(readmePath)) {
              readme = fs.readFileSync(readmePath, "utf8");
            }

            // Extract repository URL from package.json
            let repositoryUrl = "";
            if (packageData.repository) {
              repositoryUrl = typeof packageData.repository === "string" 
                ? packageData.repository 
                : packageData.repository.url || "";
            }

            // Infer package hosting URL
            let packageUrl = "";
            if (repositoryUrl && packageData.name) {
              // Check if the package is hosted on GitHub Packages
              if (packageData.publishConfig?.registry) {
                const registryUrl = new URL(packageData.publishConfig.registry);
                if (registryUrl.host === "npm.pkg.github.com") {
                  // Extract owner and repo from repository URL
                  const repoMatch = repositoryUrl.match(/github\.com\/([^\/]+)\/([^\/\.]+)/);
                  if (repoMatch) {
                    const [, owner, repo] = repoMatch;
                    // Construct GitHub Packages URL
                    packageUrl = `https://github.com/${owner}/${repo}/pkgs/npm/${packageData.name.replace('@telus/', '')}`;
                  }
                }
              }
            }

            servers.push({
              id: dir,
              name: packageData.name,
              description: packageData.description || "",
              version: packageData.version || "0.0.0",
              author: packageData.author || "",
              tags: packageData.keywords || [],
              repository: repositoryUrl,
              packageUrl,
              readme,
            });
          } catch (error) {
            console.error(`Error parsing package.json for ${dir}:`, error);
          }
        }
      }
    }

    return servers;
  } catch (error) {
    console.error("Error reading packages directory:", error);
    return [];
  }
}
