
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/backend/03-service-layer/">
      
      
        <link rel="prev" href="../02-rest-api-design/">
      
      
        <link rel="next" href="../04-integration-layer/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Service Layer - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#service-layer-architecture" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Service Layer
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" checked>
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#service-layer-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Service Layer Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Layer Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#service-architecture-pattern" class="md-nav__link">
    <span class="md-ellipsis">
      Service Architecture Pattern
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-layer-responsibilities" class="md-nav__link">
    <span class="md-ellipsis">
      Service Layer Responsibilities
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quote-service-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Service Implementation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Service Implementation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-quote-service" class="md-nav__link">
    <span class="md-ellipsis">
      Core Quote Service
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#quote-business-rules" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Business Rules
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#order-service-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Order Service Implementation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Order Service Implementation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-order-service" class="md-nav__link">
    <span class="md-ellipsis">
      Core Order Service
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#validation-service" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Service
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Validation Service">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-rule-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Business Rule Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#mapping-service" class="md-nav__link">
    <span class="md-ellipsis">
      Mapping Service
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Mapping Service">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#data-transformation" class="md-nav__link">
    <span class="md-ellipsis">
      Data Transformation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-services" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Services
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration Services">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#circuit-breaker-pattern" class="md-nav__link">
    <span class="md-ellipsis">
      Circuit Breaker Pattern
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#transaction-management" class="md-nav__link">
    <span class="md-ellipsis">
      Transaction Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Transaction Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#declarative-transactions" class="md-nav__link">
    <span class="md-ellipsis">
      Declarative Transactions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#service-layer-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Service Layer Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Layer Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#service-architecture-pattern" class="md-nav__link">
    <span class="md-ellipsis">
      Service Architecture Pattern
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-layer-responsibilities" class="md-nav__link">
    <span class="md-ellipsis">
      Service Layer Responsibilities
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quote-service-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Service Implementation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Service Implementation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-quote-service" class="md-nav__link">
    <span class="md-ellipsis">
      Core Quote Service
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#quote-business-rules" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Business Rules
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#order-service-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Order Service Implementation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Order Service Implementation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-order-service" class="md-nav__link">
    <span class="md-ellipsis">
      Core Order Service
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#validation-service" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Service
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Validation Service">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-rule-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Business Rule Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#mapping-service" class="md-nav__link">
    <span class="md-ellipsis">
      Mapping Service
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Mapping Service">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#data-transformation" class="md-nav__link">
    <span class="md-ellipsis">
      Data Transformation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-services" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Services
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration Services">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#circuit-breaker-pattern" class="md-nav__link">
    <span class="md-ellipsis">
      Circuit Breaker Pattern
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#transaction-management" class="md-nav__link">
    <span class="md-ellipsis">
      Transaction Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Transaction Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#declarative-transactions" class="md-nav__link">
    <span class="md-ellipsis">
      Declarative Transactions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="service-layer-architecture">Service Layer Architecture<a class="headerlink" href="#service-layer-architecture" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#service-layer-overview">Service Layer Overview</a></li>
<li><a href="#quote-service-implementation">Quote Service Implementation</a></li>
<li><a href="#order-service-implementation">Order Service Implementation</a></li>
<li><a href="#validation-service">Validation Service</a></li>
<li><a href="#mapping-service">Mapping Service</a></li>
<li><a href="#integration-services">Integration Services</a></li>
<li><a href="#transaction-management">Transaction Management</a></li>
</ul>
<h2 id="service-layer-overview">Service Layer Overview<a class="headerlink" href="#service-layer-overview" title="Permanent link">&para;</a></h2>
<h3 id="service-architecture-pattern">Service Architecture Pattern<a class="headerlink" href="#service-architecture-pattern" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Service Layer&quot;
        A[Quote Service]
        B[Order Service]
        C[Customer Service]
        D[Service Catalog Service]
        E[Validation Service]
        F[Mapping Service]
    end

    subgraph &quot;Integration Layer&quot;
        G[Order Capture Client]
        H[BSS Integration Client]
        I[External API Client]
        J[Event Publisher]
    end

    subgraph &quot;Utility Services&quot;
        K[Audit Service]
        L[Notification Service]
        M[Cache Service]
        N[Security Service]
    end

    A --&gt; E
    A --&gt; F
    A --&gt; G
    A --&gt; K

    B --&gt; E
    B --&gt; F
    B --&gt; H
    B --&gt; L

    C --&gt; H
    C --&gt; M

    D --&gt; I
    D --&gt; M

    E --&gt; N
    F --&gt; N

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style E fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style G fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="service-layer-responsibilities">Service Layer Responsibilities<a class="headerlink" href="#service-layer-responsibilities" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Service</th>
<th>Primary Responsibilities</th>
<th>Dependencies</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Quote Service</strong></td>
<td>Quote lifecycle management, business rules, pricing</td>
<td>Order Capture Client, Validation Service</td>
</tr>
<tr>
<td><strong>Order Service</strong></td>
<td>Order processing, status management, fulfillment</td>
<td>BSS Integration, Quote Service</td>
</tr>
<tr>
<td><strong>Customer Service</strong></td>
<td>Customer data management, location services</td>
<td>BSS Integration, Cache Service</td>
</tr>
<tr>
<td><strong>Service Catalog Service</strong></td>
<td>Service availability, pricing, configuration</td>
<td>External APIs, Cache Service</td>
</tr>
<tr>
<td><strong>Validation Service</strong></td>
<td>Business rule validation, data integrity</td>
<td>Security Service, Audit Service</td>
</tr>
<tr>
<td><strong>Mapping Service</strong></td>
<td>Data transformation, format conversion</td>
<td>Configuration Service</td>
</tr>
</tbody>
</table>
<h2 id="quote-service-implementation">Quote Service Implementation<a class="headerlink" href="#quote-service-implementation" title="Permanent link">&para;</a></h2>
<h3 id="core-quote-service">Core Quote Service<a class="headerlink" href="#core-quote-service" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Service</span>
<span class="nd">@Transactional</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QuoteServiceImpl</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">QuoteService</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">OrderCaptureClient</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ValidationService</span><span class="w"> </span><span class="n">validationService</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">MappingService</span><span class="w"> </span><span class="n">mappingService</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">AuditService</span><span class="w"> </span><span class="n">auditService</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">EventPublisher</span><span class="w"> </span><span class="n">eventPublisher</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">CacheService</span><span class="w"> </span><span class="n">cacheService</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">QuoteServiceImpl</span><span class="p">(</span>
<span class="w">            </span><span class="n">OrderCaptureClient</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">,</span>
<span class="w">            </span><span class="n">ValidationService</span><span class="w"> </span><span class="n">validationService</span><span class="p">,</span>
<span class="w">            </span><span class="n">MappingService</span><span class="w"> </span><span class="n">mappingService</span><span class="p">,</span>
<span class="w">            </span><span class="n">AuditService</span><span class="w"> </span><span class="n">auditService</span><span class="p">,</span>
<span class="w">            </span><span class="n">EventPublisher</span><span class="w"> </span><span class="n">eventPublisher</span><span class="p">,</span>
<span class="w">            </span><span class="n">CacheService</span><span class="w"> </span><span class="n">cacheService</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">orderCaptureClient</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">validationService</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validationService</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">mappingService</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">auditService</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">auditService</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">eventPublisher</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">eventPublisher</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">cacheService</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">cacheService</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="nd">@Cacheable</span><span class="p">(</span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;quotes&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">key</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;#quoteId&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">getQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Retrieving quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Validate quote access permissions</span>
<span class="w">            </span><span class="n">validationService</span><span class="p">.</span><span class="na">validateQuoteAccess</span><span class="p">(</span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Retrieve quote from Order Capture</span>
<span class="w">            </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">.</span><span class="na">getQuote</span><span class="p">(</span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply business transformations</span>
<span class="w">            </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyBusinessRules</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Audit the access</span>
<span class="w">            </span><span class="n">auditService</span><span class="p">.</span><span class="na">logQuoteAccess</span><span class="p">(</span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">getCurrentUser</span><span class="p">());</span>

<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="p">;</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">OrderCaptureException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to retrieve quote {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">QuoteNotFoundException</span><span class="p">(</span><span class="s">&quot;Quote not found: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="nd">@CacheEvict</span><span class="p">(</span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;quotes&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">allEntries</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">createQuote</span><span class="p">(</span><span class="n">CreateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Creating new quote for customer: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">request</span><span class="p">.</span><span class="na">getCustomerId</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Validate request</span>
<span class="w">            </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">validation</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validationService</span><span class="p">.</span><span class="na">validateCreateQuoteRequest</span><span class="p">(</span><span class="n">request</span><span class="p">);</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">validation</span><span class="p">.</span><span class="na">isValid</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ValidationException</span><span class="p">(</span><span class="s">&quot;Quote validation failed&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">validation</span><span class="p">.</span><span class="na">getErrors</span><span class="p">());</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">            </span><span class="c1">// Transform request</span>
<span class="w">            </span><span class="n">OrderCaptureQuoteRequest</span><span class="w"> </span><span class="n">ocRequest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">toOrderCaptureRequest</span><span class="p">(</span><span class="n">request</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Create quote in Order Capture</span>
<span class="w">            </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">.</span><span class="na">createQuote</span><span class="p">(</span><span class="n">ocRequest</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply TELUS business rules</span>
<span class="w">            </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyTelusBusinessRules</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Audit the creation</span>
<span class="w">            </span><span class="n">auditService</span><span class="p">.</span><span class="na">logQuoteCreation</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="n">getCurrentUser</span><span class="p">());</span>

<span class="w">            </span><span class="c1">// Publish quote created event</span>
<span class="w">            </span><span class="n">eventPublisher</span><span class="p">.</span><span class="na">publishQuoteCreatedEvent</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Quote created successfully: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="p">;</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to create quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">QuoteCreationException</span><span class="p">(</span><span class="s">&quot;Failed to create quote&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="nd">@CacheEvict</span><span class="p">(</span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;quotes&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">key</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;#quoteId&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">updateQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">UpdateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Updating quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Validate update permissions</span>
<span class="w">            </span><span class="n">validationService</span><span class="p">.</span><span class="na">validateQuoteUpdatePermissions</span><span class="p">(</span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate business rules</span>
<span class="w">            </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">validation</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validationService</span><span class="p">.</span><span class="na">validateUpdateQuoteRequest</span><span class="p">(</span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">request</span><span class="p">);</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">validation</span><span class="p">.</span><span class="na">isValid</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ValidationException</span><span class="p">(</span><span class="s">&quot;Quote update validation failed&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">validation</span><span class="p">.</span><span class="na">getErrors</span><span class="p">());</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">            </span><span class="c1">// Get current quote</span>
<span class="w">            </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">currentQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getQuote</span><span class="p">(</span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Check if quote is in editable state</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">isQuoteEditable</span><span class="p">(</span><span class="n">currentQuote</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BusinessException</span><span class="p">(</span><span class="s">&quot;Quote is not in editable state: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">currentQuote</span><span class="p">.</span><span class="na">getStatus</span><span class="p">());</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">            </span><span class="c1">// Transform and update</span>
<span class="w">            </span><span class="n">OrderCaptureQuoteRequest</span><span class="w"> </span><span class="n">ocRequest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">toOrderCaptureUpdateRequest</span><span class="p">(</span><span class="n">request</span><span class="p">);</span>
<span class="w">            </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">updatedQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">.</span><span class="na">updateQuote</span><span class="p">(</span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">ocRequest</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply business rules</span>
<span class="w">            </span><span class="n">updatedQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyBusinessRules</span><span class="p">(</span><span class="n">updatedQuote</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Audit the update</span>
<span class="w">            </span><span class="n">auditService</span><span class="p">.</span><span class="na">logQuoteUpdate</span><span class="p">(</span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">getCurrentUser</span><span class="p">(),</span><span class="w"> </span><span class="n">currentQuote</span><span class="p">,</span><span class="w"> </span><span class="n">updatedQuote</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Publish quote updated event</span>
<span class="w">            </span><span class="n">eventPublisher</span><span class="p">.</span><span class="na">publishQuoteUpdatedEvent</span><span class="p">(</span><span class="n">updatedQuote</span><span class="p">);</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Quote updated successfully: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">updatedQuote</span><span class="p">;</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to update quote {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">QuoteUpdateException</span><span class="p">(</span><span class="s">&quot;Failed to update quote&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">approveQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteApprovalRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Approving quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Validate approval permissions</span>
<span class="w">            </span><span class="n">validationService</span><span class="p">.</span><span class="na">validateQuoteApprovalPermissions</span><span class="p">(</span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Get current quote</span>
<span class="w">            </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">currentQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getQuote</span><span class="p">(</span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate quote is approvable</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">isQuoteApprovable</span><span class="p">(</span><span class="n">currentQuote</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BusinessException</span><span class="p">(</span><span class="s">&quot;Quote cannot be approved in current state: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">currentQuote</span><span class="p">.</span><span class="na">getStatus</span><span class="p">());</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">            </span><span class="c1">// Perform approval business logic</span>
<span class="w">            </span><span class="n">QuoteApprovalResult</span><span class="w"> </span><span class="n">approvalResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">processQuoteApproval</span><span class="p">(</span><span class="n">currentQuote</span><span class="p">,</span><span class="w"> </span><span class="n">request</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Update quote status</span>
<span class="w">            </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">approvedQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">.</span><span class="na">approveQuote</span><span class="p">(</span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">approvalResult</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply post-approval business rules</span>
<span class="w">            </span><span class="n">approvedQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyPostApprovalRules</span><span class="p">(</span><span class="n">approvedQuote</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Audit the approval</span>
<span class="w">            </span><span class="n">auditService</span><span class="p">.</span><span class="na">logQuoteApproval</span><span class="p">(</span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">getCurrentUser</span><span class="p">(),</span><span class="w"> </span><span class="n">request</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Publish quote approved event</span>
<span class="w">            </span><span class="n">eventPublisher</span><span class="p">.</span><span class="na">publishQuoteApprovedEvent</span><span class="p">(</span><span class="n">approvedQuote</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Send approval notifications</span>
<span class="w">            </span><span class="n">sendApprovalNotifications</span><span class="p">(</span><span class="n">approvedQuote</span><span class="p">);</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Quote approved successfully: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">approvedQuote</span><span class="p">;</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to approve quote {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">QuoteApprovalException</span><span class="p">(</span><span class="s">&quot;Failed to approve quote&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">applyTelusBusinessRules</span><span class="p">(</span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Apply TELUS-specific business rules</span>
<span class="w">        </span><span class="n">quote</span><span class="p">.</span><span class="na">setExpirationDate</span><span class="p">(</span><span class="n">calculateExpirationDate</span><span class="p">(</span><span class="n">quote</span><span class="p">));</span>
<span class="w">        </span><span class="n">quote</span><span class="p">.</span><span class="na">setPricing</span><span class="p">(</span><span class="n">applyTelusDiscounts</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getPricing</span><span class="p">()));</span>
<span class="w">        </span><span class="n">quote</span><span class="p">.</span><span class="na">setTermsAndConditions</span><span class="p">(</span><span class="n">getTelusTermsAndConditions</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Apply regulatory compliance rules</span>
<span class="w">        </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyRegulatoryCompliance</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isQuoteEditable</span><span class="p">(</span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">Arrays</span><span class="p">.</span><span class="na">asList</span><span class="p">(</span><span class="n">QuoteStatus</span><span class="p">.</span><span class="na">DRAFT</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteStatus</span><span class="p">.</span><span class="na">IN_REVIEW</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">contains</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getStatus</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isQuoteApprovable</span><span class="p">(</span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getStatus</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteStatus</span><span class="p">.</span><span class="na">IN_REVIEW</span><span class="w"> </span><span class="o">&amp;&amp;</span>
<span class="w">               </span><span class="n">quote</span><span class="p">.</span><span class="na">getPricing</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span>
<span class="w">               </span><span class="n">quote</span><span class="p">.</span><span class="na">getLineItems</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span>
<span class="w">               </span><span class="o">!</span><span class="n">quote</span><span class="p">.</span><span class="na">getLineItems</span><span class="p">().</span><span class="na">isEmpty</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="quote-business-rules">Quote Business Rules<a class="headerlink" href="#quote-business-rules" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QuoteBusinessRules</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ConfigurationService</span><span class="w"> </span><span class="n">configurationService</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">PricingService</span><span class="w"> </span><span class="n">pricingService</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">LocalDate</span><span class="w"> </span><span class="nf">calculateExpirationDate</span><span class="p">(</span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Get expiration period from configuration</span>
<span class="w">        </span><span class="kt">int</span><span class="w"> </span><span class="n">expirationDays</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">configurationService</span><span class="p">.</span><span class="na">getQuoteExpirationDays</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Apply business rules based on quote value</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getPricing</span><span class="p">().</span><span class="na">getTotal</span><span class="p">().</span><span class="na">compareTo</span><span class="p">(</span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">valueOf</span><span class="p">(</span><span class="mi">100000</span><span class="p">))</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">expirationDays</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">configurationService</span><span class="p">.</span><span class="na">getHighValueQuoteExpirationDays</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">LocalDate</span><span class="p">.</span><span class="na">now</span><span class="p">().</span><span class="na">plusDays</span><span class="p">(</span><span class="n">expirationDays</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuotePricing</span><span class="w"> </span><span class="nf">applyTelusDiscounts</span><span class="p">(</span><span class="n">QuotePricing</span><span class="w"> </span><span class="n">pricing</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Apply volume discounts</span>
<span class="w">        </span><span class="n">pricing</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyVolumeDiscounts</span><span class="p">(</span><span class="n">pricing</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply customer-specific discounts</span>
<span class="w">        </span><span class="n">pricing</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyCustomerDiscounts</span><span class="p">(</span><span class="n">pricing</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply promotional discounts</span>
<span class="w">        </span><span class="n">pricing</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyPromotionalDiscounts</span><span class="p">(</span><span class="n">pricing</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">pricing</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">applyRegulatoryCompliance</span><span class="p">(</span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Apply CRTC compliance rules</span>
<span class="w">        </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyCrtcCompliance</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply provincial regulations</span>
<span class="w">        </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyProvincialRegulations</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply federal regulations</span>
<span class="w">        </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyFederalRegulations</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="order-service-implementation">Order Service Implementation<a class="headerlink" href="#order-service-implementation" title="Permanent link">&para;</a></h2>
<h3 id="core-order-service">Core Order Service<a class="headerlink" href="#core-order-service" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Service</span>
<span class="nd">@Transactional</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">OrderServiceImpl</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">OrderService</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">BssIntegrationClient</span><span class="w"> </span><span class="n">bssIntegrationClient</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">QuoteService</span><span class="w"> </span><span class="n">quoteService</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ValidationService</span><span class="w"> </span><span class="n">validationService</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">WorkflowService</span><span class="w"> </span><span class="n">workflowService</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">NotificationService</span><span class="w"> </span><span class="n">notificationService</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">OrderResponse</span><span class="w"> </span><span class="nf">createOrder</span><span class="p">(</span><span class="n">CreateOrderRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Creating order from quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">request</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Validate quote exists and is approved</span>
<span class="w">            </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quoteService</span><span class="p">.</span><span class="na">getQuote</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getStatus</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="n">QuoteStatus</span><span class="p">.</span><span class="na">APPROVED</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BusinessException</span><span class="p">(</span><span class="s">&quot;Quote must be approved before creating order&quot;</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">            </span><span class="c1">// Validate order creation request</span>
<span class="w">            </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">validation</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validationService</span><span class="p">.</span><span class="na">validateCreateOrderRequest</span><span class="p">(</span><span class="n">request</span><span class="p">);</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">validation</span><span class="p">.</span><span class="na">isValid</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ValidationException</span><span class="p">(</span><span class="s">&quot;Order validation failed&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">validation</span><span class="p">.</span><span class="na">getErrors</span><span class="p">());</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">            </span><span class="c1">// Transform quote to order</span>
<span class="w">            </span><span class="n">OrderRequest</span><span class="w"> </span><span class="n">orderRequest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">quoteToOrderRequest</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">request</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Create order in BSS system</span>
<span class="w">            </span><span class="n">OrderResponse</span><span class="w"> </span><span class="n">order</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bssIntegrationClient</span><span class="p">.</span><span class="na">createOrder</span><span class="p">(</span><span class="n">orderRequest</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Initialize order workflow</span>
<span class="w">            </span><span class="n">workflowService</span><span class="p">.</span><span class="na">initializeOrderWorkflow</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Send order confirmation</span>
<span class="w">            </span><span class="n">notificationService</span><span class="p">.</span><span class="na">sendOrderConfirmation</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Audit order creation</span>
<span class="w">            </span><span class="n">auditService</span><span class="p">.</span><span class="na">logOrderCreation</span><span class="p">(</span><span class="n">order</span><span class="p">.</span><span class="na">getOrderId</span><span class="p">(),</span><span class="w"> </span><span class="n">getCurrentUser</span><span class="p">());</span>

<span class="w">            </span><span class="c1">// Publish order created event</span>
<span class="w">            </span><span class="n">eventPublisher</span><span class="p">.</span><span class="na">publishOrderCreatedEvent</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Order created successfully: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">order</span><span class="p">.</span><span class="na">getOrderId</span><span class="p">());</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">order</span><span class="p">;</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to create order from quote {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">request</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">OrderCreationException</span><span class="p">(</span><span class="s">&quot;Failed to create order&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">OrderResponse</span><span class="w"> </span><span class="nf">updateOrderStatus</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">orderId</span><span class="p">,</span><span class="w"> </span><span class="n">OrderStatus</span><span class="w"> </span><span class="n">newStatus</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Updating order status: {} to {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">orderId</span><span class="p">,</span><span class="w"> </span><span class="n">newStatus</span><span class="p">);</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Get current order</span>
<span class="w">            </span><span class="n">OrderResponse</span><span class="w"> </span><span class="n">currentOrder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getOrder</span><span class="p">(</span><span class="n">orderId</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate status transition</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">isValidStatusTransition</span><span class="p">(</span><span class="n">currentOrder</span><span class="p">.</span><span class="na">getStatus</span><span class="p">(),</span><span class="w"> </span><span class="n">newStatus</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BusinessException</span><span class="p">(</span><span class="s">&quot;Invalid status transition from &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span>
<span class="w">                    </span><span class="n">currentOrder</span><span class="p">.</span><span class="na">getStatus</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot; to &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">newStatus</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">            </span><span class="c1">// Update status in BSS system</span>
<span class="w">            </span><span class="n">OrderResponse</span><span class="w"> </span><span class="n">updatedOrder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bssIntegrationClient</span><span class="p">.</span><span class="na">updateOrderStatus</span><span class="p">(</span><span class="n">orderId</span><span class="p">,</span><span class="w"> </span><span class="n">newStatus</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Process status-specific business logic</span>
<span class="w">            </span><span class="n">processStatusChange</span><span class="p">(</span><span class="n">updatedOrder</span><span class="p">,</span><span class="w"> </span><span class="n">currentOrder</span><span class="p">.</span><span class="na">getStatus</span><span class="p">(),</span><span class="w"> </span><span class="n">newStatus</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Audit status change</span>
<span class="w">            </span><span class="n">auditService</span><span class="p">.</span><span class="na">logOrderStatusChange</span><span class="p">(</span><span class="n">orderId</span><span class="p">,</span><span class="w"> </span><span class="n">getCurrentUser</span><span class="p">(),</span><span class="w"> </span>
<span class="w">                </span><span class="n">currentOrder</span><span class="p">.</span><span class="na">getStatus</span><span class="p">(),</span><span class="w"> </span><span class="n">newStatus</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Publish order status changed event</span>
<span class="w">            </span><span class="n">eventPublisher</span><span class="p">.</span><span class="na">publishOrderStatusChangedEvent</span><span class="p">(</span><span class="n">updatedOrder</span><span class="p">);</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Order status updated successfully: {} -&gt; {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">orderId</span><span class="p">,</span><span class="w"> </span><span class="n">newStatus</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">updatedOrder</span><span class="p">;</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to update order status {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">orderId</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">OrderUpdateException</span><span class="p">(</span><span class="s">&quot;Failed to update order status&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">processStatusChange</span><span class="p">(</span><span class="n">OrderResponse</span><span class="w"> </span><span class="n">order</span><span class="p">,</span><span class="w"> </span><span class="n">OrderStatus</span><span class="w"> </span><span class="n">oldStatus</span><span class="p">,</span><span class="w"> </span><span class="n">OrderStatus</span><span class="w"> </span><span class="n">newStatus</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">newStatus</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">IN_PROGRESS</span><span class="p">:</span>
<span class="w">                </span><span class="n">workflowService</span><span class="p">.</span><span class="na">startOrderProcessing</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">                </span><span class="n">notificationService</span><span class="p">.</span><span class="na">sendOrderProcessingNotification</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">COMPLETED</span><span class="p">:</span>
<span class="w">                </span><span class="n">workflowService</span><span class="p">.</span><span class="na">completeOrderProcessing</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">                </span><span class="n">notificationService</span><span class="p">.</span><span class="na">sendOrderCompletionNotification</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">CANCELLED</span><span class="p">:</span>
<span class="w">                </span><span class="n">workflowService</span><span class="p">.</span><span class="na">cancelOrderProcessing</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">                </span><span class="n">notificationService</span><span class="p">.</span><span class="na">sendOrderCancellationNotification</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">FAILED</span><span class="p">:</span>
<span class="w">                </span><span class="n">workflowService</span><span class="p">.</span><span class="na">handleOrderFailure</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">                </span><span class="n">notificationService</span><span class="p">.</span><span class="na">sendOrderFailureNotification</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="validation-service">Validation Service<a class="headerlink" href="#validation-service" title="Permanent link">&para;</a></h2>
<h3 id="business-rule-validation">Business Rule Validation<a class="headerlink" href="#business-rule-validation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Service</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">ValidationServiceImpl</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">ValidationService</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">QuoteValidator</span><span class="o">&gt;</span><span class="w"> </span><span class="n">quoteValidators</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">OrderValidator</span><span class="o">&gt;</span><span class="w"> </span><span class="n">orderValidators</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">SecurityService</span><span class="w"> </span><span class="n">securityService</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validateCreateQuoteRequest</span><span class="p">(</span><span class="n">CreateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Validating create quote request for customer: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">request</span><span class="p">.</span><span class="na">getCustomerId</span><span class="p">());</span>

<span class="w">        </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Run all quote validators</span>
<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">QuoteValidator</span><span class="w"> </span><span class="n">validator</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">quoteValidators</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">validatorResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validator</span><span class="p">.</span><span class="na">validate</span><span class="p">(</span><span class="n">request</span><span class="p">);</span>
<span class="w">            </span><span class="n">result</span><span class="p">.</span><span class="na">merge</span><span class="p">(</span><span class="n">validatorResult</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Additional business rule validations</span>
<span class="w">        </span><span class="n">validateCustomerEligibility</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getCustomerId</span><span class="p">(),</span><span class="w"> </span><span class="n">result</span><span class="p">);</span>
<span class="w">        </span><span class="n">validateServiceAvailability</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getLineItems</span><span class="p">(),</span><span class="w"> </span><span class="n">result</span><span class="p">);</span>
<span class="w">        </span><span class="n">validatePricingRules</span><span class="p">(</span><span class="n">request</span><span class="p">,</span><span class="w"> </span><span class="n">result</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Quote validation completed. Valid: {}, Errors: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">result</span><span class="p">.</span><span class="na">isValid</span><span class="p">(),</span><span class="w"> </span><span class="n">result</span><span class="p">.</span><span class="na">getErrors</span><span class="p">().</span><span class="na">size</span><span class="p">());</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">result</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validateUpdateQuoteRequest</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">UpdateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Validating update quote request for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">        </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Validate quote exists and is editable</span>
<span class="w">        </span><span class="n">validateQuoteExists</span><span class="p">(</span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">result</span><span class="p">);</span>
<span class="w">        </span><span class="n">validateQuoteEditable</span><span class="p">(</span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">result</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Validate update permissions</span>
<span class="w">        </span><span class="n">validateUpdatePermissions</span><span class="p">(</span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">result</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Validate business rules</span>
<span class="w">        </span><span class="n">validateUpdateBusinessRules</span><span class="p">(</span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">request</span><span class="p">,</span><span class="w"> </span><span class="n">result</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">result</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateQuoteAccess</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">currentUser</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">securityService</span><span class="p">.</span><span class="na">getCurrentUser</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">securityService</span><span class="p">.</span><span class="na">hasQuoteAccess</span><span class="p">(</span><span class="n">currentUser</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">AccessDeniedException</span><span class="p">(</span><span class="s">&quot;User does not have access to quote: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">quoteId</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateCustomerEligibility</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">customerId</span><span class="p">,</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">result</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">CustomerInfo</span><span class="w"> </span><span class="n">customer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">customerService</span><span class="p">.</span><span class="na">getCustomer</span><span class="p">(</span><span class="n">customerId</span><span class="p">);</span>

<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">customer</span><span class="p">.</span><span class="na">getStatus</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="n">CustomerStatus</span><span class="p">.</span><span class="na">ACTIVE</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">result</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span><span class="s">&quot;customer.status&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Customer is not active&quot;</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">customer</span><span class="p">.</span><span class="na">getCreditRating</span><span class="p">()</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="n">getMinimumCreditRating</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">result</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span><span class="s">&quot;customer.credit&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Customer credit rating is insufficient&quot;</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">CustomerNotFoundException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">result</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span><span class="s">&quot;customer.notFound&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Customer not found: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">customerId</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateServiceAvailability</span><span class="p">(</span><span class="n">List</span><span class="o">&lt;</span><span class="n">CreateQuoteLineItemRequest</span><span class="o">&gt;</span><span class="w"> </span><span class="n">lineItems</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                           </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">result</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">CreateQuoteLineItemRequest</span><span class="w"> </span><span class="n">item</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">lineItems</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">ServiceAvailability</span><span class="w"> </span><span class="n">availability</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">serviceCatalogService</span>
<span class="w">                </span><span class="p">.</span><span class="na">checkAvailability</span><span class="p">(</span><span class="n">item</span><span class="p">.</span><span class="na">getServiceId</span><span class="p">(),</span><span class="w"> </span><span class="n">item</span><span class="p">.</span><span class="na">getLocation</span><span class="p">());</span>

<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">availability</span><span class="p">.</span><span class="na">isAvailable</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">result</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span><span class="s">&quot;service.availability&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                    </span><span class="s">&quot;Service not available: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">item</span><span class="p">.</span><span class="na">getServiceId</span><span class="p">());</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="mapping-service">Mapping Service<a class="headerlink" href="#mapping-service" title="Permanent link">&para;</a></h2>
<h3 id="data-transformation">Data Transformation<a class="headerlink" href="#data-transformation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Service</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">MappingServiceImpl</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">MappingService</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ObjectMapper</span><span class="w"> </span><span class="n">objectMapper</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ConfigurationService</span><span class="w"> </span><span class="n">configurationService</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">OrderCaptureQuoteRequest</span><span class="w"> </span><span class="nf">toOrderCaptureRequest</span><span class="p">(</span><span class="n">CreateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Mapping create quote request to Order Capture format&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">OrderCaptureQuoteRequest</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">customerId</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getCustomerId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">lineItems</span><span class="p">(</span><span class="n">mapLineItems</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getLineItems</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">expirationDate</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getExpirationDate</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">priority</span><span class="p">(</span><span class="n">mapPriority</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getPriority</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">attributes</span><span class="p">(</span><span class="n">mapAttributes</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">fromOrderCapture</span><span class="p">(</span><span class="n">OrderCaptureQuote</span><span class="w"> </span><span class="n">ocQuote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Mapping Order Capture quote to response format&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">quoteId</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">version</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">.</span><span class="na">getVersion</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">status</span><span class="p">(</span><span class="n">mapStatus</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">.</span><span class="na">getStatus</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">customer</span><span class="p">(</span><span class="n">mapCustomer</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">.</span><span class="na">getCustomer</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">lineItems</span><span class="p">(</span><span class="n">mapLineItemsFromOC</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">.</span><span class="na">getLineItems</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">pricing</span><span class="p">(</span><span class="n">mapPricing</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">.</span><span class="na">getPricing</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">validity</span><span class="p">(</span><span class="n">mapValidity</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">.</span><span class="na">getValidity</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">createdDate</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">.</span><span class="na">getCreatedDate</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">lastModifiedDate</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">.</span><span class="na">getLastModifiedDate</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">createdBy</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">.</span><span class="na">getCreatedBy</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">lastModifiedBy</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">.</span><span class="na">getLastModifiedBy</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">metadata</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">.</span><span class="na">getMetadata</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">links</span><span class="p">(</span><span class="n">generateHateoasLinks</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">OrderRequest</span><span class="w"> </span><span class="nf">quoteToOrderRequest</span><span class="p">(</span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">CreateOrderRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Mapping quote to order request: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">OrderRequest</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">quoteId</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">customerId</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getCustomer</span><span class="p">().</span><span class="na">getCustomerId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">orderItems</span><span class="p">(</span><span class="n">mapQuoteLineItemsToOrderItems</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getLineItems</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">billingAccount</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getBillingAccount</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">serviceAddress</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getServiceAddress</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">requestedDate</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getRequestedDate</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">priority</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getPriority</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">specialInstructions</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getSpecialInstructions</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">Link</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">generateHateoasLinks</span><span class="p">(</span><span class="n">OrderCaptureQuote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">List</span><span class="o">&lt;</span><span class="n">Link</span><span class="o">&gt;</span><span class="w"> </span><span class="n">links</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ArrayList</span><span class="o">&lt;&gt;</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Self link</span>
<span class="w">        </span><span class="n">links</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">Link</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;/api/v1/quotes/&quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="s">&quot;self&quot;</span><span class="p">));</span>

<span class="w">        </span><span class="c1">// Status-dependent links</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getStatus</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">OrderCaptureQuoteStatus</span><span class="p">.</span><span class="na">DRAFT</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">links</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">Link</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;/api/v1/quotes/&quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="s">&quot;update&quot;</span><span class="p">));</span>
<span class="w">            </span><span class="n">links</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">Link</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;/api/v1/quotes/&quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;/approve&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;approve&quot;</span><span class="p">));</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getStatus</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">OrderCaptureQuoteStatus</span><span class="p">.</span><span class="na">APPROVED</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">links</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">Link</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;/api/v1/orders&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;create-order&quot;</span><span class="p">));</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Export links</span>
<span class="w">        </span><span class="n">links</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">Link</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;/api/v1/quotes/&quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;/export?format=PDF&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;export-pdf&quot;</span><span class="p">));</span>
<span class="w">        </span><span class="n">links</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">Link</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;/api/v1/quotes/&quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;/export?format=EXCEL&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;export-excel&quot;</span><span class="p">));</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">links</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="integration-services">Integration Services<a class="headerlink" href="#integration-services" title="Permanent link">&para;</a></h2>
<h3 id="circuit-breaker-pattern">Circuit Breaker Pattern<a class="headerlink" href="#circuit-breaker-pattern" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">ResilientOrderCaptureClient</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">OrderCaptureClient</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">CircuitBreaker</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">RetryTemplate</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">CacheService</span><span class="w"> </span><span class="n">cacheService</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">ResilientOrderCaptureClient</span><span class="p">(</span>
<span class="w">            </span><span class="n">OrderCaptureClient</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">,</span>
<span class="w">            </span><span class="n">CircuitBreakerFactory</span><span class="w"> </span><span class="n">circuitBreakerFactory</span><span class="p">,</span>
<span class="w">            </span><span class="n">RetryTemplate</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">,</span>
<span class="w">            </span><span class="n">CacheService</span><span class="w"> </span><span class="n">cacheService</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">orderCaptureClient</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">circuitBreaker</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">circuitBreakerFactory</span><span class="p">.</span><span class="na">create</span><span class="p">(</span><span class="s">&quot;order-capture&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">retryTemplate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">cacheService</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">cacheService</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">getQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">.</span><span class="na">executeSupplier</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">.</span><span class="na">execute</span><span class="p">(</span><span class="n">context</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="k">return</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">.</span><span class="na">getQuote</span><span class="p">(</span><span class="n">quoteId</span><span class="p">);</span>
<span class="w">                </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">log</span><span class="p">.</span><span class="na">warn</span><span class="p">(</span><span class="s">&quot;Attempt {} failed for quote {}: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                            </span><span class="n">context</span><span class="p">.</span><span class="na">getRetryCount</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">                    </span><span class="k">throw</span><span class="w"> </span><span class="n">e</span><span class="p">;</span>
<span class="w">                </span><span class="p">}</span>
<span class="w">            </span><span class="p">});</span>
<span class="w">        </span><span class="p">},</span><span class="w"> </span><span class="n">throwable</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Circuit breaker fallback for quote {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">throwable</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">getCachedQuote</span><span class="p">(</span><span class="n">quoteId</span><span class="p">);</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">getCachedQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">cacheService</span><span class="p">.</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;quotes&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">orElseThrow</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ServiceUnavailableException</span><span class="p">(</span><span class="s">&quot;Quote service temporarily unavailable&quot;</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="transaction-management">Transaction Management<a class="headerlink" href="#transaction-management" title="Permanent link">&para;</a></h2>
<h3 id="declarative-transactions">Declarative Transactions<a class="headerlink" href="#declarative-transactions" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Service</span>
<span class="nd">@Transactional</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TransactionalQuoteService</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Transactional</span><span class="p">(</span><span class="n">readOnly</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">getQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Read-only transaction for better performance</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">.</span><span class="na">getQuote</span><span class="p">(</span><span class="n">quoteId</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Transactional</span><span class="p">(</span><span class="n">rollbackFor</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Exception</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">createQuote</span><span class="p">(</span><span class="n">CreateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Full transaction with rollback on any exception</span>
<span class="w">        </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">.</span><span class="na">createQuote</span><span class="p">(</span><span class="n">request</span><span class="p">);</span>
<span class="w">        </span><span class="n">auditService</span><span class="p">.</span><span class="na">logQuoteCreation</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="n">getCurrentUser</span><span class="p">());</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Transactional</span><span class="p">(</span><span class="n">propagation</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Propagation</span><span class="p">.</span><span class="na">REQUIRES_NEW</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">logAuditEvent</span><span class="p">(</span><span class="n">AuditEvent</span><span class="w"> </span><span class="n">event</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// New transaction for audit logging</span>
<span class="w">        </span><span class="n">auditService</span><span class="p">.</span><span class="na">logEvent</span><span class="p">(</span><span class="n">event</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../04-integration-layer/">Integration Layer →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>