declare module '@modelcontextprotocol/sdk/server/index.js' {
  export class Server {
    constructor(info: any, options: any);
    setRequestHandler(schema: any, handler: Function): void;
    connect(transport: any): Promise<void>;
    close(): Promise<void>;
    onerror: (error: Error) => void;
  }
}

declare module '@modelcontextprotocol/sdk/server/stdio.js' {
  export class StdioServerTransport {}
}

declare module '@modelcontextprotocol/sdk/types.js' {
  export const CallToolRequestSchema: any;
  export const ListToolsRequestSchema: any;
  export enum ErrorCode {
    MethodNotFound,
    InvalidParams,
    InternalError
  }
  export class McpError extends Error {
    constructor(code: ErrorCode, message: string);
  }

  export interface CallToolRequest {
    params: {
      name: string;
      arguments: any;
    };
  }
}
