export interface EpsConfig {
  clientId: string;
  clientSecret: string;
  epsPaymentClientId: string;
  scope: string;
}

export interface TokenCache {
  token: string;
  expiresAt: number;
}

export interface TokenResponse {
  access_token: string;
  expires_in: number;
}

export interface EpsPaymentManagementConfig extends EpsConfig {
  baseUrl: string;
}

export interface EpsPaymentMethodConfig extends EpsConfig {
  baseUrl: string;
}
