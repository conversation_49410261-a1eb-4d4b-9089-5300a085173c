
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/qss-extension/03-storage-management/">
      
      
        <link rel="prev" href="../02-snapshot-generation/">
      
      
        <link rel="next" href="../04-development-guide/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Storage Management - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#storage-management" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Storage Management
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" checked>
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#storage-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Storage Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Storage Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#qss-storage-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      QSS Storage Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#storage-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Storage Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quote-storage-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Storage Optimization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Storage Optimization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#storage-optimization-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Storage Optimization Engine
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#compression-service" class="md-nav__link">
    <span class="md-ellipsis">
      Compression Service
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#snapshot-lifecycle-management" class="md-nav__link">
    <span class="md-ellipsis">
      Snapshot Lifecycle Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Snapshot Lifecycle Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#snapshot-lifecycle-manager" class="md-nav__link">
    <span class="md-ellipsis">
      Snapshot Lifecycle Manager
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#data-retention-policies" class="md-nav__link">
    <span class="md-ellipsis">
      Data Retention Policies
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Retention Policies">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#retention-policy-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Retention Policy Engine
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-monitoring" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Monitoring
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Monitoring">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#storage-performance-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Storage Performance Metrics
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-storage-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Storage Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#storage-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Storage Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Storage Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#qss-storage-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      QSS Storage Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#storage-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Storage Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quote-storage-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Storage Optimization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Storage Optimization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#storage-optimization-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Storage Optimization Engine
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#compression-service" class="md-nav__link">
    <span class="md-ellipsis">
      Compression Service
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#snapshot-lifecycle-management" class="md-nav__link">
    <span class="md-ellipsis">
      Snapshot Lifecycle Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Snapshot Lifecycle Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#snapshot-lifecycle-manager" class="md-nav__link">
    <span class="md-ellipsis">
      Snapshot Lifecycle Manager
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#data-retention-policies" class="md-nav__link">
    <span class="md-ellipsis">
      Data Retention Policies
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Retention Policies">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#retention-policy-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Retention Policy Engine
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-monitoring" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Monitoring
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Monitoring">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#storage-performance-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Storage Performance Metrics
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-storage-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Storage Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="storage-management">Storage Management<a class="headerlink" href="#storage-management" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#storage-architecture">Storage Architecture</a></li>
<li><a href="#quote-storage-optimization">Quote Storage Optimization</a></li>
<li><a href="#snapshot-lifecycle-management">Snapshot Lifecycle Management</a></li>
<li><a href="#data-retention-policies">Data Retention Policies</a></li>
<li><a href="#performance-monitoring">Performance Monitoring</a></li>
<li><a href="#troubleshooting">Troubleshooting</a></li>
</ul>
<h2 id="storage-architecture">Storage Architecture<a class="headerlink" href="#storage-architecture" title="Permanent link">&para;</a></h2>
<h3 id="qss-storage-architecture">QSS Storage Architecture<a class="headerlink" href="#qss-storage-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Application Layer&quot;
        A[QSS Extension Plugin]
        B[Storage Manager]
        C[Lifecycle Manager]
        D[Retention Manager]
    end

    subgraph &quot;Storage Services&quot;
        E[Quote Storage Service]
        F[Snapshot Storage Service]
        G[Metadata Service]
        H[Audit Service]
    end

    subgraph &quot;Data Layer&quot;
        I[Primary Quote DB]
        J[Snapshot Storage]
        K[Metadata Store]
        L[Audit Logs]
    end

    subgraph &quot;Storage Optimization&quot;
        M[Compression Engine]
        N[Deduplication]
        O[Archival Service]
        P[Cache Layer]
    end

    A --&gt; B
    B --&gt; C
    B --&gt; D

    B --&gt; E
    C --&gt; F
    D --&gt; G
    A --&gt; H

    E --&gt; I
    F --&gt; J
    G --&gt; K
    H --&gt; L

    F --&gt; M
    F --&gt; N
    C --&gt; O
    E --&gt; P

    style B fill:#673ab7,stroke:#512da8,color:#ffffff
    style F fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style M fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="storage-flow">Storage Flow<a class="headerlink" href="#storage-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Plugin as QSS Extension Plugin
    participant Manager as Storage Manager
    participant QSS as Quote Storage Service
    participant Snapshot as Snapshot Storage
    participant Metadata as Metadata Service
    participant Retention as Retention Manager

    Plugin-&gt;&gt;Manager: Store Quote Snapshot
    Manager-&gt;&gt;QSS: Validate Quote Data
    QSS--&gt;&gt;Manager: Validation Result

    Manager-&gt;&gt;Snapshot: Create Snapshot
    Snapshot-&gt;&gt;Snapshot: Apply Compression
    Snapshot-&gt;&gt;Snapshot: Check Deduplication
    Snapshot--&gt;&gt;Manager: Snapshot ID

    Manager-&gt;&gt;Metadata: Store Metadata
    Metadata--&gt;&gt;Manager: Metadata Stored

    Manager-&gt;&gt;Retention: Apply Retention Policy
    Retention-&gt;&gt;Retention: Calculate Expiry
    Retention--&gt;&gt;Manager: Policy Applied

    Manager--&gt;&gt;Plugin: Storage Complete
</code></pre></div>
<h2 id="quote-storage-optimization">Quote Storage Optimization<a class="headerlink" href="#quote-storage-optimization" title="Permanent link">&para;</a></h2>
<h3 id="storage-optimization-engine">Storage Optimization Engine<a class="headerlink" href="#storage-optimization-engine" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Quote storage optimization and management</span>
<span class="cm"> */</span>
<span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QuoteStorageOptimizer</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">CompressionService</span><span class="w"> </span><span class="n">compressionService</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">DeduplicationService</span><span class="w"> </span><span class="n">deduplicationService</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">CacheManager</span><span class="w"> </span><span class="n">cacheManager</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.storage.compression-enabled:true}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">compressionEnabled</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.storage.deduplication-enabled:true}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">deduplicationEnabled</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.storage.cache-ttl:3600}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">long</span><span class="w"> </span><span class="n">cacheTtl</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Optimize quote storage with compression and deduplication</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">OptimizedStorageResult</span><span class="w"> </span><span class="nf">optimizeQuoteStorage</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">StorageContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Optimizing storage for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>

<span class="w">        </span><span class="n">OptimizedStorageResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">OptimizedStorageResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">quoteId</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">originalSize</span><span class="p">(</span><span class="n">calculateQuoteSize</span><span class="p">(</span><span class="n">quote</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">timestamp</span><span class="p">(</span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Step 1: Apply compression if enabled</span>
<span class="w">            </span><span class="n">Quote</span><span class="w"> </span><span class="n">processedQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">;</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">compressionEnabled</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">processedQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyCompression</span><span class="p">(</span><span class="n">processedQuote</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">            </span><span class="c1">// Step 2: Check for deduplication opportunities</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">deduplicationEnabled</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">processedQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyDeduplication</span><span class="p">(</span><span class="n">processedQuote</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">            </span><span class="c1">// Step 3: Cache frequently accessed quotes</span>
<span class="w">            </span><span class="n">cacheQuoteIfNeeded</span><span class="p">(</span><span class="n">processedQuote</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Step 4: Calculate final metrics</span>
<span class="w">            </span><span class="kt">long</span><span class="w"> </span><span class="n">finalSize</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calculateQuoteSize</span><span class="p">(</span><span class="n">processedQuote</span><span class="p">);</span>
<span class="w">            </span><span class="kt">double</span><span class="w"> </span><span class="n">compressionRatio</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="kt">double</span><span class="p">)</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">originalSize</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">finalSize</span><span class="p">;</span>

<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">resultBuilder</span>
<span class="w">                </span><span class="p">.</span><span class="na">optimizedQuote</span><span class="p">(</span><span class="n">processedQuote</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">finalSize</span><span class="p">(</span><span class="n">finalSize</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">compressionRatio</span><span class="p">(</span><span class="n">compressionRatio</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">success</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error optimizing quote storage for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">resultBuilder</span>
<span class="w">                </span><span class="p">.</span><span class="na">success</span><span class="p">(</span><span class="kc">false</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">errorMessage</span><span class="p">(</span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Apply compression to quote data</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Quote</span><span class="w"> </span><span class="nf">applyCompression</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">OptimizedStorageResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Applying compression to quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>

<span class="w">        </span><span class="n">CompressionResult</span><span class="w"> </span><span class="n">compressionResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">compressionService</span><span class="p">.</span><span class="na">compressQuote</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">compressionApplied</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">compressionAlgorithm</span><span class="p">(</span><span class="n">compressionResult</span><span class="p">.</span><span class="na">getAlgorithm</span><span class="p">())</span>
<span class="w">                    </span><span class="p">.</span><span class="na">compressionRatio</span><span class="p">(</span><span class="n">compressionResult</span><span class="p">.</span><span class="na">getRatio</span><span class="p">());</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Compression applied: {} -&gt; {} bytes (ratio: {})&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">compressionResult</span><span class="p">.</span><span class="na">getOriginalSize</span><span class="p">(),</span>
<span class="w">                 </span><span class="n">compressionResult</span><span class="p">.</span><span class="na">getCompressedSize</span><span class="p">(),</span>
<span class="w">                 </span><span class="n">compressionResult</span><span class="p">.</span><span class="na">getRatio</span><span class="p">());</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">compressionResult</span><span class="p">.</span><span class="na">getCompressedQuote</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Apply deduplication to quote data</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Quote</span><span class="w"> </span><span class="nf">applyDeduplication</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">StorageContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                   </span><span class="n">OptimizedStorageResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Checking deduplication for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>

<span class="w">        </span><span class="n">DeduplicationResult</span><span class="w"> </span><span class="n">deduplicationResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">deduplicationService</span><span class="p">.</span><span class="na">deduplicateQuote</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">);</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">deduplicationResult</span><span class="p">.</span><span class="na">isDuplicate</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Duplicate quote detected: {} -&gt; reference: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                    </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">deduplicationResult</span><span class="p">.</span><span class="na">getReferenceId</span><span class="p">());</span>

<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">deduplicationApplied</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">                        </span><span class="p">.</span><span class="na">referenceQuoteId</span><span class="p">(</span><span class="n">deduplicationResult</span><span class="p">.</span><span class="na">getReferenceId</span><span class="p">())</span>
<span class="w">                        </span><span class="p">.</span><span class="na">spaceSaved</span><span class="p">(</span><span class="n">deduplicationResult</span><span class="p">.</span><span class="na">getSpaceSaved</span><span class="p">());</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">deduplicationResult</span><span class="p">.</span><span class="na">getProcessedQuote</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Cache quote if it meets caching criteria</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">cacheQuoteIfNeeded</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">StorageContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">shouldCacheQuote</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">Cache</span><span class="w"> </span><span class="n">cache</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">cacheManager</span><span class="p">.</span><span class="na">getCache</span><span class="p">(</span><span class="s">&quot;optimized-quotes&quot;</span><span class="p">);</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">cache</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">cache</span><span class="p">.</span><span class="na">put</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">quote</span><span class="p">);</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Quote cached: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Determine if quote should be cached</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">shouldCacheQuote</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">StorageContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Cache high-value quotes</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isHighValueQuote</span><span class="p">(</span><span class="n">quote</span><span class="p">))</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>

<span class="w">        </span><span class="c1">// Cache frequently accessed quotes</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getAccessFrequency</span><span class="p">()</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">10</span><span class="p">)</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>

<span class="w">        </span><span class="c1">// Cache recent quotes</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isRecentQuote</span><span class="p">(</span><span class="n">quote</span><span class="p">))</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isHighValueQuote</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implementation to check if quote is high value</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span><span class="w"> </span><span class="c1">// Placeholder</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isRecentQuote</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implementation to check if quote is recent</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span><span class="w"> </span><span class="c1">// Placeholder</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">long</span><span class="w"> </span><span class="nf">calculateQuoteSize</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implementation to calculate quote size in bytes</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="mi">0</span><span class="n">L</span><span class="p">;</span><span class="w"> </span><span class="c1">// Placeholder</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Storage optimization result</span>
<span class="cm"> */</span>
<span class="nd">@Data</span>
<span class="nd">@Builder</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">OptimizedStorageResult</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">long</span><span class="w"> </span><span class="n">originalSize</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">long</span><span class="w"> </span><span class="n">finalSize</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">double</span><span class="w"> </span><span class="n">compressionRatio</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">compressionApplied</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">compressionAlgorithm</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">deduplicationApplied</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">referenceQuoteId</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">long</span><span class="w"> </span><span class="n">spaceSaved</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Quote</span><span class="w"> </span><span class="n">optimizedQuote</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">success</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">errorMessage</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Instant</span><span class="w"> </span><span class="n">timestamp</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="compression-service">Compression Service<a class="headerlink" href="#compression-service" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Quote data compression service</span>
<span class="cm"> */</span>
<span class="nd">@Service</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QuoteCompressionService</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.compression.algorithm:GZIP}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">compressionAlgorithm</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.compression.level:6}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">compressionLevel</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Compress quote data using configured algorithm</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">CompressionResult</span><span class="w"> </span><span class="nf">compressQuote</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Compressing quote: {} using {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">compressionAlgorithm</span><span class="p">);</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="kt">byte</span><span class="o">[]</span><span class="w"> </span><span class="n">originalData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">serializeQuote</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">            </span><span class="kt">byte</span><span class="o">[]</span><span class="w"> </span><span class="n">compressedData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">compress</span><span class="p">(</span><span class="n">originalData</span><span class="p">);</span>
<span class="w">            </span><span class="n">Quote</span><span class="w"> </span><span class="n">compressedQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createCompressedQuote</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">compressedData</span><span class="p">);</span>

<span class="w">            </span><span class="kt">double</span><span class="w"> </span><span class="n">ratio</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="kt">double</span><span class="p">)</span><span class="w"> </span><span class="n">originalData</span><span class="p">.</span><span class="na">length</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="n">compressedData</span><span class="p">.</span><span class="na">length</span><span class="p">;</span>

<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">CompressionResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">originalSize</span><span class="p">(</span><span class="n">originalData</span><span class="p">.</span><span class="na">length</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">compressedSize</span><span class="p">(</span><span class="n">compressedData</span><span class="p">.</span><span class="na">length</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">ratio</span><span class="p">(</span><span class="n">ratio</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">algorithm</span><span class="p">(</span><span class="n">compressionAlgorithm</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">compressedQuote</span><span class="p">(</span><span class="n">compressedQuote</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">success</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error compressing quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">CompressionResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">success</span><span class="p">(</span><span class="kc">false</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">errorMessage</span><span class="p">(</span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Decompress quote data</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Quote</span><span class="w"> </span><span class="nf">decompressQuote</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">compressedQuote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Decompressing quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">compressedQuote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="kt">byte</span><span class="o">[]</span><span class="w"> </span><span class="n">compressedData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractCompressedData</span><span class="p">(</span><span class="n">compressedQuote</span><span class="p">);</span>
<span class="w">            </span><span class="kt">byte</span><span class="o">[]</span><span class="w"> </span><span class="n">originalData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">decompress</span><span class="p">(</span><span class="n">compressedData</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">deserializeQuote</span><span class="p">(</span><span class="n">originalData</span><span class="p">);</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error decompressing quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">compressedQuote</span><span class="p">.</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">StorageException</span><span class="p">(</span><span class="s">&quot;Failed to decompress quote&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">byte</span><span class="o">[]</span><span class="w"> </span><span class="nf">compress</span><span class="p">(</span><span class="kt">byte</span><span class="o">[]</span><span class="w"> </span><span class="n">data</span><span class="p">)</span><span class="w"> </span><span class="kd">throws</span><span class="w"> </span><span class="n">IOException</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">compressionAlgorithm</span><span class="p">.</span><span class="na">toUpperCase</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="s">&quot;GZIP&quot;</span><span class="p">:</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="n">compressGzip</span><span class="p">(</span><span class="n">data</span><span class="p">);</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="s">&quot;LZ4&quot;</span><span class="p">:</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="n">compressLz4</span><span class="p">(</span><span class="n">data</span><span class="p">);</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="s">&quot;SNAPPY&quot;</span><span class="p">:</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="n">compressSnappy</span><span class="p">(</span><span class="n">data</span><span class="p">);</span>
<span class="w">            </span><span class="k">default</span><span class="p">:</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IllegalArgumentException</span><span class="p">(</span><span class="s">&quot;Unsupported compression algorithm: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">compressionAlgorithm</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">byte</span><span class="o">[]</span><span class="w"> </span><span class="nf">compressGzip</span><span class="p">(</span><span class="kt">byte</span><span class="o">[]</span><span class="w"> </span><span class="n">data</span><span class="p">)</span><span class="w"> </span><span class="kd">throws</span><span class="w"> </span><span class="n">IOException</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">ByteArrayOutputStream</span><span class="w"> </span><span class="n">baos</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ByteArrayOutputStream</span><span class="p">();</span>
<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">(</span><span class="n">GZIPOutputStream</span><span class="w"> </span><span class="n">gzos</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">GZIPOutputStream</span><span class="p">(</span><span class="n">baos</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">gzos</span><span class="p">.</span><span class="na">write</span><span class="p">(</span><span class="n">data</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">baos</span><span class="p">.</span><span class="na">toByteArray</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Additional compression methods...</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="snapshot-lifecycle-management">Snapshot Lifecycle Management<a class="headerlink" href="#snapshot-lifecycle-management" title="Permanent link">&para;</a></h2>
<h3 id="snapshot-lifecycle-manager">Snapshot Lifecycle Manager<a class="headerlink" href="#snapshot-lifecycle-manager" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Comprehensive snapshot lifecycle management</span>
<span class="cm"> */</span>
<span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">SnapshotLifecycleManager</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">SnapshotRepository</span><span class="w"> </span><span class="n">snapshotRepository</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">RetentionPolicyService</span><span class="w"> </span><span class="n">retentionPolicyService</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">ArchivalService</span><span class="w"> </span><span class="n">archivalService</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Scheduled</span><span class="p">(</span><span class="n">fixedRate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">3600000</span><span class="p">)</span><span class="w"> </span><span class="c1">// Run every hour</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">manageSnapshotLifecycle</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Starting snapshot lifecycle management&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Step 1: Identify snapshots for lifecycle actions</span>
<span class="w">            </span><span class="n">List</span><span class="o">&lt;</span><span class="n">SnapshotMetadata</span><span class="o">&gt;</span><span class="w"> </span><span class="n">snapshots</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">snapshotRepository</span><span class="p">.</span><span class="na">findAllActiveSnapshots</span><span class="p">();</span>

<span class="w">            </span><span class="c1">// Step 2: Apply retention policies</span>
<span class="w">            </span><span class="n">applyRetentionPolicies</span><span class="p">(</span><span class="n">snapshots</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Step 3: Archive old snapshots</span>
<span class="w">            </span><span class="n">archiveOldSnapshots</span><span class="p">(</span><span class="n">snapshots</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Step 4: Cleanup expired snapshots</span>
<span class="w">            </span><span class="n">cleanupExpiredSnapshots</span><span class="p">(</span><span class="n">snapshots</span><span class="p">);</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Snapshot lifecycle management completed&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error during snapshot lifecycle management&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Apply retention policies to snapshots</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">applyRetentionPolicies</span><span class="p">(</span><span class="n">List</span><span class="o">&lt;</span><span class="n">SnapshotMetadata</span><span class="o">&gt;</span><span class="w"> </span><span class="n">snapshots</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Applying retention policies to {} snapshots&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">snapshots</span><span class="p">.</span><span class="na">size</span><span class="p">());</span>

<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">SnapshotMetadata</span><span class="w"> </span><span class="n">snapshot</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">snapshots</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">RetentionPolicy</span><span class="w"> </span><span class="n">policy</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">retentionPolicyService</span><span class="p">.</span><span class="na">getPolicyForSnapshot</span><span class="p">(</span><span class="n">snapshot</span><span class="p">);</span>
<span class="w">                </span><span class="n">LifecycleAction</span><span class="w"> </span><span class="n">action</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">policy</span><span class="p">.</span><span class="na">evaluateAction</span><span class="p">(</span><span class="n">snapshot</span><span class="p">);</span>

<span class="w">                </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">action</span><span class="p">.</span><span class="na">getType</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="k">case</span><span class="w"> </span><span class="n">RETAIN</span><span class="p">:</span>
<span class="w">                        </span><span class="c1">// No action needed</span>
<span class="w">                        </span><span class="k">break</span><span class="p">;</span>
<span class="w">                    </span><span class="k">case</span><span class="w"> </span><span class="n">ARCHIVE</span><span class="p">:</span>
<span class="w">                        </span><span class="n">scheduleForArchival</span><span class="p">(</span><span class="n">snapshot</span><span class="p">,</span><span class="w"> </span><span class="n">action</span><span class="p">);</span>
<span class="w">                        </span><span class="k">break</span><span class="p">;</span>
<span class="w">                    </span><span class="k">case</span><span class="w"> </span><span class="n">DELETE</span><span class="p">:</span>
<span class="w">                        </span><span class="n">scheduleForDeletion</span><span class="p">(</span><span class="n">snapshot</span><span class="p">,</span><span class="w"> </span><span class="n">action</span><span class="p">);</span>
<span class="w">                        </span><span class="k">break</span><span class="p">;</span>
<span class="w">                </span><span class="p">}</span>

<span class="w">            </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error applying retention policy to snapshot: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">snapshot</span><span class="p">.</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Archive old snapshots based on age and access patterns</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">archiveOldSnapshots</span><span class="p">(</span><span class="n">List</span><span class="o">&lt;</span><span class="n">SnapshotMetadata</span><span class="o">&gt;</span><span class="w"> </span><span class="n">snapshots</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">List</span><span class="o">&lt;</span><span class="n">SnapshotMetadata</span><span class="o">&gt;</span><span class="w"> </span><span class="n">candidatesForArchival</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">snapshots</span><span class="p">.</span><span class="na">stream</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">filter</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">isArchivalCandidate</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">collect</span><span class="p">(</span><span class="n">Collectors</span><span class="p">.</span><span class="na">toList</span><span class="p">());</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Found {} snapshots for archival&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">candidatesForArchival</span><span class="p">.</span><span class="na">size</span><span class="p">());</span>

<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">SnapshotMetadata</span><span class="w"> </span><span class="n">snapshot</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">candidatesForArchival</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">archivalService</span><span class="p">.</span><span class="na">archiveSnapshot</span><span class="p">(</span><span class="n">snapshot</span><span class="p">);</span>
<span class="w">                </span><span class="n">updateSnapshotStatus</span><span class="p">(</span><span class="n">snapshot</span><span class="p">,</span><span class="w"> </span><span class="n">SnapshotStatus</span><span class="p">.</span><span class="na">ARCHIVED</span><span class="p">);</span>

<span class="w">            </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error archiving snapshot: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">snapshot</span><span class="p">.</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Cleanup expired snapshots</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">cleanupExpiredSnapshots</span><span class="p">(</span><span class="n">List</span><span class="o">&lt;</span><span class="n">SnapshotMetadata</span><span class="o">&gt;</span><span class="w"> </span><span class="n">snapshots</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">List</span><span class="o">&lt;</span><span class="n">SnapshotMetadata</span><span class="o">&gt;</span><span class="w"> </span><span class="n">expiredSnapshots</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">snapshots</span><span class="p">.</span><span class="na">stream</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">filter</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">isExpired</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">collect</span><span class="p">(</span><span class="n">Collectors</span><span class="p">.</span><span class="na">toList</span><span class="p">());</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Found {} expired snapshots for cleanup&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">expiredSnapshots</span><span class="p">.</span><span class="na">size</span><span class="p">());</span>

<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">SnapshotMetadata</span><span class="w"> </span><span class="n">snapshot</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">expiredSnapshots</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">deleteSnapshot</span><span class="p">(</span><span class="n">snapshot</span><span class="p">);</span>

<span class="w">            </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error deleting expired snapshot: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">snapshot</span><span class="p">.</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isArchivalCandidate</span><span class="p">(</span><span class="n">SnapshotMetadata</span><span class="w"> </span><span class="n">snapshot</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Check age</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">snapshot</span><span class="p">.</span><span class="na">getCreatedAt</span><span class="p">().</span><span class="na">isBefore</span><span class="p">(</span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">().</span><span class="na">minus</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofDays</span><span class="p">(</span><span class="mi">90</span><span class="p">))))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Check access frequency</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">snapshot</span><span class="p">.</span><span class="na">getAccessCount</span><span class="p">()</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">5</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>
<span class="w">            </span><span class="n">snapshot</span><span class="p">.</span><span class="na">getLastAccessedAt</span><span class="p">().</span><span class="na">isBefore</span><span class="p">(</span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">().</span><span class="na">minus</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofDays</span><span class="p">(</span><span class="mi">30</span><span class="p">))))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isExpired</span><span class="p">(</span><span class="n">SnapshotMetadata</span><span class="w"> </span><span class="n">snapshot</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">snapshot</span><span class="p">.</span><span class="na">getExpiryDate</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>
<span class="w">               </span><span class="n">snapshot</span><span class="p">.</span><span class="na">getExpiryDate</span><span class="p">().</span><span class="na">isBefore</span><span class="p">(</span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Snapshot metadata model</span>
<span class="cm"> */</span>
<span class="nd">@Data</span>
<span class="nd">@Builder</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">SnapshotMetadata</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">id</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">version</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">SnapshotStatus</span><span class="w"> </span><span class="n">status</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Instant</span><span class="w"> </span><span class="n">createdAt</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Instant</span><span class="w"> </span><span class="n">lastAccessedAt</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Instant</span><span class="w"> </span><span class="n">expiryDate</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">long</span><span class="w"> </span><span class="n">size</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">accessCount</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">storageLocation</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">attributes</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Snapshot status enumeration</span>
<span class="cm"> */</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">enum</span><span class="w"> </span><span class="n">SnapshotStatus</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">ACTIVE</span><span class="p">,</span><span class="w"> </span><span class="n">ARCHIVED</span><span class="p">,</span><span class="w"> </span><span class="n">EXPIRED</span><span class="p">,</span><span class="w"> </span><span class="n">DELETED</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="data-retention-policies">Data Retention Policies<a class="headerlink" href="#data-retention-policies" title="Permanent link">&para;</a></h2>
<h3 id="retention-policy-engine">Retention Policy Engine<a class="headerlink" href="#retention-policy-engine" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Data retention policy management</span>
<span class="cm"> */</span>
<span class="nd">@Service</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">RetentionPolicyService</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.retention.default-period:P1Y}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Duration</span><span class="w"> </span><span class="n">defaultRetentionPeriod</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.retention.prime-quote-period:P7Y}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Duration</span><span class="w"> </span><span class="n">primeQuoteRetentionPeriod</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.retention.high-value-period:P5Y}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Duration</span><span class="w"> </span><span class="n">highValueRetentionPeriod</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Get retention policy for a specific snapshot</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">RetentionPolicy</span><span class="w"> </span><span class="nf">getPolicyForSnapshot</span><span class="p">(</span><span class="n">SnapshotMetadata</span><span class="w"> </span><span class="n">snapshot</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Determining retention policy for snapshot: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">snapshot</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Check for prime quote</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isPrimeQuoteSnapshot</span><span class="p">(</span><span class="n">snapshot</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">createPrimeQuotePolicy</span><span class="p">(</span><span class="n">snapshot</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Check for high-value quote</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isHighValueQuoteSnapshot</span><span class="p">(</span><span class="n">snapshot</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">createHighValuePolicy</span><span class="p">(</span><span class="n">snapshot</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Check for enterprise customer</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isEnterpriseCustomerSnapshot</span><span class="p">(</span><span class="n">snapshot</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">createEnterprisePolicy</span><span class="p">(</span><span class="n">snapshot</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Default policy</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">createDefaultPolicy</span><span class="p">(</span><span class="n">snapshot</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Create retention policy for prime quotes</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">RetentionPolicy</span><span class="w"> </span><span class="nf">createPrimeQuotePolicy</span><span class="p">(</span><span class="n">SnapshotMetadata</span><span class="w"> </span><span class="n">snapshot</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">RetentionPolicy</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">policyName</span><span class="p">(</span><span class="s">&quot;PRIME_QUOTE_POLICY&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">retentionPeriod</span><span class="p">(</span><span class="n">primeQuoteRetentionPeriod</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">archivalPeriod</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofDays</span><span class="p">(</span><span class="mi">365</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">allowEarlyDeletion</span><span class="p">(</span><span class="kc">false</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">requiresApproval</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">priority</span><span class="p">(</span><span class="n">RetentionPriority</span><span class="p">.</span><span class="na">HIGH</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Create retention policy for high-value quotes</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">RetentionPolicy</span><span class="w"> </span><span class="nf">createHighValuePolicy</span><span class="p">(</span><span class="n">SnapshotMetadata</span><span class="w"> </span><span class="n">snapshot</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">RetentionPolicy</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">policyName</span><span class="p">(</span><span class="s">&quot;HIGH_VALUE_POLICY&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">retentionPeriod</span><span class="p">(</span><span class="n">highValueRetentionPeriod</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">archivalPeriod</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofDays</span><span class="p">(</span><span class="mi">180</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">allowEarlyDeletion</span><span class="p">(</span><span class="kc">false</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">requiresApproval</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">priority</span><span class="p">(</span><span class="n">RetentionPriority</span><span class="p">.</span><span class="na">MEDIUM</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Create default retention policy</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">RetentionPolicy</span><span class="w"> </span><span class="nf">createDefaultPolicy</span><span class="p">(</span><span class="n">SnapshotMetadata</span><span class="w"> </span><span class="n">snapshot</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">RetentionPolicy</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">policyName</span><span class="p">(</span><span class="s">&quot;DEFAULT_POLICY&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">retentionPeriod</span><span class="p">(</span><span class="n">defaultRetentionPeriod</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">archivalPeriod</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofDays</span><span class="p">(</span><span class="mi">90</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">allowEarlyDeletion</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">requiresApproval</span><span class="p">(</span><span class="kc">false</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">priority</span><span class="p">(</span><span class="n">RetentionPriority</span><span class="p">.</span><span class="na">LOW</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isPrimeQuoteSnapshot</span><span class="p">(</span><span class="n">SnapshotMetadata</span><span class="w"> </span><span class="n">snapshot</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">snapshot</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">containsKey</span><span class="p">(</span><span class="s">&quot;isPrime&quot;</span><span class="p">)</span><span class="w"> </span><span class="o">&amp;&amp;</span>
<span class="w">               </span><span class="n">Boolean</span><span class="p">.</span><span class="na">TRUE</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">snapshot</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;isPrime&quot;</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isHighValueQuoteSnapshot</span><span class="p">(</span><span class="n">SnapshotMetadata</span><span class="w"> </span><span class="n">snapshot</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Object</span><span class="w"> </span><span class="n">dealValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">snapshot</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;dealValue&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">dealValue</span><span class="w"> </span><span class="k">instanceof</span><span class="w"> </span><span class="n">Number</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="p">((</span><span class="n">Number</span><span class="p">)</span><span class="w"> </span><span class="n">dealValue</span><span class="p">).</span><span class="na">doubleValue</span><span class="p">()</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mf">100000.0</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isEnterpriseCustomerSnapshot</span><span class="p">(</span><span class="n">SnapshotMetadata</span><span class="w"> </span><span class="n">snapshot</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Object</span><span class="w"> </span><span class="n">segment</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">snapshot</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;customerSegment&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">segment</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>
<span class="w">               </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;ENTERPRISE&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;GOVERNMENT&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;WHOLESALE&quot;</span><span class="p">)</span>
<span class="w">                  </span><span class="p">.</span><span class="na">contains</span><span class="p">(</span><span class="n">segment</span><span class="p">.</span><span class="na">toString</span><span class="p">().</span><span class="na">toUpperCase</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Retention policy model</span>
<span class="cm"> */</span>
<span class="nd">@Data</span>
<span class="nd">@Builder</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">RetentionPolicy</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">policyName</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Duration</span><span class="w"> </span><span class="n">retentionPeriod</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Duration</span><span class="w"> </span><span class="n">archivalPeriod</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">allowEarlyDeletion</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">requiresApproval</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">RetentionPriority</span><span class="w"> </span><span class="n">priority</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">metadata</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Evaluate what action should be taken for a snapshot</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">LifecycleAction</span><span class="w"> </span><span class="nf">evaluateAction</span><span class="p">(</span><span class="n">SnapshotMetadata</span><span class="w"> </span><span class="n">snapshot</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Instant</span><span class="w"> </span><span class="n">now</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">();</span>
<span class="w">        </span><span class="n">Instant</span><span class="w"> </span><span class="n">createdAt</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">snapshot</span><span class="p">.</span><span class="na">getCreatedAt</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Check if snapshot should be deleted</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">createdAt</span><span class="p">.</span><span class="na">plus</span><span class="p">(</span><span class="n">retentionPeriod</span><span class="p">).</span><span class="na">isBefore</span><span class="p">(</span><span class="n">now</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">LifecycleAction</span><span class="p">.</span><span class="na">delete</span><span class="p">(</span><span class="s">&quot;Retention period exceeded&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Check if snapshot should be archived</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">createdAt</span><span class="p">.</span><span class="na">plus</span><span class="p">(</span><span class="n">archivalPeriod</span><span class="p">).</span><span class="na">isBefore</span><span class="p">(</span><span class="n">now</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">LifecycleAction</span><span class="p">.</span><span class="na">archive</span><span class="p">(</span><span class="s">&quot;Archival period reached&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Retain snapshot</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">LifecycleAction</span><span class="p">.</span><span class="na">retain</span><span class="p">(</span><span class="s">&quot;Within retention period&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Lifecycle action model</span>
<span class="cm"> */</span>
<span class="nd">@Data</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">LifecycleAction</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">LifecycleActionType</span><span class="w"> </span><span class="n">type</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">reason</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Instant</span><span class="w"> </span><span class="n">scheduledAt</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">parameters</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="n">LifecycleAction</span><span class="w"> </span><span class="nf">retain</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">reason</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">LifecycleAction</span><span class="p">(</span><span class="n">LifecycleActionType</span><span class="p">.</span><span class="na">RETAIN</span><span class="p">,</span><span class="w"> </span><span class="n">reason</span><span class="p">,</span><span class="w"> </span><span class="kc">null</span><span class="p">,</span><span class="w"> </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="n">LifecycleAction</span><span class="w"> </span><span class="nf">archive</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">reason</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">LifecycleAction</span><span class="p">(</span><span class="n">LifecycleActionType</span><span class="p">.</span><span class="na">ARCHIVE</span><span class="p">,</span><span class="w"> </span><span class="n">reason</span><span class="p">,</span><span class="w"> </span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">(),</span><span class="w"> </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="n">LifecycleAction</span><span class="w"> </span><span class="nf">delete</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">reason</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">LifecycleAction</span><span class="p">(</span><span class="n">LifecycleActionType</span><span class="p">.</span><span class="na">DELETE</span><span class="p">,</span><span class="w"> </span><span class="n">reason</span><span class="p">,</span><span class="w"> </span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">(),</span><span class="w"> </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Lifecycle action types</span>
<span class="cm"> */</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">enum</span><span class="w"> </span><span class="n">LifecycleActionType</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">RETAIN</span><span class="p">,</span><span class="w"> </span><span class="n">ARCHIVE</span><span class="p">,</span><span class="w"> </span><span class="n">DELETE</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Retention priority levels</span>
<span class="cm"> */</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">enum</span><span class="w"> </span><span class="n">RetentionPriority</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">HIGH</span><span class="p">,</span><span class="w"> </span><span class="n">MEDIUM</span><span class="p">,</span><span class="w"> </span><span class="n">LOW</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="performance-monitoring">Performance Monitoring<a class="headerlink" href="#performance-monitoring" title="Permanent link">&para;</a></h2>
<h3 id="storage-performance-metrics">Storage Performance Metrics<a class="headerlink" href="#storage-performance-metrics" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Storage performance monitoring and metrics</span>
<span class="cm"> */</span>
<span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">StoragePerformanceMonitor</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">MeterRegistry</span><span class="w"> </span><span class="n">meterRegistry</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Timer</span><span class="w"> </span><span class="n">storageOperationTimer</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Counter</span><span class="w"> </span><span class="n">storageOperations</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Gauge</span><span class="w"> </span><span class="n">storageUtilization</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Counter</span><span class="w"> </span><span class="n">compressionOperations</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">StoragePerformanceMonitor</span><span class="p">(</span><span class="n">MeterRegistry</span><span class="w"> </span><span class="n">meterRegistry</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">meterRegistry</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">meterRegistry</span><span class="p">;</span>

<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">storageOperationTimer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Timer</span><span class="p">.</span><span class="na">builder</span><span class="p">(</span><span class="s">&quot;qss.storage.operation.duration&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">description</span><span class="p">(</span><span class="s">&quot;Time taken for storage operations&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">);</span>

<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">storageOperations</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Counter</span><span class="p">.</span><span class="na">builder</span><span class="p">(</span><span class="s">&quot;qss.storage.operations.total&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">description</span><span class="p">(</span><span class="s">&quot;Total number of storage operations&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">);</span>

<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">storageUtilization</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Gauge</span><span class="p">.</span><span class="na">builder</span><span class="p">(</span><span class="s">&quot;qss.storage.utilization&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">description</span><span class="p">(</span><span class="s">&quot;Storage utilization percentage&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">,</span><span class="w"> </span><span class="n">StoragePerformanceMonitor</span><span class="p">::</span><span class="n">getStorageUtilization</span><span class="p">);</span>

<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">compressionOperations</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Counter</span><span class="p">.</span><span class="na">builder</span><span class="p">(</span><span class="s">&quot;qss.compression.operations.total&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">description</span><span class="p">(</span><span class="s">&quot;Total number of compression operations&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">recordStorageOperation</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">operation</span><span class="p">,</span><span class="w"> </span><span class="n">Duration</span><span class="w"> </span><span class="n">duration</span><span class="p">,</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">success</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">storageOperations</span><span class="p">.</span><span class="na">increment</span><span class="p">(</span>
<span class="w">            </span><span class="n">Tags</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;operation&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">operation</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;success&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">String</span><span class="p">.</span><span class="na">valueOf</span><span class="p">(</span><span class="n">success</span><span class="p">)));</span>

<span class="w">        </span><span class="n">storageOperationTimer</span><span class="p">.</span><span class="na">record</span><span class="p">(</span><span class="n">duration</span><span class="p">,</span><span class="w"> </span>
<span class="w">            </span><span class="n">Tags</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;operation&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">operation</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;success&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">String</span><span class="p">.</span><span class="na">valueOf</span><span class="p">(</span><span class="n">success</span><span class="p">)));</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">recordCompressionOperation</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">algorithm</span><span class="p">,</span><span class="w"> </span><span class="kt">double</span><span class="w"> </span><span class="n">ratio</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">compressionOperations</span><span class="p">.</span><span class="na">increment</span><span class="p">(</span><span class="n">Tags</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;algorithm&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">algorithm</span><span class="p">));</span>

<span class="w">        </span><span class="n">Gauge</span><span class="p">.</span><span class="na">builder</span><span class="p">(</span><span class="s">&quot;qss.compression.ratio&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">description</span><span class="p">(</span><span class="s">&quot;Compression ratio achieved&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">tag</span><span class="p">(</span><span class="s">&quot;algorithm&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">algorithm</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">ratio</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">double</span><span class="w"> </span><span class="nf">getStorageUtilization</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implementation to calculate storage utilization</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"> </span><span class="c1">// Placeholder</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="troubleshooting">Troubleshooting<a class="headerlink" href="#troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="common-storage-issues">Common Storage Issues<a class="headerlink" href="#common-storage-issues" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Storage troubleshooting and diagnostics</span>
<span class="cm"> */</span>
<span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">StorageTroubleshooter</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Diagnose storage issues</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">StorageDiagnostics</span><span class="w"> </span><span class="nf">diagnoseStorageIssues</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">StorageDiagnostics</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">diagnostics</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">StorageDiagnostics</span><span class="p">.</span><span class="na">builder</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Check storage capacity</span>
<span class="w">        </span><span class="n">checkStorageCapacity</span><span class="p">(</span><span class="n">diagnostics</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Check compression performance</span>
<span class="w">        </span><span class="n">checkCompressionPerformance</span><span class="p">(</span><span class="n">diagnostics</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Check retention policy compliance</span>
<span class="w">        </span><span class="n">checkRetentionCompliance</span><span class="p">(</span><span class="n">diagnostics</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Check archival status</span>
<span class="w">        </span><span class="n">checkArchivalStatus</span><span class="p">(</span><span class="n">diagnostics</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">diagnostics</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">checkStorageCapacity</span><span class="p">(</span><span class="n">StorageDiagnostics</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">diagnostics</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implementation for storage capacity checks</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">checkCompressionPerformance</span><span class="p">(</span><span class="n">StorageDiagnostics</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">diagnostics</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implementation for compression performance checks</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">checkRetentionCompliance</span><span class="p">(</span><span class="n">StorageDiagnostics</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">diagnostics</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implementation for retention compliance checks</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">checkArchivalStatus</span><span class="p">(</span><span class="n">StorageDiagnostics</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">diagnostics</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implementation for archival status checks</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../04-development-guide/">Development Guide →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>