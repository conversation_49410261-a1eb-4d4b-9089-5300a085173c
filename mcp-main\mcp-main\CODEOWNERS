# CODEOWNERS for TELUS MCP Monorepo
# Each line is a file pattern followed by one or more owners.
# Order is important; the last matching pattern takes precedence.

# Default owners for everything in the repo (optional)
# * @telus/mcp

# Package-specific owners
/packages/mcp-ams/                @jonathan-dunn-telus
/packages/mcp-api-marketplace/    @isand3r
/packages/mcp-blazemeter/         @szchow89
/packages/mcp-contentful/         @kyletsang
/packages/mcp-dynatrace/          @chengli-telus
/packages/mcp-gcloud/             @raphberube
/packages/mcp-github-actions/     @foxted
/packages/mcp-github-issues/      @pdufault
/packages/mcp-github-pr/          @raphberube
/packages/mcp-guideline/          @kurosh-sahraie
/packages/mcp-jira-server/        @jonathan-dunn-telus
/packages/mcp-pagerduty/          @chengli-telus
/packages/mcp-service-qual        @jonathan-dunn-telus
/packages/mcp-vectors-query/      @kevintelus
/packages/mcp-telusiq-data/       @alwintiwari

# Apps directory
/apps/registry/                   @azohra

# Documentation
/docs/                            # Owner needed

# Root configuration files
/*.json                           # Owner needed
/*.md                             # Owner needed
