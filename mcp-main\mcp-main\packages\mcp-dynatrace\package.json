{"name": "@telus/mcp-dynatrace", "version": "0.2.3", "description": "MCP server for interacting with Dynatrace APIs", "keywords": ["telus", "mcp", "dynatrace"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-dynatrace"}, "license": "MIT", "author": "<PERSON> (@chengli-telus)", "type": "module", "main": "dist/index.js", "bin": {"mcp-dynatrace": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "prepare": "pnpm run build", "start": "node dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "dotenv": "~16.4.7", "node-fetch": "~3.3.2", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}