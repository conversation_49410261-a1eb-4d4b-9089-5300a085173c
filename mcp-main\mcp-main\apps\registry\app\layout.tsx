import type { Metadata } from "next";
import { helveticaNeue, helveticaNeueDisplay } from "@/config/fonts";
import "./globals.css";
import { Logo } from "@/components/logo";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${helveticaNeue.variable} ${helveticaNeueDisplay.variable} antialiased`}
      >
        <header className="bg-white dark:bg-gray-950 border-b border-gray-200 dark:border-gray-800 px-6 py-4">
          <div className="flex items-center justify-between gap-4">
            <Logo className="h-10 w-auto" />
            <div>
              <img
                src="https://github.com/telus.png"
                alt="logo"
                className="size-10 rounded-full"
              />
            </div>
          </div>
        </header>
        <main>{children}</main>
      </body>
    </html>
  );
}
