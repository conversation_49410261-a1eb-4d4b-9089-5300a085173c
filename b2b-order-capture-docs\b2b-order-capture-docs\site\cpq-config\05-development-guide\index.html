
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/cpq-config/05-development-guide/">
      
      
        <link rel="prev" href="../04-security-configuration/">
      
      
        <link rel="next" href="../../04-integration-guide/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Development Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#cpq-configuration-development-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Development Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" checked>
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#ide-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      IDE Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="IDE Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#intellij-idea-setup" class="md-nav__link">
    <span class="md-ellipsis">
      IntelliJ IDEA Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#vs-code-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      VS Code Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-environment-variables" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Variables
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Development Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Development Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-process" class="md-nav__link">
    <span class="md-ellipsis">
      Development Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#git-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Git Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-chain-development" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Chain Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration Chain Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chain-development-template" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Development Template
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#groovy-script-development" class="md-nav__link">
    <span class="md-ellipsis">
      Groovy Script Development
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chain-testing-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Testing Framework
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#service-configuration-development" class="md-nav__link">
    <span class="md-ellipsis">
      Service Configuration Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Configuration Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#service-definition-template" class="md-nav__link">
    <span class="md-ellipsis">
      Service Definition Template
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#api-specification-development" class="md-nav__link">
    <span class="md-ellipsis">
      API Specification Development
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-and-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Testing and Validation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing and Validation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#test-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Test Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#maven-test-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Test Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Test Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#deployment-and-release-management" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment and Release Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Deployment and Release Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#deployment-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Pipeline
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#release-management" class="md-nav__link">
    <span class="md-ellipsis">
      Release Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#ide-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      IDE Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="IDE Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#intellij-idea-setup" class="md-nav__link">
    <span class="md-ellipsis">
      IntelliJ IDEA Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#vs-code-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      VS Code Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-environment-variables" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Variables
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Development Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Development Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-process" class="md-nav__link">
    <span class="md-ellipsis">
      Development Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#git-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Git Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-chain-development" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Chain Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration Chain Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chain-development-template" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Development Template
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#groovy-script-development" class="md-nav__link">
    <span class="md-ellipsis">
      Groovy Script Development
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chain-testing-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Testing Framework
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#service-configuration-development" class="md-nav__link">
    <span class="md-ellipsis">
      Service Configuration Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Configuration Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#service-definition-template" class="md-nav__link">
    <span class="md-ellipsis">
      Service Definition Template
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#api-specification-development" class="md-nav__link">
    <span class="md-ellipsis">
      API Specification Development
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-and-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Testing and Validation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing and Validation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#test-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Test Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#maven-test-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Test Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Test Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#deployment-and-release-management" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment and Release Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Deployment and Release Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#deployment-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Pipeline
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#release-management" class="md-nav__link">
    <span class="md-ellipsis">
      Release Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="cpq-configuration-development-guide">CPQ Configuration Development Guide<a class="headerlink" href="#cpq-configuration-development-guide" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#development-environment-setup">Development Environment Setup</a></li>
<li><a href="#configuration-development-workflow">Configuration Development Workflow</a></li>
<li><a href="#integration-chain-development">Integration Chain Development</a></li>
<li><a href="#service-configuration-development">Service Configuration Development</a></li>
<li><a href="#testing-and-validation">Testing and Validation</a></li>
<li><a href="#deployment-and-release-management">Deployment and Release Management</a></li>
</ul>
<h2 id="development-environment-setup">Development Environment Setup<a class="headerlink" href="#development-environment-setup" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Required Software Versions</span>
Java:<span class="w"> </span>OpenJDK<span class="w"> </span><span class="m">11</span><span class="w"> </span>or<span class="w"> </span>higher
Maven:<span class="w"> </span><span class="m">3</span>.8.x<span class="w"> </span>or<span class="w"> </span>higher
Docker:<span class="w"> </span><span class="m">20</span>.x<span class="w"> </span>or<span class="w"> </span>higher
Kubernetes:<span class="w"> </span><span class="m">1</span>.25+<span class="w"> </span><span class="o">(</span>minikube,<span class="w"> </span>kind,<span class="w"> </span>or<span class="w"> </span>k3s<span class="w"> </span><span class="k">for</span><span class="w"> </span><span class="nb">local</span><span class="w"> </span>development<span class="o">)</span>
Helm:<span class="w"> </span><span class="m">3</span>.x<span class="w"> </span>or<span class="w"> </span>higher
Git:<span class="w"> </span><span class="m">2</span>.x<span class="w"> </span>or<span class="w"> </span>higher
IDE:<span class="w"> </span>IntelliJ<span class="w"> </span>IDEA,<span class="w"> </span>VS<span class="w"> </span>Code,<span class="w"> </span>or<span class="w"> </span>Eclipse<span class="w"> </span><span class="o">(</span>recommended<span class="o">)</span>

<span class="c1"># Optional Tools</span>
kubectl:<span class="w"> </span>Kubernetes<span class="w"> </span>CLI
kubectx/kubens:<span class="w"> </span>Kubernetes<span class="w"> </span>context<span class="w"> </span>switching
k9s:<span class="w"> </span>Kubernetes<span class="w"> </span>cluster<span class="w"> </span>management
Postman/Insomnia:<span class="w"> </span>API<span class="w"> </span>testing
</code></pre></div>
<h3 id="local-development-setup">Local Development Setup<a class="headerlink" href="#local-development-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>&lt;repository-url&gt;
<span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-cpq-config-b2b

<span class="c1"># 2. Verify prerequisites</span>
java<span class="w"> </span>-version
mvn<span class="w"> </span>-version
docker<span class="w"> </span>--version
kubectl<span class="w"> </span>version<span class="w"> </span>--client
helm<span class="w"> </span>version

<span class="c1"># 3. Set up local Kubernetes cluster</span>
minikube<span class="w"> </span>start<span class="w"> </span>--memory<span class="o">=</span><span class="m">8192</span><span class="w"> </span>--cpus<span class="o">=</span><span class="m">4</span><span class="w"> </span>--kubernetes-version<span class="o">=</span>v1.25.0

<span class="c1"># 4. Install CPQ Engine (if available)</span>
helm<span class="w"> </span>repo<span class="w"> </span>add<span class="w"> </span>netcracker<span class="w"> </span>&lt;netcracker-helm-repo&gt;
helm<span class="w"> </span>install<span class="w"> </span>cpq-engine<span class="w"> </span>netcracker/cpq-engine<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--namespace<span class="w"> </span>cpq-system<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--create-namespace

<span class="c1"># 5. Build configuration package</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package

<span class="c1"># 6. Deploy to local environment</span>
helm<span class="w"> </span>install<span class="w"> </span>cpq-config-dev<span class="w"> </span>deployments/charts/telus-cpq-config-package<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--values<span class="w"> </span>deployments/charts/telus-cpq-config-package/values-dev.yaml<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--namespace<span class="w"> </span>cpq-config-dev<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--create-namespace
</code></pre></div>
<h3 id="ide-configuration">IDE Configuration<a class="headerlink" href="#ide-configuration" title="Permanent link">&para;</a></h3>
<h4 id="intellij-idea-setup">IntelliJ IDEA Setup<a class="headerlink" href="#intellij-idea-setup" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="cm">&lt;!-- .idea/codeStyleSettings.xml --&gt;</span>
<span class="nt">&lt;component</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;ProjectCodeStyleConfiguration&quot;</span><span class="nt">&gt;</span>
<span class="w">  </span><span class="nt">&lt;code_scheme</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Project&quot;</span><span class="w"> </span><span class="na">version=</span><span class="s">&quot;173&quot;</span><span class="nt">&gt;</span>
<span class="w">    </span><span class="nt">&lt;option</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;OTHER_INDENT_OPTIONS&quot;</span><span class="nt">&gt;</span>
<span class="w">      </span><span class="nt">&lt;value&gt;</span>
<span class="w">        </span><span class="nt">&lt;option</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;INDENT_SIZE&quot;</span><span class="w"> </span><span class="na">value=</span><span class="s">&quot;2&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">        </span><span class="nt">&lt;option</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;TAB_SIZE&quot;</span><span class="w"> </span><span class="na">value=</span><span class="s">&quot;2&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">        </span><span class="nt">&lt;option</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;USE_TAB_CHARACTER&quot;</span><span class="w"> </span><span class="na">value=</span><span class="s">&quot;false&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">      </span><span class="nt">&lt;/value&gt;</span>
<span class="w">    </span><span class="nt">&lt;/option&gt;</span>
<span class="w">    </span><span class="nt">&lt;YAMLCodeStyleSettings&gt;</span>
<span class="w">      </span><span class="nt">&lt;option</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;INDENT_SEQUENCE_VALUE&quot;</span><span class="w"> </span><span class="na">value=</span><span class="s">&quot;true&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">      </span><span class="nt">&lt;option</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;SEQUENCE_ON_NEW_LINE&quot;</span><span class="w"> </span><span class="na">value=</span><span class="s">&quot;true&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">    </span><span class="nt">&lt;/YAMLCodeStyleSettings&gt;</span>
<span class="w">  </span><span class="nt">&lt;/code_scheme&gt;</span>
<span class="nt">&lt;/component&gt;</span>
</code></pre></div>
<h4 id="vs-code-configuration">VS Code Configuration<a class="headerlink" href="#vs-code-configuration" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// .vscode/settings.json</span>
<span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;yaml.schemas&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;https://json.schemastore.org/helm-chart.json&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;deployments/charts/**/Chart.yaml&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;https://json.schemastore.org/helmfile.json&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;deployments/charts/**/values*.yaml&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;yaml.format.enable&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;yaml.validate&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;files.associations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;*.yaml&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;yaml&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;*.yml&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;yaml&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;editor.tabSize&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;editor.insertSpaces&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;groovy.classpath&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;lib/**/*.jar&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="development-environment-variables">Development Environment Variables<a class="headerlink" href="#development-environment-variables" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># .env.development</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">NAMESPACE</span><span class="o">=</span><span class="s2">&quot;cpq-config-dev&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">ENVIRONMENT</span><span class="o">=</span><span class="s2">&quot;development&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">LOG_LEVEL</span><span class="o">=</span><span class="s2">&quot;DEBUG&quot;</span>

<span class="c1"># CPQ Configuration</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">CPQ_ENGINE_URL</span><span class="o">=</span><span class="s2">&quot;http://localhost:8080&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">CPQ_CONFIG_VALIDATION</span><span class="o">=</span><span class="s2">&quot;strict&quot;</span>

<span class="c1"># External Service Mocks</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">MOCK_EXTERNAL_SERVICES</span><span class="o">=</span><span class="s2">&quot;true&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">SFDC_MOCK_URL</span><span class="o">=</span><span class="s2">&quot;http://localhost:3001&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">TMF_MOCK_URL</span><span class="o">=</span><span class="s2">&quot;http://localhost:3002&quot;</span>

<span class="c1"># Development Timeouts (relaxed)</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">CPQ_APPLY_SSF_INITIAL_DELAY_START</span><span class="o">=</span><span class="s2">&quot;0&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">CPQ_APPLY_SSF_INITIAL_DELAY_END</span><span class="o">=</span><span class="s2">&quot;1000&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">CPQ_POST_TASK_BAN_LOCK_RETRY_DELAY_START</span><span class="o">=</span><span class="s2">&quot;500&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">CPQ_POST_TASK_BAN_LOCK_RETRY_DELAY_END</span><span class="o">=</span><span class="s2">&quot;2000&quot;</span>

<span class="c1"># Development Credentials (use test values)</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">SFDC_CLIENT_ID</span><span class="o">=</span><span class="s2">&quot;test_client_id&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">SFDC_CLIENT_SECRET</span><span class="o">=</span><span class="s2">&quot;test_client_secret&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">KAFKA_CLIENT_USERNAME</span><span class="o">=</span><span class="s2">&quot;test_user&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">KAFKA_CLIENT_PASSWORD</span><span class="o">=</span><span class="s2">&quot;test_password&quot;</span>
</code></pre></div>
<h2 id="configuration-development-workflow">Configuration Development Workflow<a class="headerlink" href="#configuration-development-workflow" title="Permanent link">&para;</a></h2>
<h3 id="development-process">Development Process<a class="headerlink" href="#development-process" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;Development Workflow&quot;
        A[Requirements Analysis] --&gt; B[Design Configuration]
        B --&gt; C[Implement Changes]
        C --&gt; D[Local Testing]
        D --&gt; E[Code Review]
        E --&gt; F[Integration Testing]
        F --&gt; G[QA Deployment]
        G --&gt; H[Production Release]
    end

    subgraph &quot;Configuration Types&quot;
        I[Integration Chains]
        J[Service Definitions]
        K[Variable Updates]
        L[Security Changes]
    end

    subgraph &quot;Testing Levels&quot;
        M[Unit Tests]
        N[Integration Tests]
        O[End-to-End Tests]
        P[Performance Tests]
    end

    C --&gt; I
    C --&gt; J
    C --&gt; K
    C --&gt; L

    D --&gt; M
    F --&gt; N
    F --&gt; O
    F --&gt; P

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style C fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style D fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="git-workflow">Git Workflow<a class="headerlink" href="#git-workflow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Create feature branch</span>
git<span class="w"> </span>checkout<span class="w"> </span>-b<span class="w"> </span>feature/new-integration-chain

<span class="c1"># 2. Make configuration changes</span>
<span class="c1"># Edit files in content/chains/, content/services/, etc.</span>

<span class="c1"># 3. Validate configuration</span>
mvn<span class="w"> </span>validate
mvn<span class="w"> </span>cip:validate-configuration

<span class="c1"># 4. Run tests</span>
mvn<span class="w"> </span><span class="nb">test</span>
mvn<span class="w"> </span>integration-test

<span class="c1"># 5. Commit changes</span>
git<span class="w"> </span>add<span class="w"> </span>.
git<span class="w"> </span>commit<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;feat: add new credit assessment integration chain</span>

<span class="s2">- Add CAR creation workflow for SFDC integration</span>
<span class="s2">- Include error handling and retry logic</span>
<span class="s2">- Add comprehensive test coverage&quot;</span>

<span class="c1"># 6. Push and create pull request</span>
git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>feature/new-integration-chain
<span class="c1"># Create PR through GitHub/GitLab interface</span>

<span class="c1"># 7. After review and approval, merge</span>
git<span class="w"> </span>checkout<span class="w"> </span>main
git<span class="w"> </span>pull<span class="w"> </span>origin<span class="w"> </span>main
git<span class="w"> </span>merge<span class="w"> </span>feature/new-integration-chain
git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>main
</code></pre></div>
<h3 id="configuration-validation">Configuration Validation<a class="headerlink" href="#configuration-validation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Validate configuration syntax</span>
mvn<span class="w"> </span>validate

<span class="c1"># Validate specific components</span>
mvn<span class="w"> </span>cpq:validate-chains
mvn<span class="w"> </span>cpq:validate-services
mvn<span class="w"> </span>cpq:validate-variables
mvn<span class="w"> </span>cpq:validate-security

<span class="c1"># Validate Helm charts</span>
helm<span class="w"> </span>lint<span class="w"> </span>deployments/charts/telus-cpq-config-package

<span class="c1"># Validate Kubernetes manifests</span>
helm<span class="w"> </span>template<span class="w"> </span>cpq-config<span class="w"> </span>deployments/charts/telus-cpq-config-package<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--values<span class="w"> </span>deployments/charts/telus-cpq-config-package/values-dev.yaml<span class="w"> </span><span class="se">\</span>
<span class="w">  </span><span class="p">|</span><span class="w"> </span>kubectl<span class="w"> </span>apply<span class="w"> </span>--dry-run<span class="o">=</span>client<span class="w"> </span>-f<span class="w"> </span>-

<span class="c1"># Validate Groovy scripts</span>
mvn<span class="w"> </span>groovy:compile
</code></pre></div>
<h2 id="integration-chain-development">Integration Chain Development<a class="headerlink" href="#integration-chain-development" title="Permanent link">&para;</a></h2>
<h3 id="chain-development-template">Chain Development Template<a class="headerlink" href="#chain-development-template" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Template for new integration chain</span>
<span class="nn">---</span>
<span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;new-chain-uuid&quot;</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;New</span><span class="nv"> </span><span class="s">Integration</span><span class="nv"> </span><span class="s">Chain&quot;</span>
<span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Description</span><span class="nv"> </span><span class="s">of</span><span class="nv"> </span><span class="s">the</span><span class="nv"> </span><span class="s">integration</span><span class="nv"> </span><span class="s">chain</span><span class="nv"> </span><span class="s">purpose&quot;</span>
<span class="nt">modifiedWhen</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1234567890123</span>

<span class="nt">elements</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;element-1-uuid&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Input</span><span class="nv"> </span><span class="s">Validation&quot;</span>
<span class="w">  </span><span class="nt">element-type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script&quot;</span>
<span class="w">  </span><span class="nt">properties-filename</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script-input-validation.groovy&quot;</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">exportFileExtension</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy&quot;</span>
<span class="w">    </span><span class="nt">propertiesToExportInSeparateFile</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;element-2-uuid&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;External</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Call&quot;</span>
<span class="w">  </span><span class="nt">element-type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-call&quot;</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">integrationOperationId</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;external-service-operation&quot;</span>
<span class="w">    </span><span class="nt">integrationOperationMethod</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>
<span class="w">    </span><span class="nt">integrationOperationPath</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/api/v1/resource&quot;</span>
<span class="w">    </span><span class="nt">integrationOperationPathParameters</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{}</span>
<span class="w">    </span><span class="nt">integrationOperationQueryParameters</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{}</span>
<span class="w">    </span><span class="nt">authorizationConfiguration</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;oauth2&quot;</span>
<span class="w">    </span><span class="nt">before</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script&quot;</span>
<span class="w">      </span><span class="nt">properties-filename</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script-before-service-call.groovy&quot;</span>
<span class="w">    </span><span class="nt">after</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;200&quot;</span>
<span class="w">      </span><span class="nt">code</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;200&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script&quot;</span>
<span class="w">      </span><span class="nt">label</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Success&quot;</span>
<span class="w">      </span><span class="nt">properties-filename</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script-200-success.groovy&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;default&quot;</span>
<span class="w">      </span><span class="nt">code</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;default&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script&quot;</span>
<span class="w">      </span><span class="nt">label</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Default&quot;</span>
<span class="w">      </span><span class="nt">properties-filename</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script-default-error.groovy&quot;</span>
<span class="w">    </span><span class="nt">errorThrowing</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">retryCount</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">    </span><span class="nt">retryDelay</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5000</span>
<span class="w">    </span><span class="nt">systemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;EXTERNAL&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;element-3-uuid&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Response</span><span class="nv"> </span><span class="s">Processing&quot;</span>
<span class="w">  </span><span class="nt">element-type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script&quot;</span>
<span class="w">  </span><span class="nt">properties-filename</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script-response-processing.groovy&quot;</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">exportFileExtension</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy&quot;</span>
<span class="w">    </span><span class="nt">propertiesToExportInSeparateFile</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script&quot;</span>
</code></pre></div>
<h3 id="groovy-script-development">Groovy Script Development<a class="headerlink" href="#groovy-script-development" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Template for Groovy scripts</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">groovy.json.JsonBuilder</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">groovy.json.JsonSlurper</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">groovy.util.logging.Slf4j</span>

<span class="nd">@Slf4j</span>
<span class="kd">class</span><span class="w"> </span><span class="nc">ChainScript</span><span class="w"> </span><span class="o">{</span>

<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="nf">execute</span><span class="o">(</span><span class="n">exchange</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="o">{</span>
<span class="w">            </span><span class="n">log</span><span class="o">.</span><span class="na">info</span><span class="o">(</span><span class="s2">&quot;Starting script execution for exchange: ${exchange.exchangeId}&quot;</span><span class="o">)</span>

<span class="w">            </span><span class="c1">// Extract input parameters</span>
<span class="w">            </span><span class="kt">def</span><span class="w"> </span><span class="n">inputData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractInputData</span><span class="o">(</span><span class="n">exchange</span><span class="o">)</span>

<span class="w">            </span><span class="c1">// Validate input</span>
<span class="w">            </span><span class="n">validateInput</span><span class="o">(</span><span class="n">inputData</span><span class="o">)</span>

<span class="w">            </span><span class="c1">// Process business logic</span>
<span class="w">            </span><span class="kt">def</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">processBusinessLogic</span><span class="o">(</span><span class="n">inputData</span><span class="o">)</span>

<span class="w">            </span><span class="c1">// Set output</span>
<span class="w">            </span><span class="n">setOutput</span><span class="o">(</span><span class="n">exchange</span><span class="o">,</span><span class="w"> </span><span class="n">result</span><span class="o">)</span>

<span class="w">            </span><span class="n">log</span><span class="o">.</span><span class="na">info</span><span class="o">(</span><span class="s2">&quot;Script execution completed successfully&quot;</span><span class="o">)</span>

<span class="w">        </span><span class="o">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="o">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">            </span><span class="n">log</span><span class="o">.</span><span class="na">error</span><span class="o">(</span><span class="s2">&quot;Script execution failed: ${e.message}&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">e</span><span class="o">)</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="n">e</span>
<span class="w">        </span><span class="o">}</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">def</span><span class="w"> </span><span class="nf">extractInputData</span><span class="o">(</span><span class="n">exchange</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">body</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getIn</span><span class="o">().</span><span class="na">getBody</span><span class="o">(</span><span class="n">String</span><span class="o">.</span><span class="na">class</span><span class="o">)</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">headers</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getIn</span><span class="o">().</span><span class="na">getHeaders</span><span class="o">()</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">properties</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperties</span><span class="o">()</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="o">[</span>
<span class="w">            </span><span class="nl">body:</span><span class="w"> </span><span class="n">body</span><span class="o">,</span>
<span class="w">            </span><span class="nl">headers:</span><span class="w"> </span><span class="n">headers</span><span class="o">,</span>
<span class="w">            </span><span class="nl">properties:</span><span class="w"> </span><span class="n">properties</span>
<span class="w">        </span><span class="o">]</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">def</span><span class="w"> </span><span class="nf">validateInput</span><span class="o">(</span><span class="n">inputData</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="c1">// Add validation logic here</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="o">(!</span><span class="n">inputData</span><span class="o">.</span><span class="na">body</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IllegalArgumentException</span><span class="o">(</span><span class="s2">&quot;Request body is required&quot;</span><span class="o">)</span>
<span class="w">        </span><span class="o">}</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">def</span><span class="w"> </span><span class="nf">processBusinessLogic</span><span class="o">(</span><span class="n">inputData</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="c1">// Add business logic here</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">jsonSlurper</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">JsonSlurper</span><span class="o">()</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">requestData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">jsonSlurper</span><span class="o">.</span><span class="na">parseText</span><span class="o">(</span><span class="n">inputData</span><span class="o">.</span><span class="na">body</span><span class="o">)</span>

<span class="w">        </span><span class="c1">// Process the request</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">responseData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">[</span>
<span class="w">            </span><span class="nl">status:</span><span class="w"> </span><span class="s2">&quot;success&quot;</span><span class="o">,</span>
<span class="w">            </span><span class="nl">data:</span><span class="w"> </span><span class="n">requestData</span><span class="o">,</span>
<span class="w">            </span><span class="nl">timestamp:</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Date</span><span class="o">().</span><span class="na">format</span><span class="o">(</span><span class="s2">&quot;yyyy-MM-dd&#39;T&#39;HH:mm:ss.SSS&#39;Z&#39;&quot;</span><span class="o">)</span>
<span class="w">        </span><span class="o">]</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">responseData</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">def</span><span class="w"> </span><span class="nf">setOutput</span><span class="o">(</span><span class="n">exchange</span><span class="o">,</span><span class="w"> </span><span class="n">result</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">jsonBuilder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">JsonBuilder</span><span class="o">(</span><span class="n">result</span><span class="o">)</span>
<span class="w">        </span><span class="n">exchange</span><span class="o">.</span><span class="na">getIn</span><span class="o">().</span><span class="na">setBody</span><span class="o">(</span><span class="n">jsonBuilder</span><span class="o">.</span><span class="na">toString</span><span class="o">())</span>
<span class="w">        </span><span class="n">exchange</span><span class="o">.</span><span class="na">getIn</span><span class="o">().</span><span class="na">setHeader</span><span class="o">(</span><span class="s2">&quot;Content-Type&quot;</span><span class="o">,</span><span class="w"> </span><span class="s2">&quot;application/json&quot;</span><span class="o">)</span>
<span class="w">    </span><span class="o">}</span>
<span class="o">}</span>

<span class="c1">// Execute the script</span>
<span class="k">new</span><span class="w"> </span><span class="n">ChainScript</span><span class="o">().</span><span class="na">execute</span><span class="o">(</span><span class="n">exchange</span><span class="o">)</span>
</code></pre></div>
<h3 id="chain-testing-framework">Chain Testing Framework<a class="headerlink" href="#chain-testing-framework" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Chain testing framework</span>
<span class="nd">@Grab</span><span class="o">(</span><span class="s1">&#39;org.apache.camel:camel-test:3.20.0&#39;</span><span class="o">)</span>
<span class="nd">@Grab</span><span class="o">(</span><span class="s1">&#39;org.spockframework:spock-core:2.3-groovy-3.0&#39;</span><span class="o">)</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">spock.lang.Specification</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">org.apache.camel.test.junit5.CamelTestSupport</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">org.apache.camel.builder.RouteBuilder</span>

<span class="kd">class</span><span class="w"> </span><span class="nc">IntegrationChainTest</span><span class="w"> </span><span class="kd">extends</span><span class="w"> </span><span class="n">Specification</span><span class="w"> </span><span class="o">{</span>

<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="nf">&quot;test chain execution with valid input&quot;</span><span class="o">()</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="nl">given:</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">chainExecutor</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ChainExecutor</span><span class="o">()</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">testInput</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">[</span>
<span class="w">            </span><span class="nl">quoteId:</span><span class="w"> </span><span class="s2">&quot;test-quote-123&quot;</span><span class="o">,</span>
<span class="w">            </span><span class="nl">customerId:</span><span class="w"> </span><span class="s2">&quot;test-customer-456&quot;</span>
<span class="w">        </span><span class="o">]</span>

<span class="w">        </span><span class="nl">when:</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">chainExecutor</span><span class="o">.</span><span class="na">executeChain</span><span class="o">(</span><span class="s2">&quot;test-chain-id&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">testInput</span><span class="o">)</span>

<span class="w">        </span><span class="nl">then:</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">success</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">true</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">data</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">errors</span><span class="o">.</span><span class="na">isEmpty</span><span class="o">()</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="nf">&quot;test chain execution with invalid input&quot;</span><span class="o">()</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="nl">given:</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">chainExecutor</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ChainExecutor</span><span class="o">()</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">testInput</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">[:]</span><span class="w">  </span><span class="c1">// Empty input</span>

<span class="w">        </span><span class="nl">when:</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">chainExecutor</span><span class="o">.</span><span class="na">executeChain</span><span class="o">(</span><span class="s2">&quot;test-chain-id&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">testInput</span><span class="o">)</span>

<span class="w">        </span><span class="nl">then:</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">success</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">false</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">errors</span><span class="o">.</span><span class="na">size</span><span class="o">()</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">errors</span><span class="o">[</span><span class="mi">0</span><span class="o">].</span><span class="na">contains</span><span class="o">(</span><span class="s2">&quot;required&quot;</span><span class="o">)</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="nf">&quot;test chain execution with external service failure&quot;</span><span class="o">()</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="nl">given:</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">chainExecutor</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ChainExecutor</span><span class="o">()</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">testInput</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">[</span>
<span class="w">            </span><span class="nl">quoteId:</span><span class="w"> </span><span class="s2">&quot;test-quote-123&quot;</span><span class="o">,</span>
<span class="w">            </span><span class="nl">customerId:</span><span class="w"> </span><span class="s2">&quot;test-customer-456&quot;</span>
<span class="w">        </span><span class="o">]</span>

<span class="w">        </span><span class="nl">and:</span><span class="w"> </span><span class="s2">&quot;external service is down&quot;</span>
<span class="w">        </span><span class="n">mockExternalService</span><span class="o">.</span><span class="na">simulateFailure</span><span class="o">()</span>

<span class="w">        </span><span class="nl">when:</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">chainExecutor</span><span class="o">.</span><span class="na">executeChain</span><span class="o">(</span><span class="s2">&quot;test-chain-id&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">testInput</span><span class="o">)</span>

<span class="w">        </span><span class="nl">then:</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">success</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">false</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">errors</span><span class="o">[</span><span class="mi">0</span><span class="o">].</span><span class="na">contains</span><span class="o">(</span><span class="s2">&quot;external service&quot;</span><span class="o">)</span>
<span class="w">    </span><span class="o">}</span>
<span class="o">}</span>
</code></pre></div>
<h2 id="service-configuration-development">Service Configuration Development<a class="headerlink" href="#service-configuration-development" title="Permanent link">&para;</a></h2>
<h3 id="service-definition-template">Service Definition Template<a class="headerlink" href="#service-definition-template" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Template for new service definition</span>
<span class="nn">---</span>
<span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;new-service-uuid&quot;</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;New</span><span class="nv"> </span><span class="s">External</span><span class="nv"> </span><span class="s">Service&quot;</span>
<span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Description</span><span class="nv"> </span><span class="s">of</span><span class="nv"> </span><span class="s">the</span><span class="nv"> </span><span class="s">external</span><span class="nv"> </span><span class="s">service</span><span class="nv"> </span><span class="s">integration&quot;</span>
<span class="nt">systemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INTEGRATION_SYSTEM&quot;</span>
<span class="nt">integrationSystemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;EXTERNAL&quot;</span>
<span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;HTTP&quot;</span>
<span class="nt">activeEnvironmentId</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;prod-env-id&quot;</span>

<span class="nt">environments</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;dev-env-id&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Development&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://service-dev.example.com&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;DEVELOPMENT&quot;</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">api-version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.0&quot;</span>
<span class="w">    </span><span class="nt">authentication</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;oauth2&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;qa-env-id&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;QA&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://service-qa.example.com&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">20000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;QA&quot;</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">api-version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.0&quot;</span>
<span class="w">    </span><span class="nt">authentication</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;oauth2&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;prod-env-id&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Production&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://service.example.com&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">15000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;PRODUCTION&quot;</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">api-version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.0&quot;</span>
<span class="w">    </span><span class="nt">authentication</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;oauth2&quot;</span>
<span class="w">    </span><span class="nt">circuit-breaker</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;enabled&quot;</span>

<span class="nt">specifications</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-api-1.0&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Service</span><span class="nv"> </span><span class="s">API</span><span class="nv"> </span><span class="s">v1.0&quot;</span>
<span class="w">  </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.0&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;OpenAPI&quot;</span>
<span class="w">  </span><span class="nt">source</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;source-Service-1.0/Service-API.json&quot;</span>

<span class="nt">operations</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;createResource&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">Resource&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/api/v1/resource&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">a</span><span class="nv"> </span><span class="s">new</span><span class="nv"> </span><span class="s">resource&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;getResource&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Get</span><span class="nv"> </span><span class="s">Resource&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;GET&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/api/v1/resource/{id}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Retrieve</span><span class="nv"> </span><span class="s">resource</span><span class="nv"> </span><span class="s">by</span><span class="nv"> </span><span class="s">ID&quot;</span>
</code></pre></div>
<h3 id="api-specification-development">API Specification Development<a class="headerlink" href="#api-specification-development" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;swagger&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2.0&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;info&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;version&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;1.0&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;title&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;New Service API&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;API specification for new service integration&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;contact&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;API Team&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;host&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;service.example.com&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;basePath&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/api/v1&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;schemes&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;https&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;consumes&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;application/json&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;produces&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;application/json&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;paths&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;/resource&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;post&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;summary&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Create Resource&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Create a new resource&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;operationId&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;createResource&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;parameters&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;body&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;in&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;body&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;required&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;schema&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;$ref&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;#/definitions/ResourceRequest&quot;</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">          </span><span class="p">}</span>
<span class="w">        </span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;responses&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;201&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Resource created successfully&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;schema&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;$ref&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;#/definitions/ResourceResponse&quot;</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">          </span><span class="p">},</span>
<span class="w">          </span><span class="nt">&quot;400&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Bad request&quot;</span>
<span class="w">          </span><span class="p">},</span>
<span class="w">          </span><span class="nt">&quot;500&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Internal server error&quot;</span>
<span class="w">          </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;definitions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;ResourceRequest&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;object&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;required&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;type&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Resource name&quot;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Resource type&quot;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;object&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Additional properties&quot;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;ResourceResponse&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;object&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Resource ID&quot;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Resource name&quot;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Resource status&quot;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;createdAt&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;format&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;date-time&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Creation timestamp&quot;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="testing-and-validation">Testing and Validation<a class="headerlink" href="#testing-and-validation" title="Permanent link">&para;</a></h2>
<h3 id="test-structure">Test Structure<a class="headerlink" href="#test-structure" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>src/test/
├── groovy/
│   ├── integration/                    # Integration tests
│   │   ├── ChainIntegrationTest.groovy
│   │   ├── ServiceIntegrationTest.groovy
│   │   └── EndToEndTest.groovy
│   ├── unit/                          # Unit tests
│   │   ├── ScriptUnitTest.groovy
│   │   ├── ConfigurationTest.groovy
│   │   └── ValidationTest.groovy
│   └── performance/                   # Performance tests
│       ├── ChainPerformanceTest.groovy
│       └── ServicePerformanceTest.groovy
└── resources/
    ├── test-data/                     # Test data files
    ├── mock-responses/                # Mock service responses
    └── test-configurations/           # Test configurations
</code></pre></div>
<h3 id="maven-test-configuration">Maven Test Configuration<a class="headerlink" href="#maven-test-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">&lt;!-- pom.xml test configuration --&gt;</span>
<span class="nt">&lt;build&gt;</span>
<span class="w">  </span><span class="nt">&lt;plugins&gt;</span>
<span class="w">    </span><span class="nt">&lt;plugin&gt;</span>
<span class="w">      </span><span class="nt">&lt;groupId&gt;</span>org.apache.maven.plugins<span class="nt">&lt;/groupId&gt;</span>
<span class="w">      </span><span class="nt">&lt;artifactId&gt;</span>maven-surefire-plugin<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">      </span><span class="nt">&lt;version&gt;</span>3.0.0-M9<span class="nt">&lt;/version&gt;</span>
<span class="w">      </span><span class="nt">&lt;configuration&gt;</span>
<span class="w">        </span><span class="nt">&lt;includes&gt;</span>
<span class="w">          </span><span class="nt">&lt;include&gt;</span>**/*Test.groovy<span class="nt">&lt;/include&gt;</span>
<span class="w">          </span><span class="nt">&lt;include&gt;</span>**/*Spec.groovy<span class="nt">&lt;/include&gt;</span>
<span class="w">        </span><span class="nt">&lt;/includes&gt;</span>
<span class="w">        </span><span class="nt">&lt;systemPropertyVariables&gt;</span>
<span class="w">          </span><span class="nt">&lt;test.environment&gt;</span>development<span class="nt">&lt;/test.environment&gt;</span>
<span class="w">          </span><span class="nt">&lt;mock.external.services&gt;</span>true<span class="nt">&lt;/mock.external.services&gt;</span>
<span class="w">        </span><span class="nt">&lt;/systemPropertyVariables&gt;</span>
<span class="w">      </span><span class="nt">&lt;/configuration&gt;</span>
<span class="w">    </span><span class="nt">&lt;/plugin&gt;</span>

<span class="w">    </span><span class="nt">&lt;plugin&gt;</span>
<span class="w">      </span><span class="nt">&lt;groupId&gt;</span>org.apache.maven.plugins<span class="nt">&lt;/groupId&gt;</span>
<span class="w">      </span><span class="nt">&lt;artifactId&gt;</span>maven-failsafe-plugin<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">      </span><span class="nt">&lt;version&gt;</span>3.0.0-M9<span class="nt">&lt;/version&gt;</span>
<span class="w">      </span><span class="nt">&lt;configuration&gt;</span>
<span class="w">        </span><span class="nt">&lt;includes&gt;</span>
<span class="w">          </span><span class="nt">&lt;include&gt;</span>**/*IT.groovy<span class="nt">&lt;/include&gt;</span>
<span class="w">          </span><span class="nt">&lt;include&gt;</span>**/*IntegrationTest.groovy<span class="nt">&lt;/include&gt;</span>
<span class="w">        </span><span class="nt">&lt;/includes&gt;</span>
<span class="w">      </span><span class="nt">&lt;/configuration&gt;</span>
<span class="w">      </span><span class="nt">&lt;executions&gt;</span>
<span class="w">        </span><span class="nt">&lt;execution&gt;</span>
<span class="w">          </span><span class="nt">&lt;goals&gt;</span>
<span class="w">            </span><span class="nt">&lt;goal&gt;</span>integration-test<span class="nt">&lt;/goal&gt;</span>
<span class="w">            </span><span class="nt">&lt;goal&gt;</span>verify<span class="nt">&lt;/goal&gt;</span>
<span class="w">          </span><span class="nt">&lt;/goals&gt;</span>
<span class="w">        </span><span class="nt">&lt;/execution&gt;</span>
<span class="w">      </span><span class="nt">&lt;/executions&gt;</span>
<span class="w">    </span><span class="nt">&lt;/plugin&gt;</span>
<span class="w">  </span><span class="nt">&lt;/plugins&gt;</span>
<span class="nt">&lt;/build&gt;</span>
</code></pre></div>
<h3 id="test-commands">Test Commands<a class="headerlink" href="#test-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Run unit tests</span>
mvn<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Run integration tests</span>
mvn<span class="w"> </span>integration-test

<span class="c1"># Run all tests</span>
mvn<span class="w"> </span>verify

<span class="c1"># Run specific test</span>
mvn<span class="w"> </span><span class="nb">test</span><span class="w"> </span>-Dtest<span class="o">=</span>ChainIntegrationTest

<span class="c1"># Run tests with coverage</span>
mvn<span class="w"> </span>clean<span class="w"> </span><span class="nb">test</span><span class="w"> </span>jacoco:report

<span class="c1"># Run performance tests</span>
mvn<span class="w"> </span><span class="nb">test</span><span class="w"> </span>-Dtest<span class="o">=</span>*PerformanceTest

<span class="c1"># Run tests against specific environment</span>
mvn<span class="w"> </span><span class="nb">test</span><span class="w"> </span>-Dtest.environment<span class="o">=</span>qa
</code></pre></div>
<h2 id="deployment-and-release-management">Deployment and Release Management<a class="headerlink" href="#deployment-and-release-management" title="Permanent link">&para;</a></h2>
<h3 id="deployment-pipeline">Deployment Pipeline<a class="headerlink" href="#deployment-pipeline" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># .github/workflows/deploy.yml</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy CIP Configuration</span>

<span class="nt">on</span><span class="p">:</span>
<span class="w">  </span><span class="nt">push</span><span class="p">:</span>
<span class="w">    </span><span class="nt">branches</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">main</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">pull_request</span><span class="p">:</span>
<span class="w">    </span><span class="nt">branches</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">main</span><span class="p p-Indicator">]</span>

<span class="nt">jobs</span><span class="p">:</span>
<span class="w">  </span><span class="nt">validate</span><span class="p">:</span>
<span class="w">    </span><span class="nt">runs-on</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ubuntu-latest</span>
<span class="w">    </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/checkout@v3</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Set up JDK 11</span>
<span class="w">      </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/setup-java@v3</span>
<span class="w">      </span><span class="nt">with</span><span class="p">:</span>
<span class="w">        </span><span class="nt">java-version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;11&#39;</span>
<span class="w">        </span><span class="nt">distribution</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;temurin&#39;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Validate configuration</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mvn validate</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Run tests</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mvn test</span>

<span class="w">  </span><span class="nt">deploy-qa</span><span class="p">:</span>
<span class="w">    </span><span class="nt">needs</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">validate</span>
<span class="w">    </span><span class="nt">if</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">github.ref == &#39;refs/heads/main&#39;</span>
<span class="w">    </span><span class="nt">runs-on</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ubuntu-latest</span>
<span class="w">    </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/checkout@v3</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy to QA</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">        </span><span class="no">helm upgrade --install cip-config-qa \</span>
<span class="w">          </span><span class="no">deployments/charts/telus-cip-config-package \</span>
<span class="w">          </span><span class="no">--values deployments/charts/telus-cip-config-package/values-qa.yaml \</span>
<span class="w">          </span><span class="no">--namespace cip-config-qa</span>

<span class="w">  </span><span class="nt">deploy-production</span><span class="p">:</span>
<span class="w">    </span><span class="nt">needs</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">deploy-qa</span>
<span class="w">    </span><span class="nt">if</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">github.ref == &#39;refs/heads/main&#39;</span>
<span class="w">    </span><span class="nt">runs-on</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ubuntu-latest</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">production</span>
<span class="w">    </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/checkout@v3</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy to Production</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">        </span><span class="no">helm upgrade --install cip-config-prod \</span>
<span class="w">          </span><span class="no">deployments/charts/telus-cip-config-package \</span>
<span class="w">          </span><span class="no">--values deployments/charts/telus-cip-config-package/values-production.yaml \</span>
<span class="w">          </span><span class="no">--namespace cip-config-prod</span>
</code></pre></div>
<h3 id="release-management">Release Management<a class="headerlink" href="#release-management" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Create release branch</span>
git<span class="w"> </span>checkout<span class="w"> </span>-b<span class="w"> </span>release/v1.2.0

<span class="c1"># Update version in pom.xml</span>
mvn<span class="w"> </span>versions:set<span class="w"> </span>-DnewVersion<span class="o">=</span><span class="m">1</span>.2.0

<span class="c1"># Create release tag</span>
git<span class="w"> </span>tag<span class="w"> </span>-a<span class="w"> </span>v1.2.0<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;Release version 1.2.0&quot;</span>

<span class="c1"># Push release</span>
git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>release/v1.2.0
git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>v1.2.0

<span class="c1"># Deploy to production</span>
helm<span class="w"> </span>upgrade<span class="w"> </span>--install<span class="w"> </span>cip-config-prod<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>deployments/charts/telus-cip-config-package<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--values<span class="w"> </span>deployments/charts/telus-cip-config-package/values-production.yaml<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--namespace<span class="w"> </span>cip-config-prod<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--version<span class="w"> </span><span class="m">1</span>.2.0
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="06-troubleshooting.md">Troubleshooting →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>