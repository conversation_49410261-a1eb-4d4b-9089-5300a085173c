#!/usr/bin/env node
/**
 * TMF620 MCP Server Registration Script for Cline
 * 
 * This script updates the Cline MCP settings file to include the TMF620 MCP server.
 * It copies the OAuth client ID and client secret from the AMS server configuration.
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Define paths
const clineConfigDir = path.join(os.homedir(), 'AppData', 'Roaming', 'Code', 'User', 'globalStorage', 'saoudrizwan.claude-dev', 'settings');
const clineConfigFile = path.join(clineConfigDir, 'cline_mcp_settings.json');
const serverDir = __dirname;
const serverName = 'tmf620';

console.log(`${colors.blue}TMF620 MCP Server Registration Script for Cline${colors.reset}`);
console.log(`${colors.blue}============================================${colors.reset}`);

// Check if the Cline config directory exists
if (!fs.existsSync(clineConfigDir)) {
  console.error(`${colors.red}Error: Cline config directory not found at ${clineConfigDir}${colors.reset}`);
  console.error(`${colors.red}Please make sure Cline is installed and has been run at least once.${colors.reset}`);
  process.exit(1);
}

// Check if the Cline config file exists
if (!fs.existsSync(clineConfigFile)) {
  console.error(`${colors.red}Error: Cline config file not found at ${clineConfigFile}${colors.reset}`);
  console.error(`${colors.red}Please make sure Cline is installed and has been run at least once.${colors.reset}`);
  process.exit(1);
}

// Load existing config
let config;
try {
  const configData = fs.readFileSync(clineConfigFile, 'utf8');
  config = JSON.parse(configData);
  if (!config.mcpServers) {
    config.mcpServers = {};
  }
} catch (error) {
  console.error(`${colors.red}Error reading Cline config file: ${error.message}${colors.reset}`);
  process.exit(1);
}

// Check if the AMS server configuration exists
if (!config.mcpServers.ams) {
  console.error(`${colors.red}Error: AMS server configuration not found in Cline config file${colors.reset}`);
  console.error(`${colors.red}Please make sure the AMS server is registered in Cline.${colors.reset}`);
  process.exit(1);
}

// Get the OAuth client ID and client secret from the AMS server configuration
const clientId = config.mcpServers.ams.env.CLIENT_ID;
const clientSecret = config.mcpServers.ams.env.CLIENT_SECRET_NONPROD;

if (!clientId || !clientSecret) {
  console.error(`${colors.red}Error: OAuth client ID or client secret not found in AMS server configuration${colors.reset}`);
  process.exit(1);
}

// Get the absolute path to the start-server.js file
const startServerPath = path.join(serverDir, 'start-server.js');

// Create the server configuration
const serverConfig = {
  autoApprove: [
    "get_product_offering",
    "get_product_offering_by_external_id",
    "list_product_offerings",
    "bulk_retrieve_product_offerings",
    "get_catalog",
    "list_catalogs"
  ],
  disabled: false,
  timeout: 120,
  command: "node",
  args: [startServerPath],
  env: {
    DEV_API_URL: "https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2",
    TEST_API_URL: "https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2",
    TEST_QUOTE_ID: "a2db6e1a-27ea-419e-a7a5-10575717f8b7",
    PROD_API_URL: "https://prod-api-url.example.com",
    OAUTH_TOKEN_URL: "https://apigw-st.telus.com/st/token",
    OAUTH_SCOPE: "4823",
    OAUTH_CLIENT_ID: clientId,
    OAUTH_CLIENT_SECRET: clientSecret,
    HTTP_PROXY: "",
    HTTPS_PROXY: "",
    NO_PROXY: "*"
  },
  transportType: "stdio"
};

// Add or update the server in the config
config.mcpServers[serverName] = serverConfig;

// Write the updated config back to the file
try {
  fs.writeFileSync(clineConfigFile, JSON.stringify(config, null, 2), 'utf8');
  console.log(`${colors.green}Successfully registered ${serverName} in Cline MCP config${colors.reset}`);
  console.log(`${colors.green}Server will be started with: node ${startServerPath}${colors.reset}`);
  console.log(`${colors.green}Config file: ${clineConfigFile}${colors.reset}`);
  
  // Display the registered server configuration
  console.log(`\n${colors.blue}Registered server configuration:${colors.reset}`);
  const configToDisplay = { ...serverConfig };
  configToDisplay.env = { ...serverConfig.env };
  configToDisplay.env.OAUTH_CLIENT_ID = '[REDACTED]';
  configToDisplay.env.OAUTH_CLIENT_SECRET = '[REDACTED]';
  console.log(JSON.stringify(configToDisplay, null, 2));
  
  console.log(`\n${colors.blue}To use this server with Cline, you need to:${colors.reset}`);
  console.log(`${colors.blue}1. Restart Cline${colors.reset}`);
  console.log(`${colors.blue}2. Use the following tools in your Cline session:${colors.reset}`);
  console.log(`${colors.blue}   - use_mcp_tool with server_name: ${serverName}${colors.reset}`);
  console.log(`${colors.blue}   - Available tools: ${serverConfig.autoApprove.join(', ')}${colors.reset}`);
  
  console.log(`\n${colors.green}TMF620 MCP Server registration complete!${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Error writing Cline config file: ${error.message}${colors.reset}`);
  process.exit(1);
}
