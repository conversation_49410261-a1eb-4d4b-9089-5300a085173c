# Java API Project Guidelines

## Rules
- Rule: All code must be properly documented
- Rule: Do not create jar files.
- Must: Follow the coding style guide
- Shall: Write unit tests for new features

## Recommendations
- Should: Use meaningful variable names
- Recommended: Break down complex functions
- Should: Review code before submitting

## Requirements
- Required: use Java 21 and springboot framework.
- Required: use spring-cloud-gcp-dependencies 5.7.0.
- Required: include spring-boot-starter-web dependency.
- Required: use JUnit 5 and <PERSON><PERSON><PERSON> for unit tests and include mockito-junit-jupiter 5.14.1 for testing.
- Required: include json encoder for logging using logstash-logback-encoder.
- Required: For serialization of objects to JSON use gson.
- Required: include start and stop goals in the pom.xml.
- Required: use JaCoCo plugin to report code coverage.
- Required: For Springdoc OpenAPI 3.0 file generation use springdoc-openapi-starter-webmvc-ui 2.6.0 and springdoc-openapi-starter-webmvc-api 2.6.0.
- Required: include Maven plugin for Spring Boot with configs for OpenAPI plugin.
- Required: use maven-surefire-plugin for unit tests.
- Required: Only use the content of C:\TEMP\mcp-test\logback-spring.xml file as logging template.
- Required: the project must include application.properties files for dev, st, and prod environment including:
HOST=http://localhost
PORT=8080
CORS_ORIGIN=*
GCP_PROJECT_ID=

- Required: before generating the project code, ask user to enter a GCP Project ID. Use the given project id as value for the environment variable GCP_PROJECT_ID.
- Required: All code files must have introduction comments and include "Property of TELUS" at the top.
- Required: always use /api/ as base route.
- Required: always generate swagger oas file based on OpenAPI 3.0 for the endpoint definitions exposed in the api controller.
- Required: always include a route /api/doc to display the open api documentation ui.
- Required: always use Maven to compile and run the application
- Needs: Proper error handling
- Requirement: Follow Git commit message conventions
