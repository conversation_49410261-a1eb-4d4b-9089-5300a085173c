
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/backend/05-development-guide/">
      
      
        <link rel="prev" href="../04-integration-layer/">
      
      
        <link rel="next" href="../../extensions/00-overview/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Development Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#backend-development-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Development Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" checked>
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#java-development-kit-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Java Development Kit Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#maven-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#ide-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      IDE Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="IDE Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#intellij-idea-setup" class="md-nav__link">
    <span class="md-ellipsis">
      IntelliJ IDEA Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#code-style-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Code Style Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-multi-module-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Multi-Module Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Development Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#daily-development-process" class="md-nav__link">
    <span class="md-ellipsis">
      Daily Development Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#git-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Git Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#code-generation" class="md-nav__link">
    <span class="md-ellipsis">
      Code Generation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Development Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategy
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Strategy">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Test Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Test Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#build-and-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Build and Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Build and Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-build-profiles" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Build Profiles
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#docker-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Docker Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Build Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Pipeline
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues-and-solutions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues and Solutions
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Common Issues and Solutions">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#build-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Build Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#runtime-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Runtime Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Development Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#java-development-kit-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Java Development Kit Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#maven-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#ide-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      IDE Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="IDE Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#intellij-idea-setup" class="md-nav__link">
    <span class="md-ellipsis">
      IntelliJ IDEA Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#code-style-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Code Style Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-multi-module-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Multi-Module Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Development Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#daily-development-process" class="md-nav__link">
    <span class="md-ellipsis">
      Daily Development Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#git-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Git Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#code-generation" class="md-nav__link">
    <span class="md-ellipsis">
      Code Generation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Development Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategy
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Strategy">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Test Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Test Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#build-and-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Build and Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Build and Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-build-profiles" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Build Profiles
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#docker-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Docker Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Build Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Pipeline
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues-and-solutions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues and Solutions
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Common Issues and Solutions">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#build-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Build Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#runtime-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Runtime Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Development Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="backend-development-guide">Backend Development Guide<a class="headerlink" href="#backend-development-guide" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#development-environment-setup">Development Environment Setup</a></li>
<li><a href="#project-structure">Project Structure</a></li>
<li><a href="#development-workflow">Development Workflow</a></li>
<li><a href="#testing-strategy">Testing Strategy</a></li>
<li><a href="#build-and-deployment">Build and Deployment</a></li>
<li><a href="#troubleshooting">Troubleshooting</a></li>
</ul>
<h2 id="development-environment-setup">Development Environment Setup<a class="headerlink" href="#development-environment-setup" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Required Software Versions</span>
Java:<span class="w"> </span>OpenJDK<span class="w"> </span><span class="m">17</span><span class="w"> </span>or<span class="w"> </span>higher
Maven:<span class="w"> </span><span class="m">3</span>.8.x<span class="w"> </span>or<span class="w"> </span>higher
Docker:<span class="w"> </span><span class="m">20</span>.x<span class="w"> </span>or<span class="w"> </span>higher
Git:<span class="w"> </span><span class="m">2</span>.x<span class="w"> </span>or<span class="w"> </span>higher
IDE:<span class="w"> </span>IntelliJ<span class="w"> </span>IDEA<span class="w"> </span>or<span class="w"> </span>Eclipse<span class="w"> </span><span class="o">(</span>recommended<span class="o">)</span>
</code></pre></div>
<h3 id="java-development-kit-setup">Java Development Kit Setup<a class="headerlink" href="#java-development-kit-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Install OpenJDK 17</span>
<span class="c1"># On macOS with Homebrew</span>
brew<span class="w"> </span>install<span class="w"> </span>openjdk@17

<span class="c1"># On Ubuntu/Debian</span>
sudo<span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>openjdk-17-jdk

<span class="c1"># On Windows</span>
<span class="c1"># Download from https://adoptium.net/</span>

<span class="c1"># Verify installation</span>
java<span class="w"> </span>-version
javac<span class="w"> </span>-version
</code></pre></div>
<h3 id="maven-configuration">Maven Configuration<a class="headerlink" href="#maven-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">&lt;!-- ~/.m2/settings.xml --&gt;</span>
<span class="nt">&lt;settings</span><span class="w"> </span><span class="na">xmlns=</span><span class="s">&quot;http://maven.apache.org/SETTINGS/1.0.0&quot;</span><span class="nt">&gt;</span>
<span class="w">    </span><span class="nt">&lt;profiles&gt;</span>
<span class="w">        </span><span class="nt">&lt;profile&gt;</span>
<span class="w">            </span><span class="nt">&lt;id&gt;</span>telus-development<span class="nt">&lt;/id&gt;</span>
<span class="w">            </span><span class="nt">&lt;properties&gt;</span>
<span class="w">                </span><span class="nt">&lt;spring.profiles.active&gt;</span>dev<span class="nt">&lt;/spring.profiles.active&gt;</span>
<span class="w">                </span><span class="nt">&lt;maven.test.skip&gt;</span>false<span class="nt">&lt;/maven.test.skip&gt;</span>
<span class="w">            </span><span class="nt">&lt;/properties&gt;</span>
<span class="w">            </span><span class="nt">&lt;repositories&gt;</span>
<span class="w">                </span><span class="nt">&lt;repository&gt;</span>
<span class="w">                    </span><span class="nt">&lt;id&gt;</span>netcracker-releases<span class="nt">&lt;/id&gt;</span>
<span class="w">                    </span><span class="nt">&lt;url&gt;</span>https://nexus.netcracker.com/repository/maven-public/<span class="nt">&lt;/url&gt;</span>
<span class="w">                </span><span class="nt">&lt;/repository&gt;</span>
<span class="w">            </span><span class="nt">&lt;/repositories&gt;</span>
<span class="w">        </span><span class="nt">&lt;/profile&gt;</span>
<span class="w">    </span><span class="nt">&lt;/profiles&gt;</span>

<span class="w">    </span><span class="nt">&lt;activeProfiles&gt;</span>
<span class="w">        </span><span class="nt">&lt;activeProfile&gt;</span>telus-development<span class="nt">&lt;/activeProfile&gt;</span>
<span class="w">    </span><span class="nt">&lt;/activeProfiles&gt;</span>
<span class="nt">&lt;/settings&gt;</span>
</code></pre></div>
<h3 id="ide-configuration">IDE Configuration<a class="headerlink" href="#ide-configuration" title="Permanent link">&para;</a></h3>
<h4 id="intellij-idea-setup">IntelliJ IDEA Setup<a class="headerlink" href="#intellij-idea-setup" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># .idea/compiler.xml</span>
<span class="na">&lt;?xml</span><span class="w"> </span><span class="s">version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;</span>
<span class="na">&lt;project</span><span class="w"> </span><span class="s">version=&quot;4&quot;&gt;</span>
<span class="w">  </span><span class="na">&lt;component</span><span class="w"> </span><span class="s">name=&quot;CompilerConfiguration&quot;&gt;</span>
<span class="w">    </span><span class="na">&lt;annotationProcessing&gt;</span>
<span class="w">      </span><span class="na">&lt;profile</span><span class="w"> </span><span class="s">name=&quot;Maven default annotation processors profile&quot; enabled=&quot;true&quot;&gt;</span>
<span class="w">        </span><span class="na">&lt;sourceOutputDir</span><span class="w"> </span><span class="s">name=&quot;target/generated-sources/annotations&quot; /&gt;</span>
<span class="w">        </span><span class="na">&lt;sourceTestOutputDir</span><span class="w"> </span><span class="s">name=&quot;target/generated-test-sources/test-annotations&quot; /&gt;</span>
<span class="w">        </span><span class="na">&lt;outputRelativeToContentRoot</span><span class="w"> </span><span class="s">value=&quot;true&quot; /&gt;</span>
<span class="w">        </span><span class="na">&lt;module</span><span class="w"> </span><span class="s">name=&quot;cloud-bss-oc-ui-backend-b2b-application&quot; /&gt;</span>
<span class="w">        </span><span class="na">&lt;module</span><span class="w"> </span><span class="s">name=&quot;nc-cloud-bss-oc-ui-be-b2b-impl&quot; /&gt;</span>
<span class="w">      </span><span class="na">&lt;/profile&gt;</span>
<span class="w">    </span><span class="na">&lt;/annotationProcessing&gt;</span>
<span class="w">  </span><span class="na">&lt;/component&gt;</span>
<span class="na">&lt;/project&gt;</span>
</code></pre></div>
<h4 id="code-style-configuration">Code Style Configuration<a class="headerlink" href="#code-style-configuration" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="cm">&lt;!-- .editorconfig --&gt;</span>
root<span class="w"> </span>=<span class="w"> </span>true

[*]
charset<span class="w"> </span>=<span class="w"> </span>utf-8
end_of_line<span class="w"> </span>=<span class="w"> </span>lf
insert_final_newline<span class="w"> </span>=<span class="w"> </span>true
trim_trailing_whitespace<span class="w"> </span>=<span class="w"> </span>true

[*.java]
indent_style<span class="w"> </span>=<span class="w"> </span>space
indent_size<span class="w"> </span>=<span class="w"> </span>4
max_line_length<span class="w"> </span>=<span class="w"> </span>120

[*.{yml,yaml}]
indent_style<span class="w"> </span>=<span class="w"> </span>space
indent_size<span class="w"> </span>=<span class="w"> </span>2

[*.xml]
indent_style<span class="w"> </span>=<span class="w"> </span>space
indent_size<span class="w"> </span>=<span class="w"> </span>4
</code></pre></div>
<h2 id="project-structure">Project Structure<a class="headerlink" href="#project-structure" title="Permanent link">&para;</a></h2>
<h3 id="maven-multi-module-architecture">Maven Multi-Module Architecture<a class="headerlink" href="#maven-multi-module-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>nc-cloud-bss-oc-ui-backend-b2b/
├── cloud-bss-oc-ui-backend-b2b-application/    # Main Spring Boot application
│   ├── src/main/java/
│   │   └── com/netcracker/solutions/telus/ordercapture/backend/
│   │       ├── OrderEntryWebApplication.java   # Main application class
│   │       ├── config/                         # Configuration classes
│   │       │   ├── SecurityConfig.java         # Security configuration
│   │       │   ├── JacksonConfig.java          # JSON serialization
│   │       │   ├── OpenApiConfig.java          # API documentation
│   │       │   ├── CorsConfig.java             # CORS configuration
│   │       │   └── WebConfig.java              # Web configuration
│   │       ├── controller/                     # REST controllers
│   │       │   ├── QuoteController.java        # Quote management APIs
│   │       │   ├── OrderController.java        # Order management APIs
│   │       │   ├── ServiceController.java      # Service catalog APIs
│   │       │   ├── CustomerController.java     # Customer management APIs
│   │       │   └── HealthController.java       # Health check endpoints
│   │       └── exception/                      # Exception handling
│   │           ├── GlobalExceptionHandler.java # Global error handler
│   │           └── CustomExceptions.java       # Custom exception classes
│   ├── src/test/java/                          # Unit and integration tests
│   └── pom.xml                                 # Application module POM
├── nc-cloud-bss-oc-ui-be-b2b-impl/            # Implementation modules
│   ├── src/main/java/
│   │   └── com/netcracker/solutions/telus/ordercapture/impl/
│   │       ├── service/                        # Service layer
│   │       │   ├── QuoteServiceImpl.java       # Quote business logic
│   │       │   ├── OrderServiceImpl.java       # Order business logic
│   │       │   ├── ValidationServiceImpl.java  # Validation logic
│   │       │   ├── MappingServiceImpl.java     # Data transformation
│   │       │   └── NotificationServiceImpl.java # Notification logic
│   │       ├── client/                         # Integration clients
│   │       │   ├── OrderCaptureClient.java     # OCP integration
│   │       │   ├── BssIntegrationClient.java   # BSS system client
│   │       │   ├── CipPlatformClient.java      # CIP integration
│   │       │   └── ExternalApiClient.java      # External API client
│   │       ├── model/                          # Data models
│   │       │   ├── dto/                        # Data Transfer Objects
│   │       │   │   ├── QuoteResponse.java      # Quote response DTO
│   │       │   │   ├── OrderResponse.java      # Order response DTO
│   │       │   │   └── CustomerResponse.java   # Customer response DTO
│   │       │   ├── request/                    # Request models
│   │       │   │   ├── CreateQuoteRequest.java # Quote creation request
│   │       │   │   ├── UpdateQuoteRequest.java # Quote update request
│   │       │   │   └── CreateOrderRequest.java # Order creation request
│   │       │   └── entity/                     # JPA entities (if needed)
│   │       ├── util/                           # Utility classes
│   │       │   ├── DateUtils.java              # Date utilities
│   │       │   ├── ValidationUtils.java        # Validation helpers
│   │       │   ├── MappingUtils.java           # Mapping utilities
│   │       │   └── SecurityUtils.java          # Security utilities
│   │       └── event/                          # Event handling
│   │           ├── QuoteCreatedEvent.java      # Quote created event
│   │           ├── OrderStatusChangedEvent.java # Order status event
│   │           └── BusinessEventHandler.java   # Event handler
│   ├── src/test/java/                          # Unit tests
│   └── pom.xml                                 # Implementation module POM
├── cloud-bss-oc-ui-backend-b2b-resources/      # Resource definitions
│   └── src/main/resources/                     # Configuration files
│       ├── application.yml                     # Main configuration
│       ├── application-dev.yml                 # Development config
│       ├── application-test.yml                # Test configuration
│       ├── application-prod.yml                # Production config
│       ├── logback-spring.xml                  # Logging configuration
│       ├── banner.txt                          # Application banner
│       └── static/                             # Static resources
├── deployments/                                # Kubernetes deployment configs
│   ├── charts/                                 # Helm charts
│   │   ├── Chart.yaml                          # Helm chart definition
│   │   ├── values.yaml                         # Default values
│   │   ├── values-dev.yaml                     # Development values
│   │   ├── values-prod.yaml                    # Production values
│   │   └── templates/                          # Kubernetes templates
│   ├── configmaps/                             # Configuration maps
│   └── secrets/                                # Secret configurations
├── pom.xml                                     # Parent POM configuration
├── Dockerfile                                  # Container definition
├── docker-compose.yml                          # Local development setup
├── .gitignore                                  # Git ignore rules
├── README.md                                   # Project documentation
└── SUPPORT.md                                  # Support documentation
</code></pre></div>
<h3 id="configuration-management">Configuration Management<a class="headerlink" href="#configuration-management" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># application.yml - Base configuration</span>
<span class="nt">spring</span><span class="p">:</span>
<span class="w">  </span><span class="nt">application</span><span class="p">:</span>
<span class="w">    </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-order-capture-backend</span>
<span class="w">  </span><span class="nt">profiles</span><span class="p">:</span>
<span class="w">    </span><span class="nt">active</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${SPRING_PROFILES_ACTIVE:dev}</span>

<span class="w">  </span><span class="nt">jackson</span><span class="p">:</span>
<span class="w">    </span><span class="nt">property-naming-strategy</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">SNAKE_CASE</span>
<span class="w">    </span><span class="nt">default-property-inclusion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">NON_NULL</span>
<span class="w">    </span><span class="nt">serialization</span><span class="p">:</span>
<span class="w">      </span><span class="nt">write-dates-as-timestamps</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>
<span class="w">    </span><span class="nt">deserialization</span><span class="p">:</span>
<span class="w">      </span><span class="nt">fail-on-unknown-properties</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="nt">server</span><span class="p">:</span>
<span class="w">  </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${SERVER_PORT:8080}</span>
<span class="w">  </span><span class="nt">servlet</span><span class="p">:</span>
<span class="w">    </span><span class="nt">context-path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/api/v1</span>
<span class="w">  </span><span class="nt">compression</span><span class="p">:</span>
<span class="w">    </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">mime-types</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">application/json,application/xml,text/html,text/xml,text/plain</span>

<span class="nt">management</span><span class="p">:</span>
<span class="w">  </span><span class="nt">endpoints</span><span class="p">:</span>
<span class="w">    </span><span class="nt">web</span><span class="p">:</span>
<span class="w">      </span><span class="nt">exposure</span><span class="p">:</span>
<span class="w">        </span><span class="nt">include</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">health,info,metrics,prometheus</span>
<span class="w">  </span><span class="nt">endpoint</span><span class="p">:</span>
<span class="w">    </span><span class="nt">health</span><span class="p">:</span>
<span class="w">      </span><span class="nt">show-details</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">when-authorized</span>
<span class="w">  </span><span class="nt">metrics</span><span class="p">:</span>
<span class="w">    </span><span class="nt">export</span><span class="p">:</span>
<span class="w">      </span><span class="nt">prometheus</span><span class="p">:</span>
<span class="w">        </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="nt">logging</span><span class="p">:</span>
<span class="w">  </span><span class="nt">level</span><span class="p">:</span>
<span class="w">    </span><span class="nt">com.netcracker.solutions.telus</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${LOG_LEVEL:DEBUG}</span>
<span class="w">    </span><span class="nt">org.springframework.security</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">INFO</span>
<span class="w">    </span><span class="nt">org.springframework.web</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">INFO</span>
<span class="w">  </span><span class="nt">pattern</span><span class="p">:</span>
<span class="w">    </span><span class="nt">console</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;%d{yyyy-MM-dd</span><span class="nv"> </span><span class="s">HH:mm:ss}</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">%msg%n&quot;</span>
<span class="w">    </span><span class="nt">file</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;%d{yyyy-MM-dd</span><span class="nv"> </span><span class="s">HH:mm:ss}</span><span class="nv"> </span><span class="s">[%thread]</span><span class="nv"> </span><span class="s">%-5level</span><span class="nv"> </span><span class="s">%logger{36}</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">%msg%n&quot;</span>
</code></pre></div>
<h2 id="development-workflow">Development Workflow<a class="headerlink" href="#development-workflow" title="Permanent link">&para;</a></h2>
<h3 id="daily-development-process">Daily Development Process<a class="headerlink" href="#daily-development-process" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Start development environment</span>
<span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-backend-b2b

<span class="c1"># 2. Pull latest changes</span>
git<span class="w"> </span>pull<span class="w"> </span>origin<span class="w"> </span>develop

<span class="c1"># 3. Clean and compile</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile

<span class="c1"># 4. Run tests</span>
mvn<span class="w"> </span><span class="nb">test</span>

<span class="c1"># 5. Start application</span>
mvn<span class="w"> </span>spring-boot:run<span class="w"> </span>-pl<span class="w"> </span>cloud-bss-oc-ui-backend-b2b-application

<span class="c1"># 6. Verify application startup</span>
curl<span class="w"> </span>http://localhost:8080/actuator/health
</code></pre></div>
<h3 id="git-workflow">Git Workflow<a class="headerlink" href="#git-workflow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Create feature branch</span>
git<span class="w"> </span>checkout<span class="w"> </span>-b<span class="w"> </span>feature/order-status-enhancement

<span class="c1"># 2. Make changes and commit frequently</span>
git<span class="w"> </span>add<span class="w"> </span>.
git<span class="w"> </span>commit<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;feat: add order status validation logic&quot;</span>

<span class="c1"># 3. Run tests before pushing</span>
mvn<span class="w"> </span>clean<span class="w"> </span><span class="nb">test</span>

<span class="c1"># 4. Push to remote</span>
git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>feature/order-status-enhancement

<span class="c1"># 5. Create pull request</span>
<span class="c1"># 6. After review, merge to develop</span>
</code></pre></div>
<h3 id="code-generation">Code Generation<a class="headerlink" href="#code-generation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Generate new REST controller</span>
mvn<span class="w"> </span>archetype:generate<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-DgroupId<span class="o">=</span>com.netcracker.solutions.telus.ordercapture.backend<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-DartifactId<span class="o">=</span>location-controller<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-DarchetypeArtifactId<span class="o">=</span>maven-archetype-quickstart

<span class="c1"># Generate new service class</span>
<span class="c1"># Use IDE templates or manual creation</span>

<span class="c1"># Generate OpenAPI documentation</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile
<span class="c1"># Access at http://localhost:8080/swagger-ui.html</span>
</code></pre></div>
<h3 id="development-commands">Development Commands<a class="headerlink" href="#development-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Build commands</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile<span class="w">                    </span><span class="c1"># Compile source code</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w">                    </span><span class="c1"># Package without tests</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w">                    </span><span class="c1"># Full build with tests</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w"> </span>-DskipTests<span class="w">       </span><span class="c1"># Build without tests</span>

<span class="c1"># Module-specific builds</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w"> </span>-pl<span class="w"> </span>cloud-bss-oc-ui-backend-b2b-application
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w"> </span>-pl<span class="w"> </span>nc-cloud-bss-oc-ui-be-b2b-impl

<span class="c1"># Development commands</span>
mvn<span class="w"> </span>spring-boot:run<span class="w">                  </span><span class="c1"># Run application</span>
mvn<span class="w"> </span>spring-boot:run<span class="w"> </span>-Dspring-boot.run.profiles<span class="o">=</span>dev
mvn<span class="w"> </span><span class="nb">test</span><span class="w">                            </span><span class="c1"># Run tests</span>
mvn<span class="w"> </span>verify<span class="w">                          </span><span class="c1"># Run integration tests</span>

<span class="c1"># Code quality</span>
mvn<span class="w"> </span>checkstyle:check<span class="w">                </span><span class="c1"># Code style validation</span>
mvn<span class="w"> </span>spotbugs:check<span class="w">                  </span><span class="c1"># Static analysis</span>
mvn<span class="w"> </span>jacoco:report<span class="w">                   </span><span class="c1"># Test coverage report</span>

<span class="c1"># Dependency management</span>
mvn<span class="w"> </span>dependency:tree<span class="w">                 </span><span class="c1"># Show dependency tree</span>
mvn<span class="w"> </span>dependency:analyze<span class="w">              </span><span class="c1"># Analyze dependencies</span>
mvn<span class="w"> </span>versions:display-dependency-updates<span class="w"> </span><span class="c1"># Check for updates</span>
</code></pre></div>
<h2 id="testing-strategy">Testing Strategy<a class="headerlink" href="#testing-strategy" title="Permanent link">&para;</a></h2>
<h3 id="unit-testing">Unit Testing<a class="headerlink" href="#unit-testing" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Service Unit Test Example</span>
<span class="nd">@ExtendWith</span><span class="p">(</span><span class="n">MockitoExtension</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="kd">class</span> <span class="nc">QuoteServiceImplTest</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Mock</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">OrderCaptureClient</span><span class="w"> </span><span class="n">orderCaptureClient</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Mock</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">ValidationService</span><span class="w"> </span><span class="n">validationService</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Mock</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">MappingService</span><span class="w"> </span><span class="n">mappingService</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@InjectMocks</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">QuoteServiceImpl</span><span class="w"> </span><span class="n">quoteService</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldCreateQuoteSuccessfully</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">CreateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">CreateQuoteRequest</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">customerId</span><span class="p">(</span><span class="s">&quot;CUST-001&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">lineItems</span><span class="p">(</span><span class="n">List</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="n">createLineItem</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">        </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">validationResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">valid</span><span class="p">();</span>
<span class="w">        </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">expectedResponse</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createQuoteResponse</span><span class="p">();</span>

<span class="w">        </span><span class="n">when</span><span class="p">(</span><span class="n">validationService</span><span class="p">.</span><span class="na">validateCreateQuoteRequest</span><span class="p">(</span><span class="n">request</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">thenReturn</span><span class="p">(</span><span class="n">validationResult</span><span class="p">);</span>
<span class="w">        </span><span class="n">when</span><span class="p">(</span><span class="n">orderCaptureClient</span><span class="p">.</span><span class="na">createQuote</span><span class="p">(</span><span class="n">any</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">thenReturn</span><span class="p">(</span><span class="n">expectedResponse</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quoteService</span><span class="p">.</span><span class="na">createQuote</span><span class="p">(</span><span class="n">request</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">).</span><span class="na">isNotNull</span><span class="p">();</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">()).</span><span class="na">isEqualTo</span><span class="p">(</span><span class="n">expectedResponse</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="n">verify</span><span class="p">(</span><span class="n">validationService</span><span class="p">).</span><span class="na">validateCreateQuoteRequest</span><span class="p">(</span><span class="n">request</span><span class="p">);</span>
<span class="w">        </span><span class="n">verify</span><span class="p">(</span><span class="n">orderCaptureClient</span><span class="p">).</span><span class="na">createQuote</span><span class="p">(</span><span class="n">any</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldThrowExceptionWhenValidationFails</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">CreateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createInvalidRequest</span><span class="p">();</span>
<span class="w">        </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">validationResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">invalid</span><span class="p">(</span><span class="s">&quot;Invalid customer&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="n">when</span><span class="p">(</span><span class="n">validationService</span><span class="p">.</span><span class="na">validateCreateQuoteRequest</span><span class="p">(</span><span class="n">request</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">thenReturn</span><span class="p">(</span><span class="n">validationResult</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// When &amp; Then</span>
<span class="w">        </span><span class="n">assertThatThrownBy</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">quoteService</span><span class="p">.</span><span class="na">createQuote</span><span class="p">(</span><span class="n">request</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">isInstanceOf</span><span class="p">(</span><span class="n">ValidationException</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">hasMessageContaining</span><span class="p">(</span><span class="s">&quot;Invalid customer&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="n">verify</span><span class="p">(</span><span class="n">orderCaptureClient</span><span class="p">,</span><span class="w"> </span><span class="n">never</span><span class="p">()).</span><span class="na">createQuote</span><span class="p">(</span><span class="n">any</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="integration-testing">Integration Testing<a class="headerlink" href="#integration-testing" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Integration Test Example</span>
<span class="nd">@SpringBootTest</span><span class="p">(</span><span class="n">webEnvironment</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">SpringBootTest</span><span class="p">.</span><span class="na">WebEnvironment</span><span class="p">.</span><span class="na">RANDOM_PORT</span><span class="p">)</span>
<span class="nd">@TestPropertySource</span><span class="p">(</span><span class="n">properties</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s">&quot;spring.profiles.active=test&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s">&quot;order-capture.base-url=http://localhost:${wiremock.server.port}&quot;</span>
<span class="p">})</span>
<span class="kd">class</span> <span class="nc">QuoteControllerIntegrationTest</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">TestRestTemplate</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@RegisterExtension</span>
<span class="w">    </span><span class="kd">static</span><span class="w"> </span><span class="n">WireMockExtension</span><span class="w"> </span><span class="n">wireMock</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">WireMockExtension</span><span class="p">.</span><span class="na">newInstance</span><span class="p">()</span>
<span class="w">        </span><span class="p">.</span><span class="na">options</span><span class="p">(</span><span class="n">wireMockConfig</span><span class="p">().</span><span class="na">port</span><span class="p">(</span><span class="mi">8089</span><span class="p">))</span>
<span class="w">        </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldCreateQuoteSuccessfully</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">CreateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createValidQuoteRequest</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Mock Order Capture response</span>
<span class="w">        </span><span class="n">wireMock</span><span class="p">.</span><span class="na">stubFor</span><span class="p">(</span><span class="n">post</span><span class="p">(</span><span class="n">urlEqualTo</span><span class="p">(</span><span class="s">&quot;/quotes&quot;</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">willReturn</span><span class="p">(</span><span class="n">aResponse</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">withStatus</span><span class="p">(</span><span class="mi">201</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">withHeader</span><span class="p">(</span><span class="s">&quot;Content-Type&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;application/json&quot;</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">withBody</span><span class="p">(</span><span class="n">readFile</span><span class="p">(</span><span class="s">&quot;quote-response.json&quot;</span><span class="p">))));</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="n">ResponseEntity</span><span class="o">&lt;</span><span class="n">QuoteResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="n">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">.</span><span class="na">postForEntity</span><span class="p">(</span>
<span class="w">            </span><span class="s">&quot;/api/v1/quotes&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">request</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getStatusCode</span><span class="p">()).</span><span class="na">isEqualTo</span><span class="p">(</span><span class="n">HttpStatus</span><span class="p">.</span><span class="na">CREATED</span><span class="p">);</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">()).</span><span class="na">isNotNull</span><span class="p">();</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">().</span><span class="na">getQuoteId</span><span class="p">()).</span><span class="na">isNotBlank</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldReturnBadRequestForInvalidInput</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">CreateQuoteRequest</span><span class="w"> </span><span class="n">invalidRequest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">CreateQuoteRequest</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">customerId</span><span class="p">(</span><span class="s">&quot;&quot;</span><span class="p">)</span><span class="w"> </span><span class="c1">// Invalid empty customer ID</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="n">ResponseEntity</span><span class="o">&lt;</span><span class="n">ErrorResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="n">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">.</span><span class="na">postForEntity</span><span class="p">(</span>
<span class="w">            </span><span class="s">&quot;/api/v1/quotes&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">invalidRequest</span><span class="p">,</span><span class="w"> </span><span class="n">ErrorResponse</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getStatusCode</span><span class="p">()).</span><span class="na">isEqualTo</span><span class="p">(</span><span class="n">HttpStatus</span><span class="p">.</span><span class="na">BAD_REQUEST</span><span class="p">);</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">().</span><span class="na">getMessage</span><span class="p">()).</span><span class="na">contains</span><span class="p">(</span><span class="s">&quot;validation&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="test-configuration">Test Configuration<a class="headerlink" href="#test-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># application-test.yml</span>
<span class="nt">spring</span><span class="p">:</span>
<span class="w">  </span><span class="nt">profiles</span><span class="p">:</span>
<span class="w">    </span><span class="nt">active</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">test</span>

<span class="w">  </span><span class="nt">datasource</span><span class="p">:</span>
<span class="w">    </span><span class="nt">url</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">jdbc:h2:mem:testdb</span>
<span class="w">    </span><span class="nt">driver-class-name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">org.h2.Driver</span>
<span class="w">    </span><span class="nt">username</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">sa</span>
<span class="w">    </span><span class="nt">password</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">password</span>

<span class="w">  </span><span class="nt">jpa</span><span class="p">:</span>
<span class="w">    </span><span class="nt">hibernate</span><span class="p">:</span>
<span class="w">      </span><span class="nt">ddl-auto</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">create-drop</span>
<span class="w">    </span><span class="nt">show-sql</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="nt">order-capture</span><span class="p">:</span>
<span class="w">  </span><span class="nt">base-url</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">http://localhost:8089</span>
<span class="w">  </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>

<span class="nt">bss-integration</span><span class="p">:</span>
<span class="w">  </span><span class="nt">base-url</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">http://localhost:8090</span>
<span class="w">  </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>

<span class="nt">logging</span><span class="p">:</span>
<span class="w">  </span><span class="nt">level</span><span class="p">:</span>
<span class="w">    </span><span class="nt">com.netcracker.solutions.telus</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DEBUG</span>
<span class="w">    </span><span class="nt">org.springframework.web</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DEBUG</span>
</code></pre></div>
<h3 id="test-commands">Test Commands<a class="headerlink" href="#test-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Run all tests</span>
mvn<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Run specific test class</span>
mvn<span class="w"> </span><span class="nb">test</span><span class="w"> </span>-Dtest<span class="o">=</span>QuoteServiceImplTest

<span class="c1"># Run integration tests</span>
mvn<span class="w"> </span>verify

<span class="c1"># Run tests with coverage</span>
mvn<span class="w"> </span>clean<span class="w"> </span><span class="nb">test</span><span class="w"> </span>jacoco:report

<span class="c1"># Run tests in specific module</span>
mvn<span class="w"> </span><span class="nb">test</span><span class="w"> </span>-pl<span class="w"> </span>nc-cloud-bss-oc-ui-be-b2b-impl

<span class="c1"># Skip tests</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w"> </span>-DskipTests

<span class="c1"># Run only unit tests (exclude integration tests)</span>
mvn<span class="w"> </span><span class="nb">test</span><span class="w"> </span>-Dtest<span class="o">=</span>!*IntegrationTest
</code></pre></div>
<h2 id="build-and-deployment">Build and Deployment<a class="headerlink" href="#build-and-deployment" title="Permanent link">&para;</a></h2>
<h3 id="maven-build-profiles">Maven Build Profiles<a class="headerlink" href="#maven-build-profiles" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">&lt;!-- pom.xml profiles --&gt;</span>
<span class="nt">&lt;profiles&gt;</span>
<span class="w">    </span><span class="nt">&lt;profile&gt;</span>
<span class="w">        </span><span class="nt">&lt;id&gt;</span>dev<span class="nt">&lt;/id&gt;</span>
<span class="w">        </span><span class="nt">&lt;activation&gt;</span>
<span class="w">            </span><span class="nt">&lt;activeByDefault&gt;</span>true<span class="nt">&lt;/activeByDefault&gt;</span>
<span class="w">        </span><span class="nt">&lt;/activation&gt;</span>
<span class="w">        </span><span class="nt">&lt;properties&gt;</span>
<span class="w">            </span><span class="nt">&lt;spring.profiles.active&gt;</span>dev<span class="nt">&lt;/spring.profiles.active&gt;</span>
<span class="w">            </span><span class="nt">&lt;maven.test.skip&gt;</span>false<span class="nt">&lt;/maven.test.skip&gt;</span>
<span class="w">        </span><span class="nt">&lt;/properties&gt;</span>
<span class="w">    </span><span class="nt">&lt;/profile&gt;</span>

<span class="w">    </span><span class="nt">&lt;profile&gt;</span>
<span class="w">        </span><span class="nt">&lt;id&gt;</span>test<span class="nt">&lt;/id&gt;</span>
<span class="w">        </span><span class="nt">&lt;properties&gt;</span>
<span class="w">            </span><span class="nt">&lt;spring.profiles.active&gt;</span>test<span class="nt">&lt;/spring.profiles.active&gt;</span>
<span class="w">            </span><span class="nt">&lt;maven.test.skip&gt;</span>false<span class="nt">&lt;/maven.test.skip&gt;</span>
<span class="w">        </span><span class="nt">&lt;/properties&gt;</span>
<span class="w">    </span><span class="nt">&lt;/profile&gt;</span>

<span class="w">    </span><span class="nt">&lt;profile&gt;</span>
<span class="w">        </span><span class="nt">&lt;id&gt;</span>prod<span class="nt">&lt;/id&gt;</span>
<span class="w">        </span><span class="nt">&lt;properties&gt;</span>
<span class="w">            </span><span class="nt">&lt;spring.profiles.active&gt;</span>prod<span class="nt">&lt;/spring.profiles.active&gt;</span>
<span class="w">            </span><span class="nt">&lt;maven.test.skip&gt;</span>true<span class="nt">&lt;/maven.test.skip&gt;</span>
<span class="w">        </span><span class="nt">&lt;/properties&gt;</span>
<span class="w">        </span><span class="nt">&lt;build&gt;</span>
<span class="w">            </span><span class="nt">&lt;plugins&gt;</span>
<span class="w">                </span><span class="nt">&lt;plugin&gt;</span>
<span class="w">                    </span><span class="nt">&lt;groupId&gt;</span>org.springframework.boot<span class="nt">&lt;/groupId&gt;</span>
<span class="w">                    </span><span class="nt">&lt;artifactId&gt;</span>spring-boot-maven-plugin<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">                    </span><span class="nt">&lt;configuration&gt;</span>
<span class="w">                        </span><span class="nt">&lt;excludes&gt;</span>
<span class="w">                            </span><span class="nt">&lt;exclude&gt;</span>
<span class="w">                                </span><span class="nt">&lt;groupId&gt;</span>org.springframework.boot<span class="nt">&lt;/groupId&gt;</span>
<span class="w">                                </span><span class="nt">&lt;artifactId&gt;</span>spring-boot-devtools<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">                            </span><span class="nt">&lt;/exclude&gt;</span>
<span class="w">                        </span><span class="nt">&lt;/excludes&gt;</span>
<span class="w">                    </span><span class="nt">&lt;/configuration&gt;</span>
<span class="w">                </span><span class="nt">&lt;/plugin&gt;</span>
<span class="w">            </span><span class="nt">&lt;/plugins&gt;</span>
<span class="w">        </span><span class="nt">&lt;/build&gt;</span>
<span class="w">    </span><span class="nt">&lt;/profile&gt;</span>
<span class="nt">&lt;/profiles&gt;</span>
</code></pre></div>
<h3 id="docker-configuration">Docker Configuration<a class="headerlink" href="#docker-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c"># Dockerfile</span>
<span class="k">FROM</span><span class="w"> </span><span class="s">openjdk:17-jre-slim</span>

<span class="k">LABEL</span><span class="w"> </span><span class="nv">maintainer</span><span class="o">=</span><span class="s2">&quot;TELUS Development Team&quot;</span>
<span class="k">LABEL</span><span class="w"> </span><span class="nv">version</span><span class="o">=</span><span class="s2">&quot;1.0.0&quot;</span>
<span class="k">LABEL</span><span class="w"> </span><span class="nv">description</span><span class="o">=</span><span class="s2">&quot;TELUS B2B Order Capture Backend&quot;</span>

<span class="c"># Create application user</span>
<span class="k">RUN</span><span class="w"> </span>groupadd<span class="w"> </span>-r<span class="w"> </span>appuser<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>useradd<span class="w"> </span>-r<span class="w"> </span>-g<span class="w"> </span>appuser<span class="w"> </span>appuser

<span class="c"># Set working directory</span>
<span class="k">WORKDIR</span><span class="w"> </span><span class="s">/app</span>

<span class="c"># Copy application JAR</span>
<span class="k">COPY</span><span class="w"> </span>cloud-bss-oc-ui-backend-b2b-application/target/*.jar<span class="w"> </span>app.jar

<span class="c"># Change ownership</span>
<span class="k">RUN</span><span class="w"> </span>chown<span class="w"> </span>-R<span class="w"> </span>appuser:appuser<span class="w"> </span>/app

<span class="c"># Switch to application user</span>
<span class="k">USER</span><span class="w"> </span><span class="s">appuser</span>

<span class="c"># Expose port</span>
<span class="k">EXPOSE</span><span class="w"> </span><span class="s">8080</span>

<span class="c"># Health check</span>
<span class="k">HEALTHCHECK</span><span class="w"> </span>--interval<span class="o">=</span>30s<span class="w"> </span>--timeout<span class="o">=</span>3s<span class="w"> </span>--start-period<span class="o">=</span>5s<span class="w"> </span>--retries<span class="o">=</span><span class="m">3</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>CMD<span class="w"> </span>curl<span class="w"> </span>-f<span class="w"> </span>http://localhost:8080/actuator/health<span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="nb">exit</span><span class="w"> </span><span class="m">1</span>

<span class="c"># Run application</span>
<span class="k">ENTRYPOINT</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;java&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;-jar&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;app.jar&quot;</span><span class="p">]</span>
</code></pre></div>
<h3 id="build-commands">Build Commands<a class="headerlink" href="#build-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Development build</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w"> </span>-Pdev

<span class="c1"># Production build</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w"> </span>-Pprod

<span class="c1"># Build Docker image</span>
docker<span class="w"> </span>build<span class="w"> </span>-t<span class="w"> </span>telus-b2b-backend:latest<span class="w"> </span>.

<span class="c1"># Build with specific tag</span>
docker<span class="w"> </span>build<span class="w"> </span>-t<span class="w"> </span>telus-b2b-backend:1.0.0<span class="w"> </span>.

<span class="c1"># Multi-stage build for optimization</span>
docker<span class="w"> </span>build<span class="w"> </span>--target<span class="w"> </span>production<span class="w"> </span>-t<span class="w"> </span>telus-b2b-backend:prod<span class="w"> </span>.
</code></pre></div>
<h3 id="deployment-pipeline">Deployment Pipeline<a class="headerlink" href="#deployment-pipeline" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># .github/workflows/deploy.yml</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy Backend</span>
<span class="nt">on</span><span class="p">:</span>
<span class="w">  </span><span class="nt">push</span><span class="p">:</span>
<span class="w">    </span><span class="nt">branches</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">main</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">develop</span><span class="p p-Indicator">]</span>

<span class="nt">jobs</span><span class="p">:</span>
<span class="w">  </span><span class="nt">build-and-deploy</span><span class="p">:</span>
<span class="w">    </span><span class="nt">runs-on</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ubuntu-latest</span>
<span class="w">    </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/checkout@v3</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Set up JDK 17</span>
<span class="w">        </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/setup-java@v3</span>
<span class="w">        </span><span class="nt">with</span><span class="p">:</span>
<span class="w">          </span><span class="nt">java-version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;17&#39;</span>
<span class="w">          </span><span class="nt">distribution</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;temurin&#39;</span>
<span class="w">          </span><span class="nt">cache</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">maven</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Run tests</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mvn clean test</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Build application</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mvn clean package -Pprod</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Build Docker image</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">docker build -t telus-b2b-backend:${{ github.sha }} .</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy to staging</span>
<span class="w">        </span><span class="nt">if</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">github.ref == &#39;refs/heads/develop&#39;</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">          </span><span class="no">kubectl apply -f deployments/staging/</span>
<span class="w">          </span><span class="no">kubectl set image deployment/backend backend=telus-b2b-backend:${{ github.sha }}</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy to production</span>
<span class="w">        </span><span class="nt">if</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">github.ref == &#39;refs/heads/main&#39;</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">          </span><span class="no">kubectl apply -f deployments/production/</span>
<span class="w">          </span><span class="no">kubectl set image deployment/backend backend=telus-b2b-backend:${{ github.sha }}</span>
</code></pre></div>
<h2 id="troubleshooting">Troubleshooting<a class="headerlink" href="#troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="common-issues-and-solutions">Common Issues and Solutions<a class="headerlink" href="#common-issues-and-solutions" title="Permanent link">&para;</a></h3>
<h4 id="build-issues">Build Issues<a class="headerlink" href="#build-issues" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Clear Maven cache</span>
rm<span class="w"> </span>-rf<span class="w"> </span>~/.m2/repository
mvn<span class="w"> </span>clean<span class="w"> </span>install

<span class="c1"># Resolve dependency conflicts</span>
mvn<span class="w"> </span>dependency:tree
mvn<span class="w"> </span>dependency:analyze

<span class="c1"># Update Maven wrapper</span>
mvn<span class="w"> </span>-N<span class="w"> </span>io.takari:maven:wrapper

<span class="c1"># Fix compilation errors</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile<span class="w"> </span>-X<span class="w">  </span><span class="c1"># Debug mode</span>
</code></pre></div>
<h4 id="runtime-issues">Runtime Issues<a class="headerlink" href="#runtime-issues" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Check application logs</span>
docker<span class="w"> </span>logs<span class="w"> </span>&lt;container-id&gt;

<span class="c1"># Check health endpoint</span>
curl<span class="w"> </span>http://localhost:8080/actuator/health

<span class="c1"># Check metrics</span>
curl<span class="w"> </span>http://localhost:8080/actuator/metrics

<span class="c1"># Debug with remote debugging</span>
java<span class="w"> </span>-agentlib:jdwp<span class="o">=</span><span class="nv">transport</span><span class="o">=</span>dt_socket,server<span class="o">=</span>y,suspend<span class="o">=</span>n,address<span class="o">=</span><span class="m">5005</span><span class="w"> </span>-jar<span class="w"> </span>app.jar
</code></pre></div>
<h4 id="performance-issues">Performance Issues<a class="headerlink" href="#performance-issues" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Enable JVM profiling</span>
java<span class="w"> </span>-XX:+FlightRecorder<span class="w"> </span>-XX:StartFlightRecording<span class="o">=</span><span class="nv">duration</span><span class="o">=</span>60s,filename<span class="o">=</span>profile.jfr<span class="w"> </span>-jar<span class="w"> </span>app.jar

<span class="c1"># Memory analysis</span>
java<span class="w"> </span>-XX:+HeapDumpOnOutOfMemoryError<span class="w"> </span>-XX:HeapDumpPath<span class="o">=</span>/tmp<span class="w"> </span>-jar<span class="w"> </span>app.jar

<span class="c1"># GC tuning</span>
java<span class="w"> </span>-XX:+UseG1GC<span class="w"> </span>-XX:MaxGCPauseMillis<span class="o">=</span><span class="m">200</span><span class="w"> </span>-jar<span class="w"> </span>app.jar
</code></pre></div>
<h3 id="development-tools">Development Tools<a class="headerlink" href="#development-tools" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Spring Boot DevTools for hot reload</span>
<span class="c1"># Add to pom.xml:</span>
&lt;dependency&gt;
<span class="w">    </span>&lt;groupId&gt;org.springframework.boot&lt;/groupId&gt;
<span class="w">    </span>&lt;artifactId&gt;spring-boot-devtools&lt;/artifactId&gt;
<span class="w">    </span>&lt;scope&gt;runtime&lt;/scope&gt;
<span class="w">    </span>&lt;optional&gt;true&lt;/optional&gt;
&lt;/dependency&gt;

<span class="c1"># Actuator for monitoring</span>
curl<span class="w"> </span>http://localhost:8080/actuator/beans
curl<span class="w"> </span>http://localhost:8080/actuator/env
curl<span class="w"> </span>http://localhost:8080/actuator/configprops
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="06-testing-guide.md">Testing Guide →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>