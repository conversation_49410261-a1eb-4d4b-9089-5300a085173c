# @telus/mcp-github-issues

An MCP server that provides tools for managing GitHub repository issues.

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npm install @telus/mcp-github-issues
   npx @telus/mcp-github-issues
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for <PERSON> Desktop):

   ```json
   {
     "mcpServers": {
       "github-issues": {
         "command": "npx",
         "args": ["@telus/mcp-github-issues"],
         "env": {
           "GITHUB_TOKEN": "your_github_token_here"
         }
       }
     }
   }
   ```

### For Development

1. Clone the repository and install dependencies:

   ```bash
   <NAME_EMAIL>:telus/mcp.git
   cd mcp-github-issues
   pnpm install
   ```

2. Create a file named `.env` in the project directory and add your GitHub Personal Access Token:

   ```sh
   GITHUB_TOKEN=your_github_token_here
   ```

3. Build the server:

   ```bash
   pnpm build
   ```

4. Start the server:

   ```bash
   pnpm start
   ```

## Features

- List repository issues with filtering options
- Create new issues
- Update existing issues
- Get detailed issue information
- Configurable for any GitHub repository

## Configuration

### GitHub Personal Access Token

This server requires a GitHub Personal Access Token (PAT) for authentication.

To create a new token:

1. Visit [GitHub's Token Settings](https://github.com/settings/tokens?type=beta)
2. Click "Generate new token"
3. Select the following permissions:
   - Repository access: Select repositories (**Don't provide access to all repositories, principle of least privilege!**)
   - Permissions:
     - Issues: Read and Write
     - Metadata: Read-only (automatically included)

## Available Tools

### list_issues

Lists issues for a specified repository.

```typescript
{
  owner: string,      // Repository owner
  repo: string,       // Repository name
  state?: "open" | "closed" | "all",
  labels?: string[],
  sort?: "created" | "updated" | "comments",
  direction?: "asc" | "desc"
}
```

### create_issue

Creates a new issue in the specified repository.

```typescript
{
  owner: string,
  repo: string,
  title: string,
  body: string,
  labels?: string[],
  assignees?: string[]
}
```

### update_issue

Updates an existing issue.

```typescript
{
  owner: string,
  repo: string,
  issue_number: number,
  title?: string,
  body?: string,
  state?: "open" | "closed",
  labels?: string[],
  assignees?: string[]
}
```

### get_issue

Gets detailed information about a specific issue.

```typescript
{
  owner: string,
  repo: string,
  issue_number: number
}
```

## Error Handling

Common error messages and their solutions:

- "GitHub token not configured": Add GITHUB_TOKEN to your .env file or MCP settings
- "Invalid GitHub token": Verify your token is correct and hasn't expired
- "Insufficient permissions": Ensure your token has the required permissions
- "Rate limit exceeded": Wait before making more requests (GitHub API has rate limits)

## Examples

```typescript
// List open issues
{
  "owner": "octocat",
  "repo": "Hello-World",
  "state": "open"
}

// Create a new issue
{
  "owner": "octocat",
  "repo": "Hello-World",
  "title": "Found a bug",
  "body": "Here's the bug description...",
  "labels": ["bug"]
}
```
