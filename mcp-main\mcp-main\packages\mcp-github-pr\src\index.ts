#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import { Octokit } from '@octokit/rest';
import * as dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables from custom path if specified
const envPath = process.env.DOTENV_PATH;
const dotenvConfig = envPath ? { path: envPath } : undefined;

try {
  dotenv.config(dotenvConfig);
} catch (error) {
  if (error instanceof Error) {
    throw new Error(`Failed to load .env file${envPath ? ` from ${envPath}` : ''}: ${error.message}`);
  }
  throw error;
}

const GITHUB_TOKEN = process.env.GITHUB_TOKEN;
if (!GITHUB_TOKEN) {
  throw new Error('GITHUB_TOKEN environment variable is required');
}

const octokit = new Octokit({ 
  auth: GITHUB_TOKEN,
  log: console
});

interface GetPRDiffArgs {
  owner: string;
  repo: string;
  pull_number: number;
}

interface GetPRDescriptionArgs {
  owner: string;
  repo: string;
  pull_number: number;
}

interface CreatePRArgs {
  owner: string;
  repo: string;
  title: string;
  body: string;
  head: string;
  base: string;
}

const isValidGetPRDiffArgs = (args: any): args is GetPRDiffArgs =>
  typeof args === 'object' &&
  args !== null &&
  typeof args.owner === 'string' &&
  typeof args.repo === 'string' &&
  typeof args.pull_number === 'number';

const isValidGetPRDescriptionArgs = (args: any): args is GetPRDescriptionArgs =>
  typeof args === 'object' &&
  args !== null &&
  typeof args.owner === 'string' &&
  typeof args.repo === 'string' &&
  typeof args.pull_number === 'number';

const isValidCreatePRArgs = (args: any): args is CreatePRArgs =>
  typeof args === 'object' &&
  args !== null &&
  typeof args.owner === 'string' &&
  typeof args.repo === 'string' &&
  typeof args.title === 'string' &&
  typeof args.body === 'string' &&
  typeof args.head === 'string' &&
  typeof args.base === 'string';

class GithubPRServer {
  private server: Server;

  constructor() {
    this.server = new Server(
      {
        name: 'github-pr',
        version: '0.1.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    
    // Error handling
    this.server.onerror = (error) => console.error('[MCP Error]', error);
    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'get_pr_diff',
          description: 'Get the diff of a GitHub Pull Request',
          inputSchema: {
            type: 'object',
            properties: {
              owner: {
                type: 'string',
                description: 'Repository owner (username or organization)',
              },
              repo: {
                type: 'string',
                description: 'Repository name',
              },
              pull_number: {
                type: 'number',
                description: 'Pull request number',
              },
            },
            required: ['owner', 'repo', 'pull_number'],
            additionalProperties: false,
          },
        },
        {
          name: 'get_pr_description',
          description: 'Get the description of a GitHub Pull Request',
          inputSchema: {
            type: 'object',
            properties: {
              owner: {
                type: 'string',
                description: 'Repository owner (username or organization)',
              },
              repo: {
                type: 'string',
                description: 'Repository name',
              },
              pull_number: {
                type: 'number',
                description: 'Pull request number',
              },
            },
            required: ['owner', 'repo', 'pull_number'],
            additionalProperties: false,
          },
        },
        {
          name: 'create_pr',
          description: 'Create a new Pull Request in a GitHub repository',
          inputSchema: {
            type: 'object',
            properties: {
              owner: {
                type: 'string',
                description: 'Repository owner (username or organization)',
              },
              repo: {
                type: 'string',
                description: 'Repository name',
              },
              title: {
                type: 'string',
                description: 'Title of the pull request',
              },
              body: {
                type: 'string',
                description: 'Body of the pull request',
              },
              head: {
                type: 'string',
                description: 'The name of the branch where your changes are implemented',
              },
              base: {
                type: 'string',
                description: 'The name of the branch you want the changes pulled into',
              },
            },
            required: ['owner', 'repo', 'title', 'body', 'head', 'base'],
            additionalProperties: false,
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      if (request.params.name === 'get_pr_diff') {
        if (!isValidGetPRDiffArgs(request.params.arguments)) {
          throw new McpError(
            ErrorCode.InvalidParams,
            'Invalid get_pr_diff arguments'
          );
        }

        try {
          const { owner, repo, pull_number } = request.params.arguments;

          console.error('Fetching PR with token:', GITHUB_TOKEN);
          
          // Get the PR details first
          const prResponse = await octokit.rest.pulls.get({
            owner,
            repo,
            pull_number,
            headers: {
              authorization: `token ${GITHUB_TOKEN}`
            }
          });

          // Log successful PR fetch
          console.error('Successfully fetched PR:', prResponse.data.number);

          // Use the compare API to get the diff
          const compareResponse = await octokit.rest.repos.compareCommits({
            owner,
            repo,
            base: prResponse.data.base.sha,
            head: prResponse.data.head.sha
          });

          // Log successful comparison
          console.error('Successfully compared commits');

          // Return PR details and diff information
          return {
            content: [
              {
                type: 'text',
                text: JSON.stringify({
                  pr: {
                    title: prResponse.data.title,
                    state: prResponse.data.state,
                    base: {
                      ref: prResponse.data.base.ref,
                      sha: prResponse.data.base.sha
                    },
                    head: {
                      ref: prResponse.data.head.ref,
                      sha: prResponse.data.head.sha
                    }
                  },
                  diff: compareResponse.data.files?.map(file => ({
                    filename: file.filename,
                    status: file.status,
                    additions: file.additions,
                    deletions: file.deletions,
                    changes: file.changes,
                    patch: file.patch
                  })) || []
                }, null, 2)
              }
            ]
          };
        } catch (apiError) {
          if (apiError instanceof Error) {
            console.error('API Error:', apiError);
            throw new McpError(
              ErrorCode.InternalError,
              `GitHub API error: ${apiError.message}`
            );
          }
          throw apiError;
        }
      } else if (request.params.name === 'get_pr_description') {
        if (!isValidGetPRDescriptionArgs(request.params.arguments)) {
          throw new McpError(
            ErrorCode.InvalidParams,
            'Invalid get_pr_description arguments'
          );
        }

        try {
          const { owner, repo, pull_number } = request.params.arguments;

          console.error('Fetching PR description with token:', GITHUB_TOKEN);
          
          // Get the PR details
          const prResponse = await octokit.rest.pulls.get({
            owner,
            repo,
            pull_number,
            headers: {
              authorization: `token ${GITHUB_TOKEN}`
            }
          });

          // Log successful PR fetch
          console.error('Successfully fetched PR:', prResponse.data.number);

          // Return PR description
          return {
            content: [
              {
                type: 'text',
                text: JSON.stringify({
                  pr: {
                    number: prResponse.data.number,
                    title: prResponse.data.title,
                    description: prResponse.data.body
                  }
                }, null, 2)
              }
            ]
          };
        } catch (apiError) {
          if (apiError instanceof Error) {
            console.error('API Error:', apiError);
            throw new McpError(
              ErrorCode.InternalError,
              `GitHub API error: ${apiError.message}`
            );
          }
          throw apiError;
        }
      } else if (request.params.name === 'create_pr') {
        if (!isValidCreatePRArgs(request.params.arguments)) {
          throw new McpError(
            ErrorCode.InvalidParams,
            'Invalid create_pr arguments'
          );
        }

        try {
          const { owner, repo, title, body, head, base } = request.params.arguments;

          console.error('Creating PR with token:', GITHUB_TOKEN);
          
          // Create the PR
          const prResponse = await octokit.rest.pulls.create({
            owner,
            repo,
            title,
            body,
            head,
            base,
            headers: {
              authorization: `token ${GITHUB_TOKEN}`
            }
          });

          // Log successful PR creation
          console.error('Successfully created PR:', prResponse.data.number);

          // Return PR details
          return {
            content: [
              {
                type: 'text',
                text: JSON.stringify({
                  pr: {
                    number: prResponse.data.number,
                    title: prResponse.data.title,
                    body: prResponse.data.body,
                    html_url: prResponse.data.html_url
                  }
                }, null, 2)
              }
            ]
          };
        } catch (apiError) {
          if (apiError instanceof Error) {
            console.error('API Error:', apiError);
            throw new McpError(
              ErrorCode.InternalError,
              `GitHub API error: ${apiError.message}`
            );
          }
          throw apiError;
        }
      } else {
        throw new McpError(
          ErrorCode.MethodNotFound,
          `Unknown tool: ${request.params.name}`
        );
      }
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.info('GitHub PR MCP server running on stdio');
  }
}

const server = new GithubPRServer();
server.run().catch(console.error);
