
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/DOCUMENTATION_SUMMARY/">
      
      
        <link rel="prev" href="../07-reference/">
      
      
        <link rel="next" href="../SETUP/">
      
      
      <link rel="icon" href="../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Documentation Summary - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#telus-b2b-order-capture-ecosystem-documentation-summary" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Documentation Summary
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href=".." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#documentation-overview" class="md-nav__link">
    <span class="md-ellipsis">
      📚 Documentation Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#whats-included" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 What's Included
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 What&#39;s Included">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#complete-documentation-chapters" class="md-nav__link">
    <span class="md-ellipsis">
      📖 Complete Documentation Chapters
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#professional-styling-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎨 Professional Styling &amp; Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎨 Professional Styling &amp; Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#telus-branding" class="md-nav__link">
    <span class="md-ellipsis">
      TELUS Branding
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#interactive-elements" class="md-nav__link">
    <span class="md-ellipsis">
      Interactive Elements
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#enhanced-functionality" class="md-nav__link">
    <span class="md-ellipsis">
      Enhanced Functionality
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#setup-build-tools" class="md-nav__link">
    <span class="md-ellipsis">
      🛠️ Setup &amp; Build Tools
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🛠️ Setup &amp; Build Tools">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#cross-platform-setup-scripts" class="md-nav__link">
    <span class="md-ellipsis">
      Cross-Platform Setup Scripts
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-files" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Files
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#styling-scripts" class="md-nav__link">
    <span class="md-ellipsis">
      Styling &amp; Scripts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-architecture-covered" class="md-nav__link">
    <span class="md-ellipsis">
      🏗️ System Architecture Covered
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🏗️ System Architecture Covered">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#repository-analysis" class="md-nav__link">
    <span class="md-ellipsis">
      Repository Analysis
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Repository Analysis">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-frontend-nc-cloud-bss-oc-ui-frontend-b2b" class="md-nav__link">
    <span class="md-ellipsis">
      1. Frontend (nc-cloud-bss-oc-ui-frontend-b2b)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-backend-nc-cloud-bss-oc-ui-backend-b2b" class="md-nav__link">
    <span class="md-ellipsis">
      2. Backend (nc-cloud-bss-oc-ui-backend-b2b)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-extensions-nc-cloud-bss-oc-ui-be-extension-b2b" class="md-nav__link">
    <span class="md-ellipsis">
      3. Extensions (nc-cloud-bss-oc-ui-be-extension-b2b)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-points" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Points
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technical-details-documented" class="md-nav__link">
    <span class="md-ellipsis">
      📋 Technical Details Documented
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📋 Technical Details Documented">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-development" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Development
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-development" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Development
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-development" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Development
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-operations" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment &amp; Operations
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-with-bulk-operations" class="md-nav__link">
    <span class="md-ellipsis">
      🔗 Integration with Bulk Operations
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#getting-started" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 Getting Started
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🚀 Getting Started">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quick-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Quick Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#what-you-get" class="md-nav__link">
    <span class="md-ellipsis">
      What You Get
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#documentation-statistics" class="md-nav__link">
    <span class="md-ellipsis">
      📊 Documentation Statistics
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#target-audiences" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Target Audiences
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Target Audiences">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#for-new-developers" class="md-nav__link">
    <span class="md-ellipsis">
      For New Developers
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#for-devops-teams" class="md-nav__link">
    <span class="md-ellipsis">
      For DevOps Teams
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#for-integration-teams" class="md-nav__link">
    <span class="md-ellipsis">
      For Integration Teams
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#for-architects" class="md-nav__link">
    <span class="md-ellipsis">
      For Architects
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#support-maintenance" class="md-nav__link">
    <span class="md-ellipsis">
      📞 Support &amp; Maintenance
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#success-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      🎉 Success Metrics
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#documentation-overview" class="md-nav__link">
    <span class="md-ellipsis">
      📚 Documentation Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#whats-included" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 What's Included
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 What&#39;s Included">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#complete-documentation-chapters" class="md-nav__link">
    <span class="md-ellipsis">
      📖 Complete Documentation Chapters
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#professional-styling-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎨 Professional Styling &amp; Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎨 Professional Styling &amp; Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#telus-branding" class="md-nav__link">
    <span class="md-ellipsis">
      TELUS Branding
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#interactive-elements" class="md-nav__link">
    <span class="md-ellipsis">
      Interactive Elements
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#enhanced-functionality" class="md-nav__link">
    <span class="md-ellipsis">
      Enhanced Functionality
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#setup-build-tools" class="md-nav__link">
    <span class="md-ellipsis">
      🛠️ Setup &amp; Build Tools
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🛠️ Setup &amp; Build Tools">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#cross-platform-setup-scripts" class="md-nav__link">
    <span class="md-ellipsis">
      Cross-Platform Setup Scripts
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-files" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Files
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#styling-scripts" class="md-nav__link">
    <span class="md-ellipsis">
      Styling &amp; Scripts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-architecture-covered" class="md-nav__link">
    <span class="md-ellipsis">
      🏗️ System Architecture Covered
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🏗️ System Architecture Covered">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#repository-analysis" class="md-nav__link">
    <span class="md-ellipsis">
      Repository Analysis
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Repository Analysis">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-frontend-nc-cloud-bss-oc-ui-frontend-b2b" class="md-nav__link">
    <span class="md-ellipsis">
      1. Frontend (nc-cloud-bss-oc-ui-frontend-b2b)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-backend-nc-cloud-bss-oc-ui-backend-b2b" class="md-nav__link">
    <span class="md-ellipsis">
      2. Backend (nc-cloud-bss-oc-ui-backend-b2b)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-extensions-nc-cloud-bss-oc-ui-be-extension-b2b" class="md-nav__link">
    <span class="md-ellipsis">
      3. Extensions (nc-cloud-bss-oc-ui-be-extension-b2b)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-points" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Points
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technical-details-documented" class="md-nav__link">
    <span class="md-ellipsis">
      📋 Technical Details Documented
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📋 Technical Details Documented">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-development" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Development
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-development" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Development
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-development" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Development
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-operations" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment &amp; Operations
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-with-bulk-operations" class="md-nav__link">
    <span class="md-ellipsis">
      🔗 Integration with Bulk Operations
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#getting-started" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 Getting Started
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🚀 Getting Started">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quick-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Quick Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#what-you-get" class="md-nav__link">
    <span class="md-ellipsis">
      What You Get
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#documentation-statistics" class="md-nav__link">
    <span class="md-ellipsis">
      📊 Documentation Statistics
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#target-audiences" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Target Audiences
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Target Audiences">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#for-new-developers" class="md-nav__link">
    <span class="md-ellipsis">
      For New Developers
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#for-devops-teams" class="md-nav__link">
    <span class="md-ellipsis">
      For DevOps Teams
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#for-integration-teams" class="md-nav__link">
    <span class="md-ellipsis">
      For Integration Teams
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#for-architects" class="md-nav__link">
    <span class="md-ellipsis">
      For Architects
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#support-maintenance" class="md-nav__link">
    <span class="md-ellipsis">
      📞 Support &amp; Maintenance
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#success-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      🎉 Success Metrics
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="telus-b2b-order-capture-ecosystem-documentation-summary">TELUS B2B Order Capture Ecosystem - Documentation Summary<a class="headerlink" href="#telus-b2b-order-capture-ecosystem-documentation-summary" title="Permanent link">&para;</a></h1>
<h2 id="documentation-overview">📚 Documentation Overview<a class="headerlink" href="#documentation-overview" title="Permanent link">&para;</a></h2>
<p>This comprehensive documentation suite covers the complete TELUS B2B Order Capture ecosystem, consisting of three interconnected repositories that form an enterprise-grade B2B order capture and quote generation solution.</p>
<h2 id="whats-included">🎯 What's Included<a class="headerlink" href="#whats-included" title="Permanent link">&para;</a></h2>
<h3 id="complete-documentation-chapters">📖 Complete Documentation Chapters<a class="headerlink" href="#complete-documentation-chapters" title="Permanent link">&para;</a></h3>
<ol>
<li><strong><a href="../">README.md</a></strong> - Main documentation index and navigation</li>
<li><strong><a href="../00-overview/">00-overview.md</a></strong> - System overview and architecture</li>
<li><strong><a href="../01-frontend-guide/">01-frontend-guide.md</a></strong> - Angular frontend development guide</li>
<li><strong><a href="../02-backend-guide/">02-backend-guide.md</a></strong> - Spring Boot backend development guide</li>
<li><strong><a href="../03-extensions-guide/">03-extensions-guide.md</a></strong> - QES plugins and extensions guide</li>
<li><strong><a href="../04-integration-guide/">04-integration-guide.md</a></strong> - System integration patterns</li>
<li><strong><a href="../05-development-guide/">05-development-guide.md</a></strong> - Development environment and workflows</li>
<li><strong><a href="../06-deployment-guide/">06-deployment-guide.md</a></strong> - Kubernetes deployment and CI/CD</li>
<li><strong><a href="../07-reference/">07-reference.md</a></strong> - Complete API and configuration reference</li>
</ol>
<h3 id="professional-styling-features">🎨 Professional Styling &amp; Features<a class="headerlink" href="#professional-styling-features" title="Permanent link">&para;</a></h3>
<h4 id="telus-branding">TELUS Branding<a class="headerlink" href="#telus-branding" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Custom Theme</strong>: TELUS purple and green color scheme</li>
<li><strong>Component Badges</strong>: Automatic Frontend/Backend/Extension tagging</li>
<li><strong>Professional Layout</strong>: Consistent with TELUS brand guidelines</li>
<li><strong>Responsive Design</strong>: Mobile-friendly and accessible</li>
</ul>
<h4 id="interactive-elements">Interactive Elements<a class="headerlink" href="#interactive-elements" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Mermaid Diagrams</strong>: Interactive architecture and flow diagrams</li>
<li><strong>Code Copy Buttons</strong>: One-click code copying functionality</li>
<li><strong>Search</strong>: Full-text search across all documentation</li>
<li><strong>Navigation</strong>: Breadcrumbs, previous/next links, table of contents</li>
<li><strong>Print Support</strong>: Optimized for printing with proper page breaks</li>
</ul>
<h4 id="enhanced-functionality">Enhanced Functionality<a class="headerlink" href="#enhanced-functionality" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Status Indicators</strong>: Visual indicators for different states</li>
<li><strong>API Documentation</strong>: Enhanced REST API endpoint documentation</li>
<li><strong>Zoom Controls</strong>: For complex diagrams</li>
<li><strong>Export Options</strong>: Diagram export functionality</li>
</ul>
<h3 id="setup-build-tools">🛠️ Setup &amp; Build Tools<a class="headerlink" href="#setup-build-tools" title="Permanent link">&para;</a></h3>
<h4 id="cross-platform-setup-scripts">Cross-Platform Setup Scripts<a class="headerlink" href="#cross-platform-setup-scripts" title="Permanent link">&para;</a></h4>
<ol>
<li><strong><a href="setup-docs.ps1">setup-docs.ps1</a></strong> - PowerShell script for Windows</li>
<li><strong><a href="setup-docs.bat">setup-docs.bat</a></strong> - Batch script for Windows Command Prompt</li>
<li><strong><a href="setup-docs.sh">setup-docs.sh</a></strong> - Bash script for Linux/macOS</li>
</ol>
<h4 id="configuration-files">Configuration Files<a class="headerlink" href="#configuration-files" title="Permanent link">&para;</a></h4>
<ul>
<li><strong><a href="mkdocs.yml">mkdocs.yml</a></strong> - MkDocs configuration with Material theme</li>
<li><strong><a href="requirements.txt">requirements.txt</a></strong> - Python dependencies</li>
<li><strong><a href="../SETUP/">SETUP.md</a></strong> - Detailed setup instructions</li>
</ul>
<h4 id="styling-scripts">Styling &amp; Scripts<a class="headerlink" href="#styling-scripts" title="Permanent link">&para;</a></h4>
<ul>
<li><strong><a href="stylesheets/telus-theme.css">stylesheets/telus-theme.css</a></strong> - TELUS brand styling</li>
<li><strong><a href="stylesheets/extra.css">stylesheets/extra.css</a></strong> - Additional enhancements</li>
<li><strong><a href="javascripts/extra.js">javascripts/extra.js</a></strong> - Interactive functionality</li>
<li><strong><a href="javascripts/mermaid.js">javascripts/mermaid.js</a></strong> - Diagram configuration</li>
</ul>
<h2 id="system-architecture-covered">🏗️ System Architecture Covered<a class="headerlink" href="#system-architecture-covered" title="Permanent link">&para;</a></h2>
<h3 id="repository-analysis">Repository Analysis<a class="headerlink" href="#repository-analysis" title="Permanent link">&para;</a></h3>
<p>The documentation covers three main repositories:</p>
<h4 id="1-frontend-nc-cloud-bss-oc-ui-frontend-b2b">1. Frontend (<code>nc-cloud-bss-oc-ui-frontend-b2b</code>)<a class="headerlink" href="#1-frontend-nc-cloud-bss-oc-ui-frontend-b2b" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Technology</strong>: Angular 15.2.9, TypeScript, NgRx</li>
<li><strong>Components</strong>: 18+ custom TELUS components</li>
<li><strong>Features</strong>: Order forms, cart management, service configuration</li>
<li><strong>State Management</strong>: NgRx store with real-time updates</li>
</ul>
<h4 id="2-backend-nc-cloud-bss-oc-ui-backend-b2b">2. Backend (<code>nc-cloud-bss-oc-ui-backend-b2b</code>)<a class="headerlink" href="#2-backend-nc-cloud-bss-oc-ui-backend-b2b" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Technology</strong>: Spring Boot 3.1.12, Java 17</li>
<li><strong>Architecture</strong>: Multi-module Maven project</li>
<li><strong>APIs</strong>: RESTful services for quote and order management</li>
<li><strong>Integration</strong>: Order Capture Product, TELUS BSS systems</li>
</ul>
<h4 id="3-extensions-nc-cloud-bss-oc-ui-be-extension-b2b">3. Extensions (<code>nc-cloud-bss-oc-ui-be-extension-b2b</code>)<a class="headerlink" href="#3-extensions-nc-cloud-bss-oc-ui-be-extension-b2b" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Technology</strong>: QES Plugin Framework</li>
<li><strong>Purpose</strong>: Business rules and validation logic</li>
<li><strong>Components</strong>: Bandwidth validators, quote modificators</li>
<li><strong>Features</strong>: Custom TELUS business logic implementation</li>
</ul>
<h3 id="integration-points">Integration Points<a class="headerlink" href="#integration-points" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Frontend ↔ Backend</strong>: REST API communication</li>
<li><strong>Backend ↔ Extensions</strong>: Plugin interface integration</li>
<li><strong>External Systems</strong>: Order Capture Product, BSS, Geocoding services</li>
<li><strong>Bulk Operations</strong>: Integration with bulk address processing</li>
</ul>
<h2 id="technical-details-documented">📋 Technical Details Documented<a class="headerlink" href="#technical-details-documented" title="Permanent link">&para;</a></h2>
<h3 id="frontend-development">Frontend Development<a class="headerlink" href="#frontend-development" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Component Inventory</strong>: Complete list of 18+ custom TELUS components</li>
<li><strong>State Management</strong>: NgRx store architecture and patterns</li>
<li><strong>Routing</strong>: Custom route configuration and navigation</li>
<li><strong>Build Process</strong>: Angular CLI configuration and optimization</li>
<li><strong>Testing</strong>: Unit testing and E2E testing strategies</li>
</ul>
<h3 id="backend-development">Backend Development<a class="headerlink" href="#backend-development" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>API Documentation</strong>: Complete REST API reference</li>
<li><strong>Service Architecture</strong>: Business logic and data processing</li>
<li><strong>Configuration</strong>: Environment-specific settings</li>
<li><strong>Integration Patterns</strong>: External system communication</li>
<li><strong>Error Handling</strong>: Comprehensive error management</li>
</ul>
<h3 id="extensions-development">Extensions Development<a class="headerlink" href="#extensions-development" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Plugin Architecture</strong>: QES plugin development patterns</li>
<li><strong>Bandwidth Validators</strong>: Network service validation logic</li>
<li><strong>Quote Modificators</strong>: Business rule implementation</li>
<li><strong>State Machine</strong>: Workflow validation and transitions</li>
<li><strong>Utility Classes</strong>: Reusable business logic components</li>
</ul>
<h3 id="deployment-operations">Deployment &amp; Operations<a class="headerlink" href="#deployment-operations" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Containerization</strong>: Docker configurations for all components</li>
<li><strong>Kubernetes</strong>: Complete deployment manifests and Helm charts</li>
<li><strong>CI/CD</strong>: GitLab CI pipeline configuration</li>
<li><strong>Environment Management</strong>: Development, staging, production configs</li>
<li><strong>Monitoring</strong>: Health checks and performance metrics</li>
</ul>
<h2 id="integration-with-bulk-operations">🔗 Integration with Bulk Operations<a class="headerlink" href="#integration-with-bulk-operations" title="Permanent link">&para;</a></h2>
<p>The documentation includes detailed coverage of how the B2B Order Capture system integrates with the TELUS Bulk Operation Extension:</p>
<ul>
<li><strong>Address Validation</strong>: Bulk address processing integration</li>
<li><strong>Data Flow</strong>: How bulk operations fit into the order processing workflow</li>
<li><strong>API Integration</strong>: Callback handling and event-driven updates</li>
<li><strong>Performance</strong>: Optimization for large-scale operations</li>
</ul>
<h2 id="getting-started">🚀 Getting Started<a class="headerlink" href="#getting-started" title="Permanent link">&para;</a></h2>
<h3 id="quick-setup">Quick Setup<a class="headerlink" href="#quick-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Navigate to documentation directory</span>
<span class="nb">cd</span><span class="w"> </span>telus-b2b-order-capture-docs

<span class="c1"># Windows PowerShell</span>
.<span class="se">\s</span>etup-docs.ps1

<span class="c1"># Windows Command Prompt</span>
setup-docs.bat

<span class="c1"># Linux/macOS</span>
./setup-docs.sh
</code></pre></div>
<h3 id="what-you-get">What You Get<a class="headerlink" href="#what-you-get" title="Permanent link">&para;</a></h3>
<p>After running the setup, you'll have:
- ✅ Professional HTML documentation
- ✅ Interactive diagrams and code examples
- ✅ Full-text search functionality
- ✅ Mobile-responsive design
- ✅ TELUS brand styling
- ✅ Easy navigation and cross-references</p>
<h2 id="documentation-statistics">📊 Documentation Statistics<a class="headerlink" href="#documentation-statistics" title="Permanent link">&para;</a></h2>
<ul>
<li><strong>Total Pages</strong>: 9 comprehensive chapters</li>
<li><strong>Code Examples</strong>: 100+ code snippets and configurations</li>
<li><strong>Diagrams</strong>: 10+ Mermaid architecture diagrams</li>
<li><strong>API Endpoints</strong>: Complete REST API documentation</li>
<li><strong>Components</strong>: 18+ frontend components documented</li>
<li><strong>Plugins</strong>: 6+ QES extension plugins covered</li>
<li><strong>Configuration</strong>: Environment-specific settings for all components</li>
</ul>
<h2 id="target-audiences">🎯 Target Audiences<a class="headerlink" href="#target-audiences" title="Permanent link">&para;</a></h2>
<h3 id="for-new-developers">For New Developers<a class="headerlink" href="#for-new-developers" title="Permanent link">&para;</a></h3>
<ul>
<li>Complete onboarding guide</li>
<li>Development environment setup</li>
<li>Code examples and patterns</li>
<li>Testing strategies</li>
</ul>
<h3 id="for-devops-teams">For DevOps Teams<a class="headerlink" href="#for-devops-teams" title="Permanent link">&para;</a></h3>
<ul>
<li>Deployment configurations</li>
<li>Kubernetes manifests</li>
<li>CI/CD pipeline setup</li>
<li>Monitoring and troubleshooting</li>
</ul>
<h3 id="for-integration-teams">For Integration Teams<a class="headerlink" href="#for-integration-teams" title="Permanent link">&para;</a></h3>
<ul>
<li>API documentation</li>
<li>Integration patterns</li>
<li>External system connections</li>
<li>Data flow diagrams</li>
</ul>
<h3 id="for-architects">For Architects<a class="headerlink" href="#for-architects" title="Permanent link">&para;</a></h3>
<ul>
<li>System architecture overview</li>
<li>Component relationships</li>
<li>Technology stack details</li>
<li>Scalability considerations</li>
</ul>
<h2 id="support-maintenance">📞 Support &amp; Maintenance<a class="headerlink" href="#support-maintenance" title="Permanent link">&para;</a></h2>
<p>The documentation is designed to be:
- <strong>Self-Contained</strong>: All necessary information included
- <strong>Maintainable</strong>: Easy to update as the system evolves
- <strong>Searchable</strong>: Full-text search across all content
- <strong>Accessible</strong>: WCAG 2.1 AA compliant
- <strong>Professional</strong>: Enterprise-grade presentation</p>
<h2 id="success-metrics">🎉 Success Metrics<a class="headerlink" href="#success-metrics" title="Permanent link">&para;</a></h2>
<p>This documentation provides:
- <strong>Comprehensive Coverage</strong>: Every aspect of the ecosystem documented
- <strong>Professional Presentation</strong>: TELUS-branded, enterprise-quality documentation
- <strong>Developer Productivity</strong>: Faster onboarding and development
- <strong>Operational Excellence</strong>: Clear deployment and troubleshooting guides
- <strong>Integration Success</strong>: Detailed integration patterns and examples</p>
<p>The TELUS B2B Order Capture ecosystem documentation is now complete and ready to support development, deployment, and operational activities across the entire system lifecycle.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "..", "features": [], "search": "../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>