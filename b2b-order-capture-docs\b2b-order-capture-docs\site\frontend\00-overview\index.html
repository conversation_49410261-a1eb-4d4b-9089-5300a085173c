
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/frontend/00-overview/">
      
      
        <link rel="prev" href="../../00-overview/">
      
      
        <link rel="next" href="../01-architecture/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Overview - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#frontend-application-overview" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Overview
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#introduction" class="md-nav__link">
    <span class="md-ellipsis">
      Introduction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Introduction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#purpose-and-scope" class="md-nav__link">
    <span class="md-ellipsis">
      Purpose and Scope
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#high-level-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      High-Level Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Component Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Core Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#ui-and-design-libraries" class="md-nav__link">
    <span class="md-ellipsis">
      UI and Design Libraries
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-and-build-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Development and Build Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Business Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Business Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-management" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#order-processing" class="md-nav__link">
    <span class="md-ellipsis">
      Order Processing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#location-services" class="md-nav__link">
    <span class="md-ellipsis">
      Location Services
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technical-features" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Technical Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Technical Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#state-management" class="md-nav__link">
    <span class="md-ellipsis">
      State Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Optimization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#accessibility" class="md-nav__link">
    <span class="md-ellipsis">
      Accessibility
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#source-code-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Source Code Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#module-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Module Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#available-scripts" class="md-nav__link">
    <span class="md-ellipsis">
      Available Scripts
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-urls" class="md-nav__link">
    <span class="md-ellipsis">
      Development URLs
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#introduction" class="md-nav__link">
    <span class="md-ellipsis">
      Introduction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Introduction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#purpose-and-scope" class="md-nav__link">
    <span class="md-ellipsis">
      Purpose and Scope
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#high-level-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      High-Level Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Component Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Core Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#ui-and-design-libraries" class="md-nav__link">
    <span class="md-ellipsis">
      UI and Design Libraries
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-and-build-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Development and Build Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Business Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Business Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-management" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#order-processing" class="md-nav__link">
    <span class="md-ellipsis">
      Order Processing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#location-services" class="md-nav__link">
    <span class="md-ellipsis">
      Location Services
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technical-features" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Technical Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Technical Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#state-management" class="md-nav__link">
    <span class="md-ellipsis">
      State Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Optimization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#accessibility" class="md-nav__link">
    <span class="md-ellipsis">
      Accessibility
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#source-code-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Source Code Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#module-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Module Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#available-scripts" class="md-nav__link">
    <span class="md-ellipsis">
      Available Scripts
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-urls" class="md-nav__link">
    <span class="md-ellipsis">
      Development URLs
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="frontend-application-overview">Frontend Application Overview<a class="headerlink" href="#frontend-application-overview" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#introduction">Introduction</a></li>
<li><a href="#architecture-overview">Architecture Overview</a></li>
<li><a href="#technology-stack">Technology Stack</a></li>
<li><a href="#key-features">Key Features</a></li>
<li><a href="#project-structure">Project Structure</a></li>
<li><a href="#development-environment">Development Environment</a></li>
</ul>
<h2 id="introduction">Introduction<a class="headerlink" href="#introduction" title="Permanent link">&para;</a></h2>
<p>The <strong>nc-cloud-bss-oc-ui-frontend-b2b</strong> is a sophisticated Angular 15.2.9 application that serves as the primary user interface for the TELUS B2B Order Capture ecosystem. This enterprise-grade frontend application provides a modern, responsive, and accessible interface for B2B customers to create quotes, manage orders, and interact with TELUS services.</p>
<h3 id="purpose-and-scope">Purpose and Scope<a class="headerlink" href="#purpose-and-scope" title="Permanent link">&para;</a></h3>
<p>The frontend application is designed to:
- <strong>Streamline B2B Order Processes</strong>: Simplify complex telecommunications service ordering
- <strong>Provide Real-time Interactions</strong>: Live quote calculations and service validation
- <strong>Ensure Accessibility</strong>: WCAG 2.1 AA compliance for inclusive user experience
- <strong>Maintain Brand Consistency</strong>: TELUS design system integration
- <strong>Support Multiple Workflows</strong>: Quote generation, order management, service configuration</p>
<h2 id="architecture-overview">Architecture Overview<a class="headerlink" href="#architecture-overview" title="Permanent link">&para;</a></h2>
<h3 id="high-level-architecture">High-Level Architecture<a class="headerlink" href="#high-level-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Frontend Application&quot;
        A[Angular App] --&gt; B[Custom TELUS Components]
        A --&gt; C[NgRx State Management]
        A --&gt; D[Routing &amp; Navigation]
        A --&gt; E[HTTP Interceptors]

        B --&gt; F[Quote Components]
        B --&gt; G[Location Components]
        B --&gt; H[Cart Components]
        B --&gt; I[Service Components]

        C --&gt; J[Quote State]
        C --&gt; K[Cart State]
        C --&gt; L[User State]
        C --&gt; M[Service State]
    end

    subgraph &quot;External Dependencies&quot;
        N[Backend APIs]
        O[Google Maps API]
        P[TELUS Location Services]
        Q[NetCracker UX Library]
    end

    A --&gt; N
    B --&gt; O
    B --&gt; P
    A --&gt; Q

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style C fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="component-architecture">Component Architecture<a class="headerlink" href="#component-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;Application Layer&quot;
        A[App Component]
        B[App Module]
        C[App Routing]
    end

    subgraph &quot;Feature Modules&quot;
        D[Quotes Module]
        E[Orders Module]
        F[Services Module]
        G[Custom Module]
    end

    subgraph &quot;Shared Services&quot;
        H[HTTP Interceptors]
        I[Auth Guards]
        J[State Services]
        K[Utility Services]
    end

    subgraph &quot;Custom Components&quot;
        L[telus-start-quotation]
        M[telus-assign-location]
        N[telus-cart-total-prices]
        O[telus-quote-view]
    end

    A --&gt; D
    A --&gt; E
    A --&gt; F
    A --&gt; G

    D --&gt; L
    E --&gt; M
    F --&gt; N
    G --&gt; O

    B --&gt; H
    B --&gt; I
    B --&gt; J
    B --&gt; K

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style G fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
</code></pre></div>
<h2 id="technology-stack">Technology Stack<a class="headerlink" href="#technology-stack" title="Permanent link">&para;</a></h2>
<h3 id="core-technologies">Core Technologies<a class="headerlink" href="#core-technologies" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Technology</th>
<th>Version</th>
<th>Purpose</th>
<th>Key Features</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Angular</strong></td>
<td>15.2.9</td>
<td>Frontend Framework</td>
<td>Component-based architecture, dependency injection, reactive forms</td>
</tr>
<tr>
<td><strong>TypeScript</strong></td>
<td>4.9.5</td>
<td>Programming Language</td>
<td>Type safety, modern JavaScript features, enhanced IDE support</td>
</tr>
<tr>
<td><strong>NgRx</strong></td>
<td>15.4.0</td>
<td>State Management</td>
<td>Centralized state, predictable state updates, time-travel debugging</td>
</tr>
<tr>
<td><strong>LESS</strong></td>
<td>3.13.1</td>
<td>CSS Preprocessor</td>
<td>Variables, mixins, nested rules, TELUS theme integration</td>
</tr>
<tr>
<td><strong>Angular CLI</strong></td>
<td>15.2.8</td>
<td>Build Tool</td>
<td>Development server, build optimization, code generation</td>
</tr>
</tbody>
</table>
<h3 id="ui-and-design-libraries">UI and Design Libraries<a class="headerlink" href="#ui-and-design-libraries" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Library</th>
<th>Version</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>@netcracker/ux-ng2</strong></td>
<td>2023.3.9</td>
<td>UI Component Library</td>
</tr>
<tr>
<td><strong>Angular Material</strong></td>
<td>15.x</td>
<td>Material Design Components</td>
</tr>
<tr>
<td><strong>TELUS Design System</strong></td>
<td>Custom</td>
<td>Brand-specific styling</td>
</tr>
</tbody>
</table>
<h3 id="development-and-build-tools">Development and Build Tools<a class="headerlink" href="#development-and-build-tools" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;buildTool&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Angular CLI 15.2.8&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;bundler&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Webpack (via Angular CLI)&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;packageManager&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;npm&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;testingFramework&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Jasmine + Karma&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;linting&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ESLint + TSLint&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;styleProcessor&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;LESS 3.13.1&quot;</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="key-features">Key Features<a class="headerlink" href="#key-features" title="Permanent link">&para;</a></h2>
<h3 id="business-features">🎯 <strong>Business Features</strong><a class="headerlink" href="#business-features" title="Permanent link">&para;</a></h3>
<h4 id="quote-management">Quote Management<a class="headerlink" href="#quote-management" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Real-time Quote Generation</strong>: Dynamic pricing calculations</li>
<li><strong>Service Selection</strong>: Comprehensive TELUS service catalog</li>
<li><strong>Quote Comparison</strong>: Side-by-side quote analysis</li>
<li><strong>Quote History</strong>: Complete audit trail</li>
</ul>
<h4 id="order-processing">Order Processing<a class="headerlink" href="#order-processing" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Order Creation</strong>: Streamlined order initiation</li>
<li><strong>Order Tracking</strong>: Real-time status updates</li>
<li><strong>Order Modification</strong>: Change management workflows</li>
<li><strong>Approval Workflows</strong>: Multi-level approval processes</li>
</ul>
<h4 id="location-services">Location Services<a class="headerlink" href="#location-services" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Address Validation</strong>: Google Maps integration</li>
<li><strong>Service Availability</strong>: Location-based service checks</li>
<li><strong>Geocoding</strong>: Precise location mapping</li>
<li><strong>Multi-location Support</strong>: Enterprise location management</li>
</ul>
<h3 id="technical-features">🔧 <strong>Technical Features</strong><a class="headerlink" href="#technical-features" title="Permanent link">&para;</a></h3>
<h4 id="state-management">State Management<a class="headerlink" href="#state-management" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Centralized Store</strong>: NgRx-powered state management</li>
<li><strong>Reactive Updates</strong>: Real-time UI synchronization</li>
<li><strong>Optimistic Updates</strong>: Enhanced user experience</li>
<li><strong>State Persistence</strong>: Session and local storage</li>
</ul>
<h4 id="performance-optimization">Performance Optimization<a class="headerlink" href="#performance-optimization" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Lazy Loading</strong>: Module-based code splitting</li>
<li><strong>OnPush Strategy</strong>: Optimized change detection</li>
<li><strong>Virtual Scrolling</strong>: Large dataset handling</li>
<li><strong>Caching</strong>: HTTP response caching</li>
</ul>
<h4 id="accessibility">Accessibility<a class="headerlink" href="#accessibility" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>WCAG 2.1 AA Compliance</strong>: Full accessibility support</li>
<li><strong>Keyboard Navigation</strong>: Complete keyboard accessibility</li>
<li><strong>Screen Reader Support</strong>: ARIA labels and descriptions</li>
<li><strong>High Contrast Mode</strong>: Visual accessibility features</li>
</ul>
<h2 id="project-structure">Project Structure<a class="headerlink" href="#project-structure" title="Permanent link">&para;</a></h2>
<h3 id="source-code-organization">Source Code Organization<a class="headerlink" href="#source-code-organization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>src/
├── app/
│   ├── custom/                    # Custom TELUS components (18+ components)
│   │   ├── telus-assign-location/
│   │   ├── telus-cart-total-prices/
│   │   ├── telus-quote-view/
│   │   ├── telus-start-quotation/
│   │   ├── telus-service-selector/
│   │   ├── telus-location-picker/
│   │   ├── telus-pricing-calculator/
│   │   └── [11 more components...]
│   ├── modules/                   # Feature modules
│   │   ├── quotes/
│   │   ├── orders/
│   │   ├── services/
│   │   └── shared/
│   ├── store/                     # NgRx state management
│   │   ├── actions/
│   │   ├── reducers/
│   │   ├── effects/
│   │   └── selectors/
│   ├── interceptors/              # HTTP interceptors
│   │   ├── auth.interceptor.ts
│   │   ├── error.interceptor.ts
│   │   └── loading.interceptor.ts
│   ├── guards/                    # Route guards
│   │   ├── auth.guard.ts
│   │   └── role.guard.ts
│   ├── services/                  # Application services
│   │   ├── quote.service.ts
│   │   ├── order.service.ts
│   │   └── location.service.ts
│   ├── models/                    # TypeScript interfaces
│   │   ├── quote.model.ts
│   │   ├── order.model.ts
│   │   └── service.model.ts
│   ├── environments/              # Environment configurations
│   │   ├── environment.ts
│   │   ├── environment.prod.ts
│   │   └── environment.dev.ts
│   ├── configuration/             # App configuration
│   │   ├── app.config.ts
│   │   └── api.config.ts
│   ├── app.component.ts           # Root component
│   ├── app.module.ts              # Root module
│   └── app-routing.module.ts      # Main routing configuration
├── assets/                        # Static assets
│   ├── images/
│   ├── icons/
│   ├── styles/
│   └── i18n/
├── styles/                        # Global styles
│   ├── telus-theme.less
│   ├── variables.less
│   └── mixins.less
├── environments/                  # Build environments
├── main.ts                        # Application bootstrap
├── polyfills.ts                   # Browser compatibility
└── index.html                     # Main HTML template
</code></pre></div>
<h3 id="module-architecture">Module Architecture<a class="headerlink" href="#module-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Core Modules&quot;
        A[App Module]
        B[Shared Module]
        C[Core Module]
    end

    subgraph &quot;Feature Modules&quot;
        D[Quotes Module]
        E[Orders Module]
        F[Services Module]
        G[Custom Components Module]
    end

    subgraph &quot;Lazy Loaded Modules&quot;
        H[Admin Module]
        I[Reports Module]
        J[Settings Module]
    end

    A --&gt; B
    A --&gt; C
    A --&gt; D
    A --&gt; E
    A --&gt; F
    A --&gt; G

    D -.-&gt; H
    E -.-&gt; I
    F -.-&gt; J

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style G fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
</code></pre></div>
<h2 id="development-environment">Development Environment<a class="headerlink" href="#development-environment" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Node.js and npm</span>
node<span class="w"> </span>--version<span class="w">  </span><span class="c1"># v18.x or higher</span>
npm<span class="w"> </span>--version<span class="w">   </span><span class="c1"># v9.x or higher</span>

<span class="c1"># Angular CLI</span>
npm<span class="w"> </span>install<span class="w"> </span>-g<span class="w"> </span>@angular/cli@15.2.8

<span class="c1"># Git</span>
git<span class="w"> </span>--version<span class="w">  </span><span class="c1"># v2.x or higher</span>
</code></pre></div>
<h3 id="environment-setup">Environment Setup<a class="headerlink" href="#environment-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Clone repository</span>
git<span class="w"> </span>clone<span class="w"> </span>&lt;repository-url&gt;
<span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-frontend-b2b

<span class="c1"># Install dependencies</span>
npm<span class="w"> </span>install

<span class="c1"># Start development server</span>
npm<span class="w"> </span>run<span class="w"> </span>serve:dev

<span class="c1"># Build for production</span>
npm<span class="w"> </span>run<span class="w"> </span>build
</code></pre></div>
<h3 id="available-scripts">Available Scripts<a class="headerlink" href="#available-scripts" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;serve:dev&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ng serve --port=4200 --configuration development&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;serve:proxy&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ng serve --proxy-config demo/proxy.conf.js&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;build&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ng build --configuration production&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;build:dev&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ng build --configuration development&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;test&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ng test&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;test:coverage&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ng test --code-coverage&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;lint&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ng lint&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;e2e&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ng e2e&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;mock:server&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;tsc-watch --watch ./demo/server/server.ts&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;styles:watch&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;gulp styles:watch&quot;</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="development-urls">Development URLs<a class="headerlink" href="#development-urls" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Environment</th>
<th>URL</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Development</strong></td>
<td>http://localhost:4200</td>
<td>Local development</td>
</tr>
<tr>
<td><strong>Mock Server</strong></td>
<td>http://localhost:3000</td>
<td>Backend simulation</td>
</tr>
<tr>
<td><strong>Proxy Mode</strong></td>
<td>http://localhost:4200</td>
<td>API proxy configuration</td>
</tr>
</tbody>
</table>
<hr />
<p><strong>Next</strong>: <a href="../01-architecture/">Architecture Details →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>