
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/frontend/05-development-guide/">
      
      
        <link rel="prev" href="../04-api-integration/">
      
      
        <link rel="next" href="../../backend/00-overview/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Development Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#frontend-development-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Development Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#initial-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Initial Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#vs-code-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      VS Code Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#recommended-vs-code-extensions" class="md-nav__link">
    <span class="md-ellipsis">
      Recommended VS Code Extensions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#directory-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Directory Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#module-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Module Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Development Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#daily-development-process" class="md-nav__link">
    <span class="md-ellipsis">
      Daily Development Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#git-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Git Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#code-generation" class="md-nav__link">
    <span class="md-ellipsis">
      Code Generation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#custom-component-development" class="md-nav__link">
    <span class="md-ellipsis">
      Custom Component Development
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategy
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Strategy">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Service Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#e2e-testing" class="md-nav__link">
    <span class="md-ellipsis">
      E2E Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Test Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#build-and-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Build and Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Build and Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#build-configurations" class="md-nav__link">
    <span class="md-ellipsis">
      Build Configurations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Build Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Pipeline
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues-and-solutions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues and Solutions
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Common Issues and Solutions">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#build-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Build Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-server-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Development Server Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Optimization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#debugging-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#initial-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Initial Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#vs-code-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      VS Code Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#recommended-vs-code-extensions" class="md-nav__link">
    <span class="md-ellipsis">
      Recommended VS Code Extensions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#directory-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Directory Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#module-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Module Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Development Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#daily-development-process" class="md-nav__link">
    <span class="md-ellipsis">
      Daily Development Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#git-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Git Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#code-generation" class="md-nav__link">
    <span class="md-ellipsis">
      Code Generation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#custom-component-development" class="md-nav__link">
    <span class="md-ellipsis">
      Custom Component Development
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategy
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Strategy">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Service Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#e2e-testing" class="md-nav__link">
    <span class="md-ellipsis">
      E2E Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Test Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#build-and-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Build and Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Build and Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#build-configurations" class="md-nav__link">
    <span class="md-ellipsis">
      Build Configurations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Build Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Pipeline
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues-and-solutions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues and Solutions
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Common Issues and Solutions">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#build-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Build Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-server-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Development Server Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Optimization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#debugging-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="frontend-development-guide">Frontend Development Guide<a class="headerlink" href="#frontend-development-guide" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#development-environment-setup">Development Environment Setup</a></li>
<li><a href="#project-structure">Project Structure</a></li>
<li><a href="#development-workflow">Development Workflow</a></li>
<li><a href="#testing-strategy">Testing Strategy</a></li>
<li><a href="#build-and-deployment">Build and Deployment</a></li>
<li><a href="#troubleshooting">Troubleshooting</a></li>
</ul>
<h2 id="development-environment-setup">Development Environment Setup<a class="headerlink" href="#development-environment-setup" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Required Software Versions</span>
Node.js:<span class="w"> </span>v18.x<span class="w"> </span>or<span class="w"> </span>higher
npm:<span class="w"> </span>v9.x<span class="w"> </span>or<span class="w"> </span>higher
Angular<span class="w"> </span>CLI:<span class="w"> </span>v15.2.8
Git:<span class="w"> </span>v2.x<span class="w"> </span>or<span class="w"> </span>higher
VS<span class="w"> </span>Code:<span class="w"> </span>Latest<span class="w"> </span><span class="o">(</span>recommended<span class="o">)</span>
</code></pre></div>
<h3 id="initial-setup">Initial Setup<a class="headerlink" href="#initial-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>&lt;repository-url&gt;
<span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-frontend-b2b

<span class="c1"># 2. Install dependencies</span>
npm<span class="w"> </span>install

<span class="c1"># 3. Install Angular CLI globally (if not already installed)</span>
npm<span class="w"> </span>install<span class="w"> </span>-g<span class="w"> </span>@angular/cli@15.2.8

<span class="c1"># 4. Verify installation</span>
ng<span class="w"> </span>version
npm<span class="w"> </span>list<span class="w"> </span>--depth<span class="o">=</span><span class="m">0</span>
</code></pre></div>
<h3 id="environment-configuration">Environment Configuration<a class="headerlink" href="#environment-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// src/environments/environment.development.ts</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">environment</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">production</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="p">,</span>
<span class="w">  </span><span class="nx">apiBaseUrl</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;http://localhost:8080/api/v1&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">mockMode</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">enableDevTools</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span>
<span class="w">  </span><span class="nx">logLevel</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;debug&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">features</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">enableNewQuoteFlow</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">enableAdvancedFilters</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">enableRealTimePricing</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nx">googleMaps</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">apiKey</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;your-google-maps-api-key&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">libraries</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;places&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;geometry&#39;</span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nx">telus</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">brandingEnabled</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">themeMode</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;light&#39;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">};</span>
</code></pre></div>
<h3 id="vs-code-configuration">VS Code Configuration<a class="headerlink" href="#vs-code-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// .vscode/settings.json</span>
<span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;typescript.preferences.importModuleSpecifier&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;relative&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;editor.formatOnSave&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;editor.codeActionsOnSave&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;source.organizeImports&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;source.fixAll.eslint&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;files.associations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;*.html&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;html&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;emmet.includeLanguages&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;typescript&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;html&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;angular.enable-strict-mode-prompt&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="recommended-vs-code-extensions">Recommended VS Code Extensions<a class="headerlink" href="#recommended-vs-code-extensions" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// .vscode/extensions.json</span>
<span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;angular.ng-template&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;ms-vscode.vscode-typescript-next&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;esbenp.prettier-vscode&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;ms-vscode.vscode-eslint&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;bradlc.vscode-tailwindcss&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;formulahendry.auto-rename-tag&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;christian-kohler.path-intellisense&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;ms-vscode.vscode-json&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="project-structure">Project Structure<a class="headerlink" href="#project-structure" title="Permanent link">&para;</a></h2>
<h3 id="directory-organization">Directory Organization<a class="headerlink" href="#directory-organization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>src/
├── app/
│   ├── core/                      # Singleton services, guards, interceptors
│   │   ├── guards/
│   │   ├── interceptors/
│   │   ├── services/
│   │   └── core.module.ts
│   ├── shared/                    # Shared components, pipes, directives
│   │   ├── components/
│   │   ├── pipes/
│   │   ├── directives/
│   │   ├── models/
│   │   └── shared.module.ts
│   ├── features/                  # Feature modules
│   │   ├── quotes/
│   │   ├── orders/
│   │   ├── services/
│   │   └── customers/
│   ├── custom/                    # TELUS custom components
│   │   ├── telus-start-quotation/
│   │   ├── telus-assign-location/
│   │   ├── telus-cart-total-prices/
│   │   └── [15+ more components]
│   ├── store/                     # NgRx state management
│   │   ├── actions/
│   │   ├── reducers/
│   │   ├── effects/
│   │   ├── selectors/
│   │   └── index.ts
│   ├── layouts/                   # Application layouts
│   │   ├── main-layout/
│   │   ├── auth-layout/
│   │   └── error-layout/
│   └── app.module.ts
├── assets/                        # Static assets
│   ├── images/
│   ├── icons/
│   ├── styles/
│   └── i18n/
├── environments/                  # Environment configurations
├── styles/                        # Global styles
│   ├── telus-theme.less
│   ├── variables.less
│   └── mixins.less
└── main.ts
</code></pre></div>
<h3 id="module-architecture">Module Architecture<a class="headerlink" href="#module-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Feature Module Structure Example</span>
<span class="c1">// src/app/features/quotes/quotes.module.ts</span>
<span class="kd">@NgModule</span><span class="p">({</span>
<span class="w">  </span><span class="nx">declarations</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="nx">QuoteListComponent</span><span class="p">,</span>
<span class="w">    </span><span class="nx">QuoteDetailComponent</span><span class="p">,</span>
<span class="w">    </span><span class="nx">QuoteCreateComponent</span><span class="p">,</span>
<span class="w">    </span><span class="nx">QuoteEditComponent</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nx">imports</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="nx">CommonModule</span><span class="p">,</span>
<span class="w">    </span><span class="nx">QuotesRoutingModule</span><span class="p">,</span>
<span class="w">    </span><span class="nx">SharedModule</span><span class="p">,</span>
<span class="w">    </span><span class="nx">ReactiveFormsModule</span><span class="p">,</span>
<span class="w">    </span><span class="nx">StoreModule</span><span class="p">.</span><span class="nx">forFeature</span><span class="p">(</span><span class="s1">&#39;quotes&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">quotesReducer</span><span class="p">),</span>
<span class="w">    </span><span class="nx">EffectsModule</span><span class="p">.</span><span class="nx">forFeature</span><span class="p">([</span><span class="nx">QuotesEffects</span><span class="p">])</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nx">providers</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="nx">QuoteService</span><span class="p">,</span>
<span class="w">    </span><span class="nx">QuoteResolver</span><span class="p">,</span>
<span class="w">    </span><span class="nx">QuoteGuard</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">QuotesModule</span><span class="w"> </span><span class="p">{}</span>
</code></pre></div>
<h2 id="development-workflow">Development Workflow<a class="headerlink" href="#development-workflow" title="Permanent link">&para;</a></h2>
<h3 id="daily-development-process">Daily Development Process<a class="headerlink" href="#daily-development-process" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Start development server</span>
npm<span class="w"> </span>run<span class="w"> </span>serve:dev

<span class="c1"># 2. Run tests in watch mode (separate terminal)</span>
npm<span class="w"> </span>run<span class="w"> </span>test:watch

<span class="c1"># 3. Run linting (before commits)</span>
npm<span class="w"> </span>run<span class="w"> </span>lint

<span class="c1"># 4. Format code</span>
npm<span class="w"> </span>run<span class="w"> </span>format

<span class="c1"># 5. Build for testing</span>
npm<span class="w"> </span>run<span class="w"> </span>build:dev
</code></pre></div>
<h3 id="git-workflow">Git Workflow<a class="headerlink" href="#git-workflow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Create feature branch</span>
git<span class="w"> </span>checkout<span class="w"> </span>-b<span class="w"> </span>feature/quote-management-enhancement

<span class="c1"># 2. Make changes and commit frequently</span>
git<span class="w"> </span>add<span class="w"> </span>.
git<span class="w"> </span>commit<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;feat: add quote comparison functionality&quot;</span>

<span class="c1"># 3. Push to remote</span>
git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>feature/quote-management-enhancement

<span class="c1"># 4. Create pull request</span>
<span class="c1"># 5. After review, merge to develop</span>
<span class="c1"># 6. Deploy to staging for testing</span>
</code></pre></div>
<h3 id="code-generation">Code Generation<a class="headerlink" href="#code-generation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Generate new component</span>
ng<span class="w"> </span>generate<span class="w"> </span>component<span class="w"> </span>features/quotes/quote-comparison

<span class="c1"># Generate new service</span>
ng<span class="w"> </span>generate<span class="w"> </span>service<span class="w"> </span>core/services/pricing

<span class="c1"># Generate new guard</span>
ng<span class="w"> </span>generate<span class="w"> </span>guard<span class="w"> </span>core/guards/quote-access

<span class="c1"># Generate new pipe</span>
ng<span class="w"> </span>generate<span class="w"> </span>pipe<span class="w"> </span>shared/pipes/currency-format

<span class="c1"># Generate new directive</span>
ng<span class="w"> </span>generate<span class="w"> </span>directive<span class="w"> </span>shared/directives/auto-focus
</code></pre></div>
<h3 id="custom-component-development">Custom Component Development<a class="headerlink" href="#custom-component-development" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Template for creating new TELUS components</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">Component</span><span class="p">,</span><span class="w"> </span><span class="nx">Input</span><span class="p">,</span><span class="w"> </span><span class="nx">Output</span><span class="p">,</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="p">,</span><span class="w"> </span><span class="nx">OnInit</span><span class="p">,</span><span class="w"> </span><span class="nx">OnDestroy</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;@angular/core&#39;</span><span class="p">;</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">FormBuilder</span><span class="p">,</span><span class="w"> </span><span class="nx">FormGroup</span><span class="p">,</span><span class="w"> </span><span class="nx">Validators</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;@angular/forms&#39;</span><span class="p">;</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">Store</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;@ngrx/store&#39;</span><span class="p">;</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">Subject</span><span class="p">,</span><span class="w"> </span><span class="nx">takeUntil</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;rxjs&#39;</span><span class="p">;</span>

<span class="kd">@Component</span><span class="p">({</span>
<span class="w">  </span><span class="nx">selector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;telus-new-component&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">templateUrl</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;./telus-new-component.component.html&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">styleUrls</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;./telus-new-component.component.less&#39;</span><span class="p">],</span>
<span class="w">  </span><span class="nx">changeDetection</span><span class="o">:</span><span class="w"> </span><span class="kt">ChangeDetectionStrategy.OnPush</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">TelusNewComponent</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">OnInit</span><span class="p">,</span><span class="w"> </span><span class="nx">OnDestroy</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">config</span><span class="o">:</span><span class="w"> </span><span class="kt">ComponentConfig</span><span class="p">;</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">actionCompleted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">ActionResult</span><span class="o">&gt;</span><span class="p">();</span>

<span class="w">  </span><span class="nx">form</span><span class="o">:</span><span class="w"> </span><span class="kt">FormGroup</span><span class="p">;</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">destroy$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">Subject</span><span class="o">&lt;</span><span class="ow">void</span><span class="o">&gt;</span><span class="p">();</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">fb</span><span class="o">:</span><span class="w"> </span><span class="kt">FormBuilder</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">store</span><span class="o">:</span><span class="w"> </span><span class="kt">Store</span><span class="o">&lt;</span><span class="nx">AppState</span><span class="o">&gt;</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">cdr</span><span class="o">:</span><span class="w"> </span><span class="kt">ChangeDetectorRef</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="nx">ngOnInit</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">initializeComponent</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">ngOnDestroy</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">destroy$</span><span class="p">.</span><span class="nx">next</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">destroy$</span><span class="p">.</span><span class="nx">complete</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">initializeComponent</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">buildForm</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">setupSubscriptions</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">buildForm</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">form</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">fb</span><span class="p">.</span><span class="nx">group</span><span class="p">({</span>
<span class="w">      </span><span class="c1">// Form controls</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">setupSubscriptions</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Component subscriptions</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="testing-strategy">Testing Strategy<a class="headerlink" href="#testing-strategy" title="Permanent link">&para;</a></h2>
<h3 id="unit-testing-setup">Unit Testing Setup<a class="headerlink" href="#unit-testing-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Component Test Template</span>
<span class="nx">describe</span><span class="p">(</span><span class="s1">&#39;TelusQuoteViewComponent&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">let</span><span class="w"> </span><span class="nx">component</span><span class="o">:</span><span class="w"> </span><span class="kt">TelusQuoteViewComponent</span><span class="p">;</span>
<span class="w">  </span><span class="kd">let</span><span class="w"> </span><span class="nx">fixture</span><span class="o">:</span><span class="w"> </span><span class="kt">ComponentFixture</span><span class="o">&lt;</span><span class="nx">TelusQuoteViewComponent</span><span class="o">&gt;</span><span class="p">;</span>
<span class="w">  </span><span class="kd">let</span><span class="w"> </span><span class="nx">store</span><span class="o">:</span><span class="w"> </span><span class="kt">MockStore</span><span class="p">;</span>
<span class="w">  </span><span class="kd">let</span><span class="w"> </span><span class="nx">quoteService</span><span class="o">:</span><span class="w"> </span><span class="kt">jasmine.SpyObj</span><span class="o">&lt;</span><span class="nx">QuoteService</span><span class="o">&gt;</span><span class="p">;</span>

<span class="w">  </span><span class="nx">beforeEach</span><span class="p">(</span><span class="k">async</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">quoteServiceSpy</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">jasmine</span><span class="p">.</span><span class="nx">createSpyObj</span><span class="p">(</span><span class="s1">&#39;QuoteService&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;getQuote&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;updateQuote&#39;</span><span class="p">]);</span>

<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">TestBed</span><span class="p">.</span><span class="nx">configureTestingModule</span><span class="p">({</span>
<span class="w">      </span><span class="nx">declarations</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="nx">TelusQuoteViewComponent</span><span class="p">],</span>
<span class="w">      </span><span class="nx">imports</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="nx">ReactiveFormsModule</span><span class="p">,</span>
<span class="w">        </span><span class="nx">NoopAnimationsModule</span>
<span class="w">      </span><span class="p">],</span>
<span class="w">      </span><span class="nx">providers</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">        </span><span class="nx">provideMockStore</span><span class="p">({</span><span class="w"> </span><span class="nx">initialState</span><span class="o">:</span><span class="w"> </span><span class="kt">mockAppState</span><span class="w"> </span><span class="p">}),</span>
<span class="w">        </span><span class="p">{</span><span class="w"> </span><span class="nx">provide</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteService</span><span class="p">,</span><span class="w"> </span><span class="nx">useValue</span><span class="o">:</span><span class="w"> </span><span class="kt">quoteServiceSpy</span><span class="w"> </span><span class="p">}</span>
<span class="w">      </span><span class="p">]</span>
<span class="w">    </span><span class="p">}).</span><span class="nx">compileComponents</span><span class="p">();</span>

<span class="w">    </span><span class="nx">fixture</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">TestBed</span><span class="p">.</span><span class="nx">createComponent</span><span class="p">(</span><span class="nx">TelusQuoteViewComponent</span><span class="p">);</span>
<span class="w">    </span><span class="nx">component</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">fixture</span><span class="p">.</span><span class="nx">componentInstance</span><span class="p">;</span>
<span class="w">    </span><span class="nx">store</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">TestBed</span><span class="p">.</span><span class="nx">inject</span><span class="p">(</span><span class="nx">MockStore</span><span class="p">);</span>
<span class="w">    </span><span class="nx">quoteService</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">TestBed</span><span class="p">.</span><span class="nx">inject</span><span class="p">(</span><span class="nx">QuoteService</span><span class="p">)</span><span class="w"> </span><span class="kr">as</span><span class="w"> </span><span class="nx">jasmine</span><span class="p">.</span><span class="nx">SpyObj</span><span class="o">&lt;</span><span class="nx">QuoteService</span><span class="o">&gt;</span><span class="p">;</span>
<span class="w">  </span><span class="p">});</span>

<span class="w">  </span><span class="nx">it</span><span class="p">(</span><span class="s1">&#39;should create&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">component</span><span class="p">).</span><span class="nx">toBeTruthy</span><span class="p">();</span>
<span class="w">  </span><span class="p">});</span>

<span class="w">  </span><span class="nx">it</span><span class="p">(</span><span class="s1">&#39;should load quote on init&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">mockQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;123&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">1000</span><span class="w"> </span><span class="p">};</span>
<span class="w">    </span><span class="nx">component</span><span class="p">.</span><span class="nx">quoteId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;123&#39;</span><span class="p">;</span>

<span class="w">    </span><span class="nx">store</span><span class="p">.</span><span class="nx">overrideSelector</span><span class="p">(</span><span class="nx">selectQuoteById</span><span class="p">,</span><span class="w"> </span><span class="nx">mockQuote</span><span class="p">);</span>

<span class="w">    </span><span class="nx">component</span><span class="p">.</span><span class="nx">ngOnInit</span><span class="p">();</span>

<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">component</span><span class="p">.</span><span class="nx">quote$</span><span class="p">).</span><span class="nx">toBeDefined</span><span class="p">();</span>
<span class="w">  </span><span class="p">});</span>

<span class="w">  </span><span class="nx">it</span><span class="p">(</span><span class="s1">&#39;should emit quote updated event&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">spyOn</span><span class="p">(</span><span class="nx">component</span><span class="p">.</span><span class="nx">quoteUpdated</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;emit&#39;</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">mockQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;123&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">1500</span><span class="w"> </span><span class="p">};</span>

<span class="w">    </span><span class="nx">component</span><span class="p">.</span><span class="nx">onQuoteUpdate</span><span class="p">(</span><span class="nx">mockQuote</span><span class="p">);</span>

<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">component</span><span class="p">.</span><span class="nx">quoteUpdated</span><span class="p">.</span><span class="nx">emit</span><span class="p">).</span><span class="nx">toHaveBeenCalledWith</span><span class="p">(</span><span class="nx">mockQuote</span><span class="p">);</span>
<span class="w">  </span><span class="p">});</span>
<span class="p">});</span>
</code></pre></div>
<h3 id="service-testing">Service Testing<a class="headerlink" href="#service-testing" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Service Test Template</span>
<span class="nx">describe</span><span class="p">(</span><span class="s1">&#39;QuoteService&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">let</span><span class="w"> </span><span class="nx">service</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteService</span><span class="p">;</span>
<span class="w">  </span><span class="kd">let</span><span class="w"> </span><span class="nx">httpMock</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpTestingController</span><span class="p">;</span>

<span class="w">  </span><span class="nx">beforeEach</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">TestBed</span><span class="p">.</span><span class="nx">configureTestingModule</span><span class="p">({</span>
<span class="w">      </span><span class="nx">imports</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="nx">HttpClientTestingModule</span><span class="p">],</span>
<span class="w">      </span><span class="nx">providers</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="nx">QuoteService</span><span class="p">]</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="nx">service</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">TestBed</span><span class="p">.</span><span class="nx">inject</span><span class="p">(</span><span class="nx">QuoteService</span><span class="p">);</span>
<span class="w">    </span><span class="nx">httpMock</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">TestBed</span><span class="p">.</span><span class="nx">inject</span><span class="p">(</span><span class="nx">HttpTestingController</span><span class="p">);</span>
<span class="w">  </span><span class="p">});</span>

<span class="w">  </span><span class="nx">afterEach</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">httpMock</span><span class="p">.</span><span class="nx">verify</span><span class="p">();</span>
<span class="w">  </span><span class="p">});</span>

<span class="w">  </span><span class="nx">it</span><span class="p">(</span><span class="s1">&#39;should fetch quotes&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">mockQuotes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span>
<span class="w">      </span><span class="p">{</span><span class="w"> </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;1&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">1000</span><span class="w"> </span><span class="p">},</span>
<span class="w">      </span><span class="p">{</span><span class="w"> </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;2&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">2000</span><span class="w"> </span><span class="p">}</span>
<span class="w">    </span><span class="p">];</span>

<span class="w">    </span><span class="nx">service</span><span class="p">.</span><span class="nx">getQuotes</span><span class="p">().</span><span class="nx">subscribe</span><span class="p">(</span><span class="nx">quotes</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">expect</span><span class="p">(</span><span class="nx">quotes</span><span class="p">.</span><span class="nx">length</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="mf">2</span><span class="p">);</span>
<span class="w">      </span><span class="nx">expect</span><span class="p">(</span><span class="nx">quotes</span><span class="p">).</span><span class="nx">toEqual</span><span class="p">(</span><span class="nx">mockQuotes</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">req</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">httpMock</span><span class="p">.</span><span class="nx">expectOne</span><span class="p">(</span><span class="s1">&#39;/api/v1/quotes&#39;</span><span class="p">);</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">req</span><span class="p">.</span><span class="nx">request</span><span class="p">.</span><span class="nx">method</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="s1">&#39;GET&#39;</span><span class="p">);</span>
<span class="w">    </span><span class="nx">req</span><span class="p">.</span><span class="nx">flush</span><span class="p">(</span><span class="nx">mockQuotes</span><span class="p">);</span>
<span class="w">  </span><span class="p">});</span>

<span class="w">  </span><span class="nx">it</span><span class="p">(</span><span class="s1">&#39;should handle errors&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">service</span><span class="p">.</span><span class="nx">getQuotes</span><span class="p">().</span><span class="nx">subscribe</span><span class="p">({</span>
<span class="w">      </span><span class="nx">next</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">fail</span><span class="p">(</span><span class="s1">&#39;should have failed&#39;</span><span class="p">),</span>
<span class="w">      </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">expect</span><span class="p">(</span><span class="nx">error</span><span class="p">).</span><span class="nx">toBeTruthy</span><span class="p">();</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">req</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">httpMock</span><span class="p">.</span><span class="nx">expectOne</span><span class="p">(</span><span class="s1">&#39;/api/v1/quotes&#39;</span><span class="p">);</span>
<span class="w">    </span><span class="nx">req</span><span class="p">.</span><span class="nx">flush</span><span class="p">(</span><span class="s1">&#39;Error&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">status</span><span class="o">:</span><span class="w"> </span><span class="kt">500</span><span class="p">,</span><span class="w"> </span><span class="nx">statusText</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Server Error&#39;</span><span class="w"> </span><span class="p">});</span>
<span class="w">  </span><span class="p">});</span>
<span class="p">});</span>
</code></pre></div>
<h3 id="e2e-testing">E2E Testing<a class="headerlink" href="#e2e-testing" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// E2E Test Example</span>
<span class="nx">describe</span><span class="p">(</span><span class="s1">&#39;Quote Management Flow&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">let</span><span class="w"> </span><span class="nx">page</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteManagementPage</span><span class="p">;</span>

<span class="w">  </span><span class="nx">beforeEach</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">page</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">QuoteManagementPage</span><span class="p">();</span>
<span class="w">  </span><span class="p">});</span>

<span class="w">  </span><span class="nx">it</span><span class="p">(</span><span class="s1">&#39;should create a new quote&#39;</span><span class="p">,</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">page</span><span class="p">.</span><span class="nx">navigateToQuotes</span><span class="p">();</span>
<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">page</span><span class="p">.</span><span class="nx">clickCreateQuote</span><span class="p">();</span>
<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">page</span><span class="p">.</span><span class="nx">fillQuoteForm</span><span class="p">({</span>
<span class="w">      </span><span class="nx">customerId</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;CUST001&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">serviceType</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Internet&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">location</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;123 Main St, Toronto, ON&#39;</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">page</span><span class="p">.</span><span class="nx">submitQuote</span><span class="p">();</span>

<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="k">await</span><span class="w"> </span><span class="nx">page</span><span class="p">.</span><span class="nx">getSuccessMessage</span><span class="p">()).</span><span class="nx">toContain</span><span class="p">(</span><span class="s1">&#39;Quote created successfully&#39;</span><span class="p">);</span>
<span class="w">  </span><span class="p">});</span>

<span class="w">  </span><span class="nx">it</span><span class="p">(</span><span class="s1">&#39;should filter quotes by status&#39;</span><span class="p">,</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">page</span><span class="p">.</span><span class="nx">navigateToQuotes</span><span class="p">();</span>
<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">page</span><span class="p">.</span><span class="nx">selectStatusFilter</span><span class="p">(</span><span class="s1">&#39;approved&#39;</span><span class="p">);</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">quotes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">page</span><span class="p">.</span><span class="nx">getDisplayedQuotes</span><span class="p">();</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">quotes</span><span class="p">.</span><span class="nx">every</span><span class="p">(</span><span class="nx">quote</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">quote</span><span class="p">.</span><span class="nx">status</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="s1">&#39;approved&#39;</span><span class="p">)).</span><span class="nx">toBe</span><span class="p">(</span><span class="kc">true</span><span class="p">);</span>
<span class="w">  </span><span class="p">});</span>
<span class="p">});</span>
</code></pre></div>
<h3 id="test-commands">Test Commands<a class="headerlink" href="#test-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Run all tests</span>
npm<span class="w"> </span>run<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Run tests in watch mode</span>
npm<span class="w"> </span>run<span class="w"> </span>test:watch

<span class="c1"># Run tests with coverage</span>
npm<span class="w"> </span>run<span class="w"> </span>test:coverage

<span class="c1"># Run E2E tests</span>
npm<span class="w"> </span>run<span class="w"> </span>e2e

<span class="c1"># Run specific test suite</span>
npm<span class="w"> </span>run<span class="w"> </span><span class="nb">test</span><span class="w"> </span>--<span class="w"> </span>--include<span class="o">=</span><span class="s2">&quot;**/quote.service.spec.ts&quot;</span>

<span class="c1"># Run tests for specific component</span>
ng<span class="w"> </span><span class="nb">test</span><span class="w"> </span>--include<span class="o">=</span><span class="s2">&quot;**/telus-quote-view.component.spec.ts&quot;</span>
</code></pre></div>
<h2 id="build-and-deployment">Build and Deployment<a class="headerlink" href="#build-and-deployment" title="Permanent link">&para;</a></h2>
<h3 id="build-configurations">Build Configurations<a class="headerlink" href="#build-configurations" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// angular.json build configurations</span>
<span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;build&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;builder&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;@angular-devkit/build-angular:browser&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;options&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;outputPath&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;dist/frontend-app&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;index&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;src/index.html&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;main&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;src/main.ts&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;polyfills&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;src/polyfills.ts&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;tsConfig&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;tsconfig.app.json&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;assets&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;src/favicon.ico&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;src/assets&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;styles&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;src/styles/main.less&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;scripts&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[]</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;configurations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;production&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;budgets&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;initial&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;maximumWarning&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2mb&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;maximumError&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;5mb&quot;</span>
<span class="w">          </span><span class="p">}</span>
<span class="w">        </span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;fileReplacements&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;replace&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;src/environments/environment.ts&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;with&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;src/environments/environment.prod.ts&quot;</span>
<span class="w">          </span><span class="p">}</span>
<span class="w">        </span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;outputHashing&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;all&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;sourceMap&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;namedChunks&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;extractLicenses&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;vendorChunk&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;buildOptimizer&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;optimization&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;development&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;buildOptimizer&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;optimization&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;vendorChunk&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;extractLicenses&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;sourceMap&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;namedChunks&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="build-commands">Build Commands<a class="headerlink" href="#build-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Development build</span>
npm<span class="w"> </span>run<span class="w"> </span>build:dev

<span class="c1"># Production build</span>
npm<span class="w"> </span>run<span class="w"> </span>build

<span class="c1"># Build with analysis</span>
npm<span class="w"> </span>run<span class="w"> </span>build:analyze

<span class="c1"># Build and serve locally</span>
npm<span class="w"> </span>run<span class="w"> </span>build<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>npm<span class="w"> </span>run<span class="w"> </span>serve:dist
</code></pre></div>
<h3 id="deployment-pipeline">Deployment Pipeline<a class="headerlink" href="#deployment-pipeline" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># .github/workflows/deploy.yml</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy Frontend</span>
<span class="nt">on</span><span class="p">:</span>
<span class="w">  </span><span class="nt">push</span><span class="p">:</span>
<span class="w">    </span><span class="nt">branches</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">main</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">develop</span><span class="p p-Indicator">]</span>

<span class="nt">jobs</span><span class="p">:</span>
<span class="w">  </span><span class="nt">build-and-deploy</span><span class="p">:</span>
<span class="w">    </span><span class="nt">runs-on</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ubuntu-latest</span>
<span class="w">    </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/checkout@v3</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Setup Node.js</span>
<span class="w">        </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/setup-node@v3</span>
<span class="w">        </span><span class="nt">with</span><span class="p">:</span>
<span class="w">          </span><span class="nt">node-version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;18&#39;</span>
<span class="w">          </span><span class="nt">cache</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;npm&#39;</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Install dependencies</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">npm ci</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Run tests</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">npm run test:ci</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Build application</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">npm run build</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy to staging</span>
<span class="w">        </span><span class="nt">if</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">github.ref == &#39;refs/heads/develop&#39;</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">npm run deploy:staging</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy to production</span>
<span class="w">        </span><span class="nt">if</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">github.ref == &#39;refs/heads/main&#39;</span>
<span class="w">        </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">npm run deploy:production</span>
</code></pre></div>
<h2 id="troubleshooting">Troubleshooting<a class="headerlink" href="#troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="common-issues-and-solutions">Common Issues and Solutions<a class="headerlink" href="#common-issues-and-solutions" title="Permanent link">&para;</a></h3>
<h4 id="build-issues">Build Issues<a class="headerlink" href="#build-issues" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Clear node_modules and reinstall</span>
rm<span class="w"> </span>-rf<span class="w"> </span>node_modules<span class="w"> </span>package-lock.json
npm<span class="w"> </span>install

<span class="c1"># Clear Angular cache</span>
ng<span class="w"> </span>cache<span class="w"> </span>clean

<span class="c1"># Update Angular CLI</span>
npm<span class="w"> </span>uninstall<span class="w"> </span>-g<span class="w"> </span>@angular/cli
npm<span class="w"> </span>install<span class="w"> </span>-g<span class="w"> </span>@angular/cli@latest
</code></pre></div>
<h4 id="development-server-issues">Development Server Issues<a class="headerlink" href="#development-server-issues" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Port already in use</span>
ng<span class="w"> </span>serve<span class="w"> </span>--port<span class="w"> </span><span class="m">4201</span>

<span class="c1"># Memory issues</span>
node<span class="w"> </span>--max_old_space_size<span class="o">=</span><span class="m">8192</span><span class="w"> </span>node_modules/@angular/cli/bin/ng<span class="w"> </span>serve

<span class="c1"># Proxy configuration issues</span>
ng<span class="w"> </span>serve<span class="w"> </span>--proxy-config<span class="w"> </span>proxy.conf.json
</code></pre></div>
<h4 id="testing-issues">Testing Issues<a class="headerlink" href="#testing-issues" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Chrome not found (CI/CD)</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">CHROME_BIN</span><span class="o">=</span>/usr/bin/google-chrome-stable

<span class="c1"># Memory issues during testing</span>
ng<span class="w"> </span><span class="nb">test</span><span class="w"> </span>--watch<span class="o">=</span><span class="nb">false</span><span class="w"> </span>--browsers<span class="o">=</span>ChromeHeadless<span class="w"> </span>--code-coverage
</code></pre></div>
<h3 id="performance-optimization">Performance Optimization<a class="headerlink" href="#performance-optimization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Lazy loading implementation</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">routes</span><span class="o">:</span><span class="w"> </span><span class="kt">Routes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nx">path</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;quotes&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loadChildren</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">import</span><span class="p">(</span><span class="s1">&#39;./features/quotes/quotes.module&#39;</span><span class="p">).</span><span class="nx">then</span><span class="p">(</span><span class="nx">m</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">m</span><span class="p">.</span><span class="nx">QuotesModule</span><span class="p">)</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">];</span>

<span class="c1">// OnPush change detection</span>
<span class="kd">@Component</span><span class="p">({</span>
<span class="w">  </span><span class="nx">changeDetection</span><span class="o">:</span><span class="w"> </span><span class="kt">ChangeDetectionStrategy.OnPush</span>
<span class="p">})</span>

<span class="c1">// TrackBy functions for *ngFor</span>
<span class="nx">trackByQuoteId</span><span class="p">(</span><span class="nx">index</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">,</span><span class="w"> </span><span class="nx">quote</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">quote</span><span class="p">.</span><span class="nx">id</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="debugging-tools">Debugging Tools<a class="headerlink" href="#debugging-tools" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Redux DevTools integration</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">StoreDevtoolsModule</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;@ngrx/store-devtools&#39;</span><span class="p">;</span>

<span class="kd">@NgModule</span><span class="p">({</span>
<span class="w">  </span><span class="nx">imports</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="nx">StoreDevtoolsModule</span><span class="p">.</span><span class="nx">instrument</span><span class="p">({</span>
<span class="w">      </span><span class="nx">maxAge</span><span class="o">:</span><span class="w"> </span><span class="kt">25</span><span class="p">,</span>
<span class="w">      </span><span class="nx">logOnly</span><span class="o">:</span><span class="w"> </span><span class="kt">environment.production</span>
<span class="w">    </span><span class="p">})</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">})</span>

<span class="c1">// Angular DevTools</span>
<span class="c1">// Install: ng add @angular/devtools</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="06-testing-guide.md">Testing Guide →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>