declare module 'tunnel' {
  interface ProxyOptions {
    host: string;
    port: number;
    proxyAuth?: string;
  }

  interface TunnelOptions {
    proxy: ProxyOptions;
  }

  interface TunnelAgent {
    // Propriétés et méthodes de l'agent tunnel
  }

  export function httpsOverHttp(options: TunnelOptions): TunnelAgent;
  export function httpOverHttps(options: TunnelOptions): TunnelAgent;
  export function httpsOverHttps(options: TunnelOptions): TunnelAgent;
  export function httpOverHttp(options: TunnelOptions): TunnelAgent;
}
