import { getMCPServers } from "../lib/get-mcp-servers";
import { ServerCard } from "@/components/server-card";
import { ServerSearch } from "@/components/server-search";
import { Metadata } from "next";
import RegistryPage from "./_components/registry-page";

export const metadata: Metadata = {
  title: "MCP Servers Registry",
  description:
    "A registry of MCP servers that are available to use in your projects.",
};

export default async function HomePage() {
  const servers = await getMCPServers();

  return <RegistryPage initialServers={servers} />;
}
