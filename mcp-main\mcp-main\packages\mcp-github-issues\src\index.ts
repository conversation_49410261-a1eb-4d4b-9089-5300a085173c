#!/usr/bin/env node
import 'dotenv/config';
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  ListToolsRequestSchema,
  CallToolRequestSchema,
  ErrorCode,
  McpError,
  CallToolRequest
} from "@modelcontextprotocol/sdk/types.js";
import { GitHubAPI } from "./github-api.js";

const github = new GitHubAPI();

const server = new Server(
  {
    name: "github-issues",
    version: "1.0.0"
  },
  {
    capabilities: {
      tools: {}
    }
  }
);

server.setRequestHandler(ListToolsRequestSchema, async () => ({
  tools: [
    {
      name: "list_issues",
      description: "List issues for a repository",
      inputSchema: {
        type: "object",
        properties: {
          owner: { type: "string", description: "Repository owner" },
          repo: { type: "string", description: "Repository name" },
          state: { 
            type: "string", 
            enum: ["open", "closed", "all"],
            description: "Issue state"
          },
          labels: {
            type: "array",
            items: { type: "string" },
            description: "Issue labels"
          },
          sort: {
            type: "string",
            enum: ["created", "updated", "comments"],
            description: "Sort field"
          },
          direction: {
            type: "string",
            enum: ["asc", "desc"],
            description: "Sort direction"
          }
        },
        required: ["owner", "repo"]
      }
    },
    {
      name: "create_issue",
      description: "Create a new issue",
      inputSchema: {
        type: "object",
        properties: {
          owner: { type: "string", description: "Repository owner" },
          repo: { type: "string", description: "Repository name" },
          title: { type: "string", description: "Issue title" },
          body: { type: "string", description: "Issue body" },
          labels: {
            type: "array",
            items: { type: "string" },
            description: "Issue labels"
          },
          assignees: {
            type: "array",
            items: { type: "string" },
            description: "Issue assignees"
          }
        },
        required: ["owner", "repo", "title", "body"]
      }
    },
    {
      name: "update_issue",
      description: "Update an existing issue",
      inputSchema: {
        type: "object",
        properties: {
          owner: { type: "string", description: "Repository owner" },
          repo: { type: "string", description: "Repository name" },
          issue_number: { type: "number", description: "Issue number" },
          title: { type: "string", description: "New issue title" },
          body: { type: "string", description: "New issue body" },
          state: {
            type: "string",
            enum: ["open", "closed"],
            description: "Issue state"
          },
          labels: {
            type: "array",
            items: { type: "string" },
            description: "Issue labels"
          },
          assignees: {
            type: "array",
            items: { type: "string" },
            description: "Issue assignees"
          }
        },
        required: ["owner", "repo", "issue_number"]
      }
    },
    {
      name: "get_issue",
      description: "Get issue details",
      inputSchema: {
        type: "object",
        properties: {
          owner: { type: "string", description: "Repository owner" },
          repo: { type: "string", description: "Repository name" },
          issue_number: { type: "number", description: "Issue number" }
        },
        required: ["owner", "repo", "issue_number"]
      }
    }
  ]
}));

server.setRequestHandler(CallToolRequestSchema, async (request: CallToolRequest) => {
  try {
    switch (request.params.name) {
      case "list_issues": {
        const { owner, repo, ...options } = request.params.arguments as any;
        const issues = await github.listIssues(owner, repo, options);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(issues, null, 2)
          }]
        };
      }

      case "create_issue": {
        const { owner, repo, ...options } = request.params.arguments as any;
        const issue = await github.createIssue(owner, repo, options);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(issue, null, 2)
          }]
        };
      }

      case "update_issue": {
        const { owner, repo, issue_number, ...options } = request.params.arguments as any;
        const issue = await github.updateIssue(owner, repo, issue_number, options);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(issue, null, 2)
          }]
        };
      }

      case "get_issue": {
        const { owner, repo, issue_number } = request.params.arguments as any;
        const issue = await github.getIssue(owner, repo, issue_number);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(issue, null, 2)
          }]
        };
      }

      default:
        throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${request.params.name}`);
    }
  } catch (error: any) {
    return {
      content: [{
        type: "text",
        text: error.message
      }],
      isError: true
    };
  }
});

const transport = new StdioServerTransport();
server.connect(transport).catch(console.error);

console.error("GitHub Issues MCP server running on stdio");
