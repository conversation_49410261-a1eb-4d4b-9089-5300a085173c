
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/bulk-operation-extension/01-nifi-flow-architecture/">
      
      
        <link rel="prev" href="../00-overview/">
      
      
        <link rel="next" href="../02-api-integration/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>NiFi Flow Architecture - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#nifi-flow-architecture" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              NiFi Flow Architecture
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" checked>
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#flow-architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Flow Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Flow Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#nifi-flow-hierarchy" class="md-nav__link">
    <span class="md-ellipsis">
      NiFi Flow Hierarchy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-flow-sequence" class="md-nav__link">
    <span class="md-ellipsis">
      Data Flow Sequence
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bulk-address-upload-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Address Upload Flow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Bulk Address Upload Flow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#main-flow-components" class="md-nav__link">
    <span class="md-ellipsis">
      Main Flow Components
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#request-routing-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Request Routing Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#processor-detailed-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Processor Detailed Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Processor Detailed Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#handlehttprequest-processor" class="md-nav__link">
    <span class="md-ellipsis">
      HandleHttpRequest Processor
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#validateauthtoken-processor" class="md-nav__link">
    <span class="md-ellipsis">
      ValidateAuthToken Processor
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#updateattribute-processors" class="md-nav__link">
    <span class="md-ellipsis">
      UpdateAttribute Processors
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#controller-services" class="md-nav__link">
    <span class="md-ellipsis">
      Controller Services
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Controller Services">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#database-connection-pool" class="md-nav__link">
    <span class="md-ellipsis">
      Database Connection Pool
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#oauth2-access-token-provider" class="md-nav__link">
    <span class="md-ellipsis">
      OAuth2 Access Token Provider
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#http-context-map" class="md-nav__link">
    <span class="md-ellipsis">
      HTTP Context Map
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#schema-registry-services" class="md-nav__link">
    <span class="md-ellipsis">
      Schema Registry Services
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#parameter-contexts" class="md-nav__link">
    <span class="md-ellipsis">
      Parameter Contexts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Parameter Contexts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#bulk-address-upload-parameters" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Address Upload Parameters
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-specific-parameters" class="md-nav__link">
    <span class="md-ellipsis">
      Environment-Specific Parameters
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#processor-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Processor Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Processor Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#request-processing-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Request Processing Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#response-generation" class="md-nav__link">
    <span class="md-ellipsis">
      Response Generation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-handling" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Handling">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#error-response-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Error Response Patterns
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#error-response-format" class="md-nav__link">
    <span class="md-ellipsis">
      Error Response Format
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#retry-and-recovery" class="md-nav__link">
    <span class="md-ellipsis">
      Retry and Recovery
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#flow-architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Flow Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Flow Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#nifi-flow-hierarchy" class="md-nav__link">
    <span class="md-ellipsis">
      NiFi Flow Hierarchy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-flow-sequence" class="md-nav__link">
    <span class="md-ellipsis">
      Data Flow Sequence
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bulk-address-upload-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Address Upload Flow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Bulk Address Upload Flow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#main-flow-components" class="md-nav__link">
    <span class="md-ellipsis">
      Main Flow Components
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#request-routing-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Request Routing Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#processor-detailed-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Processor Detailed Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Processor Detailed Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#handlehttprequest-processor" class="md-nav__link">
    <span class="md-ellipsis">
      HandleHttpRequest Processor
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#validateauthtoken-processor" class="md-nav__link">
    <span class="md-ellipsis">
      ValidateAuthToken Processor
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#updateattribute-processors" class="md-nav__link">
    <span class="md-ellipsis">
      UpdateAttribute Processors
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#controller-services" class="md-nav__link">
    <span class="md-ellipsis">
      Controller Services
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Controller Services">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#database-connection-pool" class="md-nav__link">
    <span class="md-ellipsis">
      Database Connection Pool
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#oauth2-access-token-provider" class="md-nav__link">
    <span class="md-ellipsis">
      OAuth2 Access Token Provider
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#http-context-map" class="md-nav__link">
    <span class="md-ellipsis">
      HTTP Context Map
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#schema-registry-services" class="md-nav__link">
    <span class="md-ellipsis">
      Schema Registry Services
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#parameter-contexts" class="md-nav__link">
    <span class="md-ellipsis">
      Parameter Contexts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Parameter Contexts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#bulk-address-upload-parameters" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Address Upload Parameters
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-specific-parameters" class="md-nav__link">
    <span class="md-ellipsis">
      Environment-Specific Parameters
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#processor-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Processor Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Processor Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#request-processing-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Request Processing Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#response-generation" class="md-nav__link">
    <span class="md-ellipsis">
      Response Generation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-handling" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Handling">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#error-response-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Error Response Patterns
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#error-response-format" class="md-nav__link">
    <span class="md-ellipsis">
      Error Response Format
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#retry-and-recovery" class="md-nav__link">
    <span class="md-ellipsis">
      Retry and Recovery
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="nifi-flow-architecture">NiFi Flow Architecture<a class="headerlink" href="#nifi-flow-architecture" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#flow-architecture-overview">Flow Architecture Overview</a></li>
<li><a href="#bulk-address-upload-flow">Bulk Address Upload Flow</a></li>
<li><a href="#controller-services">Controller Services</a></li>
<li><a href="#parameter-contexts">Parameter Contexts</a></li>
<li><a href="#processor-configuration">Processor Configuration</a></li>
<li><a href="#error-handling">Error Handling</a></li>
</ul>
<h2 id="flow-architecture-overview">Flow Architecture Overview<a class="headerlink" href="#flow-architecture-overview" title="Permanent link">&para;</a></h2>
<h3 id="nifi-flow-hierarchy">NiFi Flow Hierarchy<a class="headerlink" href="#nifi-flow-hierarchy" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Root Process Group&quot;
        A[Bulk_Address_Upload]
    end

    subgraph &quot;Handle Request and Response&quot;
        B[HandleHttpRequest]
        C[Get Request ID and Type]
        D[ValidateAuthToken]
        E[Delete Extra HTTP Attributes]
        F[Check Path]
        G[Route Processing]
    end

    subgraph &quot;Request Type Routing&quot;
        H[Bulk Address Request]
        I[Status Check]
        J[Report Generation]
        K[Error Handling]
    end

    subgraph &quot;Response Generation&quot;
        L[Set HTTP Status Code]
        M[Generate Response Content]
        N[HandleHttpResponse]
        O[Log Messages]
    end

    A --&gt; B
    B --&gt; C
    C --&gt; D
    D --&gt; E
    E --&gt; F
    F --&gt; G

    G --&gt; H
    G --&gt; I
    G --&gt; J
    G --&gt; K

    H --&gt; L
    I --&gt; L
    J --&gt; L
    K --&gt; L

    L --&gt; M
    M --&gt; N
    N --&gt; O

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style G fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style L fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="data-flow-sequence">Data Flow Sequence<a class="headerlink" href="#data-flow-sequence" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Client as HTTP Client
    participant HTTP as HandleHttpRequest
    participant Auth as ValidateAuthToken
    participant Router as Check Path
    participant Processor as Request Processor
    participant DB as Database
    participant Response as HandleHttpResponse

    Client-&gt;&gt;HTTP: HTTP Request
    HTTP-&gt;&gt;HTTP: Extract Request Attributes
    HTTP-&gt;&gt;Auth: Validate Authorization Header

    alt Valid Token
        Auth-&gt;&gt;Router: Authorized Request
        Router-&gt;&gt;Router: Parse Request Path

        alt Bulk Request
            Router-&gt;&gt;Processor: Process Bulk Upload
            Processor-&gt;&gt;DB: Store Request Data
            DB--&gt;&gt;Processor: Confirmation
            Processor--&gt;&gt;Response: Success Response
        else Status Request
            Router-&gt;&gt;Processor: Query Status
            Processor-&gt;&gt;DB: Get Status
            DB--&gt;&gt;Processor: Status Data
            Processor--&gt;&gt;Response: Status Response
        else Report Request
            Router-&gt;&gt;Processor: Generate Report
            Processor-&gt;&gt;DB: Get Report Data
            DB--&gt;&gt;Processor: Report Data
            Processor--&gt;&gt;Response: Report Response
        end

    else Invalid Token
        Auth--&gt;&gt;Response: Unauthorized Response
    end

    Response--&gt;&gt;Client: HTTP Response
</code></pre></div>
<h2 id="bulk-address-upload-flow">Bulk Address Upload Flow<a class="headerlink" href="#bulk-address-upload-flow" title="Permanent link">&para;</a></h2>
<h3 id="main-flow-components">Main Flow Components<a class="headerlink" href="#main-flow-components" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;HTTP Layer&quot;
        A[HandleHttpRequest&lt;br/&gt;Port: 21014&lt;br/&gt;Path: /addressManagement/v1/bulkValidate/.*]
        B[HandleHttpResponse&lt;br/&gt;Status Code: ${http.status.code}&lt;br/&gt;Content-Type: ${mime.type}]
    end

    subgraph &quot;Security Layer&quot;
        C[ValidateAuthToken&lt;br/&gt;Identity Provider: http://identity-provider:8080&lt;br/&gt;Attribute: http.headers.Authorization]
    end

    subgraph &quot;Request Processing&quot;
        D[Get Request ID and Type&lt;br/&gt;Extract: request.id, request.mode&lt;br/&gt;Regex: URI parsing]
        E[Delete Extra HTTP Attributes&lt;br/&gt;Remove: Authorization headers&lt;br/&gt;Security cleanup]
    end

    subgraph &quot;Routing Logic&quot;
        F[Check Path&lt;br/&gt;Route by: URI pattern&lt;br/&gt;Methods: POST, GET]
    end

    A --&gt; C
    C --&gt; D
    D --&gt; E
    E --&gt; F
    F --&gt; B

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style C fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style F fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="request-routing-configuration">Request Routing Configuration<a class="headerlink" href="#request-routing-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># RouteOnAttribute Processor Configuration</span>
<span class="nt">routing_strategy</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Route</span><span class="nv"> </span><span class="s">to</span><span class="nv"> </span><span class="s">Property</span><span class="nv"> </span><span class="s">name&quot;</span>

<span class="c1"># Routing Rules</span>
<span class="nt">bulk.address.request</span><span class="p">:</span>
<span class="w">  </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${http.request.uri:equals(&#39;/addressManagement/v1/bulkValidate/request&#39;):and(${http.method:equals(&#39;POST&#39;)})}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Handle</span><span class="nv"> </span><span class="s">bulk</span><span class="nv"> </span><span class="s">address</span><span class="nv"> </span><span class="s">upload</span><span class="nv"> </span><span class="s">requests&quot;</span>

<span class="nt">bulk.address.status</span><span class="p">:</span>
<span class="w">  </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${http.request.uri:matches(&#39;/addressManagement/v1/bulkValidate/[^/]+/status&#39;):and(${http.method:equals(&#39;GET&#39;)})}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Handle</span><span class="nv"> </span><span class="s">status</span><span class="nv"> </span><span class="s">check</span><span class="nv"> </span><span class="s">requests&quot;</span>

<span class="nt">bulk.address.report</span><span class="p">:</span>
<span class="w">  </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${http.request.uri:matches(&#39;/addressManagement/v1/bulkValidate/[^/]+/report&#39;):and(${http.method:equals(&#39;GET&#39;)})}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Handle</span><span class="nv"> </span><span class="s">report</span><span class="nv"> </span><span class="s">generation</span><span class="nv"> </span><span class="s">requests&quot;</span>

<span class="nt">bulk.address.errors</span><span class="p">:</span>
<span class="w">  </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${http.request.uri:matches(&#39;/addressManagement/v1/bulkValidate/[^/]+/errors&#39;):and(${http.method:equals(&#39;GET&#39;)})}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Handle</span><span class="nv"> </span><span class="s">error</span><span class="nv"> </span><span class="s">report</span><span class="nv"> </span><span class="s">requests&quot;</span>
</code></pre></div>
<h3 id="processor-detailed-configuration">Processor Detailed Configuration<a class="headerlink" href="#processor-detailed-configuration" title="Permanent link">&para;</a></h3>
<h4 id="handlehttprequest-processor">HandleHttpRequest Processor<a class="headerlink" href="#handlehttprequest-processor" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;processor_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi.processors.standard.HandleHttpRequest&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;listening_port&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;21014&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allowed_paths&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/addressManagement/v1/bulkValidate/.*&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allow_get&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;true&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allow_post&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;true&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allow_put&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;false&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allow_delete&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;false&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allow_head&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;false&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;allow_options&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;false&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;maximum_threads&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;200&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;http_context_map&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;StandardHttpContextMap&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;container_queue_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;50&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;multipart_request_max_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;10 MB&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;multipart_read_buffer_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;512 KB&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;scheduling&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;strategy&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;TIMER_DRIVEN&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;period&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;5 sec&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;execution_node&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;PRIMARY&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;concurrent_tasks&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="validateauthtoken-processor">ValidateAuthToken Processor<a class="headerlink" href="#validateauthtoken-processor" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;processor_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;com.netcracker.cloud.nifi.processors.security.ValidateAuthToken&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;identity_provider_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;http://identity-provider:8080&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;attribute_to_check&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;http.headers.Authorization&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;relationships&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;success&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Continue processing&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;failure&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Return 401 Unauthorized&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="updateattribute-processors">UpdateAttribute Processors<a class="headerlink" href="#updateattribute-processors" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;get_request_id_and_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;processor_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi.processors.attributes.UpdateAttribute&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;request.mode&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;${http.request.uri:matches(&#39;/addressManagement/v1/([^/]+)/([^/]+)(/[a-z]+(\\\\?.*)*)&#39;):ifElse(${http.request.uri:replaceAll(&#39;/addressManagement/v1/([^/]+)/([^/]+)(/[a-z]+(\\\\?.*)*)&#39;,&#39;$1&#39;)},&#39;&#39;)}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">      </span><span class="nt">&quot;request.id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;${http.request.uri:matches(&#39;/addressManagement/v1/bulkValidate/([^/]+)/[a-z]+(\\\\?.*)*&#39;):ifElse(${http.request.uri:replaceAll(&#39;/addressManagement/v1/bulkValidate/([^/]+)/[a-z]+(\\\\?.*)*&#39;,&#39;$1&#39;)},&#39;&#39;)}&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;set_http_status_200&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;processor_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi.processors.attributes.UpdateAttribute&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;http.status.code&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;200&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;mime.type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;application/json&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;set_http_status_401&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;processor_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi.processors.attributes.UpdateAttribute&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;http.status.code&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;401&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;mime.type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;application/json&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;set_http_status_400&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;processor_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi.processors.attributes.UpdateAttribute&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;http.status.code&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;400&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;mime.type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;application/json&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="controller-services">Controller Services<a class="headerlink" href="#controller-services" title="Permanent link">&para;</a></h2>
<h3 id="database-connection-pool">Database Connection Pool<a class="headerlink" href="#database-connection-pool" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;service_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;com.netcracker.cloud.nifi.service.dbaas.DBaaSAwareConnectionPoolService&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Bulk Price Override DB Connection Pool&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;database_connection_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;database_driver_class_name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.postgresql.Driver&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;database_user&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;${DB_USERNAME}&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;password&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;${DB_PASSWORD}&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_wait_time&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;500 millis&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_total_connections&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;10&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;validation_query&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;SELECT 1&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;controller_service_apis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;org.apache.nifi.dbcp.DBCPService&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="oauth2-access-token-provider">OAuth2 Access Token Provider<a class="headerlink" href="#oauth2-access-token-provider" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;service_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;com.netcracker.cloud.nifi.service.security.OAuth2AccessTokenProviderImpl&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Bulk Address Upload M2mAccessTokenProvider&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;grant_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Client Credentials&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;identity_provider_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;http://identity-provider:8080/auth&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;realm&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;cloud-common&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;client_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;${OAUTH2_CLIENT_ID}&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;client_secret_path&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/etc/secret/&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;controller_service_apis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;org.apache.nifi.oauth2.OAuth2AccessTokenProvider&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;com.netcracker.cloud.nifi.service.security.OAuth2AccessTokenProvider&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="http-context-map">HTTP Context Map<a class="headerlink" href="#http-context-map" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;service_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi.processors.standard.http.StandardHttpContextMap&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;StandardHttpContextMap&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;max_request_size&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;1 MB&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;max_context_life&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;5 mins&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;controller_service_apis&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;org.apache.nifi.http.HttpContextMap&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="schema-registry-services">Schema Registry Services<a class="headerlink" href="#schema-registry-services" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;bulk_address_schema_registry&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;service_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi.schemaregistry.services.AvroSchemaRegistry&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;BulkAddressUpload_AvroTableSchema_PG&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;schemas&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;BulkAddressRequest&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;record&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;BulkAddressRequest&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;namespace&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;fields&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="p">{</span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;requestId&quot;</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">},</span>
<span class="w">          </span><span class="p">{</span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;addresses&quot;</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;array&quot;</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;items&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">}},</span>
<span class="w">          </span><span class="p">{</span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;validationType&quot;</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;null&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">]},</span>
<span class="w">          </span><span class="p">{</span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;timestamp&quot;</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;long&quot;</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;logicalType&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;timestamp-millis&quot;</span><span class="p">}}</span>
<span class="w">        </span><span class="p">]</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;ValidationResponse&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;record&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">        </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ValidationResponse&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;namespace&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;fields&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="p">{</span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;requestId&quot;</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">},</span>
<span class="w">          </span><span class="p">{</span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;status&quot;</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">},</span>
<span class="w">          </span><span class="p">{</span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;results&quot;</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;array&quot;</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;items&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">}},</span>
<span class="w">          </span><span class="p">{</span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;errors&quot;</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;null&quot;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;array&quot;</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;items&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">}]}</span>
<span class="w">        </span><span class="p">]</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="parameter-contexts">Parameter Contexts<a class="headerlink" href="#parameter-contexts" title="Permanent link">&para;</a></h2>
<h3 id="bulk-address-upload-parameters">Bulk Address Upload Parameters<a class="headerlink" href="#bulk-address-upload-parameters" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nt">parameter_context_name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Bulk_Address_Upload&quot;</span>
<span class="nt">parameters</span><span class="p">:</span>
<span class="w">  </span><span class="c1"># Source Configuration</span>
<span class="w">  </span><span class="nt">src</span><span class="p">:</span>
<span class="w">    </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;T&quot;</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Source</span><span class="nv"> </span><span class="s">identifier</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">address</span><span class="nv"> </span><span class="s">validation&quot;</span>
<span class="w">    </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="w">  </span><span class="c1"># Search Configuration  </span>
<span class="w">  </span><span class="nt">maxResults</span><span class="p">:</span>
<span class="w">    </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;5&quot;</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Maximum</span><span class="nv"> </span><span class="s">number</span><span class="nv"> </span><span class="s">of</span><span class="nv"> </span><span class="s">results</span><span class="nv"> </span><span class="s">per</span><span class="nv"> </span><span class="s">search</span><span class="nv"> </span><span class="s">query&quot;</span>
<span class="w">    </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="w">  </span><span class="nt">searchType</span><span class="p">:</span>
<span class="w">    </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;match&quot;</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Type</span><span class="nv"> </span><span class="s">of</span><span class="nv"> </span><span class="s">search</span><span class="nv"> </span><span class="s">to</span><span class="nv"> </span><span class="s">perform</span><span class="nv"> </span><span class="s">(match,</span><span class="nv"> </span><span class="s">fuzzy,</span><span class="nv"> </span><span class="s">exact)&quot;</span>
<span class="w">    </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="w">  </span><span class="nt">action</span><span class="p">:</span>
<span class="w">    </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;query&quot;</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Default</span><span class="nv"> </span><span class="s">action</span><span class="nv"> </span><span class="s">type</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">processing&quot;</span>
<span class="w">    </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="w">  </span><span class="c1"># Gateway Configuration</span>
<span class="w">  </span><span class="nt">internal-gw</span><span class="p">:</span>
<span class="w">    </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://cloud-integration-platform-engine:8080/&quot;</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Internal</span><span class="nv"> </span><span class="s">gateway</span><span class="nv"> </span><span class="s">URL</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">CIP</span><span class="nv"> </span><span class="s">integration&quot;</span>
<span class="w">    </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="w">  </span><span class="nt">qes-gw</span><span class="p">:</span>
<span class="w">    </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://quotation-engine-service:8080/&quot;</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Quote</span><span class="nv"> </span><span class="s">Engine</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">gateway</span><span class="nv"> </span><span class="s">URL&quot;</span>
<span class="w">    </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="w">  </span><span class="c1"># Service References</span>
<span class="w">  </span><span class="s">&quot;Request</span><span class="nv"> </span><span class="s">OAuth2</span><span class="nv"> </span><span class="s">Access</span><span class="nv"> </span><span class="s">Token</span><span class="nv"> </span><span class="s">Provider&quot;</span><span class="p p-Indicator">:</span>
<span class="w">    </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;a73f28ed-631b-3dd2-94f6-9c22c42f7cf3&quot;</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Reference</span><span class="nv"> </span><span class="s">to</span><span class="nv"> </span><span class="s">OAuth2</span><span class="nv"> </span><span class="s">token</span><span class="nv"> </span><span class="s">provider</span><span class="nv"> </span><span class="s">service&quot;</span>
<span class="w">    </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="w">  </span><span class="s">&quot;JDBC</span><span class="nv"> </span><span class="s">Connection</span><span class="nv"> </span><span class="s">Pool&quot;</span><span class="p p-Indicator">:</span>
<span class="w">    </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;bc81b8ca-afcd-3248-7279-f64167eb14bb&quot;</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Reference</span><span class="nv"> </span><span class="s">to</span><span class="nv"> </span><span class="s">database</span><span class="nv"> </span><span class="s">connection</span><span class="nv"> </span><span class="s">pool</span><span class="nv"> </span><span class="s">service&quot;</span>
<span class="w">    </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>
</code></pre></div>
<h3 id="environment-specific-parameters">Environment-Specific Parameters<a class="headerlink" href="#environment-specific-parameters" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Development Environment</span>
<span class="nt">development</span><span class="p">:</span>
<span class="w">  </span><span class="nt">parameters</span><span class="p">:</span>
<span class="w">    </span><span class="nt">internal-gw</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://cip-dev:8080/&quot;</span>
<span class="w">    </span><span class="nt">qes-gw</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://qes-dev:8080/&quot;</span>
<span class="w">    </span><span class="nt">maxResults</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;10&quot;</span>

<span class="c1"># Staging Environment  </span>
<span class="nt">staging</span><span class="p">:</span>
<span class="w">  </span><span class="nt">parameters</span><span class="p">:</span>
<span class="w">    </span><span class="nt">internal-gw</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://cip-staging:8080/&quot;</span>
<span class="w">    </span><span class="nt">qes-gw</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://qes-staging:8080/&quot;</span>
<span class="w">    </span><span class="nt">maxResults</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;5&quot;</span>

<span class="c1"># Production Environment</span>
<span class="nt">production</span><span class="p">:</span>
<span class="w">  </span><span class="nt">parameters</span><span class="p">:</span>
<span class="w">    </span><span class="nt">internal-gw</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://cloud-integration-platform-engine:8080/&quot;</span>
<span class="w">    </span><span class="nt">qes-gw</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://quotation-engine-service:8080/&quot;</span>
<span class="w">    </span><span class="nt">maxResults</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;5&quot;</span>
</code></pre></div>
<h2 id="processor-configuration">Processor Configuration<a class="headerlink" href="#processor-configuration" title="Permanent link">&para;</a></h2>
<h3 id="request-processing-flow">Request Processing Flow<a class="headerlink" href="#request-processing-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Request Validation&quot;
        A[Extract Request Attributes]
        B[Validate Authorization Token]
        C[Parse Request URI]
        D[Clean Security Headers]
    end

    subgraph &quot;Request Routing&quot;
        E[Route by Path Pattern]
        F[Set Request Type]
        G[Extract Request ID]
    end

    subgraph &quot;Business Processing&quot;
        H[Bulk Address Processing]
        I[Status Query Processing]
        J[Report Generation]
        K[Error Processing]
    end

    A --&gt; B
    B --&gt; C
    C --&gt; D
    D --&gt; E
    E --&gt; F
    F --&gt; G

    G --&gt; H
    G --&gt; I
    G --&gt; J
    G --&gt; K

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style E fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style H fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="response-generation">Response Generation<a class="headerlink" href="#response-generation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;response_processors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;unauthorized_response&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;processor_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi.processors.standard.ReplaceText&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;replacement_value&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;{\&quot;errors\&quot;:[{\&quot;code\&quot;:\&quot;AM-BAV-0401\&quot;,\&quot;reason\&quot;:\&quot;Unauthorized\&quot;}]}&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;evaluation_mode&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Entire text&quot;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;not_found_response&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;processor_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi.processors.standard.ReplaceText&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">      </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;replacement_value&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;{\&quot;errors\&quot;:[{\&quot;code\&quot;:\&quot;AM-BAV-0404\&quot;,\&quot;reason\&quot;:\&quot;Not found\&quot;}]}&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;evaluation_mode&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Entire text&quot;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;default_error_response&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;processor_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi.processors.standard.ReplaceText&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;replacement_value&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;{\&quot;errors\&quot;:[{\&quot;code\&quot;:\&quot;AM-BAV-0100\&quot;,\&quot;reason\&quot;:\&quot;Failed to initiate bulk address validate request\&quot;}]}&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;evaluation_mode&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Entire text&quot;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="error-handling">Error Handling<a class="headerlink" href="#error-handling" title="Permanent link">&para;</a></h2>
<h3 id="error-response-patterns">Error Response Patterns<a class="headerlink" href="#error-response-patterns" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Error Types&quot;
        A[Authentication Errors]
        B[Validation Errors]
        C[Processing Errors]
        D[System Errors]
    end

    subgraph &quot;Error Codes&quot;
        E[AM-BAV-0401: Unauthorized]
        F[AM-BAV-0404: Not Found]
        G[AM-BAV-0400: Bad Request]
        H[AM-BAV-0100: Processing Failed]
    end

    subgraph &quot;Error Handling&quot;
        I[Set Error Status Code]
        J[Generate Error Response]
        K[Log Error Details]
        L[Return Error Response]
    end

    A --&gt; E
    B --&gt; F
    C --&gt; G
    D --&gt; H

    E --&gt; I
    F --&gt; I
    G --&gt; I
    H --&gt; I

    I --&gt; J
    J --&gt; K
    K --&gt; L

    style A fill:#ff6b6b,stroke:#e55656,color:#ffffff
    style I fill:#ffa726,stroke:#ff9800,color:#ffffff
    style L fill:#66bb6a,stroke:#4caf50,color:#ffffff
</code></pre></div>
<h3 id="error-response-format">Error Response Format<a class="headerlink" href="#error-response-format" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error_response_schema&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;object&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;errors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;array&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;items&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;object&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Error code identifier&quot;</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="nt">&quot;reason&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">              </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Human-readable error description&quot;</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;object&quot;</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Additional error context (optional)&quot;</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">          </span><span class="p">},</span>
<span class="w">          </span><span class="nt">&quot;required&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;code&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;reason&quot;</span><span class="p">]</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;required&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;errors&quot;</span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="retry-and-recovery">Retry and Recovery<a class="headerlink" href="#retry-and-recovery" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Processor Retry Configuration</span>
<span class="nt">retry_configuration</span><span class="p">:</span>
<span class="w">  </span><span class="nt">retry_count</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">  </span><span class="nt">retry_relationships</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;failure&quot;</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">backoff_mechanism</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;PENALIZE_FLOWFILE&quot;</span>
<span class="w">  </span><span class="nt">max_backoff_period</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;10</span><span class="nv"> </span><span class="s">mins&quot;</span>
<span class="w">  </span><span class="nt">penalty_duration</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;30</span><span class="nv"> </span><span class="s">sec&quot;</span>

<span class="c1"># Flow File Expiration</span>
<span class="nt">flow_file_expiration</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;15</span><span class="nv"> </span><span class="s">min&quot;</span>

<span class="c1"># Back Pressure Configuration</span>
<span class="nt">back_pressure</span><span class="p">:</span>
<span class="w">  </span><span class="nt">object_threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10000</span>
<span class="w">  </span><span class="nt">data_size_threshold</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1</span><span class="nv"> </span><span class="s">GB&quot;</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../02-api-integration/">API Integration →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>