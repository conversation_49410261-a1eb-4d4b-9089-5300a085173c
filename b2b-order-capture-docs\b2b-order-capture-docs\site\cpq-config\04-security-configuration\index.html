
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/cpq-config/04-security-configuration/">
      
      
        <link rel="prev" href="../03-service-catalog/">
      
      
        <link rel="next" href="../05-development-guide/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Security Configuration - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#security-configuration" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Security Configuration
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" checked>
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Security Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#security-layers" class="md-nav__link">
    <span class="md-ellipsis">
      Security Layers
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Security Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#tls-certificate-management" class="md-nav__link">
    <span class="md-ellipsis">
      TLS Certificate Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TLS Certificate Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#certificate-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Certificate Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#gateway-tls-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Gateway TLS Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cluster-tls-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Cluster TLS Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#certificate-lifecycle-management" class="md-nav__link">
    <span class="md-ellipsis">
      Certificate Lifecycle Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#authentication-and-authorization" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication and Authorization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Authentication and Authorization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#oauth-20-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      OAuth 2.0 Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#jwt-token-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      JWT Token Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#role-based-access-control-rbac" class="md-nav__link">
    <span class="md-ellipsis">
      Role-Based Access Control (RBAC)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#network-security" class="md-nav__link">
    <span class="md-ellipsis">
      Network Security
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Network Security">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#network-policies" class="md-nav__link">
    <span class="md-ellipsis">
      Network Policies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#firewall-rules" class="md-nav__link">
    <span class="md-ellipsis">
      Firewall Rules
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#secrets-management" class="md-nav__link">
    <span class="md-ellipsis">
      Secrets Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Secrets Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#kubernetes-secrets-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Kubernetes Secrets Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#secret-rotation-policy" class="md-nav__link">
    <span class="md-ellipsis">
      Secret Rotation Policy
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-monitoring" class="md-nav__link">
    <span class="md-ellipsis">
      Security Monitoring
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Monitoring">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#security-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Security Metrics
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-scanning" class="md-nav__link">
    <span class="md-ellipsis">
      Security Scanning
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Security Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#security-layers" class="md-nav__link">
    <span class="md-ellipsis">
      Security Layers
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Security Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#tls-certificate-management" class="md-nav__link">
    <span class="md-ellipsis">
      TLS Certificate Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TLS Certificate Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#certificate-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Certificate Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#gateway-tls-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Gateway TLS Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cluster-tls-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Cluster TLS Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#certificate-lifecycle-management" class="md-nav__link">
    <span class="md-ellipsis">
      Certificate Lifecycle Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#authentication-and-authorization" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication and Authorization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Authentication and Authorization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#oauth-20-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      OAuth 2.0 Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#jwt-token-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      JWT Token Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#role-based-access-control-rbac" class="md-nav__link">
    <span class="md-ellipsis">
      Role-Based Access Control (RBAC)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#network-security" class="md-nav__link">
    <span class="md-ellipsis">
      Network Security
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Network Security">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#network-policies" class="md-nav__link">
    <span class="md-ellipsis">
      Network Policies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#firewall-rules" class="md-nav__link">
    <span class="md-ellipsis">
      Firewall Rules
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#secrets-management" class="md-nav__link">
    <span class="md-ellipsis">
      Secrets Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Secrets Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#kubernetes-secrets-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Kubernetes Secrets Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#secret-rotation-policy" class="md-nav__link">
    <span class="md-ellipsis">
      Secret Rotation Policy
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-monitoring" class="md-nav__link">
    <span class="md-ellipsis">
      Security Monitoring
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Monitoring">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#security-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Security Metrics
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-scanning" class="md-nav__link">
    <span class="md-ellipsis">
      Security Scanning
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="security-configuration">Security Configuration<a class="headerlink" href="#security-configuration" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#security-architecture-overview">Security Architecture Overview</a></li>
<li><a href="#tls-certificate-management">TLS Certificate Management</a></li>
<li><a href="#authentication-and-authorization">Authentication and Authorization</a></li>
<li><a href="#network-security">Network Security</a></li>
<li><a href="#secrets-management">Secrets Management</a></li>
<li><a href="#security-monitoring">Security Monitoring</a></li>
</ul>
<h2 id="security-architecture-overview">Security Architecture Overview<a class="headerlink" href="#security-architecture-overview" title="Permanent link">&para;</a></h2>
<h3 id="security-layers">Security Layers<a class="headerlink" href="#security-layers" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Security Architecture&quot;
        A[Network Security]
        B[Gateway Security]
        C[Service Mesh Security]
        D[Application Security]
        E[Data Security]
    end

    subgraph &quot;Network Layer&quot;
        F[Firewall Rules]
        G[Network Policies]
        H[VPC Isolation]
        I[Load Balancer Security]
    end

    subgraph &quot;Gateway Layer&quot;
        J[TLS Termination]
        K[Certificate Management]
        L[API Gateway Security]
        M[Rate Limiting]
    end

    subgraph &quot;Service Mesh&quot;
        N[Mutual TLS]
        O[Service-to-Service Auth]
        P[Traffic Encryption]
        Q[Policy Enforcement]
    end

    subgraph &quot;Application Layer&quot;
        R[OAuth 2.0]
        S[JWT Tokens]
        T[RBAC]
        U[API Key Management]
    end

    subgraph &quot;Data Layer&quot;
        V[Encryption at Rest]
        W[Encryption in Transit]
        X[Key Management]
        Y[Data Classification]
    end

    A --&gt; F
    A --&gt; G
    A --&gt; H
    A --&gt; I

    B --&gt; J
    B --&gt; K
    B --&gt; L
    B --&gt; M

    C --&gt; N
    C --&gt; O
    C --&gt; P
    C --&gt; Q

    D --&gt; R
    D --&gt; S
    D --&gt; T
    D --&gt; U

    E --&gt; V
    E --&gt; W
    E --&gt; X
    E --&gt; Y

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style C fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="security-flow">Security Flow<a class="headerlink" href="#security-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Client as External Client
    participant Gateway as API Gateway
    participant Mesh as Service Mesh
    participant Service as CPQ Service
    participant Backend as Backend Service

    Client-&gt;&gt;Gateway: HTTPS Request + API Key
    Gateway-&gt;&gt;Gateway: Validate API Key
    Gateway-&gt;&gt;Gateway: Rate Limiting Check
    Gateway-&gt;&gt;Mesh: Forward Request (mTLS)

    Mesh-&gt;&gt;Mesh: Validate Service Certificate
    Mesh-&gt;&gt;Service: Encrypted Request
    Service-&gt;&gt;Service: Validate JWT Token
    Service-&gt;&gt;Backend: Service Call (mTLS)

    Backend--&gt;&gt;Service: Encrypted Response
    Service--&gt;&gt;Mesh: Encrypted Response
    Mesh--&gt;&gt;Gateway: Encrypted Response
    Gateway--&gt;&gt;Client: HTTPS Response
</code></pre></div>
<h2 id="tls-certificate-management">TLS Certificate Management<a class="headerlink" href="#tls-certificate-management" title="Permanent link">&para;</a></h2>
<h3 id="certificate-structure">Certificate Structure<a class="headerlink" href="#certificate-structure" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>TLS-config/
├── cert/
│   ├── gateway/                    # Gateway certificates
│   │   ├── QA/
│   │   │   ├── atp-itf-tls-def.json
│   │   │   ├── gateway-cert.pem
│   │   │   ├── gateway-key.pem
│   │   │   └── ca-bundle.pem
│   │   ├── STAGING/
│   │   │   ├── atp-itf-tls-def.json
│   │   │   ├── gateway-cert.pem
│   │   │   ├── gateway-key.pem
│   │   │   └── ca-bundle.pem
│   │   └── PRODUCTION/
│   │       ├── atp-itf-tls-def.json
│   │       ├── gateway-cert.pem
│   │       ├── gateway-key.pem
│   │       └── ca-bundle.pem
│   └── cluster/                    # Cluster certificates
│       ├── QA/
│       │   ├── atp-itf-tls-def.json
│       │   ├── cluster-cert.pem
│       │   ├── cluster-key.pem
│       │   └── ca-bundle.pem
│       ├── STAGING/
│       └── PRODUCTION/
</code></pre></div>
<h3 id="gateway-tls-configuration">Gateway TLS Configuration<a class="headerlink" href="#gateway-tls-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// TLS-config/cert/gateway/QA/atp-itf-tls-def.json</span>
<span class="p">[</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;apiVersion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;nc.core.mesh/v3&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;kind&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;TlsDef&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;metadata&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;telus-itf-tls-config-gw&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;namespace&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;cpq-config-qa&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;labels&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;environment&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;qa&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;component&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;gateway&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;managed-by&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;cpq-config&quot;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;spec&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;telus-itf-tls-config-gw&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;trustedForGateways&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;egress-gateway&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;ingress-gateway&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;tls&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;insecure&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;mode&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;MUTUAL&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;caCertificates&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/etc/ssl/certs/ca-bundle.pem&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;privateKey&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/etc/ssl/private/gateway-key.pem&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;serverCertificate&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/etc/ssl/certs/gateway-cert.pem&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;clientCertificate&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/etc/ssl/certs/client-cert.pem&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;verifySubjectAltName&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="s2">&quot;*.telus.com&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;*.telus.internal&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;cpq-config-qa.telus.com&quot;</span>
<span class="w">        </span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;minProtocolVersion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;TLSV1_2&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;maxProtocolVersion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;TLSV1_3&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;cipherSuites&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="s2">&quot;ECDHE-RSA-AES256-GCM-SHA384&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;ECDHE-RSA-AES128-GCM-SHA256&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;ECDHE-RSA-AES256-SHA384&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;ECDHE-RSA-AES128-SHA256&quot;</span>
<span class="w">        </span><span class="p">]</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">]</span>
</code></pre></div>
<h3 id="cluster-tls-configuration">Cluster TLS Configuration<a class="headerlink" href="#cluster-tls-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// TLS-config/cert/cluster/PRODUCTION/atp-itf-tls-def.json</span>
<span class="p">[</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;apiVersion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;nc.core.mesh/v3&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;kind&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;TlsDef&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;metadata&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;telus-itf-tls-config-cluster&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;namespace&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;cpq-config-prod&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;labels&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;environment&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;production&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;component&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;cluster&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;managed-by&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;cpq-config&quot;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;spec&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;telus-itf-tls-config-cluster&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;trustedForClusters&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;telus-b2b-cluster&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;tls&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;insecure&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;mode&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;MUTUAL&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;caCertificates&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/etc/ssl/certs/ca-bundle.pem&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;privateKey&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/etc/ssl/private/cluster-key.pem&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;serverCertificate&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/etc/ssl/certs/cluster-cert.pem&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;clientCertificate&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/etc/ssl/certs/cluster-client-cert.pem&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;verifySubjectAltName&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="s2">&quot;*.telus.com&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;cpq-config.telus.com&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;*.cpq-config-prod.svc.cluster.local&quot;</span>
<span class="w">        </span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;minProtocolVersion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;TLSV1_3&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;maxProtocolVersion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;TLSV1_3&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;cipherSuites&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="s2">&quot;TLS_AES_256_GCM_SHA384&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;TLS_CHACHA20_POLY1305_SHA256&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="s2">&quot;TLS_AES_128_GCM_SHA256&quot;</span>
<span class="w">        </span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;sessionTimeout&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;300s&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;sessionTickets&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">]</span>
</code></pre></div>
<h3 id="certificate-lifecycle-management">Certificate Lifecycle Management<a class="headerlink" href="#certificate-lifecycle-management" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Certificate lifecycle configuration</span>
<span class="nt">certificate-management</span><span class="p">:</span>
<span class="w">  </span><span class="nt">lifecycle</span><span class="p">:</span>
<span class="w">    </span><span class="nt">validity-period</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;365</span><span class="nv"> </span><span class="s">days&quot;</span>
<span class="w">    </span><span class="nt">renewal-threshold</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;30</span><span class="nv"> </span><span class="s">days&quot;</span>
<span class="w">    </span><span class="nt">rotation-schedule</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;quarterly&quot;</span>

<span class="w">  </span><span class="nt">monitoring</span><span class="p">:</span>
<span class="w">    </span><span class="nt">expiry-alerts</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">threshold</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;30</span><span class="nv"> </span><span class="s">days&quot;</span>
<span class="w">        </span><span class="nt">severity</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;warning&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">threshold</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;7</span><span class="nv"> </span><span class="s">days&quot;</span>
<span class="w">        </span><span class="nt">severity</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;critical&quot;</span>

<span class="w">  </span><span class="nt">automation</span><span class="p">:</span>
<span class="w">    </span><span class="nt">auto-renewal</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">validation-checks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">certificate-chain</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">private-key-match</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">subject-alt-names</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">expiry-date</span>

<span class="w">  </span><span class="nt">environments</span><span class="p">:</span>
<span class="w">    </span><span class="nt">qa</span><span class="p">:</span>
<span class="w">      </span><span class="nt">ca-authority</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TELUS</span><span class="nv"> </span><span class="s">Internal</span><span class="nv"> </span><span class="s">CA</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">QA&quot;</span>
<span class="w">      </span><span class="nt">key-size</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2048</span>
<span class="w">      </span><span class="nt">signature-algorithm</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;SHA256withRSA&quot;</span>

<span class="w">    </span><span class="nt">production</span><span class="p">:</span>
<span class="w">      </span><span class="nt">ca-authority</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TELUS</span><span class="nv"> </span><span class="s">Internal</span><span class="nv"> </span><span class="s">CA</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">Production&quot;</span>
<span class="w">      </span><span class="nt">key-size</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">4096</span>
<span class="w">      </span><span class="nt">signature-algorithm</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;SHA256withRSA&quot;</span>
<span class="w">      </span><span class="nt">extended-validation</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</code></pre></div>
<h2 id="authentication-and-authorization">Authentication and Authorization<a class="headerlink" href="#authentication-and-authorization" title="Permanent link">&para;</a></h2>
<h3 id="oauth-20-configuration">OAuth 2.0 Configuration<a class="headerlink" href="#oauth-20-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># OAuth 2.0 authentication configuration</span>
<span class="nt">oauth2-config</span><span class="p">:</span>
<span class="w">  </span><span class="nt">authorization-server</span><span class="p">:</span>
<span class="w">    </span><span class="nt">issuer</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://auth.telus.com&quot;</span>
<span class="w">    </span><span class="nt">authorization-endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://auth.telus.com/oauth2/authorize&quot;</span>
<span class="w">    </span><span class="nt">token-endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://auth.telus.com/oauth2/token&quot;</span>
<span class="w">    </span><span class="nt">jwks-uri</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://auth.telus.com/.well-known/jwks.json&quot;</span>

<span class="w">  </span><span class="nt">client-credentials</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpq-config-service</span><span class="p">:</span>
<span class="w">      </span><span class="nt">client-id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${CPQ_KONG_AUTH_CLIENT_ID}&quot;</span>
<span class="w">      </span><span class="nt">client-secret</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${CPQ_KONG_AUTH_CLIENT_SECRET}&quot;</span>
<span class="w">      </span><span class="nt">scope</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;cpq:config:read</span><span class="nv"> </span><span class="s">cpq:config:write&quot;</span>

<span class="w">    </span><span class="nt">sfdc-integration</span><span class="p">:</span>
<span class="w">      </span><span class="nt">client-id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${SFDC_CLIENT_ID}&quot;</span>
<span class="w">      </span><span class="nt">client-secret</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${SFDC_CLIENT_SECRET}&quot;</span>
<span class="w">      </span><span class="nt">scope</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;api</span><span class="nv"> </span><span class="s">refresh_token&quot;</span>

<span class="w">  </span><span class="nt">token-validation</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cache-duration</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;300s&quot;</span>
<span class="w">    </span><span class="nt">clock-skew</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;30s&quot;</span>
<span class="w">    </span><span class="nt">required-claims</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;iss&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;sub&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;aud&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;exp&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;iat&quot;</span>

<span class="w">  </span><span class="nt">scopes</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpq:config:read</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Read</span><span class="nv"> </span><span class="s">CPQ</span><span class="nv"> </span><span class="s">configuration&quot;</span>
<span class="w">    </span><span class="nt">cpq:config:write</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Write</span><span class="nv"> </span><span class="s">CPQ</span><span class="nv"> </span><span class="s">configuration&quot;</span>
<span class="w">    </span><span class="nt">cpq:chains:execute</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Execute</span><span class="nv"> </span><span class="s">integration</span><span class="nv"> </span><span class="s">chains&quot;</span>
<span class="w">    </span><span class="nt">cpq:services:invoke</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Invoke</span><span class="nv"> </span><span class="s">external</span><span class="nv"> </span><span class="s">services&quot;</span>
</code></pre></div>
<h3 id="jwt-token-configuration">JWT Token Configuration<a class="headerlink" href="#jwt-token-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># JWT token configuration</span>
<span class="nt">jwt-config</span><span class="p">:</span>
<span class="w">  </span><span class="nt">signing</span><span class="p">:</span>
<span class="w">    </span><span class="nt">algorithm</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;RS256&quot;</span>
<span class="w">    </span><span class="nt">key-id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;telus-cpq-2024&quot;</span>

<span class="w">  </span><span class="nt">validation</span><span class="p">:</span>
<span class="w">    </span><span class="nt">verify-signature</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">verify-expiration</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">verify-not-before</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">verify-audience</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">verify-issuer</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">  </span><span class="nt">claims</span><span class="p">:</span>
<span class="w">    </span><span class="nt">issuer</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://auth.telus.com&quot;</span>
<span class="w">    </span><span class="nt">audience</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;cpq-config-service&quot;</span>
<span class="w">    </span><span class="nt">expiration</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;3600s&quot;</span>
<span class="w">    </span><span class="nt">not-before-skew</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;30s&quot;</span>

<span class="w">  </span><span class="nt">custom-claims</span><span class="p">:</span>
<span class="w">    </span><span class="nt">tenant</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;telus-b2b&quot;</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${ENVIRONMENT}&quot;</span>
<span class="w">    </span><span class="nt">permissions</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[]</span>
<span class="w">    </span><span class="nt">roles</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[]</span>
</code></pre></div>
<h3 id="role-based-access-control-rbac">Role-Based Access Control (RBAC)<a class="headerlink" href="#role-based-access-control-rbac" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># RBAC configuration</span>
<span class="nt">rbac-config</span><span class="p">:</span>
<span class="w">  </span><span class="nt">roles</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpq-admin</span><span class="p">:</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Full</span><span class="nv"> </span><span class="s">CPQ</span><span class="nv"> </span><span class="s">configuration</span><span class="nv"> </span><span class="s">access&quot;</span>
<span class="w">      </span><span class="nt">permissions</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:config:*&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:chains:*&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:services:*&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:security:*&quot;</span>

<span class="w">    </span><span class="nt">cpq-developer</span><span class="p">:</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;CPQ</span><span class="nv"> </span><span class="s">development</span><span class="nv"> </span><span class="s">access&quot;</span>
<span class="w">      </span><span class="nt">permissions</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:config:read&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:config:write&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:chains:read&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:chains:execute&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:services:read&quot;</span>

<span class="w">    </span><span class="nt">cpq-operator</span><span class="p">:</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;CPQ</span><span class="nv"> </span><span class="s">operations</span><span class="nv"> </span><span class="s">access&quot;</span>
<span class="w">      </span><span class="nt">permissions</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:config:read&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:chains:read&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:chains:execute&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:services:read&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:monitoring:read&quot;</span>

<span class="w">    </span><span class="nt">cpq-viewer</span><span class="p">:</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Read-only</span><span class="nv"> </span><span class="s">CPQ</span><span class="nv"> </span><span class="s">access&quot;</span>
<span class="w">      </span><span class="nt">permissions</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:config:read&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:chains:read&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cpq:services:read&quot;</span>

<span class="w">  </span><span class="nt">users</span><span class="p">:</span>
<span class="w">    </span><span class="nt">service-accounts</span><span class="p">:</span>
<span class="w">      </span><span class="nt">cpq-config-service</span><span class="p">:</span>
<span class="w">        </span><span class="nt">roles</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;cpq-admin&quot;</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">environment</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;all&quot;</span>

<span class="w">      </span><span class="nt">integration-service</span><span class="p">:</span>
<span class="w">        </span><span class="nt">roles</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;cpq-operator&quot;</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">environment</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;production&quot;</span>

<span class="w">  </span><span class="nt">policies</span><span class="p">:</span>
<span class="w">    </span><span class="nt">environment-isolation</span><span class="p">:</span>
<span class="w">      </span><span class="nt">qa-users</span><span class="p">:</span>
<span class="w">        </span><span class="nt">allowed-environments</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;qa&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;development&quot;</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">denied-environments</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;production&quot;</span><span class="p p-Indicator">]</span>

<span class="w">      </span><span class="nt">prod-users</span><span class="p">:</span>
<span class="w">        </span><span class="nt">allowed-environments</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;production&quot;</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">requires-approval</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</code></pre></div>
<h2 id="network-security">Network Security<a class="headerlink" href="#network-security" title="Permanent link">&para;</a></h2>
<h3 id="network-policies">Network Policies<a class="headerlink" href="#network-policies" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Kubernetes Network Policies</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">networking.k8s.io/v1</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">NetworkPolicy</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cpq-config-network-policy</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cpq-config-prod</span>
<span class="nt">spec</span><span class="p">:</span>
<span class="w">  </span><span class="nt">podSelector</span><span class="p">:</span>
<span class="w">    </span><span class="nt">matchLabels</span><span class="p">:</span>
<span class="w">      </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cpq-config</span>
<span class="w">  </span><span class="nt">policyTypes</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Ingress</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Egress</span>

<span class="w">  </span><span class="nt">ingress</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">from</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">namespaceSelector</span><span class="p">:</span>
<span class="w">        </span><span class="nt">matchLabels</span><span class="p">:</span>
<span class="w">          </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cpq-engine</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">podSelector</span><span class="p">:</span>
<span class="w">        </span><span class="nt">matchLabels</span><span class="p">:</span>
<span class="w">          </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">api-gateway</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">TCP</span>
<span class="w">      </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8080</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">TCP</span>
<span class="w">      </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8443</span>

<span class="w">  </span><span class="nt">egress</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">to</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">namespaceSelector</span><span class="p">:</span>
<span class="w">        </span><span class="nt">matchLabels</span><span class="p">:</span>
<span class="w">          </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">external-services</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">TCP</span>
<span class="w">      </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">443</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">TCP</span>
<span class="w">      </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">80</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">to</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[]</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">TCP</span>
<span class="w">      </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">53</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">UDP</span>
<span class="w">      </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">53</span>
</code></pre></div>
<h3 id="firewall-rules">Firewall Rules<a class="headerlink" href="#firewall-rules" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Firewall configuration</span>
<span class="nt">firewall-rules</span><span class="p">:</span>
<span class="w">  </span><span class="nt">ingress</span><span class="p">:</span>
<span class="w">    </span><span class="nt">allowed-sources</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">cidr</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;10.0.0.0/8&quot;</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Internal</span><span class="nv"> </span><span class="s">TELUS</span><span class="nv"> </span><span class="s">network&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">cidr</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;**********/12&quot;</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Private</span><span class="nv"> </span><span class="s">network</span><span class="nv"> </span><span class="s">range&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">cidr</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;***********/16&quot;</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Local</span><span class="nv"> </span><span class="s">network</span><span class="nv"> </span><span class="s">range&quot;</span>

<span class="w">    </span><span class="nt">allowed-ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">443</span>
<span class="w">        </span><span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TCP&quot;</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;HTTPS</span><span class="nv"> </span><span class="s">traffic&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8443</span>
<span class="w">        </span><span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TCP&quot;</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Secure</span><span class="nv"> </span><span class="s">management&quot;</span>

<span class="w">  </span><span class="nt">egress</span><span class="p">:</span>
<span class="w">    </span><span class="nt">allowed-destinations</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">fqdn</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;*.telus.com&quot;</span>
<span class="w">        </span><span class="nt">ports</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">443</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">80</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TELUS</span><span class="nv"> </span><span class="s">services&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">fqdn</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;*.salesforce.com&quot;</span>
<span class="w">        </span><span class="nt">ports</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">443</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;SFDC</span><span class="nv"> </span><span class="s">integration&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">cidr</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;10.0.0.0/8&quot;</span>
<span class="w">        </span><span class="nt">ports</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">443</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">80</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">8080</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Internal</span><span class="nv"> </span><span class="s">services&quot;</span>

<span class="w">  </span><span class="nt">blocked</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">cidr</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;0.0.0.0/0&quot;</span>
<span class="w">      </span><span class="nt">ports</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">22</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">23</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">3389</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Block</span><span class="nv"> </span><span class="s">remote</span><span class="nv"> </span><span class="s">access</span><span class="nv"> </span><span class="s">protocols&quot;</span>
</code></pre></div>
<h2 id="secrets-management">Secrets Management<a class="headerlink" href="#secrets-management" title="Permanent link">&para;</a></h2>
<h3 id="kubernetes-secrets-configuration">Kubernetes Secrets Configuration<a class="headerlink" href="#kubernetes-secrets-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Secured variables secret template</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">v1</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Secret</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cip-secured-variables</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.NAMESPACE</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span>
<span class="w">    </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cip-config</span>
<span class="w">    </span><span class="nt">component</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">configuration</span>
<span class="w">    </span><span class="nt">managed-by</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">helm</span>
<span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Opaque</span>
<span class="nt">stringData</span><span class="p">:</span>
<span class="w">  </span><span class="c1"># Authentication credentials</span>
<span class="w">  </span><span class="nt">CIP_KONG_AUTH_CLIENT_ID</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.CIP_KONG_AUTH_CLIENT_ID | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">CIP_KONG_AUTH_CLIENT_SECRET</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.CIP_KONG_AUTH_CLIENT_SECRET | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>

<span class="w">  </span><span class="c1"># Billing system credentials</span>
<span class="w">  </span><span class="nt">APPLY_BILL_CREDIT_AUTH_USERNAME</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.APPLY_BILL_CREDIT_AUTH_USERNAME | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">APPLY_BILL_CREDIT_AUTH_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.APPLY_BILL_CREDIT_AUTH_PASSWORD | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>

<span class="w">  </span><span class="c1"># Kafka credentials</span>
<span class="w">  </span><span class="nt">KAFKA_CLIENT_USERNAME</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.KAFKA_CLIENT_USERNAME | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">KAFKA_CLIENT_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.KAFKA_CLIENT_PASSWORD | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">CIP_OM_KAFKA_SASL_JAAS_CONFIG</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.KAFKA_SASL_JAAS_CONFIG | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>

<span class="w">  </span><span class="c1"># SFDC credentials</span>
<span class="w">  </span><span class="nt">SFDC_CLIENT_ID</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.SFDC_CLIENT_ID | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">SFDC_CLIENT_SECRET</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.SFDC_CLIENT_SECRET | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">SFDC_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.SFDC_PASSWORD | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">SFDC_USERNAME</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.SFDC_USERNAME | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">SFDC_SECURITY_TOKEN</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.SFDC_SECURITY_TOKEN | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>

<span class="w">  </span><span class="c1"># Database credentials</span>
<span class="w">  </span><span class="nt">DATABASE_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.DATABASE_PASSWORD | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>

<span class="w">  </span><span class="c1"># Encryption keys</span>
<span class="w">  </span><span class="nt">ENCRYPTION_KEY</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.ENCRYPTION_KEY | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">SIGNING_KEY</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.SIGNING_KEY | quote</span><span class="w"> </span><span class="p p-Indicator">}}</span>
</code></pre></div>
<h3 id="secret-rotation-policy">Secret Rotation Policy<a class="headerlink" href="#secret-rotation-policy" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Secret rotation configuration</span>
<span class="nt">secret-rotation</span><span class="p">:</span>
<span class="w">  </span><span class="nt">policy</span><span class="p">:</span>
<span class="w">    </span><span class="nt">rotation-interval</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;90</span><span class="nv"> </span><span class="s">days&quot;</span>
<span class="w">    </span><span class="nt">notification-threshold</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;14</span><span class="nv"> </span><span class="s">days&quot;</span>
<span class="w">    </span><span class="nt">emergency-rotation</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;immediate&quot;</span>

<span class="w">  </span><span class="nt">automation</span><span class="p">:</span>
<span class="w">    </span><span class="nt">auto-rotation</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">validation-checks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">connectivity-test</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">authentication-test</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">authorization-test</span>

<span class="w">  </span><span class="nt">secrets</span><span class="p">:</span>
<span class="w">    </span><span class="nt">sfdc-credentials</span><span class="p">:</span>
<span class="w">      </span><span class="nt">rotation-interval</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;60</span><span class="nv"> </span><span class="s">days&quot;</span>
<span class="w">      </span><span class="nt">validation-endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/services/data/v58.0/limits&quot;</span>

<span class="w">    </span><span class="nt">kafka-credentials</span><span class="p">:</span>
<span class="w">      </span><span class="nt">rotation-interval</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;90</span><span class="nv"> </span><span class="s">days&quot;</span>
<span class="w">      </span><span class="nt">validation-method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;connection-test&quot;</span>

<span class="w">    </span><span class="nt">database-credentials</span><span class="p">:</span>
<span class="w">      </span><span class="nt">rotation-interval</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;30</span><span class="nv"> </span><span class="s">days&quot;</span>
<span class="w">      </span><span class="nt">validation-method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;query-test&quot;</span>

<span class="w">  </span><span class="nt">notification</span><span class="p">:</span>
<span class="w">    </span><span class="nt">channels</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">email</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;<EMAIL>&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">slack</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;#cip-alerts&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">webhook</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://monitoring.telus.com/webhooks/secret-rotation&quot;</span>
</code></pre></div>
<h2 id="security-monitoring">Security Monitoring<a class="headerlink" href="#security-monitoring" title="Permanent link">&para;</a></h2>
<h3 id="security-metrics">Security Metrics<a class="headerlink" href="#security-metrics" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Security monitoring configuration</span>
<span class="nt">security-monitoring</span><span class="p">:</span>
<span class="w">  </span><span class="nt">metrics</span><span class="p">:</span>
<span class="w">    </span><span class="nt">authentication-failures</span><span class="p">:</span>
<span class="w">      </span><span class="nt">threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">      </span><span class="nt">window</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;5m&quot;</span>
<span class="w">      </span><span class="nt">severity</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;warning&quot;</span>

<span class="w">    </span><span class="nt">certificate-expiry</span><span class="p">:</span>
<span class="w">      </span><span class="nt">threshold</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;30</span><span class="nv"> </span><span class="s">days&quot;</span>
<span class="w">      </span><span class="nt">severity</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;critical&quot;</span>

<span class="w">    </span><span class="nt">unauthorized-access-attempts</span><span class="p">:</span>
<span class="w">      </span><span class="nt">threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">      </span><span class="nt">window</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1m&quot;</span>
<span class="w">      </span><span class="nt">severity</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;critical&quot;</span>

<span class="w">    </span><span class="nt">tls-handshake-failures</span><span class="p">:</span>
<span class="w">      </span><span class="nt">threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">20</span>
<span class="w">      </span><span class="nt">window</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;5m&quot;</span>
<span class="w">      </span><span class="nt">severity</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;warning&quot;</span>

<span class="w">  </span><span class="nt">alerts</span><span class="p">:</span>
<span class="w">    </span><span class="nt">destinations</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;email&quot;</span>
<span class="w">        </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;<EMAIL>&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;slack&quot;</span>
<span class="w">        </span><span class="nt">channel</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;#security-alerts&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;pagerduty&quot;</span>
<span class="w">        </span><span class="nt">service-key</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${PAGERDUTY_SERVICE_KEY}&quot;</span>

<span class="w">  </span><span class="nt">audit-logging</span><span class="p">:</span>
<span class="w">    </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">retention</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1</span><span class="nv"> </span><span class="s">year&quot;</span>
<span class="w">    </span><span class="nt">fields</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">timestamp</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">user-id</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">action</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">resource</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">result</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ip-address</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">user-agent</span>

<span class="w">  </span><span class="nt">compliance</span><span class="p">:</span>
<span class="w">    </span><span class="nt">standards</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;SOC</span><span class="nv"> </span><span class="s">2</span><span class="nv"> </span><span class="s">Type</span><span class="nv"> </span><span class="s">II&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;ISO</span><span class="nv"> </span><span class="s">27001&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;PCI</span><span class="nv"> </span><span class="s">DSS&quot;</span>

<span class="w">    </span><span class="nt">reporting</span><span class="p">:</span>
<span class="w">      </span><span class="nt">frequency</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;monthly&quot;</span>
<span class="w">      </span><span class="nt">recipients</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;<EMAIL>&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;<EMAIL>&quot;</span>
</code></pre></div>
<h3 id="security-scanning">Security Scanning<a class="headerlink" href="#security-scanning" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Security scanning configuration</span>
<span class="nt">security-scanning</span><span class="p">:</span>
<span class="w">  </span><span class="nt">vulnerability-scanning</span><span class="p">:</span>
<span class="w">    </span><span class="nt">schedule</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;daily&quot;</span>
<span class="w">    </span><span class="nt">tools</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Trivy&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Snyk&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;OWASP</span><span class="nv"> </span><span class="s">Dependency</span><span class="nv"> </span><span class="s">Check&quot;</span>

<span class="w">  </span><span class="nt">penetration-testing</span><span class="p">:</span>
<span class="w">    </span><span class="nt">schedule</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;quarterly&quot;</span>
<span class="w">    </span><span class="nt">scope</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;API</span><span class="nv"> </span><span class="s">endpoints&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Authentication</span><span class="nv"> </span><span class="s">mechanisms&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Network</span><span class="nv"> </span><span class="s">security&quot;</span>

<span class="w">  </span><span class="nt">compliance-scanning</span><span class="p">:</span>
<span class="w">    </span><span class="nt">schedule</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;weekly&quot;</span>
<span class="w">    </span><span class="nt">frameworks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;CIS</span><span class="nv"> </span><span class="s">Kubernetes</span><span class="nv"> </span><span class="s">Benchmark&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;NIST</span><span class="nv"> </span><span class="s">Cybersecurity</span><span class="nv"> </span><span class="s">Framework&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;OWASP</span><span class="nv"> </span><span class="s">Top</span><span class="nv"> </span><span class="s">10&quot;</span>

<span class="w">  </span><span class="nt">remediation</span><span class="p">:</span>
<span class="w">    </span><span class="nt">critical-vulnerabilities</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;24</span><span class="nv"> </span><span class="s">hours&quot;</span>
<span class="w">    </span><span class="nt">high-vulnerabilities</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;7</span><span class="nv"> </span><span class="s">days&quot;</span>
<span class="w">    </span><span class="nt">medium-vulnerabilities</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;30</span><span class="nv"> </span><span class="s">days&quot;</span>
<span class="w">    </span><span class="nt">low-vulnerabilities</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;90</span><span class="nv"> </span><span class="s">days&quot;</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../05-development-guide/">Development Guide →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>