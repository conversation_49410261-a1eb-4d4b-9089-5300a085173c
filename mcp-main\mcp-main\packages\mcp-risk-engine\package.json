{"name": "@telus/mcp-risk-engine", "version": "0.1.1", "description": "MCP server for triggering Risk Engine assessments via webhook", "keywords": ["telus", "mcp", "risk-engine"], "license": "MIT", "author": "<PERSON>", "type": "module", "main": "dist/index.js", "bin": {"mcp-risk-engine": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "prepare": "pnpm run build", "start": "node dist/index.js"}, "dependencies": {"@google-cloud/bigquery": "^7.9.4", "@modelcontextprotocol/sdk": "^1.8.0", "axios": "^1.6.2"}, "devDependencies": {"@types/node": "^20.17.30", "typescript": "^5.3.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}, "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-risk-engine"}}