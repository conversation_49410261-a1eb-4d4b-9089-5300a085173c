import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
  CallToolRequest,
} from '@modelcontextprotocol/sdk/types.js';

import { handleTriggerAssessment } from '../tools/triggerAssessment.js';
import { getToolDefinitions } from '../tools/index.js';

// Create and configure the MCP server
export function createServer(): Server {
  const server = new Server(
    {
      name: 'mcp-risk-engine',
      version: '0.1.1',
    },
    {
      capabilities: {
        tools: {},
      },
    }
  );

  // Set up request handler for listing tools
  server.setRequestHandler(ListToolsRequestSchema, async () => ({
    tools: getToolDefinitions(),
  }));

  // Set up request handler for calling tools
  server.setRequestHandler(CallToolRequestSchema, async (request: CallToolRequest) => {
    // Handle different tools
    if (request.params.name === 'trigger_assessment') {
      return await handleTriggerAssessment(request);
    } else {
      throw new McpError(
        ErrorCode.MethodNotFound,
        `Unknown tool: ${request.params.name}`
      );
    }
  });

  // Set up error handling
  server.onerror = (error) => console.error('[MCP Error]', error);

  return server;
}

// Start the server
export async function startServer(): Promise<void> {
  const server = createServer();
  
  // Set up process signal handling
  process.on('SIGINT', async () => {
    await server.close();
    process.exit(0);
  });

  // Connect the server to stdio transport
  const transport = new StdioServerTransport();
  try {
    await server.connect(transport);
    console.error('Risk Engine MCP Webhook Trigger server running on stdio');
  } catch (error) {
    console.error('Failed to start server:', error);
  }
}
