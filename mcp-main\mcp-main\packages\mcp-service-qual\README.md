# TELUS Service Qualification MCP Server

This MCP server provides access to the TELUS Service Qualification V2 API with OAuth 2.0 authentication

## Features

- Query service qualification information for a TELUS address ID
- Support for both PROD and NON_PROD environments
- OAuth 2.0 authentication with token caching
- Summarized response with key service qualification details

## Installation

Add the following configuration to your MCP settings file:

```json
{
  "mcpServers": {
    "telus-service-qual": {
      "command": "node",
      "args": ["/path/to/mcp-service-qual/dist/index.js"],
      "env": {
        "TELUS_CLIENT_ID_NP": "your_non_prod_client_id",
        "TELUS_CLIENT_SECRET_NP": "your_non_prod_client_secret",
        "TELUS_CLIENT_ID_PROD": "your_prod_client_id",
        "TELUS_CLIENT_SECRET_PROD": "your_prod_client_secret"
      },
      "disabled": false,
      "autoApprove": []
    }
  }
}
```

## Usage

The server provides a single tool:

### service_qual_by_addressId

Get service qualification information for a TELUS address ID.

**Parameters:**
- `addressId` (required): TELUS Address ID
- `environment` (optional): Environment to use (PROD or NON_PROD, defaults to NON_PROD)

**Example Response:**
```json
{
  "addressId": "18149194",
  "services": [
    {
      "type": "GPON",
      "qualificationResult": "qualified",
      "isDPORequired": "Not Applicable",
      "pathType": "Spare",
      "technologyType": "fibre-pon"
    },
    {
      "type": "lte",
      "qualificationResult": "qualified"
    }
  ]
}
```

## Development

```bash
# Install dependencies
npm install

# Build the server
npm run build

# Watch for changes during development
npm run watch
```

## Environment Variables

- `TELUS_CLIENT_ID_NP`: Client ID for NON_PROD environment
- `TELUS_CLIENT_SECRET_NP`: Client secret for NON_PROD environment
- `TELUS_CLIENT_ID_PROD`: Client ID for PROD environment
- `TELUS_CLIENT_SECRET_PROD`: Client secret for PROD environment
