{"name": "@telus/mcp-ams", "version": "0.1.0", "description": "TELUS Address search", "keywords": ["telus", "mcp", "ams"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-ams"}, "license": "MIT", "author": "<PERSON> (@jonathan-dunn-telus)", "type": "module", "main": "dist/index.js", "bin": {"mcp-ams": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "prepare": "pnpm run build", "start": "node dist/index.js", "dev": "pnpm run build && pnpm run start"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "axios": "^1.8.4", "dotenv": "~16.4.7"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}