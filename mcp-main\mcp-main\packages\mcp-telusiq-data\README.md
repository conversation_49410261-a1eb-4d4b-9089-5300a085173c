# TelusIQ Data MCP Server

An MCP server for executing read-only queries on Google Cloud SQL PostgreSQL databases.

## Features

- Execute read-only SQL queries (SELECT and SHOW statements only)
- Secure connection using Google Cloud SQL authentication
- Automatic port forwarding to database instances
- Query parameter support
- Connection pooling
- Comprehensive error handling and logging

## Installation

1. Install the package:
```bash
pnpm add @telus/mcp-telusiq-data
```

2. Configure the MCP server in your settings file:
```json
{
  "mcpServers": {
    "telusiq-data": {
      "command": "node",
      "args": ["path/to/dist/index.js"],
      "env": {
        "POD_NAME": "your-pod-name",
        "NAMESPACE": "your-namespace",
        "DB_NAME": "your-database-name",
        "DB_USER": "your-database-user",
        "DB_PASSWORD": "your-database-password",
        "DB_PORT": "5432",
        "TARGET_PORT": "15432",
        "GOOGLE_APPLICATION_CREDENTIALS": "/path/to/credentials.json",
        "INSTANCE_CONNECTION_NAME": "project:region:instance"
      },
      "disabled": false,
      "autoApprove": []
    }
  }
}
```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| POD_NAME | Name of the Kubernetes pod running the database | Yes |
| NAMESPACE | Kubernetes namespace where the pod is running | Yes |
| DB_NAME | Name of the PostgreSQL database | Yes |
| DB_USER | Database user name | Yes |
| DB_PASSWORD | Database user password | Yes |
| DB_PORT | Database port (default: 5432) | Yes |
| TARGET_PORT | Local port for port forwarding (e.g., 15432) | Yes |
| GOOGLE_APPLICATION_CREDENTIALS | Path to Google Cloud credentials JSON file | Yes |
| INSTANCE_CONNECTION_NAME | Cloud SQL instance connection name (project:region:instance) | Yes |

## Available Tools

### execute_query

Execute a read-only SQL query on the PostgreSQL database.

**Input Schema:**
```json
{
  "type": "object",
  "properties": {
    "query": {
      "type": "string",
      "description": "SQL query to execute"
    },
    "params": {
      "type": "array",
      "items": {
        "type": "any"
      },
      "description": "Query parameters"
    }
  },
  "required": ["query"]
}
```

**Example Usage:**
```typescript
const result = await mcp.use('telusiq-data', 'execute_query', {
  query: 'SELECT * FROM users WHERE id = $1',
  params: [123]
});
```

## Security

- Only SELECT and SHOW queries are allowed
- Queries are validated before execution
- Connection is secured using Google Cloud SQL authentication
- Environment variables are required for sensitive configuration

## Development

1. Build the project:
```bash
pnpm build
```

2. Run tests:
```bash
pnpm test
```

## Error Handling

The server provides detailed error messages for common issues:
- Invalid queries
- Connection failures
- Authentication errors
- Port forwarding issues

## Limitations

- Only read-only operations are supported (SELECT and SHOW statements)
- Requires Google Cloud SQL instance
- Requires kubectl access to the pod
- Port forwarding must be available
