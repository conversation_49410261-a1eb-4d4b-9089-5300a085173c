import axios, { AxiosInstance } from 'axios';

export class NiFiClient {
  private client: AxiosInstance;
  private token: string | null = null;

  constructor(private baseURL: string, private username: string, private password: string) {
    console.error('[NiFiClient] Initializing with baseURL:', baseURL);
    this.client = axios.create({
      baseURL: `${baseURL}/nifi-api`,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      response => response,
      error => {
        console.error('[NiFi Error]', {
          status: error.response?.status,
          message: error.response?.data?.message || error.message,
          endpoint: error.config?.url
        });
        throw error;
      }
    );
  }

  async login(): Promise<void> {
    try {
      // Create a separate client for the access token request
      const accessClient = axios.create({
        baseURL: this.baseURL,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      // Create form data
      const formData = new URLSearchParams();
      formData.append('username', this.username);
      formData.append('password', this.password);

      console.error('[NiFiClient] Attempting login with URL:', `${this.baseURL}/nifi-api/access/token`);
      console.error('[NiFiClient] Form data:', formData.toString());
      
      const response = await accessClient.post('/nifi-api/access/token', formData);
      
      console.error('[NiFiClient] Login response:', {
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        headers: response.headers
      });
      
      this.token = response.data;
      console.error('[NiFiClient] Login successful, token obtained');
    } catch (error) {
      console.error('[NiFiClient] Login failed. Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        response: axios.isAxiosError(error) ? error.response?.data : undefined,
        status: axios.isAxiosError(error) ? error.response?.status : undefined,
        statusText: axios.isAxiosError(error) ? error.response?.statusText : undefined
      });
      throw new Error('NiFi login failed');
    }
  }

  async request<T>(method: string, url: string, data?: any): Promise<T> {
    if (!this.token) {
      await this.login();
    }

    try {
      console.error(`[NiFi Request] Starting ${method} request to ${url}`);
      console.error('[NiFi Request] Request config:', {
        method,
        url,
        baseURL: this.client.defaults.baseURL
      });

      console.error('[NiFi Request] Token:', this.token);
      const headers = {
        'Authorization': `Bearer ${this.token}`
      };
      console.error('[NiFi Request] Headers:', headers);

      const response = await this.client.request<T>({
        method,
        url,
        data,
        headers,
        timeout: 10000 // 10 second timeout
      });

      console.error(`[NiFi Request] Response received:`, {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers
      });

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('[NiFi Request] Axios error:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message,
          code: error.code
        });
        throw new Error(`NiFi API Error: ${error.response?.data?.message || error.message}`);
      }
      console.error('[NiFi Request] Non-Axios error:', error);
      throw error;
    }
  }

  // Helper methods for common operations
  async get<T>(url: string): Promise<T> {
    console.error('[NiFiClient] Executing GET request:', url);
    return this.request<T>('GET', url);
  }

  async post<T>(url: string, data?: any): Promise<T> {
    return this.request<T>('POST', url, data);
  }

  async put<T>(url: string, data?: any): Promise<T> {
    return this.request<T>('PUT', url, data);
  }

  async delete<T>(url: string): Promise<T> {
    return this.request<T>('DELETE', url);
  }
}
