# @telus/mcp-gcloud

This MCP server provides a tool to list secrets in Google Cloud Secret Manager.

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-gcloud
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for Claude Desktop):

   ```json
   {
     "mcpServers": {
       "gcloud": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-gcloud"],
         "env": {}
       }
     }
   }
   ```

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-gcloud
   pnpm install
   ```

2. Build the server:

   ```bash
   pnpm build
   ```

3. Start the server:

   ```bash
   pnpm start
   ```

## Prerequisites

1. Ensure you have gcloud CLI installed and configured.
2. Authenticate with Google Cloud:
   ```
   gcloud auth application-default login
   ```

## Available Tools

### list_secrets

Lists secrets in Google Cloud Secret Manager.

Input schema:

```json
{
  "type": "object",
  "properties": {
    "project_id": {
      "type": "string",
      "description": "GCP project ID"
    },
    "limit": {
      "type": "number",
      "description": "Maximum number of secrets to return (optional)",
      "minimum": 1
    },
    "include_disabled": {
      "type": "boolean",
      "description": "Include disabled secrets in the result (optional)"
    }
  },
  "required": ["project_id"]
}
```

Example usage:

```json
{
  "project_id": "your-gcp-project-id",
  "limit": 10,
  "include_disabled": true
}
```

This will return a list of secret names in the specified GCP project, including disabled secrets if `include_disabled` is set to true. The `limit` parameter can be used to restrict the number of results.

## Error Handling

The server handles common error scenarios such as:
- Invalid or missing project ID
- Permission issues
- API errors

Clear error messages are provided to help diagnose and resolve issues.
