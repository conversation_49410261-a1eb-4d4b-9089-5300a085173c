import { Octokit } from '@octokit/rest';

export class GitHubAPI {
  private octokit: Octokit;

  constructor() {
    const token = process.env.GITHUB_TOKEN;
    if (!token) {
      throw new Error('GitHub token not configured. Please add GITHUB_TOKEN to MCP server configuration.');
    }
    this.octokit = new Octokit({ auth: token });
  }

  async listIssues(owner: string, repo: string, options: {
    state?: 'open' | 'closed' | 'all';
    labels?: string[];
    sort?: 'created' | 'updated' | 'comments';
    direction?: 'asc' | 'desc';
  } = {}) {
    try {
      const { labels, ...restOptions } = options;
      const response = await this.octokit.rest.issues.listForRepo({
        owner,
        repo,
        ...restOptions,
        ...(labels && { labels: labels.join(',') }),
      });
      return response.data;
    } catch (error: any) {
      this.handleError(error);
    }
  }

  async createIssue(owner: string, repo: string, options: {
    title: string;
    body: string;
    labels?: string[];
    assignees?: string[];
  }) {
    try {
      const response = await this.octokit.rest.issues.create({
        owner,
        repo,
        ...options,
      });
      return response.data;
    } catch (error: any) {
      this.handleError(error);
    }
  }

  async updateIssue(owner: string, repo: string, issue_number: number, options: {
    title?: string;
    body?: string;
    state?: 'open' | 'closed';
    labels?: string[];
    assignees?: string[];
  }) {
    try {
      const response = await this.octokit.rest.issues.update({
        owner,
        repo,
        issue_number,
        ...options,
      });
      return response.data;
    } catch (error: any) {
      this.handleError(error);
    }
  }

  async getIssue(owner: string, repo: string, issue_number: number) {
    try {
      const response = await this.octokit.rest.issues.get({
        owner,
        repo,
        issue_number,
      });
      return response.data;
    } catch (error: any) {
      this.handleError(error);
    }
  }

  private handleError(error: any) {
    if (error.status === 401) {
      throw new Error('Invalid GitHub token. Please check your Personal Access Token.');
    }
    if (error.status === 403) {
      if (error.response?.headers?.['x-ratelimit-remaining'] === '0') {
        throw new Error('Rate limit exceeded. Please wait before making more requests.');
      }
      throw new Error('Insufficient permissions. Token requires appropriate scopes.');
    }
    if (error.status === 404) {
      throw new Error('Repository or resource not found. Please check the owner and repo names.');
    }
    throw new Error(`GitHub API error: ${error.message}`);
  }
}
