#!/usr/bin/env node
/**
 * TMF620 MCP Server Cline Settings Update Script
 * 
 * This script updates the Cline MCP settings for the TMF620 server to match the .env file.
 * It fixes the timeout issue by ensuring the API URLs and other settings are consistent.
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const dotenv = require('dotenv');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

console.log(`${colors.blue}TMF620 MCP Server Cline Settings Update Script${colors.reset}`);
console.log(`${colors.blue}============================================${colors.reset}`);

// Define paths
const clineConfigDir = path.join(os.homedir(), 'AppData', 'Roaming', 'Code', 'User', 'globalStorage', 'saoudrizwan.claude-dev', 'settings');
const clineConfigFile = path.join(clineConfigDir, 'cline_mcp_settings.json');
const envFilePath = path.join(__dirname, '.env');

// Check if the Cline config file exists
if (!fs.existsSync(clineConfigFile)) {
  console.error(`${colors.red}Error: Cline config file not found at ${clineConfigFile}${colors.reset}`);
  process.exit(1);
}

// Check if the .env file exists
if (!fs.existsSync(envFilePath)) {
  console.error(`${colors.red}Error: .env file not found at ${envFilePath}${colors.reset}`);
  process.exit(1);
}

// Load the .env file
const envConfig = dotenv.parse(fs.readFileSync(envFilePath));
console.log(`${colors.green}Loaded .env file from ${envFilePath}${colors.reset}`);

// Load the Cline config file
let clineConfig;
try {
  const configData = fs.readFileSync(clineConfigFile, 'utf8');
  clineConfig = JSON.parse(configData);
  console.log(`${colors.green}Loaded Cline config file from ${clineConfigFile}${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Error reading Cline config file: ${error.message}${colors.reset}`);
  process.exit(1);
}

// Check if the tmf620 server is configured
if (!clineConfig.mcpServers || !clineConfig.mcpServers.tmf620) {
  console.error(`${colors.red}Error: TMF620 server not found in Cline config${colors.reset}`);
  process.exit(1);
}

// Get the current server config
const serverConfig = clineConfig.mcpServers.tmf620;
console.log(`${colors.blue}Current TMF620 server config:${colors.reset}`);
console.log(JSON.stringify(serverConfig, null, 2));

// Update the server config with values from the .env file
let updated = false;

// Update API URLs
if (serverConfig.env.DEV_API_URL !== envConfig.DEV_API_URL) {
  console.log(`${colors.yellow}Updating DEV_API_URL:${colors.reset}`);
  console.log(`${colors.yellow}  From: ${serverConfig.env.DEV_API_URL}${colors.reset}`);
  console.log(`${colors.yellow}  To:   ${envConfig.DEV_API_URL}${colors.reset}`);
  serverConfig.env.DEV_API_URL = envConfig.DEV_API_URL;
  updated = true;
}

if (serverConfig.env.TEST_API_URL !== envConfig.TEST_API_URL) {
  console.log(`${colors.yellow}Updating TEST_API_URL:${colors.reset}`);
  console.log(`${colors.yellow}  From: ${serverConfig.env.TEST_API_URL}${colors.reset}`);
  console.log(`${colors.yellow}  To:   ${envConfig.TEST_API_URL}${colors.reset}`);
  serverConfig.env.TEST_API_URL = envConfig.TEST_API_URL;
  updated = true;
}

if (serverConfig.env.PROD_API_URL !== envConfig.PROD_API_URL) {
  console.log(`${colors.yellow}Updating PROD_API_URL:${colors.reset}`);
  console.log(`${colors.yellow}  From: ${serverConfig.env.PROD_API_URL}${colors.reset}`);
  console.log(`${colors.yellow}  To:   ${envConfig.PROD_API_URL}${colors.reset}`);
  serverConfig.env.PROD_API_URL = envConfig.PROD_API_URL;
  updated = true;
}

// Update OAuth settings
if (serverConfig.env.OAUTH_TOKEN_URL !== envConfig.OAUTH_TOKEN_URL) {
  console.log(`${colors.yellow}Updating OAUTH_TOKEN_URL:${colors.reset}`);
  console.log(`${colors.yellow}  From: ${serverConfig.env.OAUTH_TOKEN_URL}${colors.reset}`);
  console.log(`${colors.yellow}  To:   ${envConfig.OAUTH_TOKEN_URL}${colors.reset}`);
  serverConfig.env.OAUTH_TOKEN_URL = envConfig.OAUTH_TOKEN_URL;
  updated = true;
}

if (serverConfig.env.OAUTH_SCOPE !== envConfig.OAUTH_SCOPE) {
  console.log(`${colors.yellow}Updating OAUTH_SCOPE:${colors.reset}`);
  console.log(`${colors.yellow}  From: ${serverConfig.env.OAUTH_SCOPE}${colors.reset}`);
  console.log(`${colors.yellow}  To:   ${envConfig.OAUTH_SCOPE}${colors.reset}`);
  serverConfig.env.OAUTH_SCOPE = envConfig.OAUTH_SCOPE;
  updated = true;
}

// Update TEST_QUOTE_ID
if (serverConfig.env.TEST_QUOTE_ID !== envConfig.TEST_QUOTE_ID) {
  console.log(`${colors.yellow}Updating TEST_QUOTE_ID:${colors.reset}`);
  console.log(`${colors.yellow}  From: ${serverConfig.env.TEST_QUOTE_ID}${colors.reset}`);
  console.log(`${colors.yellow}  To:   ${envConfig.TEST_QUOTE_ID}${colors.reset}`);
  serverConfig.env.TEST_QUOTE_ID = envConfig.TEST_QUOTE_ID;
  updated = true;
}

// Update proxy settings
if (serverConfig.env.HTTP_PROXY !== envConfig.HTTP_PROXY) {
  console.log(`${colors.yellow}Updating HTTP_PROXY:${colors.reset}`);
  console.log(`${colors.yellow}  From: ${serverConfig.env.HTTP_PROXY}${colors.reset}`);
  console.log(`${colors.yellow}  To:   ${envConfig.HTTP_PROXY}${colors.reset}`);
  serverConfig.env.HTTP_PROXY = envConfig.HTTP_PROXY;
  updated = true;
}

if (serverConfig.env.HTTPS_PROXY !== envConfig.HTTPS_PROXY) {
  console.log(`${colors.yellow}Updating HTTPS_PROXY:${colors.reset}`);
  console.log(`${colors.yellow}  From: ${serverConfig.env.HTTPS_PROXY}${colors.reset}`);
  console.log(`${colors.yellow}  To:   ${envConfig.HTTPS_PROXY}${colors.reset}`);
  serverConfig.env.HTTPS_PROXY = envConfig.HTTPS_PROXY;
  updated = true;
}

if (serverConfig.env.NO_PROXY !== envConfig.NO_PROXY) {
  console.log(`${colors.yellow}Updating NO_PROXY:${colors.reset}`);
  console.log(`${colors.yellow}  From: ${serverConfig.env.NO_PROXY}${colors.reset}`);
  console.log(`${colors.yellow}  To:   ${envConfig.NO_PROXY}${colors.reset}`);
  serverConfig.env.NO_PROXY = envConfig.NO_PROXY;
  updated = true;
}

// Ensure timeout is set to 120 seconds
if (serverConfig.timeout !== 120) {
  console.log(`${colors.yellow}Updating timeout:${colors.reset}`);
  console.log(`${colors.yellow}  From: ${serverConfig.timeout}${colors.reset}`);
  console.log(`${colors.yellow}  To:   120${colors.reset}`);
  serverConfig.timeout = 120;
  updated = true;
}

// Write the updated config back to the file if changes were made
if (updated) {
  try {
    fs.writeFileSync(clineConfigFile, JSON.stringify(clineConfig, null, 2), 'utf8');
    console.log(`${colors.green}Successfully updated Cline config file${colors.reset}`);
    console.log(`${colors.green}Please restart Cline to apply the changes${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}Error writing Cline config file: ${error.message}${colors.reset}`);
    process.exit(1);
  }
} else {
  console.log(`${colors.green}No changes needed. Cline config file is already up to date${colors.reset}`);
}

console.log(`\n${colors.blue}Updated TMF620 server config:${colors.reset}`);
console.log(JSON.stringify(serverConfig, null, 2));

console.log(`\n${colors.green}TMF620 MCP Server Cline settings update complete!${colors.reset}`);
