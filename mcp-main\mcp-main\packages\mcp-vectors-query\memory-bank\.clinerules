# Cline Rules for MCP Vectors Query

## Project Patterns

### Code Organization
- TypeScript source files are in the `src` directory
- Main server implementation is in `src/index.ts`
- Type definitions are in `src/types.ts`
- Build output goes to the `dist` directory

### Configuration Management
- Environment variables are loaded from `.env` file
- `.env.example` provides a template for required variables
- All sensitive values should be externalized in environment variables
- No default fallback values for critical settings

### Development Workflow
- Use `pnpm build` to compile TypeScript and set executable permissions
- Use `pnpm watch` for development with auto-recompilation
- Use `pnpm inspector` for debugging MCP communication

### Testing Approach
- Manual testing is performed by running queries against the server
- Test queries should use the `search_vectors` tool with various inputs
- Verify that search results are relevant and properly formatted

## User Preferences

### Documentation Style
- Use Markdown for all documentation
- Include mermaid diagrams for visual representation of architecture
- Organize documentation in the memory-bank directory
- Keep documentation up-to-date with code changes

### Code Style
- Use TypeScript for type safety
- Follow consistent indentation (2 spaces)
- Use async/await for asynchronous operations
- Use proper error handling with try/catch blocks

## Project Intelligence

### Key Implementation Paths
- Server initialization in `src/index.ts`
- Tool registration in `setupToolHandlers` method
- Vector search implementation in `searchVectors` method
- Embedding generation in `generateEmbedding` method

### Critical Components
- MCP Server: Handles MCP protocol communication
- OpenAI Client: Generates embeddings from text
- Turbopuffer Client: Performs vector database operations
- Environment Configuration: Manages sensitive settings

### Known Challenges
- Environment variable configuration must be correct
- Model compatibility between query and stored vectors
- Error handling for external API failures
- Performance optimization for large vector databases

### Evolution Notes
- Project started as a simple vector search implementation
- Focus on read-only operations for security and simplicity
- Potential future enhancements include advanced search options
