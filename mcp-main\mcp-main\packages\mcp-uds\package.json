{"name": "@telus/mcp-uds", "version": "1.0.0", "description": "UDS MCP Server", "keywords": ["telus", "mcp", "uds"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-uds"}, "license": "MIT", "author": "<PERSON> (@gpeitzner96)", "type": "module", "main": "dist/index.js", "bin": {"mcp-uds": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && cp src/convert-tables.js dist/ && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "prepare": "pnpm run build", "start": "node dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}