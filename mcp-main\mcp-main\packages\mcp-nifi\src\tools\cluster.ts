import { NiFiClient } from '../nifi-client.js';
import { z } from 'zod';

type ToolRequest = {
  method: string;
  params?: {
    arguments?: Record<string, unknown>;
  };
};

type ToolResponse = {
  content: Array<{ type: string; text: string }>;
  isError?: boolean;
};

const toolSchema = z.object({
  method: z.string(),
  params: z.object({
    arguments: z.record(z.unknown())
  }).optional()
});

export const tools = {
  manage_cluster: {
    description: 'Manage cluster operations',
    inputSchema: {
      type: 'object',
      properties: {
        action: {
          type: 'string',
          enum: ['get_status', 'get_node'],
          description: 'Cluster management action to perform'
        },
        nodeId: {
          type: 'string',
          description: 'Node ID for all node operations'
        }
      },
      required: ['action']
    }
  }
};

export const manageCluster = (client: NiFiClient) => async (request: ToolRequest): Promise<ToolResponse> => {
  const { action, nodeId } = (request.params?.arguments || {}) as {
    action: 'get_status' | 'get_node';
    nodeId?: string;
  };

  try {
    let response;
    switch (action) {
      case 'get_status':
        response = await client.get('/controller/cluster');
        break;

      case 'get_node':
        if (!nodeId) {
          throw new Error('Node ID is required for getting node information');
        }
        response = await client.get(`/controller/cluster/nodes/${nodeId}`);
        break;

      default:
        throw new Error(`Invalid action: ${action}`);
    }

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2)
        }
      ]
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      content: [
        {
          type: 'text',
          text: `Error managing cluster: ${errorMessage}`
        }
      ],
      isError: true
    };
  }
};
