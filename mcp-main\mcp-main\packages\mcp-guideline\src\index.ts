#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import * as fs from 'fs/promises';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class GuidelinesServer {
  private server: Server;
  private guidelinesDir: string;

  constructor() {
    this.server = new Server(
      {
        name: 'guidelines-server',
        version: '0.1.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Set the guidelines directory relative to the server script location
    this.guidelinesDir = path.join(__dirname, '..', 'guidelines');

    this.setupToolHandlers();
    
    this.server.onerror = (error) => console.error('[MCP Error]', error);
    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'get_language_guidelines',
          description: 'Generate API Project based on given programming language-specific guidelines',
          inputSchema: {
            type: 'object',
            properties: {
              language: {
                type: 'string',
                description: 'Programming language to get guidelines for',
                enum: ['java', 'typescript']
              }
            },
            required: ['language']
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      if (request.params.name !== 'get_language_guidelines') {
        throw new McpError(
          ErrorCode.MethodNotFound,
          `Unknown tool: ${request.params.name}`
        );
      }

      const { language } = request.params.arguments as {
        language: 'java' | 'typescript';
      };

      try {
        // Map language to guideline file
        const guidelineFiles: Record<string, string> = {
          java: 'java-api-guidelines.md',
          typescript: 'typescript-api-guidelines.md'
        };

        const fileName = guidelineFiles[language];
        if (!fileName) {
          throw new Error(`Guidelines not available for language: ${language}`);
        }

        const filePath = path.join(this.guidelinesDir, fileName);
        const content = await fs.readFile(filePath, 'utf-8');

        return {
          content: [
            {
              type: 'text',
              text: content,
            },
          ],
        };
      } catch (error) {
        if (error instanceof Error) {
          return {
            content: [
              {
                type: 'text',
                text: `Error retrieving guidelines: ${error.message}`,
              },
            ],
            isError: true,
          };
        }
        throw error;
      }
    });
  }

  async run() {
    // Ensure guidelines directory exists
    try {
      await fs.mkdir(this.guidelinesDir, { recursive: true });
    } catch (error) {
      console.error('Error creating guidelines directory:', error);
    }

    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Guidelines MCP server running on stdio');
    console.error('Guidelines directory:', this.guidelinesDir);
  }
}

const server = new GuidelinesServer();
server.run().catch(console.error);
