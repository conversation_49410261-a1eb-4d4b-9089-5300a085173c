
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/03-extensions-guide/">
      
      
      
      
      <link rel="icon" href="../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Extensions Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#extensions-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Extensions Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href=".." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#qes-plugin-overview" class="md-nav__link">
    <span class="md-ellipsis">
      QES Plugin Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="QES Plugin Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#plugin-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Plugin Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-registration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Registration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#core-extension-components" class="md-nav__link">
    <span class="md-ellipsis">
      Core Extension Components
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Core Extension Components">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-quote-delta-modificator" class="md-nav__link">
    <span class="md-ellipsis">
      1. Quote Delta Modificator
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-quote-expiration-provider" class="md-nav__link">
    <span class="md-ellipsis">
      2. Quote Expiration Provider
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-state-machine-extension-validator" class="md-nav__link">
    <span class="md-ellipsis">
      3. State Machine Extension Validator
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bandwidth-validators" class="md-nav__link">
    <span class="md-ellipsis">
      Bandwidth Validators
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Bandwidth Validators">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-wan-l2-cir-bandwidth-validator" class="md-nav__link">
    <span class="md-ellipsis">
      1. WAN L2 CIR Bandwidth Validator
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-wan-l2-evc-bandwidth-validator" class="md-nav__link">
    <span class="md-ellipsis">
      2. WAN L2 EVC Bandwidth Validator
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-wan-l3-ip-qos-bandwidth-validator" class="md-nav__link">
    <span class="md-ellipsis">
      3. WAN L3 IP QoS Bandwidth Validator
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quote-modificators" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Modificators
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Modificators">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#resulting-delta-modificator" class="md-nav__link">
    <span class="md-ellipsis">
      Resulting Delta Modificator
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Development Patterns
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Patterns">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#utility-classes" class="md-nav__link">
    <span class="md-ellipsis">
      Utility Classes
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Utility Classes">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#bandwidth-validator-utils" class="md-nav__link">
    <span class="md-ellipsis">
      Bandwidth Validator Utils
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Patterns
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-and-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Build and Deployment
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="extensions-guide">Extensions Guide<a class="headerlink" href="#extensions-guide" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#qes-plugin-overview">QES Plugin Overview</a></li>
<li><a href="#plugin-architecture">Plugin Architecture</a></li>
<li><a href="#core-extension-components">Core Extension Components</a></li>
<li><a href="#bandwidth-validators">Bandwidth Validators</a></li>
<li><a href="#quote-modificators">Quote Modificators</a></li>
<li><a href="#development-patterns">Development Patterns</a></li>
</ul>
<h2 id="qes-plugin-overview">QES Plugin Overview<a class="headerlink" href="#qes-plugin-overview" title="Permanent link">&para;</a></h2>
<p>The <strong>Quote Engine Service (QES) Extensions</strong> provide custom business logic and validation rules for the TELUS B2B Order Capture system. These plugins extend the core Order Capture Product functionality with TELUS-specific requirements.</p>
<h3 id="key-features">Key Features<a class="headerlink" href="#key-features" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Business Rule Validation</strong>: Custom validation logic for TELUS services</li>
<li><strong>Bandwidth Validation</strong>: Specialized validators for network services</li>
<li><strong>Quote Modifications</strong>: Dynamic quote adjustments and pricing rules</li>
<li><strong>State Machine Extensions</strong>: Custom workflow validation</li>
<li><strong>Utility Libraries</strong>: Reusable components for business logic</li>
</ul>
<h3 id="technology-stack">Technology Stack<a class="headerlink" href="#technology-stack" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;framework&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;QES Plugin Framework 2023.3.2.0&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;language&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Java 17&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;buildTool&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Maven&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;codeGeneration&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Lombok 1.18.30&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;testing&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;JUnit 5.6.3, Mockito 5.3.1&quot;</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="plugin-architecture">Plugin Architecture<a class="headerlink" href="#plugin-architecture" title="Permanent link">&para;</a></h2>
<h3 id="project-structure">Project Structure<a class="headerlink" href="#project-structure" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>nc-cloud-bss-oc-ui-be-extension-b2b/
├── b2b-qes-plugin/
│   ├── src/main/java/com/netcracker/solutions/telus/qes/extension/plugin/
│   │   ├── TelusQuoteDeltaModificatorImpl.java           # Quote modifications
│   │   ├── TelusQuoteExpirationPeriodProvider.java      # Quote lifecycle
│   │   ├── TelusQuoteResultingDeltaModificator.java     # Result modifications
│   │   ├── TelusStateMachineExtensionValidator.java     # State validation
│   │   ├── TelusWanL2CirBandwidthValidator.java         # L2 CIR validation
│   │   ├── TelusWanL2EvcBandwidthValidator.java         # L2 EVC validation
│   │   ├── TelusWanL3IpQosBandwidthValidator.java       # L3 QoS validation
│   │   └── common/utils/                                # Utility classes
│   │       ├── BandwidthValidatorUtils.java             # Bandwidth utilities
│   │       ├── CommonUtils.java                         # Common utilities
│   │       └── Constants.java                           # Application constants
│   └── pom.xml                                          # Plugin module POM
├── deployments/charts/                                  # Helm charts
└── pom.xml                                              # Parent POM
</code></pre></div>
<h3 id="plugin-registration">Plugin Registration<a class="headerlink" href="#plugin-registration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">&lt;!-- Plugin configuration in pom.xml --&gt;</span>
<span class="nt">&lt;properties&gt;</span>
<span class="w">    </span><span class="nt">&lt;qes.version&gt;</span>2023.3.2.0<span class="nt">&lt;/qes.version&gt;</span>
<span class="w">    </span><span class="nt">&lt;plugin.common.utils.version&gt;</span>2023.3.0.3-SNAPSHOT<span class="nt">&lt;/plugin.common.utils.version&gt;</span>
<span class="nt">&lt;/properties&gt;</span>

<span class="nt">&lt;dependencies&gt;</span>
<span class="w">    </span><span class="nt">&lt;dependency&gt;</span>
<span class="w">        </span><span class="nt">&lt;groupId&gt;</span>com.netcracker.qes<span class="nt">&lt;/groupId&gt;</span>
<span class="w">        </span><span class="nt">&lt;artifactId&gt;</span>qes-plugin-api<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">        </span><span class="nt">&lt;version&gt;</span>${qes.version}<span class="nt">&lt;/version&gt;</span>
<span class="w">    </span><span class="nt">&lt;/dependency&gt;</span>
<span class="nt">&lt;/dependencies&gt;</span>
</code></pre></div>
<h2 id="core-extension-components">Core Extension Components<a class="headerlink" href="#core-extension-components" title="Permanent link">&para;</a></h2>
<h3 id="1-quote-delta-modificator">1. Quote Delta Modificator<a class="headerlink" href="#1-quote-delta-modificator" title="Permanent link">&para;</a></h3>
<p>The main quote modification engine that applies TELUS-specific business rules:</p>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusQuoteDeltaModificatorImpl</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">QuoteDeltaModificator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">BandwidthValidatorUtils</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">CommonUtils</span><span class="w"> </span><span class="n">commonUtils</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="nf">modifyQuote</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Starting quote modification for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">builder</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Apply bandwidth validations</span>
<span class="w">        </span><span class="n">applyBandwidthValidations</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply pricing modifications</span>
<span class="w">        </span><span class="n">applyPricingModifications</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply business rule validations</span>
<span class="w">        </span><span class="n">applyBusinessRuleValidations</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="n">delta</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Quote modification completed with {} changes&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">delta</span><span class="p">.</span><span class="na">getChanges</span><span class="p">().</span><span class="na">size</span><span class="p">());</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">delta</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">applyBandwidthValidations</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                         </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteItems</span><span class="p">().</span><span class="na">stream</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">filter</span><span class="p">(</span><span class="n">item</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">isNetworkService</span><span class="p">(</span><span class="n">item</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">forEach</span><span class="p">(</span><span class="n">item</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">.</span><span class="na">validateBandwidth</span><span class="p">(</span><span class="n">item</span><span class="p">);</span>
<span class="w">                </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">result</span><span class="p">.</span><span class="na">isValid</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">addValidationError</span><span class="p">(</span>
<span class="w">                        </span><span class="n">item</span><span class="p">.</span><span class="na">getItemId</span><span class="p">(),</span><span class="w"> </span>
<span class="w">                        </span><span class="n">result</span><span class="p">.</span><span class="na">getErrorMessage</span><span class="p">()</span>
<span class="w">                    </span><span class="p">);</span>
<span class="w">                </span><span class="p">}</span>
<span class="w">            </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="2-quote-expiration-provider">2. Quote Expiration Provider<a class="headerlink" href="#2-quote-expiration-provider" title="Permanent link">&para;</a></h3>
<p>Manages quote lifecycle and expiration rules:</p>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusQuoteExpirationPeriodProvider</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">QuoteExpirationPeriodProvider</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">DEFAULT_EXPIRATION_DAYS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">30</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">ENTERPRISE_EXPIRATION_DAYS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">60</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">GOVERNMENT_EXPIRATION_DAYS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">90</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Duration</span><span class="w"> </span><span class="nf">getExpirationPeriod</span><span class="p">(</span><span class="n">QuoteExpirationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">CustomerType</span><span class="w"> </span><span class="n">customerType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getCustomerType</span><span class="p">();</span>
<span class="w">        </span><span class="n">QuoteType</span><span class="w"> </span><span class="n">quoteType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteType</span><span class="p">();</span>

<span class="w">        </span><span class="kt">int</span><span class="w"> </span><span class="n">expirationDays</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">customerType</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">ENTERPRISE</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">ENTERPRISE_EXPIRATION_DAYS</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">GOVERNMENT</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">GOVERNMENT_EXPIRATION_DAYS</span><span class="p">;</span>
<span class="w">            </span><span class="k">default</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">DEFAULT_EXPIRATION_DAYS</span><span class="p">;</span>
<span class="w">        </span><span class="p">};</span>

<span class="w">        </span><span class="c1">// Apply quote type modifiers</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quoteType</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteType</span><span class="p">.</span><span class="na">COMPLEX_SOLUTION</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">expirationDays</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="mi">15</span><span class="p">;</span><span class="w"> </span><span class="c1">// Extended time for complex solutions</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">Duration</span><span class="p">.</span><span class="na">ofDays</span><span class="p">(</span><span class="n">expirationDays</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">canExtendExpiration</span><span class="p">(</span><span class="n">QuoteExpirationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getCustomerType</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="n">CustomerType</span><span class="p">.</span><span class="na">STANDARD</span><span class="w"> </span><span class="o">&amp;&amp;</span>
<span class="w">               </span><span class="n">context</span><span class="p">.</span><span class="na">getExtensionCount</span><span class="p">()</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">2</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="3-state-machine-extension-validator">3. State Machine Extension Validator<a class="headerlink" href="#3-state-machine-extension-validator" title="Permanent link">&para;</a></h3>
<p>Validates workflow state transitions:</p>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusStateMachineExtensionValidator</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">StateMachineExtensionValidator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validateStateTransition</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">fromState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getFromState</span><span class="p">();</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">toState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getToState</span><span class="p">();</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuote</span><span class="p">();</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Validating state transition from {} to {} for quote {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">fromState</span><span class="p">,</span><span class="w"> </span><span class="n">toState</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Validate business rules for state transition</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">fromState</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">DRAFT</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">toState</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">SUBMITTED</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">validateDraftToSubmitted</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">fromState</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">SUBMITTED</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">toState</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">APPROVED</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">validateSubmittedToApproved</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">toState</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">CANCELLED</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">validateCancellation</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">success</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validateDraftToSubmitted</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">List</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">errors</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ArrayList</span><span class="o">&lt;&gt;</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Validate required fields</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getCustomerId</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">errors</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="s">&quot;Customer ID is required for quote submission&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getItems</span><span class="p">().</span><span class="na">isEmpty</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">errors</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="s">&quot;Quote must contain at least one item&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate pricing</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getTotalPrice</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getTotalPrice</span><span class="p">().</span><span class="na">compareTo</span><span class="p">(</span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">ZERO</span><span class="p">)</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">errors</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="s">&quot;Quote must have valid pricing&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">errors</span><span class="p">.</span><span class="na">isEmpty</span><span class="p">()</span><span class="w"> </span><span class="o">?</span><span class="w"> </span>
<span class="w">            </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">success</span><span class="p">()</span><span class="w"> </span><span class="p">:</span><span class="w"> </span>
<span class="w">            </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">failure</span><span class="p">(</span><span class="n">String</span><span class="p">.</span><span class="na">join</span><span class="p">(</span><span class="s">&quot;; &quot;</span><span class="p">,</span><span class="w"> </span><span class="n">errors</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="bandwidth-validators">Bandwidth Validators<a class="headerlink" href="#bandwidth-validators" title="Permanent link">&para;</a></h2>
<p>The system includes specialized bandwidth validators for different network service types:</p>
<h3 id="1-wan-l2-cir-bandwidth-validator">1. WAN L2 CIR Bandwidth Validator<a class="headerlink" href="#1-wan-l2-cir-bandwidth-validator" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusWanL2CirBandwidthValidator</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">BandwidthValidator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">MIN_CIR_BANDWIDTH</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">(</span><span class="s">&quot;1&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">MAX_CIR_BANDWIDTH</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">(</span><span class="s">&quot;10000&quot;</span><span class="p">);</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validate</span><span class="p">(</span><span class="n">BandwidthValidationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">serviceItem</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItem</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">isWanL2CirService</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">notApplicable</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">cirBandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractCirBandwidth</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">);</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">eirBandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractEirBandwidth</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Validate CIR bandwidth range</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">cirBandwidth</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">MIN_CIR_BANDWIDTH</span><span class="p">)</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="o">||</span><span class="w"> </span>
<span class="w">            </span><span class="n">cirBandwidth</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">MAX_CIR_BANDWIDTH</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">failure</span><span class="p">(</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;CIR bandwidth must be between %s and %s Mbps&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                             </span><span class="n">MIN_CIR_BANDWIDTH</span><span class="p">,</span><span class="w"> </span><span class="n">MAX_CIR_BANDWIDTH</span><span class="p">)</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate EIR relationship</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">eirBandwidth</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">eirBandwidth</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">cirBandwidth</span><span class="p">)</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">failure</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;EIR bandwidth cannot be less than CIR bandwidth&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">success</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isWanL2CirService</span><span class="p">(</span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">item</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="s">&quot;WAN_L2_CIR&quot;</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">item</span><span class="p">.</span><span class="na">getServiceType</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="2-wan-l2-evc-bandwidth-validator">2. WAN L2 EVC Bandwidth Validator<a class="headerlink" href="#2-wan-l2-evc-bandwidth-validator" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusWanL2EvcBandwidthValidator</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">BandwidthValidator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validate</span><span class="p">(</span><span class="n">BandwidthValidationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">serviceItem</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItem</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">isWanL2EvcService</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">notApplicable</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Extract EVC-specific parameters</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">evcBandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractEvcBandwidth</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">);</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">evcType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractEvcType</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Validate based on EVC type</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">evcType</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="s">&quot;POINT_TO_POINT&quot;</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">validatePointToPointEvc</span><span class="p">(</span><span class="n">evcBandwidth</span><span class="p">);</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="s">&quot;MULTIPOINT&quot;</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">validateMultipointEvc</span><span class="p">(</span><span class="n">evcBandwidth</span><span class="p">,</span><span class="w"> </span><span class="n">serviceItem</span><span class="p">);</span>
<span class="w">            </span><span class="k">default</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">failure</span><span class="p">(</span><span class="s">&quot;Unknown EVC type: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">evcType</span><span class="p">);</span>
<span class="w">        </span><span class="p">};</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validatePointToPointEvc</span><span class="p">(</span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">bandwidth</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">bandwidth</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">(</span><span class="s">&quot;10&quot;</span><span class="p">))</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">failure</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;Point-to-point EVC minimum bandwidth is 10 Mbps&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">success</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="3-wan-l3-ip-qos-bandwidth-validator">3. WAN L3 IP QoS Bandwidth Validator<a class="headerlink" href="#3-wan-l3-ip-qos-bandwidth-validator" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusWanL3IpQosBandwidthValidator</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">BandwidthValidator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validate</span><span class="p">(</span><span class="n">BandwidthValidationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">serviceItem</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItem</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">isWanL3IpQosService</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">notApplicable</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">List</span><span class="o">&lt;</span><span class="n">QosClass</span><span class="o">&gt;</span><span class="w"> </span><span class="n">qosClasses</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractQosClasses</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">);</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">totalBandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractTotalBandwidth</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Validate QoS class bandwidth allocation</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">allocatedBandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">qosClasses</span><span class="p">.</span><span class="na">stream</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">map</span><span class="p">(</span><span class="n">QosClass</span><span class="p">::</span><span class="n">getBandwidth</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">reduce</span><span class="p">(</span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">ZERO</span><span class="p">,</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">::</span><span class="n">add</span><span class="p">);</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">allocatedBandwidth</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">totalBandwidth</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">failure</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;Total QoS class bandwidth allocation exceeds service bandwidth&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate individual QoS classes</span>
<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">QosClass</span><span class="w"> </span><span class="n">qosClass</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">qosClasses</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">classResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validateQosClass</span><span class="p">(</span><span class="n">qosClass</span><span class="p">);</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">classResult</span><span class="p">.</span><span class="na">isValid</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="n">classResult</span><span class="p">;</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">success</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="quote-modificators">Quote Modificators<a class="headerlink" href="#quote-modificators" title="Permanent link">&para;</a></h2>
<h3 id="resulting-delta-modificator">Resulting Delta Modificator<a class="headerlink" href="#resulting-delta-modificator" title="Permanent link">&para;</a></h3>
<p>Handles final quote modifications before submission:</p>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusQuoteResultingDeltaModificator</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">QuoteResultingDeltaModificator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="nf">modifyResultingQuote</span><span class="p">(</span><span class="n">QuoteResultingContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">builder</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Apply final pricing adjustments</span>
<span class="w">        </span><span class="n">applyFinalPricingAdjustments</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply regulatory compliance modifications</span>
<span class="w">        </span><span class="n">applyRegulatoryCompliance</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply customer-specific modifications</span>
<span class="w">        </span><span class="n">applyCustomerSpecificModifications</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">applyFinalPricingAdjustments</span><span class="p">(</span><span class="n">QuoteResultingContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                            </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuote</span><span class="p">();</span>
<span class="w">        </span><span class="n">Customer</span><span class="w"> </span><span class="n">customer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getCustomer</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Apply volume discounts</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getTotalPrice</span><span class="p">().</span><span class="na">compareTo</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">(</span><span class="s">&quot;100000&quot;</span><span class="p">))</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">discount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getTotalPrice</span><span class="p">().</span><span class="na">multiply</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">(</span><span class="s">&quot;0.05&quot;</span><span class="p">));</span>
<span class="w">            </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">addPriceAdjustment</span><span class="p">(</span><span class="s">&quot;VOLUME_DISCOUNT&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">discount</span><span class="p">.</span><span class="na">negate</span><span class="p">());</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Apply customer tier discounts</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">customer</span><span class="p">.</span><span class="na">getTier</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">CustomerTier</span><span class="p">.</span><span class="na">PLATINUM</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">discount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getTotalPrice</span><span class="p">().</span><span class="na">multiply</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">(</span><span class="s">&quot;0.03&quot;</span><span class="p">));</span>
<span class="w">            </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">addPriceAdjustment</span><span class="p">(</span><span class="s">&quot;PLATINUM_DISCOUNT&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">discount</span><span class="p">.</span><span class="na">negate</span><span class="p">());</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="development-patterns">Development Patterns<a class="headerlink" href="#development-patterns" title="Permanent link">&para;</a></h2>
<h3 id="utility-classes">Utility Classes<a class="headerlink" href="#utility-classes" title="Permanent link">&para;</a></h3>
<h4 id="bandwidth-validator-utils">Bandwidth Validator Utils<a class="headerlink" href="#bandwidth-validator-utils" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">BandwidthValidatorUtils</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validateBandwidth</span><span class="p">(</span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">item</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">serviceType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">item</span><span class="p">.</span><span class="na">getServiceType</span><span class="p">();</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">serviceType</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="s">&quot;WAN_L2_CIR&quot;</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">validateL2CirBandwidth</span><span class="p">(</span><span class="n">item</span><span class="p">);</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="s">&quot;WAN_L2_EVC&quot;</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">validateL2EvcBandwidth</span><span class="p">(</span><span class="n">item</span><span class="p">);</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="s">&quot;WAN_L3_IP_QOS&quot;</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">validateL3IpQosBandwidth</span><span class="p">(</span><span class="n">item</span><span class="p">);</span>
<span class="w">            </span><span class="k">default</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">notApplicable</span><span class="p">();</span>
<span class="w">        </span><span class="p">};</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">BigDecimal</span><span class="w"> </span><span class="nf">extractBandwidthValue</span><span class="p">(</span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">item</span><span class="p">,</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">parameterName</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">item</span><span class="p">.</span><span class="na">getParameters</span><span class="p">().</span><span class="na">stream</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">filter</span><span class="p">(</span><span class="n">param</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">parameterName</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">param</span><span class="p">.</span><span class="na">getName</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">map</span><span class="p">(</span><span class="n">param</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">(</span><span class="n">param</span><span class="p">.</span><span class="na">getValue</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">findFirst</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">orElse</span><span class="p">(</span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">ZERO</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="testing-patterns">Testing Patterns<a class="headerlink" href="#testing-patterns" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@ExtendWith</span><span class="p">(</span><span class="n">MockitoExtension</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="kd">class</span> <span class="nc">TelusQuoteDeltaModificatorImplTest</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Mock</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">BandwidthValidatorUtils</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@InjectMocks</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">TelusQuoteDeltaModificatorImpl</span><span class="w"> </span><span class="n">modificator</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldApplyBandwidthValidation</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createTestContext</span><span class="p">();</span>
<span class="w">        </span><span class="n">when</span><span class="p">(</span><span class="n">bandwidthUtils</span><span class="p">.</span><span class="na">validateBandwidth</span><span class="p">(</span><span class="n">any</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">thenReturn</span><span class="p">(</span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">success</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">modificator</span><span class="p">.</span><span class="na">modifyQuote</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">.</span><span class="na">getValidationErrors</span><span class="p">()).</span><span class="na">isEmpty</span><span class="p">();</span>
<span class="w">        </span><span class="n">verify</span><span class="p">(</span><span class="n">bandwidthUtils</span><span class="p">).</span><span class="na">validateBandwidth</span><span class="p">(</span><span class="n">any</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="build-and-deployment">Build and Deployment<a class="headerlink" href="#build-and-deployment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Build the plugin</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install

<span class="c1"># Run tests</span>
mvn<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Deploy to QES environment</span>
mvn<span class="w"> </span>deploy<span class="w"> </span>-P<span class="w"> </span>qes-deployment

<span class="c1"># Docker build for containerized deployment</span>
docker<span class="w"> </span>build<span class="w"> </span>-t<span class="w"> </span>telus-qes-plugin:latest<span class="w"> </span>.
</code></pre></div>
<hr />
<p><strong>Next</strong>: Explore the <a href="../04-integration-guide/">Integration Guide</a> for system integration patterns.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "..", "features": [], "search": "../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>