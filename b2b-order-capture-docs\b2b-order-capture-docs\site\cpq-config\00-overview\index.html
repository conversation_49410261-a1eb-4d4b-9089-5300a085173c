
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/cpq-config/00-overview/">
      
      
        <link rel="prev" href="../../cip-config/09-chain-catalog/">
      
      
        <link rel="next" href="../01-configuration-management/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Overview - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#cpq-configuration-application-overview" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Overview
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" checked>
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#introduction" class="md-nav__link">
    <span class="md-ellipsis">
      Introduction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Introduction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#purpose-and-scope" class="md-nav__link">
    <span class="md-ellipsis">
      Purpose and Scope
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#cpq-architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      CPQ Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="CPQ Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#high-level-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      High-Level Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-deployment-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Deployment Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Core Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-and-infrastructure" class="md-nav__link">
    <span class="md-ellipsis">
      Security and Infrastructure
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Business Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Business Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#integration-chain-orchestration" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Chain Orchestration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-catalog-management" class="md-nav__link">
    <span class="md-ellipsis">
      Service Catalog Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technical-features" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Technical Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Technical Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#multi-environment-support" class="md-nav__link">
    <span class="md-ellipsis">
      Multi-Environment Support
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Security Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-automation" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Automation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#repository-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Repository Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Component Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Validation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#available-maven-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Available Maven Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-urls" class="md-nav__link">
    <span class="md-ellipsis">
      Development URLs
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#introduction" class="md-nav__link">
    <span class="md-ellipsis">
      Introduction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Introduction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#purpose-and-scope" class="md-nav__link">
    <span class="md-ellipsis">
      Purpose and Scope
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#cpq-architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      CPQ Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="CPQ Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#high-level-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      High-Level Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-deployment-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Deployment Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Core Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-and-infrastructure" class="md-nav__link">
    <span class="md-ellipsis">
      Security and Infrastructure
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Business Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Business Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#integration-chain-orchestration" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Chain Orchestration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-catalog-management" class="md-nav__link">
    <span class="md-ellipsis">
      Service Catalog Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technical-features" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Technical Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Technical Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#multi-environment-support" class="md-nav__link">
    <span class="md-ellipsis">
      Multi-Environment Support
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Security Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-automation" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Automation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#repository-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Repository Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Component Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Validation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#available-maven-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Available Maven Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-urls" class="md-nav__link">
    <span class="md-ellipsis">
      Development URLs
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="cpq-configuration-application-overview">CPQ Configuration Application Overview<a class="headerlink" href="#cpq-configuration-application-overview" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#introduction">Introduction</a></li>
<li><a href="#cpq-architecture-overview">CPQ Architecture Overview</a></li>
<li><a href="#technology-stack">Technology Stack</a></li>
<li><a href="#key-features">Key Features</a></li>
<li><a href="#project-structure">Project Structure</a></li>
<li><a href="#development-environment">Development Environment</a></li>
</ul>
<h2 id="introduction">Introduction<a class="headerlink" href="#introduction" title="Permanent link">&para;</a></h2>
<p>The <strong>nc-cloud-bss-cpq-config-b2b</strong> is a comprehensive Configure, Price, Quote (CPQ) configuration package that serves as the central configuration hub for TELUS B2B telecommunications services. This enterprise-grade configuration system orchestrates complex integration chains, manages service catalogs, handles API specifications, and provides deployment configurations for the entire TELUS B2B ecosystem.</p>
<h3 id="purpose-and-scope">Purpose and Scope<a class="headerlink" href="#purpose-and-scope" title="Permanent link">&para;</a></h3>
<p>The CPQ Configuration Application is designed to:
- <strong>Orchestrate Integration Workflows</strong>: Complex business process automation chains
- <strong>Manage Service Catalogs</strong>: Comprehensive API specifications and service definitions
- <strong>Handle Configuration Management</strong>: Environment-specific variables and settings
- <strong>Provide Security Configuration</strong>: TLS certificates and security policies
- <strong>Enable Deployment Automation</strong>: Helm charts and Kubernetes configurations</p>
<h2 id="cpq-architecture-overview">CPQ Architecture Overview<a class="headerlink" href="#cpq-architecture-overview" title="Permanent link">&para;</a></h2>
<h3 id="high-level-architecture">High-Level Architecture<a class="headerlink" href="#high-level-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;TELUS B2B Ecosystem&quot;
        A[Order Capture Frontend]
        B[Order Capture Backend]
        C[QES Extensions]
        D[External Systems]
    end

    subgraph &quot;Cloud Integration Platform&quot;
        E[CIP Engine]
        F[Configuration Package]
        G[Service Registry]
        H[Chain Orchestrator]
    end

    subgraph &quot;Configuration Components&quot;
        I[Integration Chains]
        J[Service Catalog]
        K[Variables Management]
        L[TLS Configuration]
    end

    subgraph &quot;External Integrations&quot;
        M[SFDC CRM]
        N[SAP Systems]
        O[Billing Systems]
        P[Network Services]
    end

    A --&gt; E
    B --&gt; E
    C --&gt; E
    D --&gt; E

    E --&gt; F
    F --&gt; G
    F --&gt; H

    F --&gt; I
    F --&gt; J
    F --&gt; K
    F --&gt; L

    H --&gt; M
    H --&gt; N
    H --&gt; O
    H --&gt; P

    style E fill:#673ab7,stroke:#512da8,color:#ffffff
    style F fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style I fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="configuration-deployment-flow">Configuration Deployment Flow<a class="headerlink" href="#configuration-deployment-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Dev as Developer
    participant Git as Git Repository
    participant CI as CI/CD Pipeline
    participant CIP as CIP Engine
    participant K8s as Kubernetes
    participant Ext as External Systems

    Dev-&gt;&gt;Git: Push Configuration Changes
    Git-&gt;&gt;CI: Trigger Build Pipeline
    CI-&gt;&gt;CI: Build Configuration Package
    CI-&gt;&gt;CI: Run Tests &amp; Validation
    CI-&gt;&gt;K8s: Deploy ConfigMaps &amp; Secrets
    CI-&gt;&gt;CIP: Deploy Configuration Package

    CIP-&gt;&gt;CIP: Load Variables from ConfigMaps
    CIP-&gt;&gt;CIP: Apply TLS Configurations
    CIP-&gt;&gt;CIP: Import Service Catalog
    CIP-&gt;&gt;CIP: Import Integration Chains
    CIP-&gt;&gt;CIP: Clear Temporary Variables

    CIP-&gt;&gt;Ext: Test External Connections
    Ext--&gt;&gt;CIP: Connection Confirmed
    CIP--&gt;&gt;CI: Deployment Success
</code></pre></div>
<h2 id="technology-stack">Technology Stack<a class="headerlink" href="#technology-stack" title="Permanent link">&para;</a></h2>
<h3 id="core-technologies">Core Technologies<a class="headerlink" href="#core-technologies" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Technology</th>
<th>Version</th>
<th>Purpose</th>
<th>Key Features</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>NetCracker CIP</strong></td>
<td>2023.3.x</td>
<td>Integration Platform</td>
<td>Chain orchestration, service registry, API gateway</td>
</tr>
<tr>
<td><strong>Apache Camel</strong></td>
<td>3.x</td>
<td>Integration Framework</td>
<td>Enterprise integration patterns, routing, mediation</td>
</tr>
<tr>
<td><strong>Kubernetes</strong></td>
<td>1.25+</td>
<td>Container Orchestration</td>
<td>ConfigMaps, Secrets, service discovery</td>
</tr>
<tr>
<td><strong>Helm</strong></td>
<td>3.x</td>
<td>Package Manager</td>
<td>Chart templating, environment management</td>
</tr>
<tr>
<td><strong>Maven</strong></td>
<td>3.8+</td>
<td>Build Tool</td>
<td>Dependency management, packaging, lifecycle</td>
</tr>
</tbody>
</table>
<h3 id="integration-technologies">Integration Technologies<a class="headerlink" href="#integration-technologies" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Technology</th>
<th>Purpose</th>
<th>Usage</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>TMF APIs</strong></td>
<td>Telecom Standards</td>
<td>Quote Management, Service Activation, Account Management</td>
</tr>
<tr>
<td><strong>REST/HTTP</strong></td>
<td>API Communication</td>
<td>Service-to-service communication</td>
</tr>
<tr>
<td><strong>SOAP/WSDL</strong></td>
<td>Legacy Integration</td>
<td>Enterprise system integration</td>
</tr>
<tr>
<td><strong>JSON/XML</strong></td>
<td>Data Exchange</td>
<td>Message transformation and routing</td>
</tr>
<tr>
<td><strong>Groovy Scripts</strong></td>
<td>Business Logic</td>
<td>Custom transformation and validation logic</td>
</tr>
</tbody>
</table>
<h3 id="security-and-infrastructure">Security and Infrastructure<a class="headerlink" href="#security-and-infrastructure" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;security&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;tls&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Environment-specific TLS certificates&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;authentication&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;OAuth 2.0, Bearer tokens, M2M authentication&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;authorization&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Role-based access control&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;secrets&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Kubernetes Secrets management&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;infrastructure&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;containerization&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Docker containers&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;orchestration&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Kubernetes&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;networking&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Service mesh with Istio&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;monitoring&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Prometheus, Grafana&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;logging&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ELK Stack&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="key-features">Key Features<a class="headerlink" href="#key-features" title="Permanent link">&para;</a></h2>
<h3 id="business-features">🎯 <strong>Business Features</strong><a class="headerlink" href="#business-features" title="Permanent link">&para;</a></h3>
<h4 id="integration-chain-orchestration">Integration Chain Orchestration<a class="headerlink" href="#integration-chain-orchestration" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Credit Assessment Workflows</strong>: SFDC integration for automated credit evaluation</li>
<li><strong>Quote Management Chains</strong>: TMF Quote API integration with state management</li>
<li><strong>Service Provisioning</strong>: Multi-system orchestration for service activation</li>
<li><strong>Billing Integration</strong>: External billing system coordination and synchronization</li>
<li><strong>Order Lifecycle Management</strong>: End-to-end order processing automation</li>
</ul>
<h4 id="service-catalog-management">Service Catalog Management<a class="headerlink" href="#service-catalog-management" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>API Specification Registry</strong>: Comprehensive OpenAPI/Swagger specifications</li>
<li><strong>Service Discovery</strong>: Dynamic service registration and discovery</li>
<li><strong>Version Management</strong>: Multiple API versions with backward compatibility</li>
<li><strong>Environment Mapping</strong>: Service endpoint configuration per environment</li>
<li><strong>Health Monitoring</strong>: Service availability and performance tracking</li>
</ul>
<h4 id="configuration-management">Configuration Management<a class="headerlink" href="#configuration-management" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Environment Variables</strong>: Centralized variable management across environments</li>
<li><strong>Secrets Management</strong>: Secure handling of sensitive configuration data</li>
<li><strong>Feature Flags</strong>: Dynamic feature enablement and configuration</li>
<li><strong>Business Rules</strong>: Externalized business logic configuration</li>
<li><strong>Integration Parameters</strong>: Timeout, retry, and connection settings</li>
</ul>
<h3 id="technical-features">🔧 <strong>Technical Features</strong><a class="headerlink" href="#technical-features" title="Permanent link">&para;</a></h3>
<h4 id="multi-environment-support">Multi-Environment Support<a class="headerlink" href="#multi-environment-support" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>QA Environment</strong>: Development and testing configurations</li>
<li><strong>Staging Environment</strong>: Pre-production validation</li>
<li><strong>Production Environment</strong>: Live system configurations</li>
<li><strong>Environment Isolation</strong>: Separate configurations and security policies</li>
<li><strong>Promotion Pipeline</strong>: Automated configuration promotion across environments</li>
</ul>
<h4 id="security-configuration">Security Configuration<a class="headerlink" href="#security-configuration" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>TLS Certificate Management</strong>: Environment-specific certificate deployment</li>
<li><strong>Gateway Security</strong>: Ingress and egress gateway configurations</li>
<li><strong>Mutual TLS</strong>: Service-to-service encryption</li>
<li><strong>Certificate Rotation</strong>: Automated certificate lifecycle management</li>
<li><strong>Security Policies</strong>: Network policies and access controls</li>
</ul>
<h4 id="deployment-automation">Deployment Automation<a class="headerlink" href="#deployment-automation" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Helm Chart Templates</strong>: Kubernetes resource templating</li>
<li><strong>ConfigMap Management</strong>: Dynamic configuration injection</li>
<li><strong>Secret Provisioning</strong>: Secure credential management</li>
<li><strong>Rolling Updates</strong>: Zero-downtime configuration updates</li>
<li><strong>Rollback Capabilities</strong>: Configuration version rollback support</li>
</ul>
<h2 id="project-structure">Project Structure<a class="headerlink" href="#project-structure" title="Permanent link">&para;</a></h2>
<h3 id="repository-organization">Repository Organization<a class="headerlink" href="#repository-organization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>nc-cloud-bss-cpq-config-b2b/
├── content/                                           # Core configuration content
│   ├── config.json                                    # Main configuration orchestrator
│   ├── chains/                                        # Integration chain definitions
│   │   ├── 901ec8bd-32c0-4e48-9996-0b2100c2b79d/     # Credit Assessment Request chain
│   │   │   ├── chain-901ec8bd-32c0-4e48-9996-0b2100c2b79d.yaml
│   │   │   ├── script-before-*.groovy                 # Pre-processing scripts
│   │   │   ├── script-200-*.groovy                    # Success response handlers
│   │   │   └── script-default-*.groovy                # Default response handlers
│   │   ├── 314187cb-4e20-4f23-be4f-6e7c7c422d44/     # Quote management chain
│   │   ├── 4b5bae82-bab0-4211-8aab-0c81692ebc88/     # Service provisioning chain
│   │   └── [other-chain-ids]/                        # Additional integration chains
│   ├── services/                                      # Service catalog and API specifications
│   │   ├── catalog-integration-tmf/                   # TMF Catalog Integration
│   │   │   ├── service-catalog-integration-tmf.yaml   # Service definition
│   │   │   └── source-citmf-620-v2.9-v2.9/           # API specification
│   │   ├── 310a1ed6-b83c-4dff-a288-231b8c486a13/     # SFDC CRM Integration
│   │   │   ├── service-310a1ed6-b83c-4dff-a288-231b8c486a13.yaml
│   │   │   └── source-310a1ed6-b83c-4dff-a288-231b8c486a13-SFDC CRM-*/
│   │   ├── quote-tmf-service/                         # TMF Quote Management
│   │   ├── install-base-service/                     # Product Inventory Service
│   │   └── [other-service-ids]/                      # Additional service definitions
│   └── variables/                                     # Environment variables
│       ├── common-variables.yaml                     # Common configuration variables
│       └── secured-variables.yaml                    # Encrypted sensitive variables
├── deployments/                                       # Deployment configurations
│   ├── charts/                                        # Helm charts
│   │   └── telus-cip-config-package/                 # Main Helm chart
│   │       ├── Chart.yaml                            # Chart metadata
│   │       ├── values.yaml                           # Default values
│   │       ├── values-qa.yaml                        # QA environment values
│   │       ├── values-staging.yaml                   # Staging environment values
│   │       ├── values-production.yaml                # Production environment values
│   │       └── templates/                            # Kubernetes templates
│   │           ├── cip-common-variables-configmap.yaml
│   │           ├── cip-secured-variables-configmap.yaml
│   │           ├── deployment.yaml                   # Application deployment
│   │           ├── service.yaml                      # Kubernetes service
│   │           ├── ingress.yaml                      # Ingress configuration
│   │           └── configmap.yaml                    # Additional ConfigMaps
│   └── deployment-configuration.json                 # Environment-specific deployment config
├── TLS-config/                                        # Security configurations
│   └── cert/                                         # TLS certificates by environment
│       ├── gateway/                                  # Gateway certificates
│       │   ├── QA/                                   # QA environment certificates
│       │   ├── STAGING/                              # Staging environment certificates
│       │   └── PRODUCTION/                           # Production environment certificates
│       └── cluster/                                  # Cluster certificates
│           ├── QA/                                   # QA cluster certificates
│           ├── STAGING/                              # Staging cluster certificates
│           └── PRODUCTION/                           # Production cluster certificates
├── src/test/groovy/                                  # Test suites
│   ├── integration/                                  # Integration tests
│   ├── unit/                                         # Unit tests
│   └── performance/                                  # Performance tests
├── descriptors/                                      # Maven assembly descriptors
│   ├── cip-config-assembly.xml                      # Configuration package assembly
│   └── test-assembly.xml                             # Test package assembly
├── pom.xml                                           # Maven build configuration
├── README.md                                         # Project documentation
├── CHANGELOG.md                                      # Version history
└── LICENSE                                           # License information
</code></pre></div>
<h3 id="configuration-component-architecture">Configuration Component Architecture<a class="headerlink" href="#configuration-component-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Configuration Package&quot;
        A[config.json]
        B[Integration Chains]
        C[Service Catalog]
        D[Variables]
        E[TLS Config]
    end

    subgraph &quot;Integration Chains&quot;
        F[Credit Assessment]
        G[Quote Management]
        H[Service Provisioning]
        I[Billing Integration]
        J[Order Lifecycle]
    end

    subgraph &quot;Service Catalog&quot;
        K[TMF APIs]
        L[SFDC Integration]
        M[SAP Systems]
        N[Billing Services]
        O[Network Services]
    end

    subgraph &quot;Variables Management&quot;
        P[Common Variables]
        Q[Secured Variables]
        R[Environment Config]
        S[Feature Flags]
    end

    subgraph &quot;Security Configuration&quot;
        T[Gateway Certificates]
        U[Cluster Certificates]
        V[TLS Policies]
        W[Security Rules]
    end

    A --&gt; B
    A --&gt; C
    A --&gt; D
    A --&gt; E

    B --&gt; F
    B --&gt; G
    B --&gt; H
    B --&gt; I
    B --&gt; J

    C --&gt; K
    C --&gt; L
    C --&gt; M
    C --&gt; N
    C --&gt; O

    D --&gt; P
    D --&gt; Q
    D --&gt; R
    D --&gt; S

    E --&gt; T
    E --&gt; U
    E --&gt; V
    E --&gt; W

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style C fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h2 id="development-environment">Development Environment<a class="headerlink" href="#development-environment" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Required Software Versions</span>
Java:<span class="w"> </span>OpenJDK<span class="w"> </span><span class="m">11</span><span class="w"> </span>or<span class="w"> </span>higher
Maven:<span class="w"> </span><span class="m">3</span>.8.x<span class="w"> </span>or<span class="w"> </span>higher
Docker:<span class="w"> </span><span class="m">20</span>.x<span class="w"> </span>or<span class="w"> </span>higher
Kubernetes:<span class="w"> </span><span class="m">1</span>.25+<span class="w"> </span><span class="o">(</span>minikube<span class="w"> </span>or<span class="w"> </span>kind<span class="w"> </span><span class="k">for</span><span class="w"> </span><span class="nb">local</span><span class="w"> </span>development<span class="o">)</span>
Helm:<span class="w"> </span><span class="m">3</span>.x<span class="w"> </span>or<span class="w"> </span>higher
Git:<span class="w"> </span><span class="m">2</span>.x<span class="w"> </span>or<span class="w"> </span>higher
IDE:<span class="w"> </span>IntelliJ<span class="w"> </span>IDEA<span class="w"> </span>or<span class="w"> </span>VS<span class="w"> </span>Code<span class="w"> </span><span class="o">(</span>recommended<span class="o">)</span>
</code></pre></div>
<h3 id="environment-setup">Environment Setup<a class="headerlink" href="#environment-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Clone repository</span>
git<span class="w"> </span>clone<span class="w"> </span>&lt;repository-url&gt;
<span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-cpq-config-b2b

<span class="c1"># 2. Verify prerequisites</span>
java<span class="w"> </span>-version
mvn<span class="w"> </span>-version
docker<span class="w"> </span>--version
kubectl<span class="w"> </span>version<span class="w"> </span>--client
helm<span class="w"> </span>version

<span class="c1"># 3. Build configuration package</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package

<span class="c1"># 4. Validate configuration structure</span>
mvn<span class="w"> </span>validate

<span class="c1"># 5. Run tests</span>
mvn<span class="w"> </span><span class="nb">test</span>
</code></pre></div>
<h3 id="local-development-setup">Local Development Setup<a class="headerlink" href="#local-development-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Start local Kubernetes cluster</span>
minikube<span class="w"> </span>start<span class="w"> </span>--memory<span class="o">=</span><span class="m">8192</span><span class="w"> </span>--cpus<span class="o">=</span><span class="m">4</span>

<span class="c1"># 2. Install CIP Engine (if available locally)</span>
helm<span class="w"> </span>repo<span class="w"> </span>add<span class="w"> </span>netcracker<span class="w"> </span>&lt;netcracker-helm-repo&gt;
helm<span class="w"> </span>install<span class="w"> </span>cip-engine<span class="w"> </span>netcracker/cip-engine

<span class="c1"># 3. Deploy configuration package</span>
helm<span class="w"> </span>install<span class="w"> </span>cip-config<span class="w"> </span>deployments/charts/telus-cip-config-package<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--values<span class="w"> </span>deployments/charts/telus-cip-config-package/values-qa.yaml

<span class="c1"># 4. Verify deployment</span>
kubectl<span class="w"> </span>get<span class="w"> </span>pods<span class="w"> </span>-l<span class="w"> </span><span class="nv">app</span><span class="o">=</span>cip-config
kubectl<span class="w"> </span>get<span class="w"> </span>configmaps
kubectl<span class="w"> </span>get<span class="w"> </span>secrets
</code></pre></div>
<h3 id="configuration-validation">Configuration Validation<a class="headerlink" href="#configuration-validation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># validation-config.yaml</span>
<span class="nt">validation</span><span class="p">:</span>
<span class="w">  </span><span class="nt">chains</span><span class="p">:</span>
<span class="w">    </span><span class="nt">syntax-check</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">dependency-validation</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">script-compilation</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">  </span><span class="nt">services</span><span class="p">:</span>
<span class="w">    </span><span class="nt">api-spec-validation</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">endpoint-connectivity</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span><span class="w">  </span><span class="c1"># Disabled for local development</span>
<span class="w">    </span><span class="nt">schema-validation</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">  </span><span class="nt">variables</span><span class="p">:</span>
<span class="w">    </span><span class="nt">required-variables-check</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">environment-consistency</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">secret-validation</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">  </span><span class="nt">security</span><span class="p">:</span>
<span class="w">    </span><span class="nt">certificate-validation</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">tls-configuration-check</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">security-policy-validation</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</code></pre></div>
<h3 id="available-maven-commands">Available Maven Commands<a class="headerlink" href="#available-maven-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Build commands</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile<span class="w">                    </span><span class="c1"># Compile configuration files</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w">                    </span><span class="c1"># Package configuration bundle</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w">                    </span><span class="c1"># Install to local repository</span>
mvn<span class="w"> </span>clean<span class="w"> </span>deploy<span class="w">                     </span><span class="c1"># Deploy to remote repository</span>

<span class="c1"># Validation commands</span>
mvn<span class="w"> </span>validate<span class="w">                         </span><span class="c1"># Validate configuration structure</span>
mvn<span class="w"> </span>cip:validate-chains<span class="w">             </span><span class="c1"># Validate integration chains</span>
mvn<span class="w"> </span>cip:validate-services<span class="w">           </span><span class="c1"># Validate service definitions</span>
mvn<span class="w"> </span>cip:validate-variables<span class="w">          </span><span class="c1"># Validate variable configurations</span>

<span class="c1"># Testing commands</span>
mvn<span class="w"> </span><span class="nb">test</span><span class="w">                            </span><span class="c1"># Run unit tests</span>
mvn<span class="w"> </span>integration-test<span class="w">                </span><span class="c1"># Run integration tests</span>
mvn<span class="w"> </span>verify<span class="w">                          </span><span class="c1"># Run all tests and validations</span>

<span class="c1"># Deployment commands</span>
mvn<span class="w"> </span>cip:deploy-config<span class="w">               </span><span class="c1"># Deploy configuration to CIP</span>
mvn<span class="w"> </span>helm:package<span class="w">                    </span><span class="c1"># Package Helm chart</span>
mvn<span class="w"> </span>helm:deploy<span class="w">                     </span><span class="c1"># Deploy Helm chart</span>

<span class="c1"># Development commands</span>
mvn<span class="w"> </span>cip:generate-docs<span class="w">               </span><span class="c1"># Generate configuration documentation</span>
mvn<span class="w"> </span>cip:export-config<span class="w">               </span><span class="c1"># Export configuration for analysis</span>
mvn<span class="w"> </span>cip:import-config<span class="w">               </span><span class="c1"># Import configuration from external source</span>
</code></pre></div>
<h3 id="development-urls">Development URLs<a class="headerlink" href="#development-urls" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Environment</th>
<th>URL</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Local CIP Engine</strong></td>
<td>http://localhost:8080</td>
<td>Local development server</td>
</tr>
<tr>
<td><strong>Configuration UI</strong></td>
<td>http://localhost:8080/config</td>
<td>Configuration management interface</td>
</tr>
<tr>
<td><strong>Chain Designer</strong></td>
<td>http://localhost:8080/chains</td>
<td>Integration chain designer</td>
</tr>
<tr>
<td><strong>Service Registry</strong></td>
<td>http://localhost:8080/services</td>
<td>Service catalog management</td>
</tr>
<tr>
<td><strong>Monitoring</strong></td>
<td>http://localhost:8080/monitoring</td>
<td>System health and metrics</td>
</tr>
</tbody>
</table>
<hr />
<p><strong>Next</strong>: <a href="../01-configuration-management/">Configuration Management →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>