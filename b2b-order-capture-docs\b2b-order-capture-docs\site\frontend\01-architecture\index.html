
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/frontend/01-architecture/">
      
      
        <link rel="prev" href="../00-overview/">
      
      
        <link rel="next" href="../02-custom-components/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Architecture - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#frontend-architecture" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Architecture
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      System Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="System Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#application-layers" class="md-nav__link">
    <span class="md-ellipsis">
      Application Layers
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-hierarchy" class="md-nav__link">
    <span class="md-ellipsis">
      Component Hierarchy
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Component Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Component Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#custom-telus-components-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Custom TELUS Components Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Custom TELUS Components Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-quote-components" class="md-nav__link">
    <span class="md-ellipsis">
      Core Quote Components
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#location-management-components" class="md-nav__link">
    <span class="md-ellipsis">
      Location Management Components
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pricing-and-cart-components" class="md-nav__link">
    <span class="md-ellipsis">
      Pricing and Cart Components
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-communication-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Component Communication Patterns
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#state-management" class="md-nav__link">
    <span class="md-ellipsis">
      State Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="State Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#ngrx-store-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      NgRx Store Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#state-interfaces" class="md-nav__link">
    <span class="md-ellipsis">
      State Interfaces
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#action-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Action Patterns
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#data-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Data Flow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Flow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#requestresponse-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Request/Response Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#error-handling-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#module-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Module Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Module Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#feature-module-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Feature Module Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#lazy-loading-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Lazy Loading Strategy
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Security Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#authentication-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-interceptors" class="md-nav__link">
    <span class="md-ellipsis">
      Security Interceptors
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      System Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="System Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#application-layers" class="md-nav__link">
    <span class="md-ellipsis">
      Application Layers
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-hierarchy" class="md-nav__link">
    <span class="md-ellipsis">
      Component Hierarchy
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Component Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Component Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#custom-telus-components-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Custom TELUS Components Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Custom TELUS Components Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-quote-components" class="md-nav__link">
    <span class="md-ellipsis">
      Core Quote Components
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#location-management-components" class="md-nav__link">
    <span class="md-ellipsis">
      Location Management Components
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pricing-and-cart-components" class="md-nav__link">
    <span class="md-ellipsis">
      Pricing and Cart Components
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-communication-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Component Communication Patterns
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#state-management" class="md-nav__link">
    <span class="md-ellipsis">
      State Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="State Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#ngrx-store-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      NgRx Store Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#state-interfaces" class="md-nav__link">
    <span class="md-ellipsis">
      State Interfaces
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#action-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Action Patterns
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#data-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Data Flow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Flow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#requestresponse-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Request/Response Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#error-handling-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#module-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Module Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Module Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#feature-module-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Feature Module Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#lazy-loading-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Lazy Loading Strategy
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Security Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#authentication-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-interceptors" class="md-nav__link">
    <span class="md-ellipsis">
      Security Interceptors
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="frontend-architecture">Frontend Architecture<a class="headerlink" href="#frontend-architecture" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#system-architecture">System Architecture</a></li>
<li><a href="#component-architecture">Component Architecture</a></li>
<li><a href="#state-management">State Management</a></li>
<li><a href="#data-flow">Data Flow</a></li>
<li><a href="#module-structure">Module Structure</a></li>
<li><a href="#security-architecture">Security Architecture</a></li>
</ul>
<h2 id="system-architecture">System Architecture<a class="headerlink" href="#system-architecture" title="Permanent link">&para;</a></h2>
<h3 id="application-layers">Application Layers<a class="headerlink" href="#application-layers" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Presentation Layer&quot;
        A[Angular Components]
        B[TELUS Custom Components]
        C[UI Templates &amp; Styles]
    end

    subgraph &quot;Business Logic Layer&quot;
        D[Services &amp; Utilities]
        E[State Management - NgRx]
        F[Business Rules]
    end

    subgraph &quot;Data Access Layer&quot;
        G[HTTP Interceptors]
        H[API Services]
        I[Local Storage]
    end

    subgraph &quot;External Systems&quot;
        J[Backend APIs]
        K[Google Maps API]
        L[TELUS Services]
        M[Authentication Service]
    end

    A --&gt; D
    B --&gt; E
    C --&gt; F
    D --&gt; G
    E --&gt; H
    F --&gt; I
    G --&gt; J
    H --&gt; K
    I --&gt; L
    G --&gt; M

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style E fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style J fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="component-hierarchy">Component Hierarchy<a class="headerlink" href="#component-hierarchy" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Root Level&quot;
        A[App Component]
        B[App Module]
    end

    subgraph &quot;Layout Components&quot;
        C[Header Component]
        D[Navigation Component]
        E[Footer Component]
        F[Sidebar Component]
    end

    subgraph &quot;Feature Components&quot;
        G[Quote Components]
        H[Order Components]
        I[Service Components]
        J[Location Components]
    end

    subgraph &quot;Custom TELUS Components&quot;
        K[telus-start-quotation]
        L[telus-assign-location]
        M[telus-cart-total-prices]
        N[telus-quote-view]
        O[telus-service-selector]
        P[telus-location-picker]
    end

    subgraph &quot;Shared Components&quot;
        Q[Loading Spinner]
        R[Error Display]
        S[Confirmation Dialog]
        T[Data Table]
    end

    A --&gt; C
    A --&gt; D
    A --&gt; E
    A --&gt; F

    C --&gt; G
    D --&gt; H
    E --&gt; I
    F --&gt; J

    G --&gt; K
    H --&gt; L
    I --&gt; M
    J --&gt; N
    G --&gt; O
    J --&gt; P

    A --&gt; Q
    A --&gt; R
    A --&gt; S
    A --&gt; T

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style K fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style L fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style M fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style N fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
</code></pre></div>
<h2 id="component-architecture">Component Architecture<a class="headerlink" href="#component-architecture" title="Permanent link">&para;</a></h2>
<h3 id="custom-telus-components-overview">Custom TELUS Components Overview<a class="headerlink" href="#custom-telus-components-overview" title="Permanent link">&para;</a></h3>
<p>The application features 18+ custom components specifically designed for TELUS B2B workflows:</p>
<h4 id="core-quote-components">Core Quote Components<a class="headerlink" href="#core-quote-components" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// telus-start-quotation Component</span>
<span class="kd">@Component</span><span class="p">({</span>
<span class="w">  </span><span class="nx">selector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;telus-start-quotation&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">templateUrl</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;./telus-start-quotation.component.html&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">styleUrls</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;./telus-start-quotation.component.less&#39;</span><span class="p">]</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">TelusStartQuotationComponent</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">OnInit</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">customerId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">quoteStarted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">Quote</span><span class="o">&gt;</span><span class="p">();</span>

<span class="w">  </span><span class="nx">quoteForm</span><span class="o">:</span><span class="w"> </span><span class="kt">FormGroup</span><span class="p">;</span>
<span class="w">  </span><span class="nx">services$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectAvailableServices</span><span class="p">);</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">fb</span><span class="o">:</span><span class="w"> </span><span class="kt">FormBuilder</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">store</span><span class="o">:</span><span class="w"> </span><span class="kt">Store</span><span class="o">&lt;</span><span class="nx">AppState</span><span class="o">&gt;</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">quoteService</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteService</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="nx">startQuotation</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">quoteForm</span><span class="p">.</span><span class="nx">valid</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">quoteData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">quoteForm</span><span class="p">.</span><span class="nx">value</span><span class="p">;</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">dispatch</span><span class="p">(</span><span class="nx">startQuotation</span><span class="p">({</span><span class="w"> </span><span class="nx">quoteData</span><span class="w"> </span><span class="p">}));</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="location-management-components">Location Management Components<a class="headerlink" href="#location-management-components" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// telus-assign-location Component</span>
<span class="kd">@Component</span><span class="p">({</span>
<span class="w">  </span><span class="nx">selector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;telus-assign-location&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">templateUrl</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;./telus-assign-location.component.html&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">styleUrls</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;./telus-assign-location.component.less&#39;</span><span class="p">]</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">TelusAssignLocationComponent</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">OnInit</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">locationAssigned</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">Location</span><span class="o">&gt;</span><span class="p">();</span>

<span class="w">  </span><span class="nx">locationForm</span><span class="o">:</span><span class="w"> </span><span class="kt">FormGroup</span><span class="p">;</span>
<span class="w">  </span><span class="nx">searchResults</span><span class="o">:</span><span class="w"> </span><span class="kt">Location</span><span class="p">[]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>
<span class="w">  </span><span class="nx">selectedLocation</span><span class="o">:</span><span class="w"> </span><span class="kt">Location</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">fb</span><span class="o">:</span><span class="w"> </span><span class="kt">FormBuilder</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">locationService</span><span class="o">:</span><span class="w"> </span><span class="kt">LocationService</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">mapsService</span><span class="o">:</span><span class="w"> </span><span class="kt">GoogleMapsService</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="nx">searchLocations</span><span class="p">(</span><span class="nx">query</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">locationService</span><span class="p">.</span><span class="nx">searchLocations</span><span class="p">(</span><span class="nx">query</span><span class="p">)</span>
<span class="w">      </span><span class="p">.</span><span class="nx">subscribe</span><span class="p">(</span><span class="nx">results</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">searchResults</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">results</span><span class="p">;</span>
<span class="w">      </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">validateAddress</span><span class="p">(</span><span class="nx">address</span><span class="o">:</span><span class="w"> </span><span class="kt">Address</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">ValidationResult</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">mapsService</span><span class="p">.</span><span class="nx">validateAddress</span><span class="p">(</span><span class="nx">address</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="pricing-and-cart-components">Pricing and Cart Components<a class="headerlink" href="#pricing-and-cart-components" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// telus-cart-total-prices Component</span>
<span class="kd">@Component</span><span class="p">({</span>
<span class="w">  </span><span class="nx">selector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;telus-cart-total-prices&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">templateUrl</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;./telus-cart-total-prices.component.html&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">styleUrls</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;./telus-cart-total-prices.component.less&#39;</span><span class="p">],</span>
<span class="w">  </span><span class="nx">changeDetection</span><span class="o">:</span><span class="w"> </span><span class="kt">ChangeDetectionStrategy.OnPush</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">TelusCartTotalPricesComponent</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">OnInit</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">cartItems</span><span class="o">:</span><span class="w"> </span><span class="kt">CartItem</span><span class="p">[];</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">priceUpdated</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">PriceCalculation</span><span class="o">&gt;</span><span class="p">();</span>

<span class="w">  </span><span class="nx">totalPrice$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectCartTotal</span><span class="p">);</span>
<span class="w">  </span><span class="nx">discounts$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectAppliedDiscounts</span><span class="p">);</span>
<span class="w">  </span><span class="nx">taxes$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectCalculatedTaxes</span><span class="p">);</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">store</span><span class="o">:</span><span class="w"> </span><span class="kt">Store</span><span class="o">&lt;</span><span class="nx">AppState</span><span class="o">&gt;</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">pricingService</span><span class="o">:</span><span class="w"> </span><span class="kt">PricingService</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">cdr</span><span class="o">:</span><span class="w"> </span><span class="kt">ChangeDetectorRef</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="nx">calculateTotal</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">dispatch</span><span class="p">(</span><span class="nx">calculateCartTotal</span><span class="p">({</span><span class="w"> </span><span class="nx">cartItems</span><span class="o">:</span><span class="w"> </span><span class="kt">this.cartItems</span><span class="w"> </span><span class="p">}));</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">applyDiscount</span><span class="p">(</span><span class="nx">discountCode</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">dispatch</span><span class="p">(</span><span class="nx">applyDiscount</span><span class="p">({</span><span class="w"> </span><span class="nx">code</span><span class="o">:</span><span class="w"> </span><span class="kt">discountCode</span><span class="w"> </span><span class="p">}));</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="component-communication-patterns">Component Communication Patterns<a class="headerlink" href="#component-communication-patterns" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant User
    participant QuoteComponent
    participant LocationComponent
    participant CartComponent
    participant Store
    participant API

    User-&gt;&gt;QuoteComponent: Start Quotation
    QuoteComponent-&gt;&gt;Store: Dispatch startQuotation
    Store-&gt;&gt;API: Create Quote Request
    API--&gt;&gt;Store: Quote Created
    Store--&gt;&gt;QuoteComponent: Quote State Updated

    QuoteComponent-&gt;&gt;LocationComponent: Assign Location
    LocationComponent-&gt;&gt;Store: Dispatch assignLocation
    Store-&gt;&gt;API: Validate Location
    API--&gt;&gt;Store: Location Validated
    Store--&gt;&gt;LocationComponent: Location State Updated

    LocationComponent-&gt;&gt;CartComponent: Add Services
    CartComponent-&gt;&gt;Store: Dispatch addToCart
    Store-&gt;&gt;API: Calculate Pricing
    API--&gt;&gt;Store: Pricing Updated
    Store--&gt;&gt;CartComponent: Cart State Updated
    CartComponent--&gt;&gt;User: Display Total
</code></pre></div>
<h2 id="state-management">State Management<a class="headerlink" href="#state-management" title="Permanent link">&para;</a></h2>
<h3 id="ngrx-store-architecture">NgRx Store Architecture<a class="headerlink" href="#ngrx-store-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;NgRx Store&quot;
        A[App State]
        B[Quote State]
        C[Cart State]
        D[User State]
        E[Service State]
        F[Location State]
    end

    subgraph &quot;Actions&quot;
        G[Quote Actions]
        H[Cart Actions]
        I[User Actions]
        J[Service Actions]
        K[Location Actions]
    end

    subgraph &quot;Reducers&quot;
        L[Quote Reducer]
        M[Cart Reducer]
        N[User Reducer]
        O[Service Reducer]
        P[Location Reducer]
    end

    subgraph &quot;Effects&quot;
        Q[Quote Effects]
        R[Cart Effects]
        S[User Effects]
        T[Service Effects]
        U[Location Effects]
    end

    subgraph &quot;Selectors&quot;
        V[Quote Selectors]
        W[Cart Selectors]
        X[User Selectors]
        Y[Service Selectors]
        Z[Location Selectors]
    end

    A --&gt; B
    A --&gt; C
    A --&gt; D
    A --&gt; E
    A --&gt; F

    G --&gt; L
    H --&gt; M
    I --&gt; N
    J --&gt; O
    K --&gt; P

    L --&gt; B
    M --&gt; C
    N --&gt; D
    O --&gt; E
    P --&gt; F

    Q --&gt; G
    R --&gt; H
    S --&gt; I
    T --&gt; J
    U --&gt; K

    B --&gt; V
    C --&gt; W
    D --&gt; X
    E --&gt; Y
    F --&gt; Z

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style C fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
</code></pre></div>
<h3 id="state-interfaces">State Interfaces<a class="headerlink" href="#state-interfaces" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Application State Structure</span>
<span class="kd">interface</span><span class="w"> </span><span class="nx">AppState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">cart</span><span class="o">:</span><span class="w"> </span><span class="kt">CartState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">user</span><span class="o">:</span><span class="w"> </span><span class="kt">UserState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">services</span><span class="o">:</span><span class="w"> </span><span class="kt">ServiceState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">locations</span><span class="o">:</span><span class="w"> </span><span class="kt">LocationState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">ui</span><span class="o">:</span><span class="w"> </span><span class="kt">UIState</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// Quote State</span>
<span class="kd">interface</span><span class="w"> </span><span class="nx">QuoteState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">selectedQuote</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="nx">filters</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteFilters</span><span class="p">;</span>
<span class="w">  </span><span class="nx">pagination</span><span class="o">:</span><span class="w"> </span><span class="kt">Pagination</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// Cart State</span>
<span class="kd">interface</span><span class="w"> </span><span class="nx">CartState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">items</span><span class="o">:</span><span class="w"> </span><span class="kt">CartItem</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">discounts</span><span class="o">:</span><span class="w"> </span><span class="kt">Discount</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">taxes</span><span class="o">:</span><span class="w"> </span><span class="kt">Tax</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// Service State</span>
<span class="kd">interface</span><span class="w"> </span><span class="nx">ServiceState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">availableServices</span><span class="o">:</span><span class="w"> </span><span class="kt">Service</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">selectedServices</span><span class="o">:</span><span class="w"> </span><span class="kt">Service</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">serviceCategories</span><span class="o">:</span><span class="w"> </span><span class="kt">ServiceCategory</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// Location State</span>
<span class="kd">interface</span><span class="w"> </span><span class="nx">LocationState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">searchResults</span><span class="o">:</span><span class="w"> </span><span class="kt">Location</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">selectedLocation</span><span class="o">:</span><span class="w"> </span><span class="kt">Location</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="nx">serviceAreas</span><span class="o">:</span><span class="w"> </span><span class="kt">ServiceArea</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="action-patterns">Action Patterns<a class="headerlink" href="#action-patterns" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Quote Actions</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">startQuotation</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Quote] Start Quotation&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">quoteData</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteRequest</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">startQuotationSuccess</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Quote] Start Quotation Success&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">quote</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">startQuotationFailure</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Quote] Start Quotation Failure&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="c1">// Cart Actions</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">addToCart</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Cart] Add To Cart&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">service</span><span class="o">:</span><span class="w"> </span><span class="kt">Service</span><span class="p">,</span><span class="w"> </span><span class="nx">quantity</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">calculateCartTotal</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Cart] Calculate Total&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">cartItems</span><span class="o">:</span><span class="w"> </span><span class="kt">CartItem</span><span class="p">[]</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">applyDiscount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Cart] Apply Discount&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">code</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>
</code></pre></div>
<h2 id="data-flow">Data Flow<a class="headerlink" href="#data-flow" title="Permanent link">&para;</a></h2>
<h3 id="requestresponse-flow">Request/Response Flow<a class="headerlink" href="#requestresponse-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Component
    participant Store
    participant Effect
    participant Service
    participant API
    participant Backend

    Component-&gt;&gt;Store: Dispatch Action
    Store-&gt;&gt;Effect: Action Intercepted
    Effect-&gt;&gt;Service: Call Service Method
    Service-&gt;&gt;API: HTTP Request
    API-&gt;&gt;Backend: API Call
    Backend--&gt;&gt;API: Response
    API--&gt;&gt;Service: HTTP Response
    Service--&gt;&gt;Effect: Processed Data
    Effect-&gt;&gt;Store: Dispatch Success/Failure
    Store--&gt;&gt;Component: State Updated
    Component--&gt;&gt;Component: UI Updated
</code></pre></div>
<h3 id="error-handling-flow">Error Handling Flow<a class="headerlink" href="#error-handling-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    A[HTTP Request] --&gt; B{Request Success?}
    B --&gt;|Yes| C[Process Response]
    B --&gt;|No| D[HTTP Interceptor]

    C --&gt; E[Update State]
    D --&gt; F{Error Type?}

    F --&gt;|401| G[Redirect to Login]
    F --&gt;|403| H[Show Access Denied]
    F --&gt;|500| I[Show Server Error]
    F --&gt;|Network| J[Show Network Error]

    G --&gt; K[Clear User State]
    H --&gt; L[Log Error]
    I --&gt; L
    J --&gt; L

    L --&gt; M[Display Error Message]
    M --&gt; N[Allow Retry]

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style D fill:#f44336,stroke:#d32f2f,color:#ffffff
    style M fill:#ff9800,stroke:#f57c00,color:#ffffff
</code></pre></div>
<h2 id="module-structure">Module Structure<a class="headerlink" href="#module-structure" title="Permanent link">&para;</a></h2>
<h3 id="feature-module-architecture">Feature Module Architecture<a class="headerlink" href="#feature-module-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Quotes Module Structure</span>
<span class="kd">@NgModule</span><span class="p">({</span>
<span class="w">  </span><span class="nx">declarations</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="nx">QuoteListComponent</span><span class="p">,</span>
<span class="w">    </span><span class="nx">QuoteDetailComponent</span><span class="p">,</span>
<span class="w">    </span><span class="nx">QuoteCreateComponent</span><span class="p">,</span>
<span class="w">    </span><span class="nx">QuoteEditComponent</span><span class="p">,</span>
<span class="w">    </span><span class="nx">TelusStartQuotationComponent</span><span class="p">,</span>
<span class="w">    </span><span class="nx">TelusQuoteViewComponent</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nx">imports</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="nx">CommonModule</span><span class="p">,</span>
<span class="w">    </span><span class="nx">QuotesRoutingModule</span><span class="p">,</span>
<span class="w">    </span><span class="nx">SharedModule</span><span class="p">,</span>
<span class="w">    </span><span class="nx">ReactiveFormsModule</span><span class="p">,</span>
<span class="w">    </span><span class="nx">StoreModule</span><span class="p">.</span><span class="nx">forFeature</span><span class="p">(</span><span class="s1">&#39;quotes&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">quotesReducer</span><span class="p">),</span>
<span class="w">    </span><span class="nx">EffectsModule</span><span class="p">.</span><span class="nx">forFeature</span><span class="p">([</span><span class="nx">QuotesEffects</span><span class="p">])</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nx">providers</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="nx">QuoteService</span><span class="p">,</span>
<span class="w">    </span><span class="nx">QuoteResolver</span><span class="p">,</span>
<span class="w">    </span><span class="nx">QuoteGuard</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">QuotesModule</span><span class="w"> </span><span class="p">{}</span>
</code></pre></div>
<h3 id="lazy-loading-strategy">Lazy Loading Strategy<a class="headerlink" href="#lazy-loading-strategy" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// App Routing with Lazy Loading</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">routes</span><span class="o">:</span><span class="w"> </span><span class="kt">Routes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nx">path</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;quotes&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loadChildren</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">import</span><span class="p">(</span><span class="s1">&#39;./modules/quotes/quotes.module&#39;</span><span class="p">).</span><span class="nx">then</span><span class="p">(</span><span class="nx">m</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">m</span><span class="p">.</span><span class="nx">QuotesModule</span><span class="p">),</span>
<span class="w">    </span><span class="nx">canActivate</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="nx">AuthGuard</span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nx">path</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;orders&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loadChildren</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">import</span><span class="p">(</span><span class="s1">&#39;./modules/orders/orders.module&#39;</span><span class="p">).</span><span class="nx">then</span><span class="p">(</span><span class="nx">m</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">m</span><span class="p">.</span><span class="nx">OrdersModule</span><span class="p">),</span>
<span class="w">    </span><span class="nx">canActivate</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="nx">AuthGuard</span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nx">path</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;services&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loadChildren</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">import</span><span class="p">(</span><span class="s1">&#39;./modules/services/services.module&#39;</span><span class="p">).</span><span class="nx">then</span><span class="p">(</span><span class="nx">m</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">m</span><span class="p">.</span><span class="nx">ServicesModule</span><span class="p">),</span>
<span class="w">    </span><span class="nx">canActivate</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="nx">AuthGuard</span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nx">path</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;admin&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loadChildren</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">import</span><span class="p">(</span><span class="s1">&#39;./modules/admin/admin.module&#39;</span><span class="p">).</span><span class="nx">then</span><span class="p">(</span><span class="nx">m</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">m</span><span class="p">.</span><span class="nx">AdminModule</span><span class="p">),</span>
<span class="w">    </span><span class="nx">canActivate</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="nx">AuthGuard</span><span class="p">,</span><span class="w"> </span><span class="nx">AdminGuard</span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">];</span>
</code></pre></div>
<h2 id="security-architecture">Security Architecture<a class="headerlink" href="#security-architecture" title="Permanent link">&para;</a></h2>
<h3 id="authentication-flow">Authentication Flow<a class="headerlink" href="#authentication-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant User
    participant App
    participant AuthGuard
    participant AuthService
    participant Backend
    participant TokenStorage

    User-&gt;&gt;App: Access Protected Route
    App-&gt;&gt;AuthGuard: Check Access
    AuthGuard-&gt;&gt;AuthService: Validate Token
    AuthService-&gt;&gt;TokenStorage: Get Token
    TokenStorage--&gt;&gt;AuthService: Return Token
    AuthService-&gt;&gt;Backend: Validate Token
    Backend--&gt;&gt;AuthService: Token Valid
    AuthService--&gt;&gt;AuthGuard: User Authenticated
    AuthGuard--&gt;&gt;App: Allow Access
    App--&gt;&gt;User: Display Content
</code></pre></div>
<h3 id="security-interceptors">Security Interceptors<a class="headerlink" href="#security-interceptors" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Authentication Interceptor</span>
<span class="kd">@Injectable</span><span class="p">()</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">AuthInterceptor</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">HttpInterceptor</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="k">private</span><span class="w"> </span><span class="nx">authService</span><span class="o">:</span><span class="w"> </span><span class="kt">AuthService</span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="nx">intercept</span><span class="p">(</span><span class="nx">req</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpRequest</span><span class="o">&lt;</span><span class="nx">any</span><span class="o">&gt;</span><span class="p">,</span><span class="w"> </span><span class="nx">next</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpHandler</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">HttpEvent</span><span class="o">&lt;</span><span class="nx">any</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">token</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">authService</span><span class="p">.</span><span class="nx">getToken</span><span class="p">();</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">token</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">authReq</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">req</span><span class="p">.</span><span class="nx">clone</span><span class="p">({</span>
<span class="w">        </span><span class="nx">setHeaders</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nx">Authorization</span><span class="o">:</span><span class="w"> </span><span class="sb">`Bearer </span><span class="si">${</span><span class="nx">token</span><span class="si">}</span><span class="sb">`</span><span class="p">,</span>
<span class="w">          </span><span class="s1">&#39;X-Requested-With&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;XMLHttpRequest&#39;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">});</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">next</span><span class="p">.</span><span class="nx">handle</span><span class="p">(</span><span class="nx">authReq</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">next</span><span class="p">.</span><span class="nx">handle</span><span class="p">(</span><span class="nx">req</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>

<span class="c1">// Error Interceptor</span>
<span class="kd">@Injectable</span><span class="p">()</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">ErrorInterceptor</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">HttpInterceptor</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">intercept</span><span class="p">(</span><span class="nx">req</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpRequest</span><span class="o">&lt;</span><span class="nx">any</span><span class="o">&gt;</span><span class="p">,</span><span class="w"> </span><span class="nx">next</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpHandler</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">HttpEvent</span><span class="o">&lt;</span><span class="nx">any</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">next</span><span class="p">.</span><span class="nx">handle</span><span class="p">(</span><span class="nx">req</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">catchError</span><span class="p">((</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpErrorResponse</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">.</span><span class="nx">status</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="mf">401</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">this</span><span class="p">.</span><span class="nx">authService</span><span class="p">.</span><span class="nx">logout</span><span class="p">();</span>
<span class="w">          </span><span class="k">this</span><span class="p">.</span><span class="nx">router</span><span class="p">.</span><span class="nx">navigate</span><span class="p">([</span><span class="s1">&#39;/login&#39;</span><span class="p">]);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="nx">throwError</span><span class="p">(</span><span class="nx">error</span><span class="p">);</span>
<span class="w">      </span><span class="p">})</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../02-custom-components/">Custom Components →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>