import { COMPONENTS_PATH, DEFAULT_MIME_TYPE } from "./config.js";
import fs from "fs";
import path from "path";

import { Resource } from "@modelcontextprotocol/sdk/types.js";

/**
 * Extracts a title from MDX content, looking for frontmatter title or first heading
 * @param content The MDX file content
 * @param fallbackName Fallback name to use if no title is found
 * @returns The extracted title or fallback name
 */
function extractTitleFromMdx(content: string, fallbackName: string): string {
  // Try to extract from frontmatter
  const frontmatterMatch = content.match(/---\s*\n([\s\S]*?)\n\s*---/);
  if (frontmatterMatch) {
    const frontmatter = frontmatterMatch[1];
    const titleMatch = frontmatter.match(/title:\s*["']?(.*?)["']?\s*(\n|$)/);
    if (titleMatch) {
      return titleMatch[1].trim();
    }
  }
  
  // Try to extract from first heading
  const headingMatch = content.match(/^#\s+(.*?)$/m);
  if (headingMatch) {
    return headingMatch[1].trim();
  }
  
  // Fall back to filename
  return fallbackName;
}

/**
 * Generates resource objects by scanning the components directory
 * @returns Array of Resource objects
 */
function generateResources(): Resource[] {
  console.error("Generating UDS component resources...");
  const resources: Resource[] = [];
  
  try {
    // Check if COMPONENTS_PATH exists
    if (!fs.existsSync(COMPONENTS_PATH)) {
      console.error(`Components path does not exist: ${COMPONENTS_PATH}`);
      return resources;
    }
    
    // Get all .mdx files in the components directory
    const files = fs.readdirSync(COMPONENTS_PATH)
      .filter(file => file.endsWith('.mdx'));
    
    console.error(`Found ${files.length} MDX files in ${COMPONENTS_PATH}`);
    
    for (const file of files) {
      try {
        // Extract component name (remove .mdx extension)
        const componentName = path.basename(file, '.mdx');
        
        // Read file content
        const filePath = path.join(COMPONENTS_PATH, file);
        const content = fs.readFileSync(filePath, "utf-8");
        
        // Extract title or use fallback
        const title = extractTitleFromMdx(content, componentName);
        
        // Create resource object
        const resource: Resource = {
          uri: `file://${filePath}`,
          name: `UDS ${title} component`,
          description: `UDS ${title} component documentation`,
          mimeType: DEFAULT_MIME_TYPE,
          text: content,
        };
        
        resources.push(resource);
      } catch (error) {
        console.error(`Error processing file ${file}:`, error);
      }
    }
  } catch (error) {
    console.error("Error generating resources:", error);
  }
  
  return resources;
}

// Cache the resources to avoid repeated file system operations
let cachedResources: Resource[] | null = null;

/**
 * Gets the list of UDS component resources, using cache if available
 * @returns Array of Resource objects
 */
export function getResources(): Resource[] {
  if (!cachedResources) {
    cachedResources = generateResources();
  }
  return cachedResources;
}

// For backward compatibility, export a resources array that will be populated on first access
export const resources: Resource[] = [];

// Initialize the resources array
setTimeout(() => {
  const dynamicResources = getResources();
  resources.length = 0; // Clear the array
  resources.push(...dynamicResources); // Add all dynamic resources
}, 0);
