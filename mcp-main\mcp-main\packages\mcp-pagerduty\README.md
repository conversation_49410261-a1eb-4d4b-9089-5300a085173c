# @telus/mcp-pagerduty

A Model Context Protocol (MCP) server that provides tools for interacting with PagerDuty's API.

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-pagerduty
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for Claude Desktop):

   ```json
   {
     "mcpServers": {
       "pagerduty": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-pagerduty"],
         "env": {
           "PAGERDUTY_API_BASE": "your_pagerduty_url",
           "PAGERDUTY_API_TOKEN": "your_pagerduty_api_token"
         }
       }
     }
   }
   ```
   Create your API token via this url https://telusdigital.pagerduty.com/api_keys
   Replace `your_pagerduty_api_token` with your actual PagerDuty API token.

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-pagerduty
   pnpm install
   ```

2. Create a `.env` file in the root directory with the following content:
   ```
   PAGERDUTY_API_BASE=your_pagerduty_url
   PAGERDUTY_API_TOKEN=your_pagerduty_api_token
   ```

3. Build the server:

   ```bash
   pnpm build
   ```

4. Start the server:

   ```bash
   pnpm start
   ```

## Features

- List PagerDuty incidents
- List PagerDuty services
- List PagerDuty users

## Available Tools

### list-incidents

Lists PagerDuty incidents with comprehensive filtering options.

Parameters:
- `status` (optional): Array of incident statuses to filter by (triggered/acknowledged/resolved)
- `offset` (optional): Offset for pagination
- `limit` (optional): Number of results per page (max 100)
- `date_range` (optional): When set to 'all', ignores since/until parameters
- `incident_key` (optional): Incident de-duplication key
- `include` (optional): Array of additional details to include (e.g., ['acknowledgers', 'assignments'])
- `service_ids` (optional): Array of service IDs to filter by
- `since` (optional): Start of date range (ISO 8601 format)
- `sort_by` (optional): Array of fields to sort by (e.g., ['created_at:desc', 'urgency:asc'])
- `team_ids` (optional): Array of team IDs to filter by
- `time_zone` (optional): Time zone for response
- `until` (optional): End of date range (ISO 8601 format)
- `urgencies` (optional): Array of urgencies to filter by (high/low)
- `user_ids` (optional): Array of user IDs to filter by (only returns triggered/acknowledged incidents)

Examples:
```javascript
// List all high-urgency incidents from a specific service
{
  "urgencies": ["high"],
  "service_ids": ["SERVICE_ID"],
  "status": ["triggered", "acknowledged"]
}

// List all incidents from the last 24 hours with additional details
{
  "since": "2024-03-30T14:00:00Z",
  "until": "2024-03-31T14:00:00Z",
  "include": ["acknowledgers", "assignments"],
  "sort_by": ["created_at:desc"]
}
```

### list-services

Lists PagerDuty services.

### list-users

Lists PagerDuty users.

### find-service

Finds PagerDuty services by name.

Parameters:
- `serviceName`: Name of the service to search for

### select-service

Selects a service from the search results and retrieves its details.

Parameters:
- `serviceNumber`: Number of the service from the search results

Note: You must use the `find-service` tool before using `select-service`.

## Development

To add new tools or modify existing ones, edit the `src/tools/api.ts` file. Make sure to register new tools in `src/index.ts`.

## License

MIT