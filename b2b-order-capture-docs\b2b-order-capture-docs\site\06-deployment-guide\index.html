
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/06-deployment-guide/">
      
      
        <link rel="prev" href="../05-development-guide/">
      
      
        <link rel="next" href="../07-reference/">
      
      
      <link rel="icon" href="../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Deployment Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#deployment-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Deployment Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href=".." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#deployment-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Deployment Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#deployment-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#containerization" class="md-nav__link">
    <span class="md-ellipsis">
      Containerization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Containerization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-dockerfile" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Dockerfile
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Frontend Dockerfile">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#nginx-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Nginx Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-dockerfile" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Dockerfile
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-dockerfile" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Dockerfile
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#kubernetes-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Kubernetes Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Kubernetes Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Deployment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Deployment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Deployment
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#helm-charts" class="md-nav__link">
    <span class="md-ellipsis">
      Helm Charts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Helm Charts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-helm-chart" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Helm Chart
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-helm-chart" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Helm Chart
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Environment Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#production-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Production Environment
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#cicd-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      CI/CD Pipeline
    </span>
  </a>
  
    <nav class="md-nav" aria-label="CI/CD Pipeline">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#gitlab-ci-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      GitLab CI Pipeline
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#deployment-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Deployment Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#deployment-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#containerization" class="md-nav__link">
    <span class="md-ellipsis">
      Containerization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Containerization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-dockerfile" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Dockerfile
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Frontend Dockerfile">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#nginx-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Nginx Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-dockerfile" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Dockerfile
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-dockerfile" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Dockerfile
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#kubernetes-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Kubernetes Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Kubernetes Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Deployment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Deployment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Deployment
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#helm-charts" class="md-nav__link">
    <span class="md-ellipsis">
      Helm Charts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Helm Charts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-helm-chart" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Helm Chart
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-helm-chart" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Helm Chart
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Environment Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#production-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Production Environment
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#cicd-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      CI/CD Pipeline
    </span>
  </a>
  
    <nav class="md-nav" aria-label="CI/CD Pipeline">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#gitlab-ci-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      GitLab CI Pipeline
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="deployment-guide">Deployment Guide<a class="headerlink" href="#deployment-guide" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#deployment-overview">Deployment Overview</a></li>
<li><a href="#containerization">Containerization</a></li>
<li><a href="#kubernetes-deployment">Kubernetes Deployment</a></li>
<li><a href="#helm-charts">Helm Charts</a></li>
<li><a href="#environment-configuration">Environment Configuration</a></li>
<li><a href="#cicd-pipeline">CI/CD Pipeline</a></li>
</ul>
<h2 id="deployment-overview">Deployment Overview<a class="headerlink" href="#deployment-overview" title="Permanent link">&para;</a></h2>
<p>The TELUS B2B Order Capture ecosystem is designed for cloud-native deployment using Docker containers and Kubernetes orchestration. Each component has its own deployment strategy optimized for scalability and reliability.</p>
<h3 id="deployment-architecture">Deployment Architecture<a class="headerlink" href="#deployment-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Kubernetes Cluster&quot;
        subgraph &quot;Frontend Namespace&quot;
            FE_POD[Frontend Pods]
            FE_SVC[Frontend Service]
            FE_ING[Ingress Controller]
        end

        subgraph &quot;Backend Namespace&quot;
            BE_POD[Backend Pods]
            BE_SVC[Backend Service]
            BE_CONFIG[ConfigMaps]
        end

        subgraph &quot;Extensions Namespace&quot;
            EXT_POD[Extension Pods]
            EXT_SVC[Extension Service]
            EXT_CONFIG[Plugin ConfigMaps]
        end

        subgraph &quot;Infrastructure&quot;
            DB[(PostgreSQL)]
            REDIS[(Redis Cache)]
            SECRETS[Secrets]
        end
    end

    subgraph &quot;External Services&quot;
        OCP[Order Capture Product]
        BSS[TELUS BSS]
        LB[Load Balancer]
    end

    LB --&gt; FE_ING
    FE_ING --&gt; FE_SVC
    FE_SVC --&gt; FE_POD
    FE_POD --&gt; BE_SVC
    BE_SVC --&gt; BE_POD
    BE_POD --&gt; EXT_SVC
    EXT_SVC --&gt; EXT_POD
    BE_POD --&gt; DB
    BE_POD --&gt; REDIS
    BE_POD --&gt; OCP
    EXT_POD --&gt; BSS
</code></pre></div>
<h2 id="containerization">Containerization<a class="headerlink" href="#containerization" title="Permanent link">&para;</a></h2>
<h3 id="frontend-dockerfile">Frontend Dockerfile<a class="headerlink" href="#frontend-dockerfile" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c"># nc-cloud-bss-oc-ui-frontend-b2b/Dockerfile</span>
<span class="k">FROM</span><span class="w"> </span><span class="s">node:16-alpine</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="s">builder</span>

<span class="k">WORKDIR</span><span class="w"> </span><span class="s">/app</span>

<span class="c"># Copy package files</span>
<span class="k">COPY</span><span class="w"> </span>package*.json<span class="w"> </span>./
<span class="k">RUN</span><span class="w"> </span>npm<span class="w"> </span>ci<span class="w"> </span>--only<span class="o">=</span>production

<span class="c"># Copy source code</span>
<span class="k">COPY</span><span class="w"> </span>.<span class="w"> </span>.

<span class="c"># Build application</span>
<span class="k">RUN</span><span class="w"> </span>npm<span class="w"> </span>run<span class="w"> </span>build

<span class="c"># Production stage</span>
<span class="k">FROM</span><span class="w"> </span><span class="s">nginx:alpine</span>

<span class="c"># Copy custom nginx configuration</span>
<span class="k">COPY</span><span class="w"> </span>nginx.conf<span class="w"> </span>/etc/nginx/nginx.conf
<span class="k">COPY</span><span class="w"> </span>default.conf<span class="w"> </span>/etc/nginx/conf.d/default.conf

<span class="c"># Copy built application</span>
<span class="k">COPY</span><span class="w"> </span>--from<span class="o">=</span>builder<span class="w"> </span>/app/dist<span class="w"> </span>/usr/share/nginx/html

<span class="c"># Copy entrypoint script</span>
<span class="k">COPY</span><span class="w"> </span>docker-entrypoint.sh<span class="w"> </span>/docker-entrypoint.sh
<span class="k">RUN</span><span class="w"> </span>chmod<span class="w"> </span>+x<span class="w"> </span>/docker-entrypoint.sh

<span class="k">EXPOSE</span><span class="w"> </span><span class="s">80</span>

<span class="k">ENTRYPOINT</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;/docker-entrypoint.sh&quot;</span><span class="p">]</span>
<span class="k">CMD</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;nginx&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;-g&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;daemon off;&quot;</span><span class="p">]</span>
</code></pre></div>
<h4 id="nginx-configuration">Nginx Configuration<a class="headerlink" href="#nginx-configuration" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># nginx.conf</span>
<span class="k">worker_processes</span><span class="w"> </span><span class="s">auto</span><span class="p">;</span>
<span class="k">error_log</span><span class="w"> </span><span class="s">/var/log/nginx/error.log</span><span class="w"> </span><span class="s">warn</span><span class="p">;</span>
<span class="k">pid</span><span class="w"> </span><span class="s">/var/run/nginx.pid</span><span class="p">;</span>

<span class="k">events</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kn">worker_connections</span><span class="w"> </span><span class="mi">1024</span><span class="p">;</span>
<span class="p">}</span>

<span class="k">http</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kn">include</span><span class="w"> </span><span class="s">/etc/nginx/mime.types</span><span class="p">;</span>
<span class="w">    </span><span class="kn">default_type</span><span class="w"> </span><span class="s">application/octet-stream</span><span class="p">;</span>

<span class="w">    </span><span class="kn">log_format</span><span class="w"> </span><span class="s">main</span><span class="w"> </span><span class="s">&#39;</span><span class="nv">$remote_addr</span><span class="w"> </span><span class="s">-</span><span class="w"> </span><span class="nv">$remote_user</span><span class="w"> </span><span class="s">[</span><span class="nv">$time_local]</span><span class="w"> </span><span class="s">&quot;</span><span class="nv">$request&quot;</span><span class="w"> </span><span class="s">&#39;</span>
<span class="w">                    </span><span class="s">&#39;</span><span class="nv">$status</span><span class="w"> </span><span class="nv">$body_bytes_sent</span><span class="w"> </span><span class="s">&quot;</span><span class="nv">$http_referer&quot;</span><span class="w"> </span><span class="s">&#39;</span>
<span class="w">                    </span><span class="s">&#39;&quot;</span><span class="nv">$http_user_agent&quot;</span><span class="w"> </span><span class="s">&quot;</span><span class="nv">$http_x_forwarded_for&quot;&#39;</span><span class="p">;</span>

<span class="w">    </span><span class="kn">access_log</span><span class="w"> </span><span class="s">/var/log/nginx/access.log</span><span class="w"> </span><span class="s">main</span><span class="p">;</span>

<span class="w">    </span><span class="kn">sendfile</span><span class="w"> </span><span class="no">on</span><span class="p">;</span>
<span class="w">    </span><span class="kn">tcp_nopush</span><span class="w"> </span><span class="no">on</span><span class="p">;</span>
<span class="w">    </span><span class="kn">tcp_nodelay</span><span class="w"> </span><span class="no">on</span><span class="p">;</span>
<span class="w">    </span><span class="kn">keepalive_timeout</span><span class="w"> </span><span class="mi">65</span><span class="p">;</span>
<span class="w">    </span><span class="kn">types_hash_max_size</span><span class="w"> </span><span class="mi">2048</span><span class="p">;</span>

<span class="w">    </span><span class="kn">gzip</span><span class="w"> </span><span class="no">on</span><span class="p">;</span>
<span class="w">    </span><span class="kn">gzip_vary</span><span class="w"> </span><span class="no">on</span><span class="p">;</span>
<span class="w">    </span><span class="kn">gzip_min_length</span><span class="w"> </span><span class="mi">1024</span><span class="p">;</span>
<span class="w">    </span><span class="kn">gzip_types</span><span class="w"> </span><span class="s">text/plain</span><span class="w"> </span><span class="s">text/css</span><span class="w"> </span><span class="s">application/json</span><span class="w"> </span><span class="s">application/javascript</span><span class="w"> </span><span class="s">text/xml</span><span class="w"> </span><span class="s">application/xml</span><span class="w"> </span><span class="s">application/xml+rss</span><span class="w"> </span><span class="s">text/javascript</span><span class="p">;</span>

<span class="w">    </span><span class="kn">include</span><span class="w"> </span><span class="s">/etc/nginx/conf.d/*.conf</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="backend-dockerfile">Backend Dockerfile<a class="headerlink" href="#backend-dockerfile" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c"># nc-cloud-bss-oc-ui-backend-b2b/Dockerfile</span>
<span class="k">FROM</span><span class="w"> </span><span class="s">openjdk:17-jdk-slim</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="s">builder</span>

<span class="k">WORKDIR</span><span class="w"> </span><span class="s">/app</span>

<span class="c"># Copy Maven files</span>
<span class="k">COPY</span><span class="w"> </span>pom.xml<span class="w"> </span>.
<span class="k">COPY</span><span class="w"> </span>*/pom.xml<span class="w"> </span>./*/

<span class="c"># Download dependencies</span>
<span class="k">RUN</span><span class="w"> </span>apt-get<span class="w"> </span>update<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>maven
<span class="k">RUN</span><span class="w"> </span>mvn<span class="w"> </span>dependency:go-offline<span class="w"> </span>-B

<span class="c"># Copy source code</span>
<span class="k">COPY</span><span class="w"> </span>.<span class="w"> </span>.

<span class="c"># Build application</span>
<span class="k">RUN</span><span class="w"> </span>mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w"> </span>-DskipTests

<span class="c"># Production stage</span>
<span class="k">FROM</span><span class="w"> </span><span class="s">openjdk:17-jre-slim</span>

<span class="k">WORKDIR</span><span class="w"> </span><span class="s">/app</span>

<span class="c"># Create non-root user</span>
<span class="k">RUN</span><span class="w"> </span>groupadd<span class="w"> </span>-r<span class="w"> </span>telus<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>useradd<span class="w"> </span>-r<span class="w"> </span>-g<span class="w"> </span>telus<span class="w"> </span>telus

<span class="c"># Copy JAR file</span>
<span class="k">COPY</span><span class="w"> </span>--from<span class="o">=</span>builder<span class="w"> </span>/app/cloud-bss-oc-ui-backend-b2b-application/target/*.jar<span class="w"> </span>app.jar

<span class="c"># Change ownership</span>
<span class="k">RUN</span><span class="w"> </span>chown<span class="w"> </span>telus:telus<span class="w"> </span>app.jar

<span class="k">USER</span><span class="w"> </span><span class="s">telus</span>

<span class="k">EXPOSE</span><span class="w"> </span><span class="s">8080</span>

<span class="k">HEALTHCHECK</span><span class="w"> </span>--interval<span class="o">=</span>30s<span class="w"> </span>--timeout<span class="o">=</span>3s<span class="w"> </span>--start-period<span class="o">=</span>60s<span class="w"> </span>--retries<span class="o">=</span><span class="m">3</span><span class="w"> </span><span class="se">\</span>
<span class="w">    </span>CMD<span class="w"> </span>curl<span class="w"> </span>-f<span class="w"> </span>http://localhost:8080/actuator/health<span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="nb">exit</span><span class="w"> </span><span class="m">1</span>

<span class="k">ENTRYPOINT</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;java&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;-jar&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;app.jar&quot;</span><span class="p">]</span>
</code></pre></div>
<h3 id="extensions-dockerfile">Extensions Dockerfile<a class="headerlink" href="#extensions-dockerfile" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c"># nc-cloud-bss-oc-ui-be-extension-b2b/Dockerfile</span>
<span class="k">FROM</span><span class="w"> </span><span class="s">openjdk:17-jdk-slim</span><span class="w"> </span><span class="k">AS</span><span class="w"> </span><span class="s">builder</span>

<span class="k">WORKDIR</span><span class="w"> </span><span class="s">/app</span>

<span class="c"># Copy Maven files</span>
<span class="k">COPY</span><span class="w"> </span>pom.xml<span class="w"> </span>.
<span class="k">COPY</span><span class="w"> </span>b2b-qes-plugin/pom.xml<span class="w"> </span>./b2b-qes-plugin/

<span class="c"># Download dependencies</span>
<span class="k">RUN</span><span class="w"> </span>apt-get<span class="w"> </span>update<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>apt-get<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>maven
<span class="k">RUN</span><span class="w"> </span>mvn<span class="w"> </span>dependency:go-offline<span class="w"> </span>-B

<span class="c"># Copy source code</span>
<span class="k">COPY</span><span class="w"> </span>.<span class="w"> </span>.

<span class="c"># Build plugin</span>
<span class="k">RUN</span><span class="w"> </span>mvn<span class="w"> </span>clean<span class="w"> </span>package

<span class="c"># Production stage</span>
<span class="k">FROM</span><span class="w"> </span><span class="s">openjdk:17-jre-slim</span>

<span class="k">WORKDIR</span><span class="w"> </span><span class="s">/app</span>

<span class="c"># Create plugin directory</span>
<span class="k">RUN</span><span class="w"> </span>mkdir<span class="w"> </span>-p<span class="w"> </span>/app/plugins

<span class="c"># Copy plugin JAR</span>
<span class="k">COPY</span><span class="w"> </span>--from<span class="o">=</span>builder<span class="w"> </span>/app/b2b-qes-plugin/target/*.jar<span class="w"> </span>/app/plugins/

<span class="c"># Copy configuration</span>
<span class="k">COPY</span><span class="w"> </span>deployments/charts/b2b-qes-plugin/templates/ConfigMap.yaml<span class="w"> </span>/app/config/

<span class="k">EXPOSE</span><span class="w"> </span><span class="s">8081</span>

<span class="k">CMD</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;java&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;-cp&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;/app/plugins/*&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;com.netcracker.qes.plugin.PluginRunner&quot;</span><span class="p">]</span>
</code></pre></div>
<h2 id="kubernetes-deployment">Kubernetes Deployment<a class="headerlink" href="#kubernetes-deployment" title="Permanent link">&para;</a></h2>
<h3 id="frontend-deployment">Frontend Deployment<a class="headerlink" href="#frontend-deployment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># frontend-deployment.yaml</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">apps/v1</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deployment</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-frontend</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span>
<span class="w">    </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-frontend</span>
<span class="w">    </span><span class="nt">component</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">frontend</span>
<span class="nt">spec</span><span class="p">:</span>
<span class="w">  </span><span class="nt">replicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">  </span><span class="nt">selector</span><span class="p">:</span>
<span class="w">    </span><span class="nt">matchLabels</span><span class="p">:</span>
<span class="w">      </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-frontend</span>
<span class="w">  </span><span class="nt">template</span><span class="p">:</span>
<span class="w">    </span><span class="nt">metadata</span><span class="p">:</span>
<span class="w">      </span><span class="nt">labels</span><span class="p">:</span>
<span class="w">        </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-frontend</span>
<span class="w">    </span><span class="nt">spec</span><span class="p">:</span>
<span class="w">      </span><span class="nt">containers</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">frontend</span>
<span class="w">        </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus/nc-cloud-bss-oc-ui-frontend-b2b:1.0.0</span>
<span class="w">        </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">containerPort</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">80</span>
<span class="w">        </span><span class="nt">env</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">API_BASE_URL</span>
<span class="w">          </span><span class="nt">valueFrom</span><span class="p">:</span>
<span class="w">            </span><span class="nt">configMapKeyRef</span><span class="p">:</span>
<span class="w">              </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">frontend-config</span>
<span class="w">              </span><span class="nt">key</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">api-base-url</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">GOOGLE_MAPS_API_KEY</span>
<span class="w">          </span><span class="nt">valueFrom</span><span class="p">:</span>
<span class="w">            </span><span class="nt">secretKeyRef</span><span class="p">:</span>
<span class="w">              </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">frontend-secrets</span>
<span class="w">              </span><span class="nt">key</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">google-maps-api-key</span>
<span class="w">        </span><span class="nt">resources</span><span class="p">:</span>
<span class="w">          </span><span class="nt">requests</span><span class="p">:</span>
<span class="w">            </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;128Mi&quot;</span>
<span class="w">            </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;100m&quot;</span>
<span class="w">          </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">            </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;256Mi&quot;</span>
<span class="w">            </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;200m&quot;</span>
<span class="w">        </span><span class="nt">livenessProbe</span><span class="p">:</span>
<span class="w">          </span><span class="nt">httpGet</span><span class="p">:</span>
<span class="w">            </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/health</span>
<span class="w">            </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">80</span>
<span class="w">          </span><span class="nt">initialDelaySeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30</span>
<span class="w">          </span><span class="nt">periodSeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">        </span><span class="nt">readinessProbe</span><span class="p">:</span>
<span class="w">          </span><span class="nt">httpGet</span><span class="p">:</span>
<span class="w">            </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/health</span>
<span class="w">            </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">80</span>
<span class="w">          </span><span class="nt">initialDelaySeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">          </span><span class="nt">periodSeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>

<span class="nn">---</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">v1</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Service</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-frontend-service</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b</span>
<span class="nt">spec</span><span class="p">:</span>
<span class="w">  </span><span class="nt">selector</span><span class="p">:</span>
<span class="w">    </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-frontend</span>
<span class="w">  </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">TCP</span>
<span class="w">    </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">80</span>
<span class="w">    </span><span class="nt">targetPort</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">80</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ClusterIP</span>

<span class="nn">---</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">networking.k8s.io/v1</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Ingress</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-frontend-ingress</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b</span>
<span class="w">  </span><span class="nt">annotations</span><span class="p">:</span>
<span class="w">    </span><span class="nt">kubernetes.io/ingress.class</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">nginx</span>
<span class="w">    </span><span class="nt">cert-manager.io/cluster-issuer</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">letsencrypt-prod</span>
<span class="w">    </span><span class="nt">nginx.ingress.kubernetes.io/ssl-redirect</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;true&quot;</span>
<span class="nt">spec</span><span class="p">:</span>
<span class="w">  </span><span class="nt">tls</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">hosts</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">b2b-order-capture.telus.com</span>
<span class="w">    </span><span class="nt">secretName</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">frontend-tls</span>
<span class="w">  </span><span class="nt">rules</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">host</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">b2b-order-capture.telus.com</span>
<span class="w">    </span><span class="nt">http</span><span class="p">:</span>
<span class="w">      </span><span class="nt">paths</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/</span>
<span class="w">        </span><span class="nt">pathType</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Prefix</span>
<span class="w">        </span><span class="nt">backend</span><span class="p">:</span>
<span class="w">          </span><span class="nt">service</span><span class="p">:</span>
<span class="w">            </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-frontend-service</span>
<span class="w">            </span><span class="nt">port</span><span class="p">:</span>
<span class="w">              </span><span class="nt">number</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">80</span>
</code></pre></div>
<h3 id="backend-deployment">Backend Deployment<a class="headerlink" href="#backend-deployment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># backend-deployment.yaml</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">apps/v1</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deployment</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-backend</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span>
<span class="w">    </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-backend</span>
<span class="w">    </span><span class="nt">component</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">backend</span>
<span class="nt">spec</span><span class="p">:</span>
<span class="w">  </span><span class="nt">replicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
<span class="w">  </span><span class="nt">selector</span><span class="p">:</span>
<span class="w">    </span><span class="nt">matchLabels</span><span class="p">:</span>
<span class="w">      </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-backend</span>
<span class="w">  </span><span class="nt">template</span><span class="p">:</span>
<span class="w">    </span><span class="nt">metadata</span><span class="p">:</span>
<span class="w">      </span><span class="nt">labels</span><span class="p">:</span>
<span class="w">        </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-backend</span>
<span class="w">    </span><span class="nt">spec</span><span class="p">:</span>
<span class="w">      </span><span class="nt">containers</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">backend</span>
<span class="w">        </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus/nc-cloud-bss-oc-ui-backend-b2b:1.2.0</span>
<span class="w">        </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">containerPort</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8080</span>
<span class="w">        </span><span class="nt">env</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">SPRING_PROFILES_ACTIVE</span>
<span class="w">          </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;production&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DATABASE_URL</span>
<span class="w">          </span><span class="nt">valueFrom</span><span class="p">:</span>
<span class="w">            </span><span class="nt">secretKeyRef</span><span class="p">:</span>
<span class="w">              </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">backend-secrets</span>
<span class="w">              </span><span class="nt">key</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">database-url</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">OCP_CLIENT_ID</span>
<span class="w">          </span><span class="nt">valueFrom</span><span class="p">:</span>
<span class="w">            </span><span class="nt">secretKeyRef</span><span class="p">:</span>
<span class="w">              </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">backend-secrets</span>
<span class="w">              </span><span class="nt">key</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ocp-client-id</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">OCP_CLIENT_SECRET</span>
<span class="w">          </span><span class="nt">valueFrom</span><span class="p">:</span>
<span class="w">            </span><span class="nt">secretKeyRef</span><span class="p">:</span>
<span class="w">              </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">backend-secrets</span>
<span class="w">              </span><span class="nt">key</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ocp-client-secret</span>
<span class="w">        </span><span class="nt">resources</span><span class="p">:</span>
<span class="w">          </span><span class="nt">requests</span><span class="p">:</span>
<span class="w">            </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;512Mi&quot;</span>
<span class="w">            </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;250m&quot;</span>
<span class="w">          </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">            </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1Gi&quot;</span>
<span class="w">            </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;500m&quot;</span>
<span class="w">        </span><span class="nt">livenessProbe</span><span class="p">:</span>
<span class="w">          </span><span class="nt">httpGet</span><span class="p">:</span>
<span class="w">            </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/actuator/health</span>
<span class="w">            </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8080</span>
<span class="w">          </span><span class="nt">initialDelaySeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">60</span>
<span class="w">          </span><span class="nt">periodSeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30</span>
<span class="w">        </span><span class="nt">readinessProbe</span><span class="p">:</span>
<span class="w">          </span><span class="nt">httpGet</span><span class="p">:</span>
<span class="w">            </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/actuator/health/readiness</span>
<span class="w">            </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8080</span>
<span class="w">          </span><span class="nt">initialDelaySeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30</span>
<span class="w">          </span><span class="nt">periodSeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">        </span><span class="nt">volumeMounts</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">config-volume</span>
<span class="w">          </span><span class="nt">mountPath</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/app/config</span>
<span class="w">      </span><span class="nt">volumes</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">config-volume</span>
<span class="w">        </span><span class="nt">configMap</span><span class="p">:</span>
<span class="w">          </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">backend-config</span>

<span class="nn">---</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">v1</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Service</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-backend-service</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b</span>
<span class="nt">spec</span><span class="p">:</span>
<span class="w">  </span><span class="nt">selector</span><span class="p">:</span>
<span class="w">    </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-backend</span>
<span class="w">  </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">TCP</span>
<span class="w">    </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8080</span>
<span class="w">    </span><span class="nt">targetPort</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8080</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ClusterIP</span>
</code></pre></div>
<h3 id="extensions-deployment">Extensions Deployment<a class="headerlink" href="#extensions-deployment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># extensions-deployment.yaml</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">apps/v1</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deployment</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-extensions</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span>
<span class="w">    </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-extensions</span>
<span class="w">    </span><span class="nt">component</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">extensions</span>
<span class="nt">spec</span><span class="p">:</span>
<span class="w">  </span><span class="nt">replicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
<span class="w">  </span><span class="nt">selector</span><span class="p">:</span>
<span class="w">    </span><span class="nt">matchLabels</span><span class="p">:</span>
<span class="w">      </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-extensions</span>
<span class="w">  </span><span class="nt">template</span><span class="p">:</span>
<span class="w">    </span><span class="nt">metadata</span><span class="p">:</span>
<span class="w">      </span><span class="nt">labels</span><span class="p">:</span>
<span class="w">        </span><span class="nt">app</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-extensions</span>
<span class="w">    </span><span class="nt">spec</span><span class="p">:</span>
<span class="w">      </span><span class="nt">containers</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">extensions</span>
<span class="w">        </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus/nc-cloud-bss-oc-ui-be-extension-b2b:1.2.0</span>
<span class="w">        </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">containerPort</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8081</span>
<span class="w">        </span><span class="nt">env</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">QES_ENVIRONMENT</span>
<span class="w">          </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;production&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">PLUGIN_CONFIG_PATH</span>
<span class="w">          </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/app/config&quot;</span>
<span class="w">        </span><span class="nt">resources</span><span class="p">:</span>
<span class="w">          </span><span class="nt">requests</span><span class="p">:</span>
<span class="w">            </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;256Mi&quot;</span>
<span class="w">            </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;150m&quot;</span>
<span class="w">          </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">            </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;512Mi&quot;</span>
<span class="w">            </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;300m&quot;</span>
<span class="w">        </span><span class="nt">volumeMounts</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">plugin-config</span>
<span class="w">          </span><span class="nt">mountPath</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/app/config</span>
<span class="w">      </span><span class="nt">volumes</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">plugin-config</span>
<span class="w">        </span><span class="nt">configMap</span><span class="p">:</span>
<span class="w">          </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">extensions-config</span>
</code></pre></div>
<h2 id="helm-charts">Helm Charts<a class="headerlink" href="#helm-charts" title="Permanent link">&para;</a></h2>
<h3 id="frontend-helm-chart">Frontend Helm Chart<a class="headerlink" href="#frontend-helm-chart" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># charts/frontend/Chart.yaml</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">v2</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-frontend</span>
<span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">TELUS B2B Order Capture Frontend</span>
<span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">application</span>
<span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1.0.0</span>
<span class="nt">appVersion</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.0.0&quot;</span>
</code></pre></div>
<div class="highlight"><pre><span></span><code><span class="c1"># charts/frontend/values.yaml</span>
<span class="nt">replicaCount</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>

<span class="nt">image</span><span class="p">:</span>
<span class="w">  </span><span class="nt">repository</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus/nc-cloud-bss-oc-ui-frontend-b2b</span>
<span class="w">  </span><span class="nt">tag</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.0.0&quot;</span>
<span class="w">  </span><span class="nt">pullPolicy</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">IfNotPresent</span>

<span class="nt">service</span><span class="p">:</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ClusterIP</span>
<span class="w">  </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">80</span>

<span class="nt">ingress</span><span class="p">:</span>
<span class="w">  </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">  </span><span class="nt">className</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">nginx</span>
<span class="w">  </span><span class="nt">annotations</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cert-manager.io/cluster-issuer</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">letsencrypt-prod</span>
<span class="w">    </span><span class="nt">nginx.ingress.kubernetes.io/ssl-redirect</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;true&quot;</span>
<span class="w">  </span><span class="nt">hosts</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">host</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">b2b-order-capture.telus.com</span>
<span class="w">      </span><span class="nt">paths</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/</span>
<span class="w">          </span><span class="nt">pathType</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Prefix</span>
<span class="w">  </span><span class="nt">tls</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">secretName</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">frontend-tls</span>
<span class="w">      </span><span class="nt">hosts</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">b2b-order-capture.telus.com</span>

<span class="nt">resources</span><span class="p">:</span>
<span class="w">  </span><span class="nt">requests</span><span class="p">:</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;128Mi&quot;</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;100m&quot;</span>
<span class="w">  </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;256Mi&quot;</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;200m&quot;</span>

<span class="nt">config</span><span class="p">:</span>
<span class="w">  </span><span class="nt">apiBaseUrl</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://api.b2b-order-capture.telus.com&quot;</span>
<span class="w">  </span><span class="nt">googleMapsApiKey</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;&quot;</span><span class="w">  </span><span class="c1"># Set via secrets</span>

<span class="nt">autoscaling</span><span class="p">:</span>
<span class="w">  </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">  </span><span class="nt">minReplicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">  </span><span class="nt">maxReplicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">  </span><span class="nt">targetCPUUtilizationPercentage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">80</span>
</code></pre></div>
<h3 id="backend-helm-chart">Backend Helm Chart<a class="headerlink" href="#backend-helm-chart" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># charts/backend/values.yaml</span>
<span class="nt">replicaCount</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>

<span class="nt">image</span><span class="p">:</span>
<span class="w">  </span><span class="nt">repository</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus/nc-cloud-bss-oc-ui-backend-b2b</span>
<span class="w">  </span><span class="nt">tag</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.2.0&quot;</span>
<span class="w">  </span><span class="nt">pullPolicy</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">IfNotPresent</span>

<span class="nt">service</span><span class="p">:</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ClusterIP</span>
<span class="w">  </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8080</span>

<span class="nt">resources</span><span class="p">:</span>
<span class="w">  </span><span class="nt">requests</span><span class="p">:</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;512Mi&quot;</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;250m&quot;</span>
<span class="w">  </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1Gi&quot;</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;500m&quot;</span>

<span class="nt">config</span><span class="p">:</span>
<span class="w">  </span><span class="nt">springProfiles</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;production&quot;</span>
<span class="w">  </span><span class="nt">logLevel</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INFO&quot;</span>

<span class="nt">database</span><span class="p">:</span>
<span class="w">  </span><span class="nt">host</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;postgres.telus.com&quot;</span>
<span class="w">  </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5432</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;telus_b2b&quot;</span>
<span class="w">  </span><span class="c1"># Credentials stored in secrets</span>

<span class="nt">orderCapture</span><span class="p">:</span>
<span class="w">  </span><span class="nt">baseUrl</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://order-capture.telus.com/api/v2&quot;</span>
<span class="w">  </span><span class="c1"># Credentials stored in secrets</span>

<span class="nt">autoscaling</span><span class="p">:</span>
<span class="w">  </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">  </span><span class="nt">minReplicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
<span class="w">  </span><span class="nt">maxReplicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8</span>
<span class="w">  </span><span class="nt">targetCPUUtilizationPercentage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">70</span>
<span class="w">  </span><span class="nt">targetMemoryUtilizationPercentage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">80</span>
</code></pre></div>
<h2 id="environment-configuration">Environment Configuration<a class="headerlink" href="#environment-configuration" title="Permanent link">&para;</a></h2>
<h3 id="development-environment">Development Environment<a class="headerlink" href="#development-environment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># environments/development/values.yaml</span>
<span class="nt">global</span><span class="p">:</span>
<span class="w">  </span><span class="nt">environment</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">development</span>

<span class="nt">frontend</span><span class="p">:</span>
<span class="w">  </span><span class="nt">replicaCount</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">  </span><span class="nt">config</span><span class="p">:</span>
<span class="w">    </span><span class="nt">apiBaseUrl</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://localhost:8080&quot;</span>

<span class="nt">backend</span><span class="p">:</span>
<span class="w">  </span><span class="nt">replicaCount</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">  </span><span class="nt">config</span><span class="p">:</span>
<span class="w">    </span><span class="nt">springProfiles</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;development&quot;</span>
<span class="w">    </span><span class="nt">logLevel</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;DEBUG&quot;</span>
<span class="w">  </span><span class="nt">database</span><span class="p">:</span>
<span class="w">    </span><span class="nt">host</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;dev-postgres.telus.com&quot;</span>

<span class="nt">extensions</span><span class="p">:</span>
<span class="w">  </span><span class="nt">replicaCount</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">  </span><span class="nt">config</span><span class="p">:</span>
<span class="w">    </span><span class="nt">qesEnvironment</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;development&quot;</span>
</code></pre></div>
<h3 id="production-environment">Production Environment<a class="headerlink" href="#production-environment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># environments/production/values.yaml</span>
<span class="nt">global</span><span class="p">:</span>
<span class="w">  </span><span class="nt">environment</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">production</span>

<span class="nt">frontend</span><span class="p">:</span>
<span class="w">  </span><span class="nt">replicaCount</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">  </span><span class="nt">autoscaling</span><span class="p">:</span>
<span class="w">    </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">maxReplicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">15</span>

<span class="nt">backend</span><span class="p">:</span>
<span class="w">  </span><span class="nt">replicaCount</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">  </span><span class="nt">autoscaling</span><span class="p">:</span>
<span class="w">    </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">maxReplicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">12</span>
<span class="w">  </span><span class="nt">config</span><span class="p">:</span>
<span class="w">    </span><span class="nt">springProfiles</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;production&quot;</span>
<span class="w">    </span><span class="nt">logLevel</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;WARN&quot;</span>

<span class="nt">extensions</span><span class="p">:</span>
<span class="w">  </span><span class="nt">replicaCount</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
<span class="w">  </span><span class="nt">autoscaling</span><span class="p">:</span>
<span class="w">    </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">maxReplicas</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">6</span>
</code></pre></div>
<h2 id="cicd-pipeline">CI/CD Pipeline<a class="headerlink" href="#cicd-pipeline" title="Permanent link">&para;</a></h2>
<h3 id="gitlab-ci-pipeline">GitLab CI Pipeline<a class="headerlink" href="#gitlab-ci-pipeline" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># .gitlab-ci.yml</span>
<span class="nt">stages</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">build</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">test</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">package</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">deploy</span>

<span class="nt">variables</span><span class="p">:</span>
<span class="w">  </span><span class="nt">DOCKER_REGISTRY</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">registry.telus.com</span>
<span class="w">  </span><span class="nt">KUBERNETES_NAMESPACE</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b</span>

<span class="c1"># Build stage</span>
<span class="nt">build-extensions</span><span class="p">:</span>
<span class="w">  </span><span class="nt">stage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">build</span>
<span class="w">  </span><span class="nt">script</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cd nc-cloud-bss-oc-ui-be-extension-b2b</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mvn clean package</span>
<span class="w">  </span><span class="nt">artifacts</span><span class="p">:</span>
<span class="w">    </span><span class="nt">paths</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">nc-cloud-bss-oc-ui-be-extension-b2b/target/</span>

<span class="nt">build-backend</span><span class="p">:</span>
<span class="w">  </span><span class="nt">stage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">build</span>
<span class="w">  </span><span class="nt">script</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cd nc-cloud-bss-oc-ui-backend-b2b</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mvn clean package</span>
<span class="w">  </span><span class="nt">artifacts</span><span class="p">:</span>
<span class="w">    </span><span class="nt">paths</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">nc-cloud-bss-oc-ui-backend-b2b/*/target/</span>

<span class="nt">build-frontend</span><span class="p">:</span>
<span class="w">  </span><span class="nt">stage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">build</span>
<span class="w">  </span><span class="nt">script</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cd nc-cloud-bss-oc-ui-frontend-b2b</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">npm ci</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">npm run build</span>
<span class="w">  </span><span class="nt">artifacts</span><span class="p">:</span>
<span class="w">    </span><span class="nt">paths</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">nc-cloud-bss-oc-ui-frontend-b2b/dist/</span>

<span class="c1"># Package stage</span>
<span class="nt">package-images</span><span class="p">:</span>
<span class="w">  </span><span class="nt">stage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">package</span>
<span class="w">  </span><span class="nt">script</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">docker build -t $DOCKER_REGISTRY/telus-b2b-frontend:$CI_COMMIT_SHA nc-cloud-bss-oc-ui-frontend-b2b/</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">docker build -t $DOCKER_REGISTRY/telus-b2b-backend:$CI_COMMIT_SHA nc-cloud-bss-oc-ui-backend-b2b/</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">docker build -t $DOCKER_REGISTRY/telus-b2b-extensions:$CI_COMMIT_SHA nc-cloud-bss-oc-ui-be-extension-b2b/</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">docker push $DOCKER_REGISTRY/telus-b2b-frontend:$CI_COMMIT_SHA</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">docker push $DOCKER_REGISTRY/telus-b2b-backend:$CI_COMMIT_SHA</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">docker push $DOCKER_REGISTRY/telus-b2b-extensions:$CI_COMMIT_SHA</span>

<span class="c1"># Deploy stage</span>
<span class="nt">deploy-development</span><span class="p">:</span>
<span class="w">  </span><span class="nt">stage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">deploy</span>
<span class="w">  </span><span class="nt">script</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">helm upgrade --install telus-b2b-dev ./charts/telus-b2b</span>
<span class="w">      </span><span class="l l-Scalar l-Scalar-Plain">--namespace $KUBERNETES_NAMESPACE-dev</span>
<span class="w">      </span><span class="l l-Scalar l-Scalar-Plain">--values environments/development/values.yaml</span>
<span class="w">      </span><span class="l l-Scalar l-Scalar-Plain">--set global.imageTag=$CI_COMMIT_SHA</span>
<span class="w">  </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">    </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">development</span>
<span class="w">    </span><span class="nt">url</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">https://dev.b2b-order-capture.telus.com</span>
<span class="w">  </span><span class="nt">only</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">develop</span>

<span class="nt">deploy-production</span><span class="p">:</span>
<span class="w">  </span><span class="nt">stage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">deploy</span>
<span class="w">  </span><span class="nt">script</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">helm upgrade --install telus-b2b-prod ./charts/telus-b2b</span>
<span class="w">      </span><span class="l l-Scalar l-Scalar-Plain">--namespace $KUBERNETES_NAMESPACE-prod</span>
<span class="w">      </span><span class="l l-Scalar l-Scalar-Plain">--values environments/production/values.yaml</span>
<span class="w">      </span><span class="l l-Scalar l-Scalar-Plain">--set global.imageTag=$CI_COMMIT_SHA</span>
<span class="w">  </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">    </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">production</span>
<span class="w">    </span><span class="nt">url</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">https://b2b-order-capture.telus.com</span>
<span class="w">  </span><span class="nt">only</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">main</span>
<span class="w">  </span><span class="nt">when</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">manual</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: Explore the <a href="../07-reference/">Reference Guide</a> for complete API and configuration reference.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "..", "features": [], "search": "../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>