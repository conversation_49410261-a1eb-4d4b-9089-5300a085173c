import { NiFiClient } from '../nifi-client.js';
import { z } from 'zod';

type ToolRequest = {
  method: string;
  params?: {
    arguments?: Record<string, unknown>;
  };
};

type ToolResponse = {
  content: Array<{ type: string; text: string }>;
  isError?: boolean;
};

const toolSchema = z.object({
  method: z.string(),
  params: z.object({
    arguments: z.record(z.unknown())
  }).optional()
});

export const tools = {
  manage_process_group: {
    description: 'Manage process groups',
    inputSchema: {
      type: 'object',
      properties: {
        action: {
          type: 'string',
          enum: ['create', 'update', 'get'],
          description: 'Action to perform on the process group'
        },
        parentGroupId: {
          type: 'string',
          description: 'ID of the parent process group'
        },
        processGroupId: {
          type: 'string',
          description: 'ID of the process group (required for update/get)'
        },
        name: {
          type: 'string',
          description: 'Name for the process group (required for create/update)'
        },
        position: {
          type: 'object',
          properties: {
            x: { type: 'number' },
            y: { type: 'number' }
          },
          description: 'Position coordinates for the process group'
        },
        comments: {
          type: 'string',
          description: 'Optional comments for the process group'
        }
      },
      required: ['action', 'parentGroupId']
    }
  }
};

export const manageProcessGroup = (client: NiFiClient) => async (request: ToolRequest): Promise<ToolResponse> => {
  const { action, parentGroupId, processGroupId, name, position, comments } = (request.params?.arguments || {}) as {
    action: 'create' | 'update' | 'get';
    parentGroupId: string;
    processGroupId?: string;
    name?: string;
    position?: { x: number; y: number };
    comments?: string;
  };

  try {
    let response;
    switch (action) {
      case 'create':
        if (!name) {
          throw new Error('Name is required for creating a process group');
        }
        response = await client.post(`/process-groups/${parentGroupId}/process-groups`, {
          component: {
            name,
            position: position || { x: 0, y: 0 },
            comments
          }
        });
        break;

      case 'update':
        if (!processGroupId) {
          throw new Error('Process group ID is required for updating');
        }
        response = await client.put(`/process-groups/${processGroupId}`, {
          component: {
            id: processGroupId,
            name,
            comments
          }
        });
        break;

      case 'get':
        if (!processGroupId) {
          throw new Error('Process group ID is required for retrieval');
        }
        response = await client.get(`/process-groups/${processGroupId}`);
        break;

      default:
        throw new Error(`Invalid action: ${action}`);
    }

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2)
        }
      ]
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      content: [
        {
          type: 'text',
          text: `Error managing process group: ${errorMessage}`
        }
      ],
      isError: true
    };
  }
};
