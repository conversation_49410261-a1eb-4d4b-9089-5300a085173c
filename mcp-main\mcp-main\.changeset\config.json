{"$schema": "https://unpkg.com/@changesets/config@3.0.5/schema.json", "changelog": "@changesets/cli/changelog", "commit": true, "fixed": [], "linked": [], "access": "restricted", "baseBranch": "main", "updateInternalDependencies": "patch", "ignore": [], "___experimentalUnsafeOptions_WILL_CHANGE_IN_PATCH": {"onlyUpdatePeerDependentsWhenOutOfRange": true}, "privatePackages": {"version": true, "tag": false}}