#!/usr/bin/env node

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";
import {
  blazeMeterTools,
  handleBlazeMeterTool,
} from "./handlers/blazemeter.js";

const server = new Server(
  {
    name: "blazemeter-server",
    version: "0.1.0",
  },
  {
    capabilities: {
      resources: {},
      tools: {},
    },
  },
);

server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: blazeMeterTools,
  };
});

server.setRequestHandler(CallToolRequestSchema, async (request) => {
  try {
    const result = await handleBlazeMeterTool(
      request.params.name,
      request.params.arguments,
    );
    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  } catch (error: any) {
    console.error(`Error handling BlazeMeter tool: ${error.message}`);
    return {
      content: [
        {
          type: "text",
          text: `Error: ${error.message}`,
        },
      ],
      isError: true,
    };
  }
});

const transport = new StdioServerTransport();
server.connect(transport);
console.error("BlazeMeter MCP server running on stdio");
