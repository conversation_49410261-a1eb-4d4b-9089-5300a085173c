
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/extensions/01-plugin-architecture/">
      
      
        <link rel="prev" href="../00-overview/">
      
      
        <link rel="next" href="../02-quote-modificators/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Plugin Architecture - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#plugin-architecture" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Plugin Architecture
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" checked>
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#qes-plugin-framework" class="md-nav__link">
    <span class="md-ellipsis">
      QES Plugin Framework
    </span>
  </a>
  
    <nav class="md-nav" aria-label="QES Plugin Framework">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#framework-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Framework Overview
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-registration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Registration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#plugin-lifecycle" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Lifecycle
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Plugin Lifecycle">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#lifecycle-states" class="md-nav__link">
    <span class="md-ellipsis">
      Lifecycle States
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#lifecycle-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Lifecycle Implementation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Component Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Component Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#component-hierarchy" class="md-nav__link">
    <span class="md-ellipsis">
      Component Hierarchy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-registration" class="md-nav__link">
    <span class="md-ellipsis">
      Component Registration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#interface-contracts" class="md-nav__link">
    <span class="md-ellipsis">
      Interface Contracts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Interface Contracts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-modificator-interface" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Modificator Interface
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bandwidth-validator-interface" class="md-nav__link">
    <span class="md-ellipsis">
      Bandwidth Validator Interface
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#state-machine-validator-interface" class="md-nav__link">
    <span class="md-ellipsis">
      State Machine Validator Interface
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#dependency-injection" class="md-nav__link">
    <span class="md-ellipsis">
      Dependency Injection
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Dependency Injection">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#spring-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Spring Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-wiring" class="md-nav__link">
    <span class="md-ellipsis">
      Component Wiring
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#configuration-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-binding" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Binding
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#qes-plugin-framework" class="md-nav__link">
    <span class="md-ellipsis">
      QES Plugin Framework
    </span>
  </a>
  
    <nav class="md-nav" aria-label="QES Plugin Framework">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#framework-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Framework Overview
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-registration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Registration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#plugin-lifecycle" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Lifecycle
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Plugin Lifecycle">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#lifecycle-states" class="md-nav__link">
    <span class="md-ellipsis">
      Lifecycle States
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#lifecycle-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Lifecycle Implementation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Component Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Component Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#component-hierarchy" class="md-nav__link">
    <span class="md-ellipsis">
      Component Hierarchy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-registration" class="md-nav__link">
    <span class="md-ellipsis">
      Component Registration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#interface-contracts" class="md-nav__link">
    <span class="md-ellipsis">
      Interface Contracts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Interface Contracts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-modificator-interface" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Modificator Interface
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bandwidth-validator-interface" class="md-nav__link">
    <span class="md-ellipsis">
      Bandwidth Validator Interface
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#state-machine-validator-interface" class="md-nav__link">
    <span class="md-ellipsis">
      State Machine Validator Interface
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#dependency-injection" class="md-nav__link">
    <span class="md-ellipsis">
      Dependency Injection
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Dependency Injection">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#spring-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Spring Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-wiring" class="md-nav__link">
    <span class="md-ellipsis">
      Component Wiring
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#configuration-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-binding" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Binding
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="plugin-architecture">Plugin Architecture<a class="headerlink" href="#plugin-architecture" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#qes-plugin-framework">QES Plugin Framework</a></li>
<li><a href="#plugin-lifecycle">Plugin Lifecycle</a></li>
<li><a href="#component-architecture">Component Architecture</a></li>
<li><a href="#interface-contracts">Interface Contracts</a></li>
<li><a href="#dependency-injection">Dependency Injection</a></li>
<li><a href="#configuration-management">Configuration Management</a></li>
</ul>
<h2 id="qes-plugin-framework">QES Plugin Framework<a class="headerlink" href="#qes-plugin-framework" title="Permanent link">&para;</a></h2>
<h3 id="framework-overview">Framework Overview<a class="headerlink" href="#framework-overview" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Order Capture Product&quot;
        A[Quote Engine Service]
        B[Plugin Registry]
        C[Extension Points]
    end

    subgraph &quot;QES Plugin Framework&quot;
        D[Plugin Loader]
        E[Lifecycle Manager]
        F[Dependency Injector]
        G[Configuration Manager]
    end

    subgraph &quot;TELUS B2B Plugin&quot;
        H[Plugin Descriptor]
        I[Component Registry]
        J[Business Logic]
        K[Validation Rules]
    end

    subgraph &quot;Plugin Components&quot;
        L[Quote Modificators]
        M[Bandwidth Validators]
        N[State Validators]
        O[Utility Services]
    end

    A --&gt; B
    B --&gt; C
    C --&gt; D
    D --&gt; E
    E --&gt; F
    F --&gt; G

    G --&gt; H
    H --&gt; I
    I --&gt; J
    J --&gt; K

    J --&gt; L
    J --&gt; M
    J --&gt; N
    J --&gt; O

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style D fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style H fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="plugin-registration">Plugin Registration<a class="headerlink" href="#plugin-registration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Plugin Descriptor</span>
<span class="nd">@SmartPlug</span><span class="p">(</span>
<span class="w">    </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;telus-b2b-qes-plugin&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="n">version</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;1.2.0&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="n">description</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;TELUS B2B Order Capture QES Extensions&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="n">vendor</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;TELUS&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="n">dependencies</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nd">@Dependency</span><span class="p">(</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;qes-core&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">version</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;2023.3.2.0&quot;</span><span class="p">),</span>
<span class="w">        </span><span class="nd">@Dependency</span><span class="p">(</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;order-capture-model&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">version</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;2023.3.2.63&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">)</span>
<span class="nd">@Configuration</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusB2BPluginDescriptor</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="nd">@Primary</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">PluginMetadata</span><span class="w"> </span><span class="nf">pluginMetadata</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">PluginMetadata</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">name</span><span class="p">(</span><span class="s">&quot;telus-b2b-qes-plugin&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">version</span><span class="p">(</span><span class="s">&quot;1.2.0&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">description</span><span class="p">(</span><span class="s">&quot;TELUS B2B Order Capture QES Extensions&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">vendor</span><span class="p">(</span><span class="s">&quot;TELUS&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">supportedExtensionPoints</span><span class="p">(</span><span class="n">Arrays</span><span class="p">.</span><span class="na">asList</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;quote.modification&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;bandwidth.validation&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;state.validation&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;quote.expiration&quot;</span>
<span class="w">            </span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">PluginConfiguration</span><span class="w"> </span><span class="nf">pluginConfiguration</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">PluginConfiguration</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">enableHotReload</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">enableMetrics</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">enableHealthCheck</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">configurationSource</span><span class="p">(</span><span class="s">&quot;classpath:plugin.properties&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="plugin-lifecycle">Plugin Lifecycle<a class="headerlink" href="#plugin-lifecycle" title="Permanent link">&para;</a></h2>
<h3 id="lifecycle-states">Lifecycle States<a class="headerlink" href="#lifecycle-states" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>stateDiagram-v2
    [*] --&gt; Loaded
    Loaded --&gt; Initialized: initialize()
    Initialized --&gt; Started: start()
    Started --&gt; Active: activate()
    Active --&gt; Suspended: suspend()
    Suspended --&gt; Active: resume()
    Active --&gt; Stopped: stop()
    Stopped --&gt; Destroyed: destroy()
    Destroyed --&gt; [*]

    Active --&gt; Error: exception
    Error --&gt; Stopped: recover()
    Error --&gt; Destroyed: fail()
</code></pre></div>
<h3 id="lifecycle-implementation">Lifecycle Implementation<a class="headerlink" href="#lifecycle-implementation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusB2BPluginLifecycleManager</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">PluginLifecycleManager</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">QuoteDeltaModificator</span><span class="o">&gt;</span><span class="w"> </span><span class="n">quoteModificators</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">BandwidthValidator</span><span class="o">&gt;</span><span class="w"> </span><span class="n">bandwidthValidators</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">StateMachineExtensionValidator</span><span class="o">&gt;</span><span class="w"> </span><span class="n">stateValidators</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">PluginMetrics</span><span class="w"> </span><span class="n">pluginMetrics</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">initialize</span><span class="p">(</span><span class="n">PluginContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Initializing TELUS B2B QES Plugin&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Initialize configuration</span>
<span class="w">            </span><span class="n">initializeConfiguration</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Initialize components</span>
<span class="w">            </span><span class="n">initializeComponents</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Initialize metrics</span>
<span class="w">            </span><span class="n">initializeMetrics</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate plugin integrity</span>
<span class="w">            </span><span class="n">validatePluginIntegrity</span><span class="p">();</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;TELUS B2B QES Plugin initialized successfully&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to initialize TELUS B2B QES Plugin&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">PluginInitializationException</span><span class="p">(</span><span class="s">&quot;Plugin initialization failed&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">start</span><span class="p">(</span><span class="n">PluginContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Starting TELUS B2B QES Plugin&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Start quote modificators</span>
<span class="w">            </span><span class="n">quoteModificators</span><span class="p">.</span><span class="na">forEach</span><span class="p">(</span><span class="n">modificator</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Starting quote modificator: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">modificator</span><span class="p">.</span><span class="na">getClass</span><span class="p">().</span><span class="na">getSimpleName</span><span class="p">());</span>
<span class="w">                </span><span class="n">modificator</span><span class="p">.</span><span class="na">initialize</span><span class="p">();</span>
<span class="w">            </span><span class="p">});</span>

<span class="w">            </span><span class="c1">// Start bandwidth validators</span>
<span class="w">            </span><span class="n">bandwidthValidators</span><span class="p">.</span><span class="na">forEach</span><span class="p">(</span><span class="n">validator</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Starting bandwidth validator: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">validator</span><span class="p">.</span><span class="na">getClass</span><span class="p">().</span><span class="na">getSimpleName</span><span class="p">());</span>
<span class="w">                </span><span class="n">validator</span><span class="p">.</span><span class="na">initialize</span><span class="p">();</span>
<span class="w">            </span><span class="p">});</span>

<span class="w">            </span><span class="c1">// Start state validators</span>
<span class="w">            </span><span class="n">stateValidators</span><span class="p">.</span><span class="na">forEach</span><span class="p">(</span><span class="n">validator</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Starting state validator: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">validator</span><span class="p">.</span><span class="na">getClass</span><span class="p">().</span><span class="na">getSimpleName</span><span class="p">());</span>
<span class="w">                </span><span class="n">validator</span><span class="p">.</span><span class="na">initialize</span><span class="p">();</span>
<span class="w">            </span><span class="p">});</span>

<span class="w">            </span><span class="c1">// Register health checks</span>
<span class="w">            </span><span class="n">registerHealthChecks</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Start metrics collection</span>
<span class="w">            </span><span class="n">pluginMetrics</span><span class="p">.</span><span class="na">startCollection</span><span class="p">();</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;TELUS B2B QES Plugin started successfully&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to start TELUS B2B QES Plugin&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">PluginStartException</span><span class="p">(</span><span class="s">&quot;Plugin start failed&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">stop</span><span class="p">(</span><span class="n">PluginContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Stopping TELUS B2B QES Plugin&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Stop metrics collection</span>
<span class="w">            </span><span class="n">pluginMetrics</span><span class="p">.</span><span class="na">stopCollection</span><span class="p">();</span>

<span class="w">            </span><span class="c1">// Unregister health checks</span>
<span class="w">            </span><span class="n">unregisterHealthChecks</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Stop components</span>
<span class="w">            </span><span class="n">stopComponents</span><span class="p">();</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;TELUS B2B QES Plugin stopped successfully&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to stop TELUS B2B QES Plugin&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">PluginStopException</span><span class="p">(</span><span class="s">&quot;Plugin stop failed&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">destroy</span><span class="p">(</span><span class="n">PluginContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Destroying TELUS B2B QES Plugin&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Cleanup resources</span>
<span class="w">            </span><span class="n">cleanupResources</span><span class="p">();</span>

<span class="w">            </span><span class="c1">// Clear caches</span>
<span class="w">            </span><span class="n">clearCaches</span><span class="p">();</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;TELUS B2B QES Plugin destroyed successfully&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to destroy TELUS B2B QES Plugin&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">initializeConfiguration</span><span class="p">(</span><span class="n">PluginContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Load plugin configuration</span>
<span class="w">        </span><span class="n">PluginConfiguration</span><span class="w"> </span><span class="n">config</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getConfiguration</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Validate configuration</span>
<span class="w">        </span><span class="n">validateConfiguration</span><span class="p">(</span><span class="n">config</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply configuration</span>
<span class="w">        </span><span class="n">applyConfiguration</span><span class="p">(</span><span class="n">config</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">initializeComponents</span><span class="p">(</span><span class="n">PluginContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Initialize dependency injection container</span>
<span class="w">        </span><span class="n">ApplicationContext</span><span class="w"> </span><span class="n">applicationContext</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createApplicationContext</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Register components</span>
<span class="w">        </span><span class="n">registerComponents</span><span class="p">(</span><span class="n">applicationContext</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Wire dependencies</span>
<span class="w">        </span><span class="n">wireDependencies</span><span class="p">(</span><span class="n">applicationContext</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="component-architecture">Component Architecture<a class="headerlink" href="#component-architecture" title="Permanent link">&para;</a></h2>
<h3 id="component-hierarchy">Component Hierarchy<a class="headerlink" href="#component-hierarchy" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Plugin Root&quot;
        A[TelusB2BPluginDescriptor]
    end

    subgraph &quot;Core Components&quot;
        B[Quote Modificators]
        C[Bandwidth Validators]
        D[State Validators]
        E[Utility Services]
    end

    subgraph &quot;Quote Modificators&quot;
        F[TelusQuoteDeltaModificatorImpl]
        G[TelusQuoteResultingDeltaModificator]
        H[TelusQuoteExpirationPeriodProvider]
    end

    subgraph &quot;Bandwidth Validators&quot;
        I[TelusWanL2CirBandwidthValidator]
        J[TelusWanL2EvcBandwidthValidator]
        K[TelusWanL3IpQosBandwidthValidator]
    end

    subgraph &quot;State Validators&quot;
        L[TelusStateMachineExtensionValidator]
    end

    subgraph &quot;Utility Services&quot;
        M[BandwidthValidatorUtils]
        N[CommonUtils]
        O[ValidationUtils]
        P[QuoteUtils]
    end

    A --&gt; B
    A --&gt; C
    A --&gt; D
    A --&gt; E

    B --&gt; F
    B --&gt; G
    B --&gt; H

    C --&gt; I
    C --&gt; J
    C --&gt; K

    D --&gt; L

    E --&gt; M
    E --&gt; N
    E --&gt; O
    E --&gt; P

    F --&gt; M
    F --&gt; N
    G --&gt; O
    H --&gt; P
    I --&gt; M
    J --&gt; M
    K --&gt; M
    L --&gt; O

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style C fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="component-registration">Component Registration<a class="headerlink" href="#component-registration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Configuration</span>
<span class="nd">@ComponentScan</span><span class="p">(</span><span class="n">basePackages</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;com.netcracker.solutions.telus.qes.extension.plugin&quot;</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">PluginComponentConfiguration</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="nd">@ConditionalOnProperty</span><span class="p">(</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;telus.plugin.quote-modificators.enabled&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">havingValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;true&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">QuoteDeltaModificator</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">quoteModificators</span><span class="p">(</span>
<span class="w">            </span><span class="n">TelusQuoteDeltaModificatorImpl</span><span class="w"> </span><span class="n">deltaModificator</span><span class="p">,</span>
<span class="w">            </span><span class="n">TelusQuoteResultingDeltaModificator</span><span class="w"> </span><span class="n">resultingModificator</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">Arrays</span><span class="p">.</span><span class="na">asList</span><span class="p">(</span><span class="n">deltaModificator</span><span class="p">,</span><span class="w"> </span><span class="n">resultingModificator</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="nd">@ConditionalOnProperty</span><span class="p">(</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;telus.plugin.bandwidth-validators.enabled&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">havingValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;true&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">BandwidthValidator</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">bandwidthValidators</span><span class="p">(</span>
<span class="w">            </span><span class="n">TelusWanL2CirBandwidthValidator</span><span class="w"> </span><span class="n">l2CirValidator</span><span class="p">,</span>
<span class="w">            </span><span class="n">TelusWanL2EvcBandwidthValidator</span><span class="w"> </span><span class="n">l2EvcValidator</span><span class="p">,</span>
<span class="w">            </span><span class="n">TelusWanL3IpQosBandwidthValidator</span><span class="w"> </span><span class="n">l3QosValidator</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">Arrays</span><span class="p">.</span><span class="na">asList</span><span class="p">(</span><span class="n">l2CirValidator</span><span class="p">,</span><span class="w"> </span><span class="n">l2EvcValidator</span><span class="p">,</span><span class="w"> </span><span class="n">l3QosValidator</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="nd">@ConditionalOnProperty</span><span class="p">(</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;telus.plugin.state-validators.enabled&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">havingValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;true&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">StateMachineExtensionValidator</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">stateValidators</span><span class="p">(</span>
<span class="w">            </span><span class="n">TelusStateMachineExtensionValidator</span><span class="w"> </span><span class="n">stateMachineValidator</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">Arrays</span><span class="p">.</span><span class="na">asList</span><span class="p">(</span><span class="n">stateMachineValidator</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">BandwidthValidatorUtils</span><span class="w"> </span><span class="nf">bandwidthValidatorUtils</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BandwidthValidatorUtils</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">CommonUtils</span><span class="w"> </span><span class="nf">commonUtils</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">CommonUtils</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ValidationUtils</span><span class="w"> </span><span class="nf">validationUtils</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ValidationUtils</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteUtils</span><span class="w"> </span><span class="nf">quoteUtils</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">QuoteUtils</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="interface-contracts">Interface Contracts<a class="headerlink" href="#interface-contracts" title="Permanent link">&para;</a></h2>
<h3 id="quote-modificator-interface">Quote Modificator Interface<a class="headerlink" href="#quote-modificator-interface" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Interface for quote modification plugins</span>
<span class="cm"> */</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">interface</span> <span class="nc">QuoteDeltaModificator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Modify quote based on business rules</span>
<span class="cm">     * </span>
<span class="cm">     * @param context Quote modification context</span>
<span class="cm">     * @return Quote delta with modifications</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="nf">modifyQuote</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">);</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Get modificator priority (higher values execute first)</span>
<span class="cm">     * </span>
<span class="cm">     * @return Priority value</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="k">default</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="nf">getPriority</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="mi">100</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Check if modificator applies to the given context</span>
<span class="cm">     * </span>
<span class="cm">     * @param context Quote modification context</span>
<span class="cm">     * @return true if applicable</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="k">default</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isApplicable</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Initialize modificator</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="k">default</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">initialize</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Default implementation</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Cleanup modificator resources</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="k">default</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">cleanup</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Default implementation</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="bandwidth-validator-interface">Bandwidth Validator Interface<a class="headerlink" href="#bandwidth-validator-interface" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Interface for bandwidth validation plugins</span>
<span class="cm"> */</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">interface</span> <span class="nc">BandwidthValidator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Validate bandwidth parameters for a service item</span>
<span class="cm">     * </span>
<span class="cm">     * @param context Bandwidth validation context</span>
<span class="cm">     * @return Validation result</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validate</span><span class="p">(</span><span class="n">BandwidthValidationContext</span><span class="w"> </span><span class="n">context</span><span class="p">);</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Get supported service types</span>
<span class="cm">     * </span>
<span class="cm">     * @return Set of supported service types</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">getSupportedServiceTypes</span><span class="p">();</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Get validator priority (higher values execute first)</span>
<span class="cm">     * </span>
<span class="cm">     * @return Priority value</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="k">default</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="nf">getPriority</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="mi">100</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Check if validator applies to the given service type</span>
<span class="cm">     * </span>
<span class="cm">     * @param serviceType Service type to check</span>
<span class="cm">     * @return true if applicable</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="k">default</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isApplicable</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">serviceType</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">getSupportedServiceTypes</span><span class="p">().</span><span class="na">contains</span><span class="p">(</span><span class="n">serviceType</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Initialize validator</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="k">default</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">initialize</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Default implementation</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="state-machine-validator-interface">State Machine Validator Interface<a class="headerlink" href="#state-machine-validator-interface" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Interface for state machine validation plugins</span>
<span class="cm"> */</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">interface</span> <span class="nc">StateMachineExtensionValidator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Validate state transition</span>
<span class="cm">     * </span>
<span class="cm">     * @param context State transition context</span>
<span class="cm">     * @return Validation result</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validateStateTransition</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">);</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Get supported state transitions</span>
<span class="cm">     * </span>
<span class="cm">     * @return Map of from-state to allowed to-states</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">QuoteState</span><span class="p">,</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">QuoteState</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="nf">getSupportedTransitions</span><span class="p">();</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Check if validator applies to the given transition</span>
<span class="cm">     * </span>
<span class="cm">     * @param fromState Source state</span>
<span class="cm">     * @param toState Target state</span>
<span class="cm">     * @return true if applicable</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="k">default</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isApplicable</span><span class="p">(</span><span class="n">QuoteState</span><span class="w"> </span><span class="n">fromState</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">toState</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">QuoteState</span><span class="o">&gt;</span><span class="w"> </span><span class="n">allowedStates</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getSupportedTransitions</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="n">fromState</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">allowedStates</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">allowedStates</span><span class="p">.</span><span class="na">contains</span><span class="p">(</span><span class="n">toState</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Initialize validator</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="k">default</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">initialize</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Default implementation</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="dependency-injection">Dependency Injection<a class="headerlink" href="#dependency-injection" title="Permanent link">&para;</a></h2>
<h3 id="spring-integration">Spring Integration<a class="headerlink" href="#spring-integration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Configuration</span>
<span class="nd">@EnableAutoConfiguration</span>
<span class="nd">@ComponentScan</span><span class="p">(</span><span class="n">basePackages</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s">&quot;com.netcracker.solutions.telus.qes.extension.plugin&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s">&quot;com.netcracker.qes.plugin.framework&quot;</span>
<span class="p">})</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">PluginSpringConfiguration</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="nd">@Primary</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ApplicationContext</span><span class="w"> </span><span class="nf">pluginApplicationContext</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">AnnotationConfigApplicationContext</span><span class="w"> </span><span class="n">context</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">AnnotationConfigApplicationContext</span><span class="p">();</span>
<span class="w">        </span><span class="n">context</span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">PluginComponentConfiguration</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>
<span class="w">        </span><span class="n">context</span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">PluginValidationConfiguration</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>
<span class="w">        </span><span class="n">context</span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">PluginUtilityConfiguration</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>
<span class="w">        </span><span class="n">context</span><span class="p">.</span><span class="na">refresh</span><span class="p">();</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">context</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">BeanFactory</span><span class="w"> </span><span class="nf">pluginBeanFactory</span><span class="p">(</span><span class="n">ApplicationContext</span><span class="w"> </span><span class="n">applicationContext</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">applicationContext</span><span class="p">.</span><span class="na">getAutowireCapableBeanFactory</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="nd">@Scope</span><span class="p">(</span><span class="s">&quot;prototype&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">PluginContext</span><span class="w"> </span><span class="nf">pluginContext</span><span class="p">(</span>
<span class="w">            </span><span class="n">PluginMetadata</span><span class="w"> </span><span class="n">metadata</span><span class="p">,</span>
<span class="w">            </span><span class="n">PluginConfiguration</span><span class="w"> </span><span class="n">configuration</span><span class="p">,</span>
<span class="w">            </span><span class="n">ApplicationContext</span><span class="w"> </span><span class="n">applicationContext</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">PluginContext</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">metadata</span><span class="p">(</span><span class="n">metadata</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">configuration</span><span class="p">(</span><span class="n">configuration</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">applicationContext</span><span class="p">(</span><span class="n">applicationContext</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="component-wiring">Component Wiring<a class="headerlink" href="#component-wiring" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">PluginComponentWiring</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">ApplicationContext</span><span class="w"> </span><span class="n">applicationContext</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@PostConstruct</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">wireComponents</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Wiring plugin components&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Wire quote modificators</span>
<span class="w">        </span><span class="n">wireQuoteModificators</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Wire bandwidth validators</span>
<span class="w">        </span><span class="n">wireBandwidthValidators</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Wire state validators</span>
<span class="w">        </span><span class="n">wireStateValidators</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Wire utility services</span>
<span class="w">        </span><span class="n">wireUtilityServices</span><span class="p">();</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Plugin components wired successfully&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">wireQuoteModificators</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteDeltaModificator</span><span class="o">&gt;</span><span class="w"> </span><span class="n">modificators</span><span class="w"> </span><span class="o">=</span><span class="w"> </span>
<span class="w">            </span><span class="n">applicationContext</span><span class="p">.</span><span class="na">getBeansOfType</span><span class="p">(</span><span class="n">QuoteDeltaModificator</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>

<span class="w">        </span><span class="n">modificators</span><span class="p">.</span><span class="na">forEach</span><span class="p">((</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">modificator</span><span class="p">)</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Wiring quote modificator: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">name</span><span class="p">);</span>
<span class="w">            </span><span class="n">autowireBean</span><span class="p">(</span><span class="n">modificator</span><span class="p">);</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">wireBandwidthValidators</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">BandwidthValidator</span><span class="o">&gt;</span><span class="w"> </span><span class="n">validators</span><span class="w"> </span><span class="o">=</span><span class="w"> </span>
<span class="w">            </span><span class="n">applicationContext</span><span class="p">.</span><span class="na">getBeansOfType</span><span class="p">(</span><span class="n">BandwidthValidator</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>

<span class="w">        </span><span class="n">validators</span><span class="p">.</span><span class="na">forEach</span><span class="p">((</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">validator</span><span class="p">)</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Wiring bandwidth validator: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">name</span><span class="p">);</span>
<span class="w">            </span><span class="n">autowireBean</span><span class="p">(</span><span class="n">validator</span><span class="p">);</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">autowireBean</span><span class="p">(</span><span class="n">Object</span><span class="w"> </span><span class="n">bean</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">AutowireCapableBeanFactory</span><span class="w"> </span><span class="n">beanFactory</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applicationContext</span><span class="p">.</span><span class="na">getAutowireCapableBeanFactory</span><span class="p">();</span>
<span class="w">        </span><span class="n">beanFactory</span><span class="p">.</span><span class="na">autowireBean</span><span class="p">(</span><span class="n">bean</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="configuration-management">Configuration Management<a class="headerlink" href="#configuration-management" title="Permanent link">&para;</a></h2>
<h3 id="configuration-structure">Configuration Structure<a class="headerlink" href="#configuration-structure" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># plugin.properties</span>
<span class="nt">telus</span><span class="p">:</span>
<span class="w">  </span><span class="nt">plugin</span><span class="p">:</span>
<span class="w">    </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-qes-plugin</span>
<span class="w">    </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1.2.0</span>
<span class="w">    </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">    </span><span class="nt">quote-modificators</span><span class="p">:</span>
<span class="w">      </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">components</span><span class="p">:</span>
<span class="w">        </span><span class="nt">delta-modificator</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">priority</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span>
<span class="w">          </span><span class="nt">rules</span><span class="p">:</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">volume-discount</span>
<span class="w">              </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">              </span><span class="nt">threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100000</span>
<span class="w">              </span><span class="nt">discount-percentage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">customer-tier-discount</span>
<span class="w">              </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">              </span><span class="nt">platinum-discount</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">              </span><span class="nt">gold-discount</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>

<span class="w">        </span><span class="nt">resulting-modificator</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">priority</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">200</span>
<span class="w">          </span><span class="nt">rules</span><span class="p">:</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">regulatory-compliance</span>
<span class="w">              </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">final-pricing-adjustment</span>
<span class="w">              </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">    </span><span class="nt">bandwidth-validators</span><span class="p">:</span>
<span class="w">      </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">strict-mode</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">components</span><span class="p">:</span>
<span class="w">        </span><span class="nt">l2-cir-validator</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">min-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">          </span><span class="nt">max-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10000</span>
<span class="w">          </span><span class="nt">unit</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Mbps</span>
<span class="w">          </span><span class="nt">validation-rules</span><span class="p">:</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cir-eir-ratio</span>
<span class="w">              </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">              </span><span class="nt">max-ratio</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2.0</span>

<span class="w">        </span><span class="nt">l2-evc-validator</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">min-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">          </span><span class="nt">max-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
<span class="w">          </span><span class="nt">unit</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Mbps</span>
<span class="w">          </span><span class="nt">evc-types</span><span class="p">:</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">POINT_TO_POINT</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">MULTIPOINT</span>

<span class="w">        </span><span class="nt">l3-qos-validator</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">min-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">          </span><span class="nt">max-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
<span class="w">          </span><span class="nt">unit</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Mbps</span>
<span class="w">          </span><span class="nt">qos-classes</span><span class="p">:</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">PREMIUM</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">STANDARD</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">BASIC</span>

<span class="w">    </span><span class="nt">state-validators</span><span class="p">:</span>
<span class="w">      </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">components</span><span class="p">:</span>
<span class="w">        </span><span class="nt">state-machine-validator</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">strict-transitions</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">allowed-transitions</span><span class="p">:</span>
<span class="w">            </span><span class="nt">DRAFT</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">SUBMITTED</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">CANCELLED</span><span class="p p-Indicator">]</span>
<span class="w">            </span><span class="nt">SUBMITTED</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">APPROVED</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">REJECTED</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">CANCELLED</span><span class="p p-Indicator">]</span>
<span class="w">            </span><span class="nt">APPROVED</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">ORDERED</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">CANCELLED</span><span class="p p-Indicator">]</span>
<span class="w">            </span><span class="nt">REJECTED</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">DRAFT</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">CANCELLED</span><span class="p p-Indicator">]</span>
<span class="w">            </span><span class="nt">ORDERED</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">COMPLETED</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">CANCELLED</span><span class="p p-Indicator">]</span>
<span class="w">            </span><span class="nt">COMPLETED</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[]</span>
<span class="w">            </span><span class="nt">CANCELLED</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[]</span>

<span class="w">    </span><span class="nt">utilities</span><span class="p">:</span>
<span class="w">      </span><span class="nt">caching</span><span class="p">:</span>
<span class="w">        </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">cache-size</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
<span class="w">        </span><span class="nt">ttl-minutes</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30</span>

<span class="w">      </span><span class="nt">metrics</span><span class="p">:</span>
<span class="w">        </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">collection-interval-seconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">60</span>

<span class="w">      </span><span class="nt">health-checks</span><span class="p">:</span>
<span class="w">        </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">check-interval-seconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30</span>
</code></pre></div>
<h3 id="configuration-binding">Configuration Binding<a class="headerlink" href="#configuration-binding" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@ConfigurationProperties</span><span class="p">(</span><span class="n">prefix</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;telus.plugin&quot;</span><span class="p">)</span>
<span class="nd">@Data</span>
<span class="nd">@Builder</span>
<span class="nd">@NoArgsConstructor</span>
<span class="nd">@AllArgsConstructor</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">PluginConfigurationProperties</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">name</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">version</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">enabled</span><span class="p">;</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">QuoteModificatorConfig</span><span class="w"> </span><span class="n">quoteModificators</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">BandwidthValidatorConfig</span><span class="w"> </span><span class="n">bandwidthValidators</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">StateValidatorConfig</span><span class="w"> </span><span class="n">stateValidators</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">UtilityConfig</span><span class="w"> </span><span class="n">utilities</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Data</span>
<span class="w">    </span><span class="nd">@Builder</span>
<span class="w">    </span><span class="nd">@NoArgsConstructor</span>
<span class="w">    </span><span class="nd">@AllArgsConstructor</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QuoteModificatorConfig</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">enabled</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">ComponentConfig</span><span class="o">&gt;</span><span class="w"> </span><span class="n">components</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Data</span>
<span class="w">    </span><span class="nd">@Builder</span>
<span class="w">    </span><span class="nd">@NoArgsConstructor</span>
<span class="w">    </span><span class="nd">@AllArgsConstructor</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">class</span> <span class="nc">BandwidthValidatorConfig</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">enabled</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">strictMode</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">ValidatorConfig</span><span class="o">&gt;</span><span class="w"> </span><span class="n">components</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Data</span>
<span class="w">    </span><span class="nd">@Builder</span>
<span class="w">    </span><span class="nd">@NoArgsConstructor</span>
<span class="w">    </span><span class="nd">@AllArgsConstructor</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">class</span> <span class="nc">StateValidatorConfig</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">enabled</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">ComponentConfig</span><span class="o">&gt;</span><span class="w"> </span><span class="n">components</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Data</span>
<span class="w">    </span><span class="nd">@Builder</span>
<span class="w">    </span><span class="nd">@NoArgsConstructor</span>
<span class="w">    </span><span class="nd">@AllArgsConstructor</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">class</span> <span class="nc">ComponentConfig</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">enabled</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">priority</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">properties</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../02-quote-modificators/">Quote Modificators →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>