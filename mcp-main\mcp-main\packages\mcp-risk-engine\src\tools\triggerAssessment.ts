import axios from 'axios';
import { CallToolRequest } from '@modelcontextprotocol/sdk/types.js';
import { SRE_RISK_ENGINE_URL, GITHUB_APP_INSTALLATION_ID } from '../constants.js';
import { TriggerAssessmentParams, AssessmentPayload, ConciseResult, Category, CategoryInput } from '../types/assessment.js';

/**
 * Handles the trigger_assessment tool request
 * @param originalRequest The original tool request
 * @returns The tool response
 */
export async function handleTriggerAssessment(originalRequest: CallToolRequest) {
  const args = originalRequest.params.arguments as TriggerAssessmentParams;
  const { owner, repo, branch, sha, commit_message } = args;

  // Default owner to 'telus' if not provided
  const effectiveOwner = owner === undefined ? 'telus' : owner;
  // Default branch to 'main' if not provided
  const effectiveBranch = branch === undefined ? 'main' : branch;

  const logs: string[] = [];
  const log = (message: string) => {
    console.log(message); // Also log to server console
    logs.push(message);   // Add to logs returned to client
  };

  try {
    log(`Processing request. Owner: "${effectiveOwner}" (Received: "${owner}"), Repo: "${repo}", Branch: "${effectiveBranch}" (Received: "${branch}"), SHA: "${sha}"`);

    const installationId = GITHUB_APP_INSTALLATION_ID || 0;

    // Construct the payload for SRE_RISK_ENGINE_URL (/queued-assessment)
    const payload: AssessmentPayload = {
      data: {  // The queued-assessment endpoint expects data at the top level
        metadata: {
          installationId: installationId,
          owner: effectiveOwner,
          repo: repo,
          branch: effectiveBranch,
          gitCommitId: sha,
          gitCommitMsg: `MCP Triggered Assessment for ${sha}`,
          gitHubEventName: 'mcp_trigger',
          gitHubEventAction: 'triggered',
          triggerSource: 'mcp',
          project: repo  // The project name is the repo name
        },
        risk_inputs: []  // Empty array as per createAssessmentBody.js
      }
    };

    const payloadString = JSON.stringify(payload);

    log(`Sending assessment request to: ${SRE_RISK_ENGINE_URL}`);
    log(`Payload: ${payloadString}`);

    const response = await axios.post(SRE_RISK_ENGINE_URL, payloadString, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'MCP-Risk-Engine-Trigger/1.0',
        'X-MCP-Trigger': 'true',
        'X-Trigger-Source': 'mcp'
      },
      validateStatus: () => true,
    });

    log(`Risk Engine responded with status: ${response.status}`);
    log(`Risk Engine response data: ${JSON.stringify(response.data || {})}`);
    
    // Handle different status codes
    if (response.status >= 400) {
      // For 4xx and 5xx responses, return an error
      let errorMessage = "";
      
      // Use the specific error message from the response if available
      if (response.data && response.data.error && response.data.error.message) {
        errorMessage = response.data.error.message;
      } else {
        // Fallback to generic messages if no specific error message is available
        if (response.status === 404) {
          errorMessage = `Repository ${effectiveOwner}/${repo} not found`;
        } else if (response.status === 500) {
          errorMessage = `Risk Engine encountered an internal server error`;
        } else {
          errorMessage = `Risk Engine returned an error with status: ${response.status}`;
        }
      }
      
      // When isError is true, the MCP framework will automatically add "Error:" prefix
      // So we should not add it ourselves to avoid duplication
      return {
        content: [
          {
            type: 'text',
            text: `${errorMessage}\n\nLogs:\n${logs.join('\n')}`,
          },
        ],
        isError: true
      };
    } else if (response.status !== 200) {
      // For other non-200 responses (e.g., 204), return a message
      return {
        content: [
          {
            type: 'text',
            text: `Risk Engine responded with status: ${response.status}\n\nLogs:\n${logs.join('\n')}`,
          },
        ],
      };
    }
    
    // Handle 200 response
    log(`Assessment result received directly from Risk Engine for ${effectiveOwner}/${repo} (${sha}).`);
    // The response.data should contain the assessment result directly
    const fullAssessmentResult = response.data;
    
    try {
      // Check if the response has the expected structure
      if (!fullAssessmentResult || !fullAssessmentResult.metadata) {
        log(`Response does not have the expected structure. Returning error.`);
        const errorMessage = "Risk Engine returned an invalid response structure.";
        return {
          content: [
            {
              type: 'text',
              text: `${errorMessage}\n\nLogs:\n${logs.join('\n')}`,
            },
          ],
          isError: true
        };
      }
      
      // Extract and format the specific data points requested
      const conciseResult: ConciseResult = {
        metadata: {
          owner: fullAssessmentResult.metadata.owner,
          repo: fullAssessmentResult.metadata.repo,
          branch: fullAssessmentResult.metadata.branch,
          gitCommitId: fullAssessmentResult.metadata.gitCommitId,
          riskThreshold: fullAssessmentResult.metadata.riskThreshold
        },
        assessmentDbRecord: {
          id: fullAssessmentResult.assessmentDbRecord.id,
          createdAt: fullAssessmentResult.assessmentDbRecord.createdAt
        },
        categories: {},
        assessmentSummary: {
          totalRisk: fullAssessmentResult.assessmentSummary.totalRisk,
          callToAction: fullAssessmentResult.assessmentSummary.callToAction
        }
      };
      
      // Process each category and extract plugin risks
      if (fullAssessmentResult.assessmentSummary.categorizedInputs) {
        fullAssessmentResult.assessmentSummary.categorizedInputs.forEach((category: Category) => {
          const categoryName = category.name;
          conciseResult.categories[categoryName] = {
            categoryTotalRisk: category.categoryTotalRisk,
            plugins: {}
          };
          
          // Extract plugin risks for this category
          if (category.processedInputs) {
            category.processedInputs.forEach((input: CategoryInput) => {
              if (input.status === 'fulfilled' && input.value) {
                conciseResult.categories[categoryName].plugins[input.value.decorator] = input.value.risk;
              }
            });
          }
        });
      }
      
      log("Successfully created concise result format");
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(conciseResult, null, 2),
          },
        ],
      };
    } catch (error) {
      log(`Error formatting concise result: ${error instanceof Error ? error.message : String(error)}`);
      
      const errorMessage = `formatting assessment result: ${error instanceof Error ? error.message : String(error)}`;
      return {
        content: [
          {
            type: 'text',
            text: `${errorMessage}\n\nLogs:\n${logs.join('\n')}`,
          },
        ],
        isError: true,
      };
    }
  } catch (error) {
    log(`Error occurred during assessment trigger: ${error instanceof Error ? error.message : String(error)}`);
    if (axios.isAxiosError(error)) {
      const status = error.response?.status || 'N/A';
      const responseData = error.response?.data ? JSON.stringify(error.response.data) : 'No response data';
      const errorMessage = `triggering assessment: Risk Engine endpoint failed with status ${status}. Response: ${responseData}. Error: ${error.message}`;
      log(`Axios error details: ${errorMessage}`);
      
      return {
        content: [ { type: 'text', text: `${errorMessage}\n\nLogs:\n${logs.join('\n')}` } ],
        isError: true,
      };
    }
    const genericErrorMessage = error instanceof Error ? error.message : String(error);
    log(`Non-axios error: ${genericErrorMessage}`);
    
    return {
        content: [ { type: 'text', text: `An unexpected error occurred: ${genericErrorMessage}\n\nLogs:\n${logs.join('\n')}` } ],
        isError: true,
    };
  }
}
