# @telus/mcp-gke-logs

An MCP server for searching logs from Google Kubernetes Engine (GKE) workloads in a specified namespace.

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-gke-logs
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for <PERSON> Desktop):

   ```json
   {
     "mcpServers": {
       "gke-logs": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-gke-logs"],
         "env": {
           "GCP_PROJECT_ID": "your-gcp-project-id",
           "GCP_LOCATION": "your-gke-cluster-location",
           "GOOGLE_APPLICATION_CREDENTIALS": "/path/to/application_default_credentials.json",
           "K8S_NAMESPACE": "your-k8s-namespace",
           "K8S_POD_NAME": "optional-specific-pod-name",
           "K8S_APP_NAME": "optional-specific-app-name",
           "GCP_BUCKET_PROJECT_ID": "optional-specific-bucket-project-id",
           "GCP_BUCKET_NAME": "optional-specific-bucket-name"
         }
       }
     }
   }
   ```

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-gke-logs
   pnpm install
   ```

2. Build the server:

   ```bash
   pnpm build
   ```

3. Start the server:

   ```bash
   pnpm start
   ```

   For development with auto-rebuild:

   ```bash
   pnpm watch
   ```

## Configuration

Required Environment Variables:

- `GCP_PROJECT_ID`: Your Google Cloud Project ID
- `GCP_LOCATION`: The location of your GKE cluster
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to your Google Cloud Application Default Credentials
- `K8S_NAMESPACE`: The Kubernetes namespace to search logs in

Optional Environment Variables:

- `K8S_POD_NAME`: Specific pod name to filter logs from (can be overridden by the tool's podName parameter)
- `K8S_APP_NAME`: Specific app/workload name to filter logs from (ignores K8S_POD_NAME/podNAme and can be overridden by the tool's appName parameter)
- `GCP_BUCKET_PROJECT_ID`: Specific bucket project id to filter logs from
- `GCP_BUCKET_NAME`: Specific bucket name to filter logs from (can be overridden by the tool's bucket parameter)

NOTE: GCP_PROJECT_ID, GCP_LOCATION, and K8S_NAMESPACE are locked down to avoid having control over where logs should be fetched from. Exposing these as tool inputs would allow deciding where to fetch logs from.

## Available Tools

### search_logs

Search logs by keyword and time range for the configured Kubernetes namespace.

Required parameters:

- `keyword`: String - Keyword or text to search for in logs

Optional parameters:

- `podName`: String - Specific pod name to filter logs from (overrides K8S_POD_NAME environment variable if provided)
- `appName`: String - Specific app/workload name to filter logs from (ignores K8S_POD_NAME/podName and overrides K8S_APP_NAME if provided)
- `bucket`: String - Specific bucket name to filter logs from (overrides GCP_BUCKET_NAME environment variable if provided)
- `startTime`: String - Start time in ISO format (e.g. 2024-01-20T10:00:00Z) or relative time (e.g. -1h, -30m)
- `endTime`: String - End time in ISO format (e.g. 2024-01-20T11:00:00Z) or relative time (e.g. now)
- `maxResults`: Number - Maximum number of log entries to return (1-1000, default: 100)
- `filter`: String - Additional filter criteria in GCP log filter syntax

## Debugging

Since MCP servers communicate over stdio, debugging can be challenging. The MCP Inspector is available as a package script:

```bash
pnpm inspector
```

The Inspector will provide a URL to access debugging tools in your browser.
