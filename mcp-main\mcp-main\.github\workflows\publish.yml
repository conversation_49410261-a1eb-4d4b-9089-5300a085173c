name: Release

on:
  push:
    branches: [main]

# Add explicit permissions following the principle of least privilege
permissions:
  contents: write  # Needed for creating releases and pushing changes
  pull-requests: write  # Needed for creating release PRs
  packages: write  # Needed for publishing packages

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:

  release:
    name: Check for Changes 
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Cache turbo build setup
        uses: actions/cache@v4
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          registry-url: 'https://npm.pkg.github.com'
          scope: '@telus'
          cache: 'pnpm'

      - name: Install Dependencies
        run: pnpm install
        env:
          NODE_AUTH_TOKEN: ${{ secrets.MCP_REGISTRY_TOKEN }}

      - name: Create Release Pull Request or Publish
        id: changesets
        uses: changesets/action@v1
        with:
          publish: pnpm release
          commit: "chore: version packages"
          title: "chore: version packages"
        env:
          GITHUB_TOKEN: ${{ secrets.MCP_REGISTRY_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.MCP_REGISTRY_TOKEN }}
