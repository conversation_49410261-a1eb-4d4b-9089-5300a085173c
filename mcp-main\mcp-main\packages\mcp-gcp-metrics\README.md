# @telus/mcp-gcp-metrics

This package provides a Model Context Protocol (MCP) server for interacting with Google Cloud Platform (GCP) Monitoring API. It allows you to query metrics from any GCP project. It uses the [Cloud Monitoring API](https://cloud.google.com/monitoring/api/ref_v3/rest/v3/projects.timeSeries/query) and you can use that link to test your queries and metric types.

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-gcp-metrics
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for Claude Desktop):

   ```json
   {
     "mcpServers": {
       "gcp-metrics": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-gcp-metrics"],
         "env": {
           "PROJECT_ID": "my-project-id"
         }
       }
     }
   }
   ```

   The `PROJECT_ID` environment variable should be set to your GCP project ID.

3. Install the Google Cloud SDK if you haven't already: [https://cloud.google.com/sdk/docs/install](https://cloud.google.com/sdk/docs/install)

4. Run the following command to authenticate:
   ```bash
   gcloud auth login
   ```

   Follow the prompts to log in with your Google account that has access to the GCP project. The MCP server will automatically use the generated token file for authentication.

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-gcp-metrics
   npm install
   ```

2. Create a `.env` file in the root directory with the following variable:

   ```
   PROJECT_ID=my-project-id
   ```

3. Build the server:

   ```bash
   npm run build
   ```

4. Start the server:

   ```bash
   npm start
   ```

## Features

- Query metrics from GCP Monitoring
- Filter metrics by various criteria such as time range and custom filters
- Apply aggregations to metrics data
- Verbose logging for easier debugging and monitoring

## Prerequisites

- Node.js (version 14 or higher recommended)
- Google Cloud SDK
- A Google Cloud Platform account with access to Cloud Monitoring

## Available Tools

| Tool Name | Description | Input | Output |
|-----------|-------------|-------|--------|
| `query_metrics` | Query metrics from GCP Monitoring | `metricType` (string), `startTime` (string), `endTime` (string), `filter` (optional string), `pageSize` (optional number, default 100), `aggregation` (optional object with `alignmentPeriod`, `perSeriesAligner`, and `crossSeriesReducer`) | Array of time series data matching the query criteria |

## Example Usage

Here are examples of how to use the `query_metrics` tool:

### CPU Utilization Example

```json
{
  "metricType": "compute.googleapis.com/instance/cpu/utilization",
  "startTime": "-1h",
  "endTime": "now",
  "filter": "resource.labels.instance_name = 'my-instance'",
  "aggregation": {
    "alignmentPeriod": "60s",
    "perSeriesAligner": "ALIGN_MEAN",
    "crossSeriesReducer": "REDUCE_MEAN"
  }
}
```

This query will fetch the CPU utilization for a specific instance over the last hour, with 1-minute alignment and mean aggregation.

### Log Ingestion Example

```json
{
  "metricType": "logging.googleapis.com/billing/bytes_ingested",
  "startTime": "-24h",
  "endTime": "now",
  "pageSize": 50,
  "aggregation": {
    "alignmentPeriod": "3600s",
    "perSeriesAligner": "ALIGN_SUM"
  }
}
```

This query will fetch the total bytes ingested by Cloud Logging over the last 24 hours, aggregated hourly. The response includes data broken down by resource type (e.g., GCE instances, BigQuery, Cloud Scheduler).

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

MIT © TELUS
