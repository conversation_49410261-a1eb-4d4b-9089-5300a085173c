import axios, { AxiosProxyConfig } from 'axios';
import { DebugLogger } from './debug.js';
import https from 'https';

const logger = new DebugLogger('AuthManager');

/**
 * Response structure from the OAuth token endpoint.
 */
interface TokenResponse {
  /** The access token string */
  access_token: string;
  /** Token expiration time in seconds */
  expires_in: number;
  /** The type of token, typically "Bearer" */
  token_type: string;
}

/**
 * AuthManager class for handling OAuth 2.0 authentication with client credentials flow.
 * Manages token acquisition, caching, and automatic refresh.
 */
class AuthManager {
  private clientId: string;
  private clientSecret: string;
  private tokenUrl: string;
  private scope: string;
  private token: string | null = null;
  private tokenExpiry: Date | null = null;
  private readonly REQUEST_TIMEOUT = 120000; // Increased to 120 seconds
  private readonly TOKEN_REFRESH_BUFFER = 60; // Refresh token 60 seconds before expiry
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 2000; // 2 seconds

  /**
   * Creates an instance of AuthManager.
   * @param {string} clientId - The OAuth client ID.
   * @param {string} clientSecret - The OAuth client secret.
   * @param {string} tokenUrl - The OAuth token endpoint URL.
   * @param {string} scope - Space-separated list of OAuth scopes to request.
   */
  constructor(clientId: string, clientSecret: string, tokenUrl: string, scope: string) {
    logger.info('Initializing AuthManager', {
      clientId,
      tokenUrl,
      scope,
      // Don't log clientSecret for security
    });
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.tokenUrl = tokenUrl;
    this.scope = scope;
  }

  /**
   * Gets proxy configuration from environment variables
   */
  private getProxyConfig(): AxiosProxyConfig | false {
    const proxyUrl = process.env.https_proxy || process.env.HTTPS_PROXY;
    if (!proxyUrl) {
      return false;
    }

    try {
      const url = new URL(proxyUrl);
      logger.debug('Using proxy configuration', {
        host: url.hostname,
        port: url.port,
        protocol: url.protocol
      });
      
      return {
        host: url.hostname,
        port: parseInt(url.port, 10),
        protocol: url.protocol.replace(':', '')
      };
    } catch (error) {
      logger.warn('Failed to parse proxy URL', { proxyUrl, error });
      return false;
    }
  }

  /**
   * Delay helper for retry mechanism
   */
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Gets a valid OAuth token, either from cache or by requesting a new one.
   * Handles automatic token refresh when expired.
   * @returns {Promise<string>} A promise that resolves with the access token.
   * @throws {Error} If token acquisition fails.
   */
  async getToken(): Promise<string> {
    logger.debug('getToken called');
    
    // Return cached token if it's still valid
    if (this.token && this.tokenExpiry && this.tokenExpiry > new Date()) {
      logger.debug('Using cached token', { expires: this.tokenExpiry });
      return this.token;
    }

    logger.info('Token expired or not present, requesting new token');

    let lastError: Error | null = null;
    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        // Prepare token request parameters - include client_id and client_secret in the body
        const params = new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: this.clientId,
          client_secret: this.clientSecret,
          scope: this.scope,
        });
        
        logger.debug(`Making token request (attempt ${attempt}/${this.MAX_RETRIES})`, {
          url: this.tokenUrl,
          params: 'grant_type=client_credentials&client_id=[REDACTED]&client_secret=[REDACTED]&scope=' + this.scope
        });
        
        logger.startTimer('tokenRequest');

        // Configure axios instance with proxy and SSL settings
        const axiosConfig = {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          timeout: this.REQUEST_TIMEOUT,
          proxy: this.getProxyConfig(),
          httpsAgent: new https.Agent({
            rejectUnauthorized: false // Only for debugging - remove in production
          })
        };

        // Request new token
        const response = await axios.post<TokenResponse>(
          this.tokenUrl,
          params,
          axiosConfig
        );

        const requestDuration = logger.endTimer('tokenRequest');

        logger.info('Token response received', {
          status: response.status,
          tokenType: response.data.token_type,
          expiresIn: response.data.expires_in,
          requestDuration,
          attempt,
          // Don't log the actual token
        });

        // Cache the token and calculate expiry
        this.token = response.data.access_token;
        // Subtract buffer time to ensure we refresh before actual expiry
        const expiresInMs = (response.data.expires_in - this.TOKEN_REFRESH_BUFFER) * 1000;
        this.tokenExpiry = new Date(Date.now() + expiresInMs);
        
        logger.debug('Token cached', { expires: this.tokenExpiry });
        return this.token;
      } catch (error) {
        lastError = error as Error;
        logger.error(`Token request failed (attempt ${attempt}/${this.MAX_RETRIES})`, error);

        if (axios.isAxiosError(error)) {
          logger.error('Axios error details', {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            code: error.code,
            message: error.message
          });

          // Don't retry on certain error types
          if (error.response?.status === 401 || error.response?.status === 403) {
            throw new Error(`Authentication failed: ${error.response.statusText}`);
          }
        }

        // If this wasn't the last attempt, wait before retrying
        if (attempt < this.MAX_RETRIES) {
          const delayMs = this.RETRY_DELAY * attempt; // Exponential backoff
          logger.info(`Retrying in ${delayMs}ms...`);
          await this.delay(delayMs);
        }
      }
    }

    // If we get here, all retries failed
    throw new Error(`Failed to obtain token after ${this.MAX_RETRIES} attempts: ${lastError?.message}`);
  }

  /**
   * Gets the Authorization header value for API requests.
   * Automatically handles token acquisition/refresh.
   * @returns {Promise<{ Authorization: string }>} A promise that resolves with the Authorization header object.
   */
  async getAuthHeader(): Promise<{ Authorization: string }> {
    logger.debug('getAuthHeader called');
    try {
      const token = await this.getToken();
      logger.debug('Auth header generated');
      return { Authorization: `Bearer ${token}` };
    } catch (error) {
      logger.error('Failed to get auth header', error);
      throw error;
    }
  }
}

export default AuthManager;
