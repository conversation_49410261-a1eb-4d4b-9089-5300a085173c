"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const debug_1 = require("./utils/debug");
const rate_limiter_1 = require("./utils/rate-limiter");
const logger = new debug_1.DebugLogger('TMF620Api');
/**
 * TMF620Api class for interacting with the TMF620 Product Catalog Management API.
 */
class TMF620Api {
    /**
     * Creates an instance of TMF620Api.
     * @param {AuthManager} authManager - The authentication manager for handling OAuth tokens.
     * @param {ApiConfig} apiConfig - The configuration object containing API URLs for different environments.
     */
    constructor(authManager, apiConfig) {
        this.REQUEST_TIMEOUT = 120000; // 120 seconds timeout
        logger.info('Initializing TMF620Api', apiConfig);
        this.authManager = authManager;
        this.apiConfig = apiConfig;
    }
    /**
     * Initializes the TMF620Api instance.
     * This method should be called after constructing the object.
     */
    async initialize() {
        await this.initializeAxios();
        this.rateLimiter = new rate_limiter_1.RateLimiter(this.apiConfig.maxRequestsPerSecond, this.apiConfig.maxRequestsPerSecond);
        logger.info('TMF620Api initialized successfully');
    }
    async initializeAxios() {
        const axiosConfig = {
            timeout: this.REQUEST_TIMEOUT,
            validateStatus: null, // Don't throw on any status
            headers: {
                'User-Agent': 'TMF620-MCP-Server/1.0',
            },
            proxy: false // Explicitly disable proxy for all requests
        };
        // Check if proxy environment variables are set
        const httpProxy = process.env.HTTP_PROXY || process.env.http_proxy;
        const httpsProxy = process.env.HTTPS_PROXY || process.env.https_proxy;
        const noProxy = process.env.NO_PROXY || process.env.no_proxy;
        // Log proxy configuration
        logger.info('Proxy configuration:', {
            httpProxy: httpProxy || 'not set',
            httpsProxy: httpsProxy || 'not set',
            noProxy: noProxy || 'not set',
            proxyDisabled: true
        });
        // Create a custom HTTPS agent that doesn't verify SSL certificates
        const https = await Promise.resolve().then(() => __importStar(require('https')));
        axiosConfig.httpsAgent = new https.Agent({
            rejectUnauthorized: false // WARNING: Only for testing, not for production
        });
        logger.info('Created custom HTTPS agent with SSL verification disabled (for testing only)');
        this.axiosInstance = axios_1.default.create(axiosConfig);
        // Add request interceptor for logging
        this.axiosInstance.interceptors.request.use((config) => {
            logger.debug('Outgoing request:', {
                method: config.method,
                url: config.url,
                headers: {
                    ...config.headers,
                    Authorization: '[REDACTED]' // Don't log auth token
                }
            });
            return config;
        }, (error) => {
            logger.error('Request error:', error);
            return Promise.reject(error);
        });
        // Add response interceptor for logging
        this.axiosInstance.interceptors.response.use((response) => {
            logger.debug('Response received:', {
                status: response.status,
                statusText: response.statusText,
                headers: response.headers,
                data: response.data
            });
            return response;
        }, (error) => {
            logger.error('Response error:', {
                message: error.message,
                code: error.code,
                response: error.response ? {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    data: error.response.data
                } : undefined
            });
            return Promise.reject(error);
        });
    }
    /**
     * Gets the appropriate API URL for the specified environment.
     * @param {Environment} environment - The environment (dev, test, or prod).
     * @returns {string} The API URL for the specified environment.
     * @throws {Error} If an invalid environment is provided.
     */
    getApiUrl(environment) {
        logger.debug(`Getting API URL for environment: ${environment}`);
        // Get the base URL from environment variables each time to ensure we use the latest value
        let baseUrl = '';
        switch (environment) {
            case 'dev':
                baseUrl = process.env.DEV_API_URL || this.apiConfig.devApiUrl;
                break;
            case 'test':
                baseUrl = process.env.TEST_API_URL || this.apiConfig.testApiUrl;
                break;
            case 'prod':
                baseUrl = process.env.PROD_API_URL || this.apiConfig.prodApiUrl;
                break;
            default:
                throw new Error(`Invalid environment: ${environment}`);
        }
        // Remove trailing '/productOffering' if it exists to prevent duplication
        if (baseUrl.endsWith('/productOffering')) {
            baseUrl = baseUrl.substring(0, baseUrl.length - '/productOffering'.length);
        }
        logger.debug(`Using API URL: ${baseUrl} for environment: ${environment}`);
        return baseUrl;
    }
    /**
     * Makes an HTTP request to the API.
     * @param {string} method - The HTTP method (get or post).
     * @param {string} url - The API endpoint URL.
     * @param {Environment} environment - The environment (dev, test, or prod).
     * @param {Record<string, any>} [params] - Query parameters for the request.
     * @param {any} [data] - The request body for POST requests.
     * @returns {Promise<T>} A promise that resolves with the API response data.
     * @throws {ErrorResponse} If the API request fails.
     */
    async makeRequest(method, url, environment, params, data) {
        logger.debug(`Making ${method.toUpperCase()} request`, { url, environment, params, data });
        try {
            // Wait for a token from the rate limiter
            await this.rateLimiter.waitForToken();
            logger.startTimer('getAuthHeader');
            const authHeader = await this.authManager.getAuthHeader();
            logger.endTimer('getAuthHeader');
            const apiUrl = this.getApiUrl(environment);
            const fullUrl = `${apiUrl}${url}`;
            logger.debug('Request details', { fullUrl, headers: { ...authHeader, 'env': 'itn05' } });
            logger.startTimer('apiRequest');
            const response = await this.axiosInstance.request({
                method,
                url: fullUrl,
                headers: {
                    ...authHeader,
                    'env': 'itn05', // Use the required environment value
                },
                params,
                data,
                timeout: this.REQUEST_TIMEOUT,
                proxy: false // Explicitly disable proxy for this request
            });
            const requestDuration = logger.endTimer('apiRequest');
            logger.debug('Response received', { status: response.status, headers: response.headers, duration: requestDuration });
            // Handle non-2xx status codes
            if (response.status < 200 || response.status >= 300) {
                logger.error('API request failed', { status: response.status, data: response.data });
                const errorData = response.data;
                throw {
                    code: `HTTP_${response.status}`,
                    reason: response.statusText,
                    message: errorData?.message || 'API request failed',
                    status: response.status,
                    referenceError: errorData?.referenceError,
                };
            }
            logger.debug('Response data', response.data);
            return response.data;
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                if (error.code === 'ECONNABORTED') {
                    logger.error(`API request timed out after ${this.REQUEST_TIMEOUT}ms`);
                    throw {
                        code: 'TIMEOUT',
                        reason: 'Request Timeout',
                        message: `Request timed out after ${this.REQUEST_TIMEOUT}ms`,
                        status: 408,
                    };
                }
                logger.error('API request failed', {
                    status: error.response?.status,
                    statusText: error.response?.statusText,
                    data: error.response?.data,
                    headers: error.response?.headers,
                });
                const errorData = error.response?.data;
                throw {
                    code: errorData?.code || `HTTP_${error.response?.status || 500}`,
                    reason: error.response?.statusText || 'API Error',
                    message: errorData?.message || error.message,
                    status: error.response?.status || 500,
                    referenceError: errorData?.referenceError,
                };
            }
            logger.error('Unexpected error', error);
            throw {
                code: 'UNKNOWN_ERROR',
                reason: 'Unknown Error',
                message: error instanceof Error ? error.message : 'An unexpected error occurred',
                status: 500,
            };
        }
    }
    /**
     * Retrieves a specific product offering by ID.
     * @param {string} id - The ID of the product offering.
     * @param {Environment} environment - The environment (dev, test, or prod).
     * @param {string} [fields] - Comma-separated list of fields to include in the response.
     * @returns {Promise<ProductOffering>} A promise that resolves with the product offering data.
     */
    async getProductOffering(id, environment, fields) {
        logger.info(`Getting product offering`, { id, environment, fields });
        // Use the direct URL format with the ID
        return this.makeRequest('get', `/${id}`, environment, { fields });
    }
    /**
     * Retrieves a specific product offering by external ID.
     * @param {string} externalId - The external ID of the product offering.
     * @param {Environment} environment - The environment (dev, test, or prod).
     * @param {string} [fields] - Comma-separated list of fields to include in the response.
     * @returns {Promise<ProductOffering>} A promise that resolves with the product offering data.
     */
    async getProductOfferingByExternalId(externalId, environment, fields) {
        logger.info(`Getting product offering by external ID`, { externalId, environment, fields });
        return this.makeRequest('get', `/productOffering/externalId/${externalId}`, environment, { fields });
    }
    /**
     * Lists product offerings with optional filtering and pagination.
     * @param {Environment} environment - The environment (dev, test, or prod).
     * @param {Record<string, any>} [filters] - Optional filters to apply to the list.
     * @param {string} [fields] - Comma-separated list of fields to include in the response.
     * @param {number} [offset] - The number of items to skip (for pagination).
     * @param {number} [limit] - The maximum number of items to return (for pagination).
     * @returns {Promise<ListResponse<ProductOffering>>} A promise that resolves with the list of product offerings.
     */
    async listProductOfferings(environment, filters, fields, offset, limit) {
        logger.info('Listing product offerings', { environment, filters, fields, offset, limit });
        const params = { ...filters };
        if (fields)
            params.fields = fields;
        if (offset !== undefined)
            params.offset = offset;
        if (limit !== undefined)
            params.limit = limit;
        // Use the TEST_QUOTE_ID from environment variables if available
        const quoteId = process.env.TEST_QUOTE_ID;
        if (quoteId) {
            logger.info(`Using TEST_QUOTE_ID: ${quoteId}`);
            return this.makeRequest('get', `/productOffering/${quoteId}`, environment, params);
        }
        else {
            return this.makeRequest('get', '/productOffering', environment, params);
        }
    }
    /**
     * Retrieves multiple product offerings by their IDs.
     * @param {string[]} ids - An array of product offering IDs to retrieve.
     * @param {Environment} environment - The environment (dev, test, or prod).
     * @param {string} [fields] - Comma-separated list of fields to include in the response.
     * @returns {Promise<ListResponse<ProductOffering>>} A promise that resolves with the list of product offerings.
     */
    async bulkRetrieveProductOfferings(ids, environment, fields) {
        logger.info(`Bulk retrieving product offerings`, { ids, environment, fields });
        return this.makeRequest('post', '/productOffering/bulkRetrieve', environment, { fields }, { ids });
    }
    /**
     * Retrieves a specific catalog by ID.
     * @param {string} id - The ID of the catalog.
     * @param {Environment} environment - The environment (dev, test, or prod).
     * @param {string} [fields] - Comma-separated list of fields to include in the response.
     * @returns {Promise<Catalog>} A promise that resolves with the catalog data.
     */
    async getCatalog(id, environment, fields) {
        logger.info(`Getting catalog`, { id, environment, fields });
        return this.makeRequest('get', `/catalog/${id}`, environment, { fields });
    }
    /**
     * Lists catalogs with optional filtering and pagination.
     * @param {Environment} environment - The environment (dev, test, or prod).
     * @param {Record<string, any>} [filters] - Optional filters to apply to the list.
     * @param {string} [fields] - Comma-separated list of fields to include in the response.
     * @param {number} [offset] - The number of items to skip (for pagination).
     * @param {number} [limit] - The maximum number of items to return (for pagination).
     * @returns {Promise<ListResponse<Catalog>>} A promise that resolves with the list of catalogs.
     */
    async listCatalogs(environment, filters, fields, offset, limit) {
        logger.info('Listing catalogs', { environment, filters, fields, offset, limit });
        const params = { ...filters };
        if (fields)
            params.fields = fields;
        if (offset !== undefined)
            params.offset = offset;
        if (limit !== undefined)
            params.limit = limit;
        return this.makeRequest('get', '/catalog', environment, params);
    }
}
exports.default = TMF620Api;
