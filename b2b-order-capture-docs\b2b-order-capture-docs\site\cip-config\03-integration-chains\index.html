
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/cip-config/03-integration-chains/">
      
      
        <link rel="prev" href="../02-configuration-management/">
      
      
        <link rel="next" href="../04-service-catalog/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Integration Chains - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#integration-chains" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Integration Chains
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" checked>
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chain-execution-model" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Execution Model
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-types-and-categories" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Types and Categories
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Types and Categories">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-credit-assessment-chains" class="md-nav__link">
    <span class="md-ellipsis">
      1. Credit Assessment Chains
    </span>
  </a>
  
    <nav class="md-nav" aria-label="1. Credit Assessment Chains">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#credit-assessment-request-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Credit Assessment Request Chain
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-quote-management-chains" class="md-nav__link">
    <span class="md-ellipsis">
      2. Quote Management Chains
    </span>
  </a>
  
    <nav class="md-nav" aria-label="2. Quote Management Chains">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-state-management-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Quote State Management Chain
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-service-provisioning-chains" class="md-nav__link">
    <span class="md-ellipsis">
      3. Service Provisioning Chains
    </span>
  </a>
  
    <nav class="md-nav" aria-label="3. Service Provisioning Chains">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#service-activation-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Service Activation Chain
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#4-billing-integration-chains" class="md-nav__link">
    <span class="md-ellipsis">
      4. Billing Integration Chains
    </span>
  </a>
  
    <nav class="md-nav" aria-label="4. Billing Integration Chains">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#billing-synchronization-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Billing Synchronization Chain
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-configuration-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Configuration Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Configuration Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#standard-chain-definition" class="md-nav__link">
    <span class="md-ellipsis">
      Standard Chain Definition
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-types-and-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Step Types and Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Step Types and Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-groovy-script-steps" class="md-nav__link">
    <span class="md-ellipsis">
      1. Groovy Script Steps
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-service-call-steps" class="md-nav__link">
    <span class="md-ellipsis">
      2. Service Call Steps
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-conditional-steps" class="md-nav__link">
    <span class="md-ellipsis">
      3. Conditional Steps
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#4-loop-steps" class="md-nav__link">
    <span class="md-ellipsis">
      4. Loop Steps
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#5-reusable-component-steps" class="md-nav__link">
    <span class="md-ellipsis">
      5. Reusable Component Steps
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-development-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Development Patterns
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Development Patterns">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-error-handling-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      1. Error Handling Patterns
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-data-transformation-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      2. Data Transformation Patterns
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-parallel-processing-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      3. Parallel Processing Patterns
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-testing-and-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Testing and Validation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Testing and Validation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#test-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Test Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing-strategies" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategies
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-monitoring-and-observability" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Monitoring and Observability
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Monitoring and Observability">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#execution-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Execution Metrics
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#monitoring-dashboard" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring Dashboard
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-design-principles" class="md-nav__link">
    <span class="md-ellipsis">
      1. Design Principles
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-performance-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      2. Performance Optimization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-security-considerations" class="md-nav__link">
    <span class="md-ellipsis">
      3. Security Considerations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#4-maintainability" class="md-nav__link">
    <span class="md-ellipsis">
      4. Maintainability
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chain-execution-model" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Execution Model
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-types-and-categories" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Types and Categories
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Types and Categories">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-credit-assessment-chains" class="md-nav__link">
    <span class="md-ellipsis">
      1. Credit Assessment Chains
    </span>
  </a>
  
    <nav class="md-nav" aria-label="1. Credit Assessment Chains">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#credit-assessment-request-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Credit Assessment Request Chain
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-quote-management-chains" class="md-nav__link">
    <span class="md-ellipsis">
      2. Quote Management Chains
    </span>
  </a>
  
    <nav class="md-nav" aria-label="2. Quote Management Chains">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-state-management-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Quote State Management Chain
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-service-provisioning-chains" class="md-nav__link">
    <span class="md-ellipsis">
      3. Service Provisioning Chains
    </span>
  </a>
  
    <nav class="md-nav" aria-label="3. Service Provisioning Chains">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#service-activation-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Service Activation Chain
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#4-billing-integration-chains" class="md-nav__link">
    <span class="md-ellipsis">
      4. Billing Integration Chains
    </span>
  </a>
  
    <nav class="md-nav" aria-label="4. Billing Integration Chains">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#billing-synchronization-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Billing Synchronization Chain
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-configuration-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Configuration Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Configuration Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#standard-chain-definition" class="md-nav__link">
    <span class="md-ellipsis">
      Standard Chain Definition
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#step-types-and-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Step Types and Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Step Types and Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-groovy-script-steps" class="md-nav__link">
    <span class="md-ellipsis">
      1. Groovy Script Steps
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-service-call-steps" class="md-nav__link">
    <span class="md-ellipsis">
      2. Service Call Steps
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-conditional-steps" class="md-nav__link">
    <span class="md-ellipsis">
      3. Conditional Steps
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#4-loop-steps" class="md-nav__link">
    <span class="md-ellipsis">
      4. Loop Steps
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#5-reusable-component-steps" class="md-nav__link">
    <span class="md-ellipsis">
      5. Reusable Component Steps
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-development-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Development Patterns
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Development Patterns">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-error-handling-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      1. Error Handling Patterns
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-data-transformation-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      2. Data Transformation Patterns
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-parallel-processing-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      3. Parallel Processing Patterns
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-testing-and-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Testing and Validation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Testing and Validation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#test-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Test Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing-strategies" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategies
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-monitoring-and-observability" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Monitoring and Observability
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Monitoring and Observability">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#execution-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Execution Metrics
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#monitoring-dashboard" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring Dashboard
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-design-principles" class="md-nav__link">
    <span class="md-ellipsis">
      1. Design Principles
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-performance-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      2. Performance Optimization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-security-considerations" class="md-nav__link">
    <span class="md-ellipsis">
      3. Security Considerations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#4-maintainability" class="md-nav__link">
    <span class="md-ellipsis">
      4. Maintainability
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="integration-chains">Integration Chains<a class="headerlink" href="#integration-chains" title="Permanent link">&para;</a></h1>
<h2 id="overview">Overview<a class="headerlink" href="#overview" title="Permanent link">&para;</a></h2>
<p>Integration chains are the core business logic orchestrators in the CIP Configuration Package. They define complex workflows that coordinate multiple systems, services, and data transformations to implement TELUS B2B business processes.</p>
<h2 id="chain-architecture">Chain Architecture<a class="headerlink" href="#chain-architecture" title="Permanent link">&para;</a></h2>
<h3 id="chain-execution-model">Chain Execution Model<a class="headerlink" href="#chain-execution-model" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Chain Lifecycle&quot;
        Trigger[Event Trigger]
        Router[Chain Router]
        Executor[Chain Executor]
        Monitor[Execution Monitor]
        Completion[Completion Handler]
    end

    subgraph &quot;Chain Components&quot;
        Conditions[Conditional Logic]
        ServiceCalls[Service Calls]
        Scripts[Groovy Scripts]
        Loops[Loop Constructs]
        Reuse[Reusable Components]
        ErrorHandling[Error Handling]
    end

    subgraph &quot;External Integrations&quot;
        SFDC[Salesforce CRM]
        Quote[Quote Service]
        Config[Config Service]
        WLS[Work Log Service]
        TMF[TMF APIs]
    end

    Trigger --&gt; Router
    Router --&gt; Executor
    Executor --&gt; Monitor
    Monitor --&gt; Completion

    Executor --&gt; Conditions
    Executor --&gt; ServiceCalls
    Executor --&gt; Scripts
    Executor --&gt; Loops
    Executor --&gt; Reuse
    Executor --&gt; ErrorHandling

    ServiceCalls --&gt; SFDC
    ServiceCalls --&gt; Quote
    ServiceCalls --&gt; Config
    ServiceCalls --&gt; WLS
    ServiceCalls --&gt; TMF
</code></pre></div>
<h2 id="chain-types-and-categories">Chain Types and Categories<a class="headerlink" href="#chain-types-and-categories" title="Permanent link">&para;</a></h2>
<h3 id="1-credit-assessment-chains">1. Credit Assessment Chains<a class="headerlink" href="#1-credit-assessment-chains" title="Permanent link">&para;</a></h3>
<p><strong>Purpose</strong>: Orchestrate credit evaluation workflows with Salesforce integration</p>
<h4 id="credit-assessment-request-chain">Credit Assessment Request Chain<a class="headerlink" href="#credit-assessment-request-chain" title="Permanent link">&para;</a></h4>
<p><augment_code_snippet path="nc-cloud-bss-cip-config-b2b/content/chains/901ec8bd-32c0-4e48-9996-0b2100c2b79d/chain-901ec8bd-32c0-4e48-9996-0b2100c2b79d.yaml" mode="EXCERPT">
<div class="highlight"><pre><span></span><code><span class="nt">chain</span><span class="p">:</span>
<span class="w">  </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;901ec8bd-32c0-4e48-9996-0b2100c2b79d&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">Or</span><span class="nv"> </span><span class="s">Amend</span><span class="nv"> </span><span class="s">Credit</span><span class="nv"> </span><span class="s">Assessment</span><span class="nv"> </span><span class="s">Request&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Orchestrates</span><span class="nv"> </span><span class="s">credit</span><span class="nv"> </span><span class="s">assessment</span><span class="nv"> </span><span class="s">workflow</span><span class="nv"> </span><span class="s">with</span><span class="nv"> </span><span class="s">SFDC</span><span class="nv"> </span><span class="s">integration&quot;</span>

<span class="w">  </span><span class="nt">triggers</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">event</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;quote.state.changed&quot;</span>
<span class="w">      </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;state</span><span class="nv"> </span><span class="s">==</span><span class="nv"> </span><span class="s">&#39;CREDIT_ASSESSMENT_REQUIRED&#39;&quot;</span>

<span class="w">  </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;validateRequest&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy&quot;</span>
<span class="w">      </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;scripts/validate-credit-request.groovy&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;callSFDC&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service&quot;</span>
<span class="w">      </span><span class="nt">service</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;sfdc-integration&quot;</span>
</code></pre></div>
</augment_code_snippet></p>
<p><strong>Credit Assessment Flow:</strong></p>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Quote as Quote Service
    participant Chain as Credit Chain
    participant SFDC as Salesforce
    participant WLS as Work Log Service
    participant Config as Config Service

    Quote-&gt;&gt;Chain: Quote state changed to CREDIT_ASSESSMENT_REQUIRED
    Chain-&gt;&gt;Chain: Validate credit request data
    Chain-&gt;&gt;Config: Retrieve credit assessment configuration
    Config-&gt;&gt;Chain: Return configuration parameters
    Chain-&gt;&gt;SFDC: Create credit assessment request
    SFDC-&gt;&gt;Chain: Return credit assessment ID
    Chain-&gt;&gt;WLS: Log credit assessment initiation
    Chain-&gt;&gt;Quote: Update quote with credit assessment ID

    Note over SFDC: Async credit processing

    SFDC-&gt;&gt;Chain: Credit assessment completed
    Chain-&gt;&gt;Quote: Update quote with credit result
    Chain-&gt;&gt;WLS: Log credit assessment completion
</code></pre></div>
<h3 id="2-quote-management-chains">2. Quote Management Chains<a class="headerlink" href="#2-quote-management-chains" title="Permanent link">&para;</a></h3>
<p><strong>Purpose</strong>: Handle TMF Quote API integration and state management</p>
<h4 id="quote-state-management-chain">Quote State Management Chain<a class="headerlink" href="#quote-state-management-chain" title="Permanent link">&para;</a></h4>
<p>Key quote management workflows include:</p>
<ul>
<li><strong>Quote Creation</strong>: Initialize new quotes with product configurations</li>
<li><strong>Quote Amendment</strong>: Handle quote modifications and recalculations</li>
<li><strong>Quote Approval</strong>: Manage approval workflows and state transitions</li>
<li><strong>Quote Conversion</strong>: Convert approved quotes to orders</li>
</ul>
<p><strong>Quote Management Flow:</strong></p>
<div class="highlight"><pre><span></span><code>graph TD
    subgraph &quot;Quote Lifecycle&quot;
        Create[Quote Creation]
        Validate[Validation]
        Price[Pricing Calculation]
        Approve[Approval Process]
        Convert[Order Conversion]
    end

    subgraph &quot;External Systems&quot;
        TMF[TMF Quote API]
        Pricing[Pricing Engine]
        Approval[Approval System]
        Order[Order Management]
    end

    Create --&gt; Validate
    Validate --&gt; Price
    Price --&gt; Approve
    Approve --&gt; Convert

    Create --&gt; TMF
    Price --&gt; Pricing
    Approve --&gt; Approval
    Convert --&gt; Order
</code></pre></div>
<h3 id="3-service-provisioning-chains">3. Service Provisioning Chains<a class="headerlink" href="#3-service-provisioning-chains" title="Permanent link">&para;</a></h3>
<p><strong>Purpose</strong>: Orchestrate multi-system service activation and provisioning</p>
<h4 id="service-activation-chain">Service Activation Chain<a class="headerlink" href="#service-activation-chain" title="Permanent link">&para;</a></h4>
<p>Service provisioning involves:</p>
<ul>
<li><strong>Resource Allocation</strong>: Reserve network and system resources</li>
<li><strong>Configuration Deployment</strong>: Deploy service-specific configurations</li>
<li><strong>System Integration</strong>: Coordinate with billing, inventory, and CRM systems</li>
<li><strong>Service Validation</strong>: Verify successful service activation</li>
</ul>
<p><strong>Service Provisioning Flow:</strong></p>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Order as Order System
    participant Chain as Provisioning Chain
    participant Inventory as Inventory System
    participant Network as Network Management
    participant Billing as Billing System
    participant CRM as CRM System

    Order-&gt;&gt;Chain: Service provisioning request
    Chain-&gt;&gt;Inventory: Check resource availability
    Inventory-&gt;&gt;Chain: Confirm resource allocation
    Chain-&gt;&gt;Network: Configure network services
    Network-&gt;&gt;Chain: Confirm network configuration
    Chain-&gt;&gt;Billing: Setup billing configuration
    Billing-&gt;&gt;Chain: Confirm billing setup
    Chain-&gt;&gt;CRM: Update customer service records
    CRM-&gt;&gt;Chain: Confirm CRM update
    Chain-&gt;&gt;Order: Service provisioning completed
</code></pre></div>
<h3 id="4-billing-integration-chains">4. Billing Integration Chains<a class="headerlink" href="#4-billing-integration-chains" title="Permanent link">&para;</a></h3>
<p><strong>Purpose</strong>: Coordinate with external billing systems for invoice and payment processing</p>
<h4 id="billing-synchronization-chain">Billing Synchronization Chain<a class="headerlink" href="#billing-synchronization-chain" title="Permanent link">&para;</a></h4>
<p>Billing integration includes:</p>
<ul>
<li><strong>Account Synchronization</strong>: Sync customer account data</li>
<li><strong>Invoice Generation</strong>: Trigger invoice creation</li>
<li><strong>Payment Processing</strong>: Handle payment transactions</li>
<li><strong>Billing Reconciliation</strong>: Reconcile billing discrepancies</li>
</ul>
<h2 id="chain-configuration-structure">Chain Configuration Structure<a class="headerlink" href="#chain-configuration-structure" title="Permanent link">&para;</a></h2>
<h3 id="standard-chain-definition">Standard Chain Definition<a class="headerlink" href="#standard-chain-definition" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nt">chain</span><span class="p">:</span>
<span class="w">  </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;unique-chain-identifier&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Human-readable</span><span class="nv"> </span><span class="s">chain</span><span class="nv"> </span><span class="s">name&quot;</span>
<span class="w">  </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.0.0&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Detailed</span><span class="nv"> </span><span class="s">chain</span><span class="nv"> </span><span class="s">description&quot;</span>

<span class="w">  </span><span class="nt">metadata</span><span class="p">:</span>
<span class="w">    </span><span class="nt">category</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;credit-assessment|quote-management|provisioning|billing&quot;</span>
<span class="w">    </span><span class="nt">priority</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;high|medium|low&quot;</span>
<span class="w">    </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">300000</span><span class="w">  </span><span class="c1"># 5 minutes</span>
<span class="w">    </span><span class="nt">retryPolicy</span><span class="p">:</span>
<span class="w">      </span><span class="nt">maxAttempts</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">      </span><span class="nt">backoffMultiplier</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
<span class="w">      </span><span class="nt">retryableExceptions</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;java.net.SocketTimeoutException&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;org.springframework.web.client.ResourceAccessException&quot;</span>

<span class="w">  </span><span class="nt">triggers</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">event</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;event.type&quot;</span>
<span class="w">      </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy</span><span class="nv"> </span><span class="s">expression&quot;</span>
<span class="w">      </span><span class="nt">filter</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;additional</span><span class="nv"> </span><span class="s">filtering</span><span class="nv"> </span><span class="s">logic&quot;</span>

<span class="w">  </span><span class="nt">variables</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;variableName&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string|number|boolean|object&quot;</span>
<span class="w">      </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">default</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;defaultValue&quot;</span>

<span class="w">  </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;stepName&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy|service|condition|loop|reuse&quot;</span>
<span class="w">      </span><span class="c1"># Step-specific configuration</span>
</code></pre></div>
<h3 id="step-types-and-configuration">Step Types and Configuration<a class="headerlink" href="#step-types-and-configuration" title="Permanent link">&para;</a></h3>
<h4 id="1-groovy-script-steps">1. Groovy Script Steps<a class="headerlink" href="#1-groovy-script-steps" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;processData&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy&quot;</span>
<span class="w">  </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;scripts/process-data.groovy&quot;</span>
<span class="w">  </span><span class="nt">inputs</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;inputVariable1&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;inputVariable2&quot;</span>
<span class="w">  </span><span class="nt">outputs</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;outputVariable1&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;outputVariable2&quot;</span>
<span class="w">  </span><span class="nt">errorHandling</span><span class="p">:</span>
<span class="w">    </span><span class="nt">onError</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;continue|stop|retry&quot;</span>
<span class="w">    </span><span class="nt">maxRetries</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
</code></pre></div>
<h4 id="2-service-call-steps">2. Service Call Steps<a class="headerlink" href="#2-service-call-steps" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;callExternalService&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service&quot;</span>
<span class="w">  </span><span class="nt">service</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-name&quot;</span>
<span class="w">  </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;endpoint-name&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;GET|POST|PUT|DELETE&quot;</span>
<span class="w">  </span><span class="nt">headers</span><span class="p">:</span>
<span class="w">    </span><span class="nt">Content-Type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;application/json&quot;</span>
<span class="w">    </span><span class="nt">Authorization</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Bearer</span><span class="nv"> </span><span class="s">${token}&quot;</span>
<span class="w">  </span><span class="nt">body</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${requestBody}&quot;</span>
<span class="w">  </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30000</span>
<span class="w">  </span><span class="nt">retries</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
</code></pre></div>
<h4 id="3-conditional-steps">3. Conditional Steps<a class="headerlink" href="#3-conditional-steps" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;conditionalLogic&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;condition&quot;</span>
<span class="w">  </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${variable}</span><span class="nv"> </span><span class="s">==</span><span class="nv"> </span><span class="s">&#39;expectedValue&#39;&quot;</span>
<span class="w">  </span><span class="nt">then</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;trueStep&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy&quot;</span>
<span class="w">      </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;scripts/true-path.groovy&quot;</span>
<span class="w">  </span><span class="nt">else</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;falseStep&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy&quot;</span>
<span class="w">      </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;scripts/false-path.groovy&quot;</span>
</code></pre></div>
<h4 id="4-loop-steps">4. Loop Steps<a class="headerlink" href="#4-loop-steps" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;processItems&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;loop&quot;</span>
<span class="w">  </span><span class="nt">collection</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${itemList}&quot;</span>
<span class="w">  </span><span class="nt">variable</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;currentItem&quot;</span>
<span class="w">  </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;processItem&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy&quot;</span>
<span class="w">      </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;scripts/process-item.groovy&quot;</span>
<span class="w">  </span><span class="nt">parallel</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>
<span class="w">  </span><span class="nt">maxConcurrency</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
</code></pre></div>
<h4 id="5-reusable-component-steps">5. Reusable Component Steps<a class="headerlink" href="#5-reusable-component-steps" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;reuseComponent&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;reuse&quot;</span>
<span class="w">  </span><span class="nt">component</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;common-validation&quot;</span>
<span class="w">  </span><span class="nt">inputs</span><span class="p">:</span>
<span class="w">    </span><span class="nt">data</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${inputData}&quot;</span>
<span class="w">    </span><span class="nt">rules</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${validationRules}&quot;</span>
<span class="w">  </span><span class="nt">outputs</span><span class="p">:</span>
<span class="w">    </span><span class="nt">result</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;validationResult&quot;</span>
<span class="w">    </span><span class="nt">errors</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;validationErrors&quot;</span>
</code></pre></div>
<h2 id="chain-development-patterns">Chain Development Patterns<a class="headerlink" href="#chain-development-patterns" title="Permanent link">&para;</a></h2>
<h3 id="1-error-handling-patterns">1. Error Handling Patterns<a class="headerlink" href="#1-error-handling-patterns" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TD
    subgraph &quot;Error Handling Strategy&quot;
        Try[Try Block]
        Catch[Catch Block]
        Retry[Retry Logic]
        Fallback[Fallback Action]
        Log[Error Logging]
    end

    subgraph &quot;Error Types&quot;
        Transient[Transient Errors]
        Permanent[Permanent Errors]
        Business[Business Logic Errors]
        System[System Errors]
    end

    Try --&gt; Catch
    Catch --&gt; Retry
    Retry --&gt; Fallback
    Fallback --&gt; Log

    Transient --&gt; Retry
    Permanent --&gt; Fallback
    Business --&gt; Log
    System --&gt; Retry
</code></pre></div>
<h3 id="2-data-transformation-patterns">2. Data Transformation Patterns<a class="headerlink" href="#2-data-transformation-patterns" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Data transformation example</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;transformData&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy&quot;</span>
<span class="w">  </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">    </span><span class="no">// Transform input data to required format</span>
<span class="w">    </span><span class="no">def transformedData = [:]</span>
<span class="w">    </span><span class="no">transformedData.customerId = input.customer.id</span>
<span class="w">    </span><span class="no">transformedData.products = input.items.collect { item -&gt;</span>
<span class="w">        </span><span class="no">[</span>
<span class="w">            </span><span class="no">id: item.productId,</span>
<span class="w">            </span><span class="no">quantity: item.qty,</span>
<span class="w">            </span><span class="no">price: item.unitPrice</span>
<span class="w">        </span><span class="no">]</span>
<span class="w">    </span><span class="no">}</span>
<span class="w">    </span><span class="no">return transformedData</span>
</code></pre></div>
<h3 id="3-parallel-processing-patterns">3. Parallel Processing Patterns<a class="headerlink" href="#3-parallel-processing-patterns" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Parallel processing example</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;parallelProcessing&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;parallel&quot;</span>
<span class="w">  </span><span class="nt">branches</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;branch1&quot;</span>
<span class="w">      </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;processA&quot;</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service&quot;</span>
<span class="w">          </span><span class="nt">service</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-a&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;branch2&quot;</span>
<span class="w">      </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;processB&quot;</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service&quot;</span>
<span class="w">          </span><span class="nt">service</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-b&quot;</span>
<span class="w">  </span><span class="nt">joinType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;all|any|first&quot;</span>
<span class="w">  </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">60000</span>
</code></pre></div>
<h2 id="chain-testing-and-validation">Chain Testing and Validation<a class="headerlink" href="#chain-testing-and-validation" title="Permanent link">&para;</a></h2>
<h3 id="test-structure">Test Structure<a class="headerlink" href="#test-structure" title="Permanent link">&para;</a></h3>
<p><augment_code_snippet path="nc-cloud-bss-cip-config-b2b/src/test/groovy/Create_Or_Amend_Credit_Assessment_Request/Compose_Credit_Related_Product_Request_Tests.groovy" mode="EXCERPT">
<div class="highlight"><pre><span></span><code><span class="kd">class</span><span class="w"> </span><span class="nc">Compose_Credit_Related_Product_Request_Tests</span><span class="w"> </span><span class="kd">extends</span><span class="w"> </span><span class="n">Specification</span><span class="w"> </span><span class="o">{</span>

<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="nf">&quot;should compose credit related product request successfully&quot;</span><span class="o">()</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="nl">given:</span><span class="w"> </span><span class="s2">&quot;valid input data&quot;</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">inputData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">[</span>
<span class="w">            </span><span class="nl">customerId:</span><span class="w"> </span><span class="s2">&quot;12345&quot;</span><span class="o">,</span>
<span class="w">            </span><span class="nl">products:</span><span class="w"> </span><span class="o">[</span>
<span class="w">                </span><span class="o">[</span><span class="nl">id:</span><span class="w"> </span><span class="s2">&quot;product1&quot;</span><span class="o">,</span><span class="w"> </span><span class="nl">quantity:</span><span class="w"> </span><span class="mi">1</span><span class="o">]</span>
<span class="w">            </span><span class="o">]</span>
<span class="w">        </span><span class="o">]</span>

<span class="w">        </span><span class="nl">when:</span><span class="w"> </span><span class="s2">&quot;chain is executed&quot;</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">chainExecutor</span><span class="o">.</span><span class="na">execute</span><span class="o">(</span><span class="s2">&quot;credit-assessment-chain&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">inputData</span><span class="o">)</span>

<span class="w">        </span><span class="nl">then:</span><span class="w"> </span><span class="s2">&quot;request is composed correctly&quot;</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">success</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">true</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">data</span><span class="o">.</span><span class="na">creditRequest</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span>
<span class="w">    </span><span class="o">}</span>
<span class="o">}</span>
</code></pre></div>
</augment_code_snippet></p>
<h3 id="testing-strategies">Testing Strategies<a class="headerlink" href="#testing-strategies" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Testing Pyramid&quot;
        Unit[Unit Tests]
        Integration[Integration Tests]
        E2E[End-to-End Tests]
        Performance[Performance Tests]
    end

    subgraph &quot;Test Types&quot;
        Mock[Mock Services]
        Stub[Service Stubs]
        Contract[Contract Tests]
        Load[Load Tests]
    end

    Unit --&gt; Integration
    Integration --&gt; E2E
    E2E --&gt; Performance

    Unit --&gt; Mock
    Integration --&gt; Stub
    E2E --&gt; Contract
    Performance --&gt; Load
</code></pre></div>
<h2 id="chain-monitoring-and-observability">Chain Monitoring and Observability<a class="headerlink" href="#chain-monitoring-and-observability" title="Permanent link">&para;</a></h2>
<h3 id="execution-metrics">Execution Metrics<a class="headerlink" href="#execution-metrics" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;Chain Metrics&quot;
        Duration[Execution Duration]
        Success[Success Rate]
        Errors[Error Rate]
        Throughput[Throughput]
    end

    subgraph &quot;Step Metrics&quot;
        StepDuration[Step Duration]
        StepErrors[Step Errors]
        StepRetries[Step Retries]
        StepSuccess[Step Success]
    end

    subgraph &quot;Business Metrics&quot;
        ProcessingTime[Processing Time]
        BusinessErrors[Business Errors]
        SLA[SLA Compliance]
        Volume[Transaction Volume]
    end

    Duration --&gt; StepDuration
    Success --&gt; StepSuccess
    Errors --&gt; StepErrors
    Throughput --&gt; Volume
</code></pre></div>
<h3 id="monitoring-dashboard">Monitoring Dashboard<a class="headerlink" href="#monitoring-dashboard" title="Permanent link">&para;</a></h3>
<p>Key monitoring metrics include:</p>
<ul>
<li><strong>Chain Execution Metrics</strong>: Success rate, duration, error rate</li>
<li><strong>Step Performance</strong>: Individual step execution times and success rates</li>
<li><strong>Business KPIs</strong>: Credit assessment completion time, quote processing volume</li>
<li><strong>System Health</strong>: Resource utilization, queue depths, response times</li>
</ul>
<h2 id="chain-best-practices">Chain Best Practices<a class="headerlink" href="#chain-best-practices" title="Permanent link">&para;</a></h2>
<h3 id="1-design-principles">1. Design Principles<a class="headerlink" href="#1-design-principles" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Single Responsibility</strong>: Each chain should have a clear, focused purpose</li>
<li><strong>Idempotency</strong>: Chains should be safe to retry</li>
<li><strong>Statelessness</strong>: Avoid maintaining state between executions</li>
<li><strong>Error Resilience</strong>: Implement comprehensive error handling</li>
</ul>
<h3 id="2-performance-optimization">2. Performance Optimization<a class="headerlink" href="#2-performance-optimization" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Async Processing</strong>: Use asynchronous calls where possible</li>
<li><strong>Parallel Execution</strong>: Parallelize independent operations</li>
<li><strong>Caching</strong>: Cache frequently accessed data</li>
<li><strong>Resource Management</strong>: Properly manage connections and resources</li>
</ul>
<h3 id="3-security-considerations">3. Security Considerations<a class="headerlink" href="#3-security-considerations" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Input Validation</strong>: Validate all input data</li>
<li><strong>Authorization</strong>: Verify permissions for each operation</li>
<li><strong>Audit Logging</strong>: Log all significant actions</li>
<li><strong>Secret Management</strong>: Use secure credential storage</li>
</ul>
<h3 id="4-maintainability">4. Maintainability<a class="headerlink" href="#4-maintainability" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Documentation</strong>: Document chain purpose and logic</li>
<li><strong>Version Control</strong>: Maintain chain version history</li>
<li><strong>Testing</strong>: Comprehensive test coverage</li>
<li><strong>Monitoring</strong>: Implement observability and alerting</li>
</ul>
<p>This integration chain system provides a powerful, flexible foundation for implementing complex B2B business processes while maintaining high reliability, performance, and maintainability standards.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>