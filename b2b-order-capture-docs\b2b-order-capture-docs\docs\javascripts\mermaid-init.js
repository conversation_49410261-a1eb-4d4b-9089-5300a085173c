// Simple Mermaid initialization for MkDocs
console.log('Mermaid init script loaded');

// Wait for both DOM and Mermaid to be ready
function initializeMermaid() {
    console.log('Attempting to initialize Mermaid...');
    
    if (typeof mermaid === 'undefined') {
        console.log('Mermaid not loaded yet, retrying...');
        setTimeout(initializeMermaid, 1000);
        return;
    }
    
    console.log('Mermaid found, initializing...');
    
    try {
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
        console.log('Mermaid initialized successfully');
    } catch (error) {
        console.error('Error initializing Mermaid:', error);
    }
}

// Start initialization when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeMermaid);
} else {
    initializeMermaid();
}

// Also try on window load as backup
window.addEventListener('load', initializeMermaid);
