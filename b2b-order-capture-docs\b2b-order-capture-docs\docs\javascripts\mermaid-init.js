// Comprehensive Mermaid initialization for MkDocs
console.log('=== MERMAID INITIALIZATION SCRIPT LOADED ===');

let mermaidInitialized = false;

function initializeMermaid() {
    console.log('Attempting to initialize Mermaid...');

    if (typeof mermaid === 'undefined') {
        console.log('Mermaid library not loaded yet, retrying in 1000ms...');
        setTimeout(initializeMermaid, 1000);
        return;
    }

    if (mermaidInitialized) {
        console.log('Mermaid already initialized, processing diagrams...');
        processMermaidDiagrams();
        return;
    }

    console.log('Mermaid library found! Initializing...');

    try {
        mermaid.initialize({
            startOnLoad: false, // We'll handle this manually
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            sequence: {
                useMaxWidth: true
            },
            gantt: {
                useMaxWidth: true
            }
        });

        mermaidInitialized = true;
        console.log('Mermaid initialized successfully');

        // Process existing diagrams
        processMermaidDiagrams();

    } catch (error) {
        console.error('Error initializing Mermaid:', error);
    }
}

function processMermaidDiagrams() {
    console.log('Processing Mermaid diagrams...');

    // Find all code blocks with mermaid class or language
    const codeBlocks = document.querySelectorAll('pre code.language-mermaid, pre code[class*="mermaid"], .mermaid');
    console.log(`Found ${codeBlocks.length} potential mermaid elements`);

    // Also look for code blocks that contain mermaid syntax
    const allCodeBlocks = document.querySelectorAll('pre code');
    console.log(`Checking ${allCodeBlocks.length} total code blocks for mermaid content`);

    let processedCount = 0;

    allCodeBlocks.forEach((codeBlock, index) => {
        const text = codeBlock.textContent.trim();

        // Check if this looks like a mermaid diagram
        if (isMermaidContent(text)) {
            console.log(`Processing mermaid diagram ${processedCount + 1}:`, text.substring(0, 50) + '...');

            try {
                // Create a new div for the mermaid diagram
                const mermaidDiv = document.createElement('div');
                mermaidDiv.className = 'mermaid';
                mermaidDiv.textContent = text;
                mermaidDiv.style.textAlign = 'center';
                mermaidDiv.style.margin = '20px 0';
                mermaidDiv.id = `mermaid-diagram-${processedCount}`;

                // Replace the pre/code block with the mermaid div
                const preElement = codeBlock.parentElement;
                if (preElement && preElement.tagName === 'PRE') {
                    preElement.parentNode.replaceChild(mermaidDiv, preElement);
                } else {
                    codeBlock.parentNode.replaceChild(mermaidDiv, codeBlock);
                }

                // Render the diagram
                mermaid.init(undefined, mermaidDiv);

                processedCount++;
                console.log(`Successfully rendered mermaid diagram ${processedCount}`);

            } catch (error) {
                console.error(`Error rendering mermaid diagram ${processedCount + 1}:`, error);

                // Show error message
                const errorDiv = document.createElement('div');
                errorDiv.innerHTML = `
                    <div style="color: red; border: 1px solid red; padding: 15px; margin: 10px 0; background: #ffe6e6; border-radius: 5px;">
                        <strong>⚠️ Mermaid Diagram Error</strong><br>
                        ${error.message}<br>
                        <details style="margin-top: 10px;">
                            <summary>Show diagram code</summary>
                            <pre style="background: #f5f5f5; padding: 10px; margin-top: 5px; border-radius: 3px; white-space: pre-wrap;">${text}</pre>
                        </details>
                    </div>
                `;

                const preElement = codeBlock.parentElement;
                if (preElement && preElement.tagName === 'PRE') {
                    preElement.parentNode.replaceChild(errorDiv, preElement);
                } else {
                    codeBlock.parentNode.replaceChild(errorDiv, codeBlock);
                }
            }
        }
    });

    console.log(`Processed ${processedCount} mermaid diagrams`);

    // Also handle any existing .mermaid divs
    const existingMermaidDivs = document.querySelectorAll('.mermaid:not([data-processed])');
    if (existingMermaidDivs.length > 0) {
        console.log(`Found ${existingMermaidDivs.length} existing .mermaid divs to process`);
        existingMermaidDivs.forEach(div => {
            try {
                mermaid.init(undefined, div);
                div.setAttribute('data-processed', 'true');
                console.log('Processed existing mermaid div');
            } catch (error) {
                console.error('Error processing existing mermaid div:', error);
            }
        });
    }
}

function isMermaidContent(text) {
    // Check if the text contains mermaid diagram syntax
    const mermaidKeywords = [
        'graph ', 'digraph ', 'flowchart ', 'sequenceDiagram', 'classDiagram',
        'stateDiagram', 'gantt', 'pie', 'gitgraph', 'journey', 'erDiagram',
        '-->', '--->', '-.->', '==>', '-.->',
        '->>', '-->>',
        'participant ', 'actor ', 'note ',
        'class ', 'state ', 'title ', 'dateFormat'
    ];

    return mermaidKeywords.some(keyword => text.includes(keyword));
}

// Start initialization when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeMermaid);
} else {
    initializeMermaid();
}

// Also try on window load as backup
window.addEventListener('load', initializeMermaid);

// Handle navigation changes (for SPA-like behavior in MkDocs Material)
document.addEventListener('DOMContentLoaded', function() {
    // Re-initialize on navigation changes
    const observer = new MutationObserver(function(mutations) {
        let shouldReinit = false;
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Check if new content was added that might contain mermaid diagrams
                for (let node of mutation.addedNodes) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const codeBlocks = node.querySelectorAll ? node.querySelectorAll('pre code') : [];
                        if (codeBlocks.length > 0) {
                            shouldReinit = true;
                            break;
                        }
                    }
                }
            }
        });
        if (shouldReinit) {
            console.log('New content detected, re-processing mermaid diagrams...');
            setTimeout(initializeMermaid, 500);
        }
    });

    if (document.body) {
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        console.log('MutationObserver set up for dynamic content');
    }
});

console.log('=== MERMAID SCRIPT SETUP COMPLETE ===');
