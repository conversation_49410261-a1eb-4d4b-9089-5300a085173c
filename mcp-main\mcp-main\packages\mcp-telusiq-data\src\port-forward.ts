import { spawn } from 'child_process';
import type { ChildProcess } from 'child_process';
import { Readable } from 'stream';
import { DatabaseConfig } from './config.js';

declare const process: {
  kill(pid: number): void;
};

type SpawnProcess = ChildProcess & {
  stdout?: Readable;
  stderr?: Readable;
};

export class PortForwardManager {
  private process: SpawnProcess | null = null;
  private config: DatabaseConfig;

  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  async isPortInUse(): Promise<boolean> {
    return new Promise((resolve) => {
      const findProcess = spawn('netstat', ['-ano']);
      let isInUse = false;

      findProcess.stdout?.on('data', (data: Buffer) => {
        const output = data.toString();
        if (output.includes(`:${this.config.targetPort}`)) {
          isInUse = true;
        }
      });

      findProcess.on('error', () => {
        // If command fails, assume port is not in use
        resolve(false);
      });

      findProcess.on('close', () => {
        resolve(isInUse);
      });
    });
  }

  async killExistingProcess(): Promise<void> {
    try {
      // First check if port is in use
      const portInUse = await this.isPortInUse();
      if (!portInUse) {
        console.error(`[Port Forward] Port ${this.config.targetPort} is not in use`);
        return;
      }

      // Use netstat on Windows to find process using the port
      const findProcess = spawn('netstat', ['-ano']);
      
      const output = await new Promise<string>((resolve) => {
        let data = '';
        findProcess.stdout?.on('data', (chunk: Buffer) => {
          data += chunk;
        });
        findProcess.on('close', () => resolve(data));
      });

      const lines = output.split('\n');
      for (const line of lines) {
        if (line.includes(`:${this.config.targetPort}`)) {
          const parts = line.trim().split(/\s+/);
          if (parts.length > 4) {
            const pid = parseInt(parts[4], 10);
            if (!isNaN(pid)) {
              // Use taskkill on Windows
              spawn('taskkill', ['/F', '/PID', pid.toString()]);
              console.error(`[Port Forward] Killed process ${pid} using port ${this.config.targetPort}`);
            }
          }
        }
      }
    } catch (error) {
      console.error('[Port Forward] Error killing existing process:', error);
      // Continue even if process killing fails - the port might not be in use
    }
  }

  async start(): Promise<void> {
    try {
      // Check if port is already in use
      const portInUse = await this.isPortInUse();
      if (portInUse) {
        // Kill any existing processes using the target port
        await this.killExistingProcess();
      }

      return new Promise((resolve, reject) => {
        const command = 'kubectl';
        const args = [
          'port-forward',
          `-n${this.config.namespace}`,
          `pod/${this.config.podName}`,
          `${this.config.targetPort}:${this.config.dbPort}`
        ];

        console.error('[Port Forward] Starting port forward with command:', command, args.join(' '));

        this.process = spawn(command, args);

        this.process.stdout?.on('data', (data: Buffer) => {
          console.error('[Port Forward] stdout:', data.toString());
        });

        this.process.stderr?.on('data', (data: Buffer) => {
          const output = data.toString();
          console.error('[Port Forward] stderr:', output);
          
          // Check for successful connection
          if (output.includes('Forwarding from')) {
            resolve();
          }
        });

        this.process.on('error', (error: Error) => {
          console.error('[Port Forward] Error:', error);
          reject(error);
        });

        this.process.on('exit', (code: number | null) => {
          if (code !== 0) {
            console.error(`[Port Forward] Process exited with code ${code}`);
            reject(new Error(`Port forward process exited with code ${code}`));
          }
        });
      });
    } catch (error) {
      console.error('[Port Forward] Error during start:', error);
      throw error;
    }
  }

  stop(): void {
    if (this.process) {
      this.process.kill();
      this.process = null;
      console.error('[Port Forward] Stopped port forwarding');
    }
  }
}
