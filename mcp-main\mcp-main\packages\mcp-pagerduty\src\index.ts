#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";

import {
  listIncidentsApi,
  listServicesApi,
  listUsersApi,
  findServiceApi,
  selectServiceApi,
  getIncidentApi,
  getAlertApi,
  listAlertsForIncidentApi
} from "./tools/api.js";


const server = new McpServer({
  name: "mcp-pagerduty",
  version: "0.1.4",
});

listIncidentsApi(server);
listServicesApi(server);
listUsersApi(server);
findServiceApi(server);
selectServiceApi(server);
getIncidentApi(server);
getAlertApi(server);
listAlertsForIncidentApi(server);

async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.log("PagerDuty MCP Server running on stdio");
}

main().catch((error) => {
  console.error("Fatal error in main():", error);
  process.exit(1);
});