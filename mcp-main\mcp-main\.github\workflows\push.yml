name: PR Checks

on:
  pull_request:
    types: [opened, synchronize, reopened]

# Add explicit permissions following the principle of least privilege
permissions:
  contents: write # Read the repo contents
  issues: write # Create or update Github Issue
  pull-requests: write # Comment on PR

jobs:
  validate-changes:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          registry-url: 'https://npm.pkg.github.com'
          scope: '@telus'
          cache: 'pnpm'

      # Detect Package Changes
      - name: Detect Package Changes
        id: package-changes
        run: |
          changed_files=$(git diff --name-only ${{ github.event.pull_request.base.sha }} ${{ github.sha }})
          # Get unique package names from changed files
          changed_packages=$(echo "$changed_files" | grep '^packages/' | cut -d'/' -f2 | sort -u || true)
          if [ -n "$changed_packages" ]; then
            echo "packages_changed=true" >> $GITHUB_OUTPUT
            echo "changed_packages<<EOF" >> $GITHUB_OUTPUT
            echo "$changed_packages" >> $GITHUB_OUTPUT
            echo "EOF" >> $GITHUB_OUTPUT
          else
            echo "packages_changed=false" >> $GITHUB_OUTPUT
            echo "changed_packages=" >> $GITHUB_OUTPUT
          fi

      # Comprehensive PR Check
      - name: Comprehensive PR Check
        id: pr-check
        run: |
          # Initialize package data as JSON
          package_data="{"
          first=true

          # Process each changed package
          if [[ "${{ steps.package-changes.outputs.packages_changed }}" == "true" ]]; then
            changed_packages="${{ steps.package-changes.outputs.changed_packages }}"
            all_packages=$(find packages -name 'package.json')

            for package in $changed_packages; do
              package_path="packages/$package"
              
              if [ -d "$package_path" ]; then
                # Get list of changed files for this package
                package_changed_files=$(git diff --name-only ${{ github.event.pull_request.base.sha }} ${{ github.sha }} -- "$package_path")
                
                if [ "$first" = true ]; then
                  first=false
                else
                  package_data="$package_data,"
                fi

                # Start package entry
                if [ -f "$package_path/package.json" ]; then
                  NAME=$(node -p "require('./$package_path/package.json').name")
                  PUBLISH_REGISTRY=$(node -p "require('./$package_path/package.json').publishConfig && require('./$package_path/package.json').publishConfig.registry")
                  
                  # Track issues for this package
                  ISSUES=""
                  
                  # Check package scope
                  if [[ $NAME != @telus/* ]]; then
                    ISSUES="$ISSUES\n- Package does not have the @telus scope"
                    SCOPE="false"
                  else
                    SCOPE="true"
                  fi

                  # Check NPM registry
                  if [[ $PUBLISH_REGISTRY != "https://npm.pkg.github.com" ]]; then
                    ISSUES="$ISSUES\n- Package does not have the correct registry in publishConfig"
                    REGISTRY="false"
                  else
                    REGISTRY="true"
                  fi

                  # Check package name starts with 'mcp-'
                  if [[ $NAME != @telus/mcp-* ]]; then
                    ISSUES="$ISSUES\n- Package name does not start with 'mcp-'"
                    PREFIX="false"
                  else
                    PREFIX="true"
                  fi

                  # Check package name uniqueness
                  PACKAGE_NAME=$(echo $NAME | sed 's/@telus\///')
                  if [ $(grep -r "\"name\": \"@telus/$PACKAGE_NAME\"" $all_packages | wc -l) -gt 1 ]; then
                    ISSUES="$ISSUES\n- Package name is used multiple times in the repository"
                    UNIQUE="false"
                  else
                    UNIQUE="true"
                  fi

                  # Check for author field
                  AUTHOR=$(node -p "require('./$package_path/package.json').author || ''")
                  if [[ -z "$AUTHOR" ]]; then
                    ISSUES="$ISSUES\n- Package is missing the author field"
                    AUTHOR_EXISTS="false"
                  else
                    AUTHOR_EXISTS="true"
                  fi

                  # Check for keywords field
                  KEYWORDS=$(node -p "Array.isArray(require('./$package_path/package.json').keywords) && require('./$package_path/package.json').keywords.length > 0 ? 'true' : 'false'")
                  if [[ $KEYWORDS != "true" ]]; then
                    ISSUES="$ISSUES\n- Package is missing the keywords field or it's empty"
                    KEYWORDS_EXISTS="false"
                  else
                    KEYWORDS_EXISTS="true"
                  fi

                  # Check for repository field
                  REPOSITORY=$(node -p "require('./$package_path/package.json').repository || ''")
                  if [[ -z "$REPOSITORY" ]]; then
                    ISSUES="$ISSUES\n- Package is missing the repository field"
                    REPOSITORY_EXISTS="false"
                  else
                    REPOSITORY_EXISTS="true"
                  fi

                  # Add package data as JSON
                  package_data="$package_data\"$NAME\":{"
                  package_data="$package_data\"scope\":$SCOPE,"
                  package_data="$package_data\"registry\":$REGISTRY,"
                  package_data="$package_data\"prefix\":$PREFIX,"
                  package_data="$package_data\"unique\":$UNIQUE,"
                  package_data="$package_data\"author\":$AUTHOR_EXISTS,"
                  package_data="$package_data\"keywords\":$KEYWORDS_EXISTS,"
                  package_data="$package_data\"repository\":$REPOSITORY_EXISTS,"
                  package_data="$package_data\"package_json_exists\":true,"
                  if [ -n "$ISSUES" ]; then
                    package_data="$package_data\"issues\":\"$(echo -e "$ISSUES" | sed 's/"/\\"/g')\","
                  else
                    package_data="$package_data\"issues\":null,"
                  fi
                else
                  # Handle packages without package.json
                  package_data="$package_data\"$package\":{"
                  package_data="$package_data\"package_json_exists\":false,"
                fi

                # Add changed files information
                package_data="$package_data\"changed_files\":[\"$(echo "$package_changed_files" | tr '\n' ',' | sed 's/,/\",\"/g' | sed 's/,\"\"$//')\"]}"
              fi
            done
          fi

          # Close the JSON object
          package_data="$package_data}"

          # Output the package data
          echo "package_data<<EOF" >> $GITHUB_OUTPUT
          echo "$package_data" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

          # Check lockfile
          if ! pnpm install --frozen-lockfile --lockfile-only; then
            echo "lockfile_valid=false" >> $GITHUB_OUTPUT
          else
            echo "lockfile_valid=true" >> $GITHUB_OUTPUT
          fi

      # Manage PR status comment
      - name: Manage PR Status Comment
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.MCP_REGISTRY_TOKEN }}
          script: |
            const { repo, owner, number } = context.issue;
            
            // Find existing PR status comment
            const comments = await github.rest.issues.listComments({
              owner,
              repo,
              issue_number: number
            });
            
            const statusComment = comments.data.find(comment => 
              comment.body.includes('MCP Monorepo Bot: PR Status')
            );
            
            // Prepare comment body
            let body = `## 🤖 MCP Monorepo Bot: PR Status\n\n`;
            let packageData;
            try {
              packageData = JSON.parse('${{ steps.pr-check.outputs.package_data }}');
            } catch (error) {
              core.setFailed(`Failed to parse package data: ${error.message}`);
              return;
            }
            
            const lockfileValid = '${{ steps.pr-check.outputs.lockfile_valid }}' === 'true';

            const checkStatus = (value) => value === true || value === 'true' ? '✅' : '❌';
            const checkAction = (value) => value === true || value === 'true' ? 'None' : 'Update package.json';

            if (Object.keys(packageData).length > 0) {
              for (const [packageName, data] of Object.entries(packageData)) {
                body += `\n### 📦 Package: ${packageName}\n\n`;
                
                if (!data.package_json_exists) {
                  body += `⚠️ Warning: No package.json found in this package.\n\n`;
                } else {
                  body += `| Check | Status | Action Required |\n`;
                  body += `| ----- | ------ | --------------- |\n`;
                  body += `| NPM Scope | ${checkStatus(data.scope)} | ${checkAction(data.scope)} |\n`;
                  body += `| NPM Registry | ${checkStatus(data.registry)} | ${checkAction(data.registry)} |\n`;
                  body += `| Package Prefix | ${checkStatus(data.prefix)} | ${checkAction(data.prefix)} |\n`;
                  body += `| Package Uniqueness | ${checkStatus(data.unique)} | ${checkAction(data.unique)} |\n`;
                  body += `| Author Field | ${checkStatus(data.author)} | ${checkAction(data.author)} |\n`;
                  body += `| Keywords Field | ${checkStatus(data.keywords)} | ${checkAction(data.keywords)} |\n`;
                  body += `| Repository Field | ${checkStatus(data.repository)} | ${checkAction(data.repository)} |\n`;

                  if (data.issues) {
                    body += `\n<details><summary>📦 Package Issues</summary>\n\n\`\`\`\n${data.issues}\n\`\`\`\n\n`;
                    body += `Ensure all TELUS packages:\n`;
                    body += `1. Have a name that starts with '@telus/mcp-'\n`;
                    body += `2. Include publishConfig with registry set to 'https://npm.pkg.github.com'\n`;
                    body += `3. Have a unique name within the repository\n`;
                    body += `4. Include author information\n`;
                    body += `5. Include relevant keywords\n`;
                    body += `6. Include repository information\n`;
                    body += `</details>\n`;
                  }
                }

                // Display changed files
                if (data.changed_files && data.changed_files.length > 0) {
                  body += `\n<details><summary>📄 Changed Files</summary>\n\n`;
                  data.changed_files.forEach(file => {
                    body += `- ${file}\n`;
                  });
                  body += `</details>\n`;
                }
              }
            } else {
              body += `### 📦 Package Changes\n\n`;
              body += `No package changes detected in this PR.\n`;
            }
            
            // Lockfile status
            body += `\n### 🔒 Lockfile Status\n\n`;
            body += `| Check | Status | Action Required |\n`;
            body += `| ----- | ------ | --------------- |\n`;
            body += `| Lockfile Updated | ${checkStatus(lockfileValid.toString())} | ${lockfileValid ? 'None' : 'Run \`pnpm install\` and commit changes'} |\n`;

            if (statusComment) {
              // Update existing comment
              await github.rest.issues.updateComment({
                owner,
                repo,
                comment_id: statusComment.id,
                body: body.trim()
              });
            } else {
              // Create new comment
              await github.rest.issues.createComment({
                owner,
                repo,
                issue_number: number,
                body: body.trim()
              });
            }
            
            // Fail the workflow if there are any issues
            const hasPackageIssues = Object.values(packageData).some(data => 
              data.scope === 'false' || data.registry === 'false' || data.prefix === 'false' || data.unique === 'false' ||
              data.author === 'false' || data.keywords === 'false' || data.repository === 'false'
            );
            
            if (hasPackageIssues || !lockfileValid) {
              core.setFailed('PR checks failed. Please review the PR status comment for details.');
            }
