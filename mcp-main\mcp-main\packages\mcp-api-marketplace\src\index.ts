import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { KongClient } from '@telus/sdk-kong-auth';

import { searchApis, getApiSpecification } from "./tools/api.js";

// Create server instance
const server = new McpServer({
  name: "api-marketplace",
  version: "0.1.0",
});

const {
  API_MARKETPLACE_CLIENT_ID: clientId = '',
  API_MARKETPLACE_CLIENT_SECRET: clientSecret = '',
} = process.env;

const scopes = ['4402'];
const client = new KongClient({ clientId, clientSecret, scopes, accessTokenExpiryBuffer: 10, env: 'production' });
client.getAccessToken();

searchApis(server, client);
getApiSpecification(server, client);

async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error("API Marketplace MCP Server running on stdio");
}

main().catch((error) => {
  console.error("Fatal error in main():", error);
  process.exit(1);
});
