{"name": "@telus/mcp-change-management", "version": "1.2.0", "main": "index.js", "type": "module", "bin": {"mcp-change-management": "build/index.js"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "start": "node build/index.js"}, "keywords": ["mcp", "change-management", "github", "itsm", "echange"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-change-management"}, "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "@octokit/rest": "^21.1.0", "axios": "^1.8.4", "octokit": "^4.1.0", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "files": ["build"], "description": "MCP server for change management including GitHub, ITSM, and eChange sources", "publishConfig": {"registry": "https://npm.pkg.github.com"}}