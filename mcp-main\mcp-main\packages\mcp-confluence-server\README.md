# @telus/mcp-confluence-server

Welcome to the TELUS Model Context Protocol (MCP) Confluence Server! This MCP server provides tools for interacting with both Confluence Cloud instances (like https://telus-cio.atlassian.net/) and on-premise Confluence Server instances.

## Installation and Usage

> **Important Note:** As a user, you only need to configure two files:
> 1. Your Cline settings file (`cline_mcp_settings.json`)
> 2. Your npm configuration file (`.npmrc`)
>
> You do **not** need to manually start, build, or manage the server. <PERSON>line will automatically handle everything else.

### Setting Up with Cline

To use this server with Cline, you need to:

1. **Create a Confluence API Token** (if you don't already have one)
   - Go to https://id.atlassian.com/manage-profile/security/api-tokens
   - Click "Create API token"
   - Give your token a name (e.g., "Cline Confluence Access")
   - Copy the generated token and save it in a secure location

2. **Configure Cline MCP Settings**
   - In VS Code, open the Command Palette (Ctrl+Shift+P or Cmd+Shift+P on Mac)
   - Type "Cline: Open MCP Settings" and select it
   - Add the following configuration to the `mcpServers` object:

```json
"confluence": {
  "autoApprove": [
    "confluence_search",
    "confluence_get_page",
    "confluence_get_space",
    "confluence_get_space_content",
    "confluence_get_comments"
  ],
  "disabled": false,
  "timeout": 120,
  "command": "npx",
  "args": [
    "-y",
    "@telus/mcp-confluence-server"
  ],
  "env": {
    "CONFLUENCE_USERNAME": "<EMAIL>",
    "CONFLUENCE_TOKEN": "your-api-token",
    "CONFLUENCE_BASE_URL": "https://telushealth.atlassian.net/wiki",
    
    // Optional proxy configuration
    "PROXY_HOST": "[proxy-host]",
    "PROXY_PORT": "[proxy-port]"
  },
  "transportType": "stdio"
}
```

3. **Configure npm for GitHub Packages**
   - Create or edit your `.npmrc` file in your home directory:
     - Windows: `C:\Users\<USER>\.npmrc`
     - Mac/Linux: `~/.npmrc`
   - Add the following lines:
   ```
   @telus:registry=https://npm.pkg.github.com
   //npm.pkg.github.com/:_authToken=YOUR_GITHUB_TOKEN
   ```
   - Replace `YOUR_GITHUB_TOKEN` with a GitHub personal access token that has the `read:packages` scope
   - To create a token, go to [GitHub Settings > Developer settings > Personal access tokens](https://github.com/settings/tokens)

> **How it works:** When you ask Cline to perform a Confluence operation, Cline will automatically:
> 1. Start the server using the npx command
> 2. Download the package from GitHub Packages
> 3. Run the server with your credentials
> 4. Perform the requested operation
> 5. Stop the server when complete
>
> You don't need to manually start, stop, or manage the server process.

### Configuration File Locations

#### For Windows (VS Code)
Typically located at: `%APPDATA%\Code\User\globalStorage\saoudrizwan.claude-dev\settings\cline_mcp_settings.json`

#### For macOS (VS Code)
Typically located at: `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json`

#### For macOS (Claude Desktop)
Typically located at: `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json`

### Authentication Notes

- For cloud-hosted Confluence instances (like https://telus-cio.atlassian.net/), use your email as the username and an API token as the password
- To create an API token, go to https://id.atlassian.com/manage-profile/security/api-tokens
- For on-premise Confluence Server instances, use your regular username and password or token
- If your network requires a proxy to access Confluence, you can set additional environment variables:
  ```bash
  export PROXY_HOST="your-proxy-host"
  export PROXY_PORT="your-proxy-port"
  ```
- **SSL verification is strictly enforced for security. For testing with self-signed certificates, use trusted certificates or configure your system's certificate store.**

## Recent Updates

- Added support for both Confluence Cloud and on-premise Confluence Server instances
- Improved error handling and logging with different log levels
- Added conditional debug logging that only outputs when DEBUG=true
- Enhanced type safety with proper TypeScript interfaces
- Fixed API endpoint handling to properly work with different Confluence URL structures
- **SECURITY: Enforced SSL verification to prevent man-in-the-middle attacks**
- **SECURITY: Implemented automatic redaction of sensitive information in logs**
- **SECURITY: Enhanced URL validation for Atlassian domains**
- Improved error messages with more context

## Security Features

- **SSL Verification**: SSL certificate validation is strictly enforced to prevent man-in-the-middle attacks.
- **Sensitive Data Protection**: All logs automatically redact sensitive information such as passwords, tokens, and credentials.
- **Secure URL Validation**: Enhanced URL validation ensures proper handling of Atlassian domain verification.
- **Structured Logging**: Improved logging system with different log levels and context-aware output.

## 🛠 Available Tools

### 1. 🔍 Search Content (confluence_search)
Search for Confluence content using CQL queries.

```typescript
{
  "cql": string;           // CQL query string
  "limit"?: number;        // Maximum number of results to return (default: 10)
  "expand"?: string;       // Additional fields to expand in the response
}
```

Example Cline prompt:
```
Find all Confluence pages containing "automation"

[Cline will use:]
<use_mcp_tool>
<server_name>confluence</server_name>
<tool_name>confluence_search</tool_name>
<arguments>
{
  "cql": "text ~ \"automation\"",
  "limit": 10
}
</arguments>
</use_mcp_tool>
```

### 2. 📄 Get Page Content (confluence_get_page)
Get a Confluence page by ID.

```typescript
{
  "pageId": string;        // Confluence page ID
  "expand"?: string;       // Optional fields to expand (default: "body.storage,version,space,children.attachment")
}
```

Example Cline prompt:
```
Show me the content of Confluence page 123456

[Cline will use:]
<use_mcp_tool>
<server_name>confluence</server_name>
<tool_name>confluence_get_page</tool_name>
<arguments>
{
  "pageId": "123456",
  "expand": "body.storage"
}
</arguments>
</use_mcp_tool>
```

### 3. 📝 Create Page (confluence_create_page)
Create a new Confluence page.

```typescript
{
  "spaceKey": string;      // Space key where the page will be created
  "title": string;         // Page title
  "content": string;       // Page content in Confluence storage format
  "parentId"?: string;     // Parent page ID (optional)
  "labels"?: string[];     // Labels to add to the page
}
```

### 4. 🔄 Update Page (confluence_update_page)
Update an existing Confluence page.

```typescript
{
  "pageId": string;        // Page ID to update
  "title": string;         // New page title
  "content": string;       // New page content in Confluence storage format
  "version": number;       // Page version number
  "labels"?: string[];     // Labels to set on the page
}
```

### 5. 🗑️ Delete Page (confluence_delete_page)
Delete a Confluence page.

```typescript
{
  "pageId": string;        // Page ID to delete
}
```

### 6. 🏢 Get Space (confluence_get_space)
Get details about a Confluence space.

```typescript
{
  "spaceKey": string;      // Space key
  "expand"?: string;       // Additional fields to expand in the response
}
```

### 7. 📚 Get Space Content (confluence_get_space_content)
Get content in a Confluence space.

```typescript
{
  "spaceKey": string;      // Space key
  "type"?: string;         // Content type (page, blogpost, or comment)
  "limit"?: number;        // Maximum number of results to return
  "expand"?: string;       // Additional fields to expand in the response
}
```

### 8. 💬 Get Comments (confluence_get_comments)
Get comments on a Confluence page.

```typescript
{
  "pageId": string;        // Page ID
  "limit"?: number;        // Maximum number of comments to return
  "expand"?: string;       // Additional fields to expand in the response
}
```

### 9. 💬 Add Comment (confluence_add_comment)
Add a comment to a Confluence page.

```typescript
{
  "pageId": string;        // Page ID
  "content": string;       // Comment content in Confluence storage format
}
```

### 10. 📎 Get Attachments (confluence_get_attachments)
Get attachments for a Confluence page.

```typescript
{
  "pageId": string;        // Page ID
  "limit"?: number;        // Maximum number of attachments to return (default: 25)
  "expand"?: string;       // Additional fields to expand in the response
}
```

### 11. 🏷️ Get Labels (confluence_get_labels)
Get labels for a Confluence content item.

```typescript
{
  "contentId": string;     // Content ID (page, blog post, etc.)
  "prefix"?: string;       // Filter labels by prefix
  "limit"?: number;        // Maximum number of labels to return (default: 25)
}
```

### 12. 🏷️ Add Label (confluence_add_label)
Add a label to a Confluence content item.

```typescript
{
  "contentId": string;     // Content ID (page, blog post, etc.)
  "label": string;         // Label to add
  "prefix"?: string;       // Label prefix (default: "global")
}
```

### 13. 🏷️ Remove Label (confluence_remove_label)
Remove a label from a Confluence content item.

```typescript
{
  "contentId": string;     // Content ID (page, blog post, etc.)
  "label": string;         // Label to remove
  "prefix"?: string;       // Label prefix (default: "global")
}
```

### 14. 👨‍👩‍👧‍👦 Get Content Children (confluence_get_content_children)
Get children of a Confluence content item.

```typescript
{
  "contentId": string;     // Content ID (page, blog post, etc.)
  "type"?: string;         // Child type (page, attachment, comment)
  "limit"?: number;        // Maximum number of children to return (default: 25)
  "expand"?: string;       // Additional fields to expand in the response
}
```

## Troubleshooting

- If you encounter connection issues, check that your `CONFLUENCE_BASE_URL` is correct and includes `/wiki` for cloud instances
- For authentication issues, verify your username and API token
- Enable debug logging by setting `DEBUG=true` in your MCP settings
- If you're behind a corporate firewall, configure the proxy settings
- If the server doesn't appear in the Cline MCP Servers list, check for JSON syntax errors in your settings file (especially comments, which are not allowed in JSON)
