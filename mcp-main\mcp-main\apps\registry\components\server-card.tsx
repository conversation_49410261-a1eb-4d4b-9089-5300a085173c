"use client";

import { useState } from "react";
import { ServerModal } from "@/components/server-modal";
import type { MCPServer } from "../types/servers";

interface ServerCardProps {
  server: MCPServer;
}

export function ServerCard({ server }: ServerCardProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <div
        className="flex flex-col gap-6 border border-gray-200 dark:border-gray-800 rounded-lg bg-white dark:bg-gray-900 p-6 hover:shadow-md transition-shadow cursor-pointer"
        onClick={() => setIsModalOpen(true)}
      >
        <div className="flex-1">
          <h2 className="text-xl font-semibold mb-2">{server.name}</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {server.description}
          </p>

          <div className="flex flex-wrap gap-2 mb-4">
            {server.tags.map((tag) => (
              <span
                key={tag}
                className="bg-purple-100 dark:bg-purple-800 text-purple-800 dark:text-purple-100 px-2 py-1 rounded-full text-xs"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>

        <div className="text-sm text-gray-500">
          <p>
            <strong className="text-gray-950 dark:text-white font-bold">
              Version
            </strong>
            : {server.version}
          </p>
          <p>
            <strong className="text-gray-950 dark:text-white font-bold">
              Author
            </strong>
            : {server.author}
          </p>
        </div>
      </div>

      <ServerModal
        server={server}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
}
