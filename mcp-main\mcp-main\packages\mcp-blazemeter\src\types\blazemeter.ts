export interface GetWorkspaceArgs {
  workspaceId: string;
}

export interface GetWorkspaceTestsArgs {
  workspaceId: string;
  type?: string; // Optional parameter to filter by test type (e.g., 'taurus')
}

export interface RunTestArgs {
  test_id: string;
  virtual_users: number;
  duration: number;
}

export interface GetTestExecutionResultsSummaryArgs {
  test_execution_id: string;
  scenarios?: string[];
  filter?: "ALL" | "FULL" | string;
}

export interface GetTestDetailsArgs {
  test_id: string;
}

export interface GetTestExecutionStatusArgs {
  test_execution_id: string;
  level?: "INFO" | "DEBUG" | "WARN" | "ERROR";
}

export interface TestExecutionStatus {
  id: string;
  status: string;
  progress: number;
  currentDuration: number;
  error?: string;
}

export interface TestMetrics {
  requests_per_minute: number;
  average_response_time: number;
  error_rate: number;
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  average_latency: number;
  percentiles: {
    p90: number;
    p95: number;
    p99: number;
  };
}

export interface ScenarioResultSummary {
  scenarioId: string;
  scenarioName: string;
  summary: {
    avg: number;
    bytes: number;
    concurrency: number;
    duration: number;
    failed: number;
    hits: number;
    tp90: number;
    tp95: number;
    tp99: number;
    hits_avg: number;
    size_avg: number;
    latency: number;
    std: number;
    min: number;
    max: number;
    first: number;
    last: number;
    histogram: {
      mean: number;
    };
  };
}

export interface TestExecutionResultsSummary {
  summaries: ScenarioResultSummary[];
  maxUsers: number;
}

export interface WorkspaceTest {
  id: number;
  name: string;
  isNewTest: boolean;
  userId: number;
  created: number;
  updated: number;
  creatorClientId: string;
  overrideExecutions: Array<{
    executor: string;
    holdFor: string;
    rampUp: string;
    concurrency: number;
    locations: { [key: string]: number };
    locationsPercents: { [key: string]: number };
    steps: number;
  }>;
  executions: Array<{
    concurrency: number;
    holdFor: string;
    rampUp: string;
    steps: number;
    locations: { [key: string]: number };
    locationsPercents: { [key: string]: number };
    executor: string;
    scenario: string;
  }>;
  hasThreadGroupsToOverride: boolean;
  hasNonRegularThreadGroup: boolean;
  shouldSendReportEmail: boolean;
  dependencies: any[];
  tags: Array<{
    id: number;
    label: string;
  }>;
  shouldRemoveJmeter: boolean;
  projectId: number;
  lastUpdatedById: number;
  configuration: {
    type: string;
    dedicatedIpsEnabled: boolean;
    canControlRampup: boolean;
    targetThreads: number;
    executionType: string;
    enableFailureCriteria: boolean;
    enableMockServices: boolean;
    enableLoadConfiguration: boolean;
    scriptType: string;
    threads: number;
    filename: string;
    testMode: string;
    extraSlots: number;
    plugins: {
      jmeter?: {
        version: string;
        consoleArgs: string;
        enginesArgs: string;
      };
      thresholds?: {
        thresholds: any[];
        ignoreRampup: boolean;
        fromTaurus: boolean;
        slidingWindow: boolean;
      };
    };
  };
}

export interface Workspace {
  id: string | number;
  name: string;
  created: number;
  updated: number;
  enabled: boolean;
  owner: {
    name: string;
    email: string;
  };
  membersCount: number;
  allowance: {
    amount: number;
    type: string;
  };
  activeMember: {
    name: string;
    email: string;
    role: string;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  error?: any;
}

export interface UpdateMultiTestArgs {
  collectionId: string;
  index: number;
  overrideExecutions: {
    concurrency?: number;
    holdFor?: string;
    rampUp?: string;
    steps?: number;
    executor?: string;
    locations?: { [key: string]: number };
    locationsPercents?: { [key: string]: number };
  }[];
}

export interface StartMultiTestArgs {
  collectionId: string;
  delayedStart?: boolean;
}

export interface GetMultiTestDetailsArgs {
  collectionId: string;
  populateTests?: boolean;
}

export interface MultiTestDetails {
  id: number;
  name: string;
  description: string;
  collectionType: string;
  userId: number;
  items: any[];
  testsForExecutions: Array<{
    testId: number;
    executions: Array<{
      concurrency: number;
      holdFor: string;
      rampUp?: string;
      steps?: number;
      throughput?: number;
      locations?: { [key: string]: number };
      locationsPercents?: { [key: string]: number };
      executor: string;
      scenario: string;
    }>;
    overrideExecutions: Array<{
      concurrency?: number;
      holdFor?: string;
      rampUp?: string;
      steps?: number;
      throughput?: number;
      locations?: { [key: string]: number };
      locationsPercents?: { [key: string]: number };
    } | null>;
    enableLoadConfiguration: boolean;
  }>;
  created: number;
  updated: number;
  projectId: number;
  shouldSendReportEmail: boolean;
  tests?: Array<{
    id: number;
    name: string;
    description: string;
    configuration: {
      type: string;
      dedicatedIpsEnabled: boolean;
      plugins?: {
        jmeter?: {
          version: string;
          consoleArgs?: string;
          enginesArgs?: string;
        };
        network?: {
          preset: string;
          latency: number;
          bandwidth: number;
          packetloss: number;
        };
        thresholds?: {
          thresholds: Array<{
            field: string;
            label: string;
            op: string;
            stopTestOnViolation: boolean;
            value: string;
            isEmpty: boolean;
          }>;
        };
      };
    };
  }>;
}

export interface GetTestExecutionDetailsArgs {
  masterId: string;
}

export interface TestExecutionDetails {
  id: number;
  name: string;
  created: number;
  ended: number;
  maxUsers: number;
  scenariosMapping: Array<{
    id: string;
    name: string;
    test: string;
  }>;
  executions: Array<{
    concurrency: number;
    holdFor: string;
    executor: string;
    scenario: string;
  }>;
}
