#!/usr/bin/env node

/**
 * TELUS Service Qualification V2 MCP Server
 * This server provides access to the TELUS Service Qualification V2 API
 * with OAuth 2.0 authentication.
 */

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  ErrorCode,
  McpError,
} from "@modelcontextprotocol/sdk/types.js";
import axios from "axios";

// Environment type
type Environment = "PROD" | "NON_PROD";

// API version type
type ApiVersion = "v2" | "v4";

// Configuration
const CONFIG = {
  NON_PROD: {
    tokenUrl: "https://apigw-st.telus.com/st/token",
    apiUrl: "https://apigw-st.telus.com/service/serviceQualification/v2/serviceQualification",
    apiUrlV4: "https://apigw-st.telus.com/customer/serviceQualification/v4/checkServiceQualification",
    clientId: process.env.TELUS_CLIENT_ID_NP || "",
    clientSecret: process.env.TELUS_CLIENT_SECRET_NP || "",
  },
  PROD: {
    tokenUrl: "https://apigw-pr.telus.com/token",
    apiUrl: "https://apigw-pr.telus.com/service/serviceQualification/v2/serviceQualification",
    apiUrlV4: "https://apigw-pr.telus.com/customer/serviceQualification/v4/checkServiceQualification",
    clientId: process.env.TELUS_CLIENT_ID_PROD || "",
    clientSecret: process.env.TELUS_CLIENT_SECRET_PROD || "",
  },
};

// API version specific configuration
const API_CONFIG = {
  v2: {
    scope: "26"
  },
  v4: {
    scope: "3744"
  }
};

// Token cache
interface TokenCache {
  token: string;
  expiresAt: number;
}

// Cache by environment and API version
const tokenCache: Record<Environment, Record<ApiVersion, TokenCache | null>> = {
  NON_PROD: {
    v2: null,
    v4: null
  },
  PROD: {
    v2: null,
    v4: null
  }
};

// Types for Service Qualification API
interface ServiceQualificationRequest {
  serviceQualificationItem: Array<{
    id: string;
  }>;
}

interface ServiceCharacteristic {
  name: string;
  value: string;
}

interface ServiceQualificationItem {
  id: number;
  qualificationResult: string;
  expectedServiceAvailabilityDate?: string;
  serviceSpecification: {
    name: string;
  };
  service: {
    characteristic: ServiceCharacteristic[];
    place: Array<{
      role: string;
      id: number;
    }>;
  };
}

interface ServiceQualificationResponse {
  id: number;
  qualificationResult: string;
  externalId: string;
  description: string;
  serviceQualificationItem: ServiceQualificationItem[];
}

// Types for Service Qualification V4 API
interface ServiceCharacteristicV4 {
  id: string;
  name: string;
  valueType: string;
  value: string;
}

interface ServiceQualificationItemV4 {
  id: string;
  expectedServiceAvailabilityDate: string;
  qualificationResult: string;
  state: string;
  category: {
    name: string;
  };
  service: {
    id: string;
    category: string;
    description: string;
    serviceType: string;
    place: {
      id: string;
      href: string;
      unit: string;
      floor: string;
      streetNumberPrefix: string;
      streetNumber: string;
      streetNumberSuffix: string;
      dirPrefix: string;
      streetTypePrefix: string;
      streetName: string;
      streetTypeSuffix: string;
      dirSuffix: string;
      city: string;
      province: string;
      postalCode: string;
      role: string;
      name: string;
    };
    serviceCharacteristic: ServiceCharacteristicV4[];
    serviceSpecification: {
      id: string;
      name: string;
      version: string;
      "@type": string;
    };
    state: string;
    "@type": string;
  };
  "@type": string;
}

interface ServiceQualificationV4Response {
  id: string;
  href: string;
  checkServiceQualificationDate: string;
  description: string;
  externalId: string;
  instantSyncQualification: boolean;
  provideUnavailabilityReason: boolean;
  qualificationResult: string;
  state: string;
  serviceQualificationItem: ServiceQualificationItemV4[];
  type: string;
}

// Summary response type for Offnet qualification
interface OffnetServiceQualSummary {
  addressId: string;
  qualificationResult: string;
  serviceCategory: string;
  serviceCharacteristics: Array<{
    name: string;
    value: string;
  }>;
}

// Summary response type for V2 API
interface ServiceQualSummary {
  addressId: string;
  services: Array<{
    type: string;
    qualificationResult: string;
    isDPORequired?: string;
    pathType?: string;
    technologyType?: string;
  }>;
}

/**
 * Create an MCP server with capabilities for tools to access
 * the TELUS Service Qualification V2 API.
 */
const server = new Server(
  {
    name: "service-qual-v2",
    version: "0.1.0",
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

/**
 * Get an OAuth token for the specified environment and API version
 */
async function getAuthToken(environment: Environment, apiVersion: ApiVersion): Promise<string> {
  console.info(`[Auth] Requesting OAuth token for ${environment} ${apiVersion}...`);
  
  // Check if we have a valid cached token
  const cache = tokenCache[environment][apiVersion];
  if (cache && cache.expiresAt > Date.now()) {
    console.info(`[Auth] Using cached token for ${apiVersion}`);
    return cache.token;
  }
  
  const config = CONFIG[environment];
  const apiConfig = API_CONFIG[apiVersion];
  
  if (!config.clientId || !config.clientSecret) {
    throw new McpError(
      ErrorCode.InvalidParams,
      `Missing client credentials for ${environment} environment`
    );
  }
  
  try {
    // Prepare form data for token request
    const params = new URLSearchParams();
    params.append("grant_type", "client_credentials");
    params.append("scope", apiConfig.scope);
    params.append("client_id", config.clientId);
    params.append("client_secret", config.clientSecret);
    
    console.error(`[Auth] Token request params: grant_type=client_credentials, scope=${apiConfig.scope}, client_id=${config.clientId.substring(0, 8)}...`);
    console.error(`[Auth] Token URL: ${config.tokenUrl}`);
    
    // Request token
    const response = await axios.post(config.tokenUrl, params, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json",
      },
    });
    
    if (!response.data.access_token) {
      throw new McpError(
        ErrorCode.InternalError,
        "Failed to obtain access token"
      );
    }
    
    // Cache the token
    const expiresIn = response.data.expires_in || 3600;
    tokenCache[environment][apiVersion] = {
      token: response.data.access_token,
      expiresAt: Date.now() + (expiresIn * 1000) - 60000, // Expire 1 minute early
    };
    
    console.info(`[Auth] Successfully obtained new token for ${apiVersion}`);
    return response.data.access_token;
  } catch (error) {
    console.error("[Auth] Error obtaining token:", error);
    throw new McpError(
      ErrorCode.InternalError,
      `Failed to authenticate: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Call the Service Qualification V2 API
 */
async function callServiceQualification(
  addressId: string,
  environment: Environment
): Promise<ServiceQualificationResponse> {
  console.info(`[API] Calling Service Qualification API for addressId ${addressId} in ${environment} environment`);
  
  const config = CONFIG[environment];
  const token = await getAuthToken(environment, "v2");
  
  try {
    const requestBody: ServiceQualificationRequest = {
      serviceQualificationItem: [
        {
          id: addressId
        }
      ]
    };
    
    const response = await axios.post(
      config.apiUrl,
      requestBody,
      {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    
    console.info("[API] Service Qualification API call successful");
    return response.data;
  } catch (error) {
    console.error("[API] Service Qualification API call failed:", error);
    
    if (axios.isAxiosError(error)) {
      const statusCode = error.response?.status;
      const errorMessage = error.response?.data?.message || error.message;
      
      if (statusCode === 401 || statusCode === 403) {
        throw new McpError(
          ErrorCode.InternalError,
          `Authentication failed: ${errorMessage}`
        );
      } else if (statusCode === 404) {
        throw new McpError(
          ErrorCode.InvalidParams,
          `Address ID ${addressId} not found: ${errorMessage}`
        );
      } else {
        throw new McpError(
          ErrorCode.InternalError,
          `Service Qualification API error (${statusCode}): ${errorMessage}`
        );
      }
    }
    
    throw new McpError(
      ErrorCode.InternalError,
      `Service Qualification API error: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Call the Service Qualification V4 API for Offnet qualifications
 */
async function callOffnetServiceQualification(
  addressId: string,
  environment: Environment
): Promise<ServiceQualificationV4Response> {
  console.info(`[API] Calling Offnet Service Qualification API for addressId ${addressId} in ${environment} environment`);
  
  const config = CONFIG[environment];
  const token = await getAuthToken(environment, "v4");
  
  try {
    // Build the URL with query parameters
    const baseUrl = config.apiUrlV4;
    const queryParams = new URLSearchParams({
      fields: "id,href,checkServiceQualificationDate,description,externalId,instantSyncQualification,ProvideUnavailabilityReason,qualificationResult,state,@type",
      "serviceQualificationItem.category.name": "OFFNET",
      "Service.serviceType": "FIBRE",
      "place.id": addressId,
    });
    
    if (environment === "NON_PROD") {
      queryParams.append("env", "itn01");
    }
    
    const url = `${baseUrl}?${queryParams.toString()}`;
    
    console.error(`[API] Request URL: ${url}`);
    console.error(`[API] Authorization: Bearer ${token.substring(0, 10)}...`);
    
    const response = await axios.get(
      url,
      {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
        },
      }
    );
    
    console.info("[API] Offnet Service Qualification API call successful");
    return response.data;
  } catch (error) {
    console.error("[API] Offnet Service Qualification API call failed:", error);
    
    if (axios.isAxiosError(error)) {
      const statusCode = error.response?.status;
      const errorMessage = error.response?.data?.message || error.message;
      
      if (statusCode === 401 || statusCode === 403) {
        throw new McpError(
          ErrorCode.InternalError,
          `Authentication failed: ${errorMessage}`
        );
      } else if (statusCode === 404) {
        throw new McpError(
          ErrorCode.InvalidParams,
          `Address ID ${addressId} not found: ${errorMessage}`
        );
      } else {
        throw new McpError(
          ErrorCode.InternalError,
          `Offnet Service Qualification API error (${statusCode}): ${errorMessage}`
        );
      }
    }
    
    throw new McpError(
      ErrorCode.InternalError,
      `Offnet Service Qualification API error: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Extract a summary from the Offnet Service Qualification response
 */
function extractOffnetServiceQualSummary(
  addressId: string,
  response: ServiceQualificationV4Response
): OffnetServiceQualSummary {
  const summary: OffnetServiceQualSummary = {
    addressId,
    qualificationResult: response.qualificationResult,
    serviceCategory: response.serviceQualificationItem[0]?.category?.name || "",
    serviceCharacteristics: [],
  };
  
  // Extract service characteristics
  const serviceCharacteristics = response.serviceQualificationItem[0]?.service?.serviceCharacteristic || [];
  for (const characteristic of serviceCharacteristics) {
    summary.serviceCharacteristics.push({
      name: characteristic.name,
      value: characteristic.value,
    });
  }
  
  return summary;
}

/**
 * Extract a summary from the Service Qualification V2 response
 */
function extractServiceQualSummary(
  addressId: string,
  response: ServiceQualificationResponse
): ServiceQualSummary {
  const summary: ServiceQualSummary = {
    addressId,
    services: [],
  };
  
  for (const item of response.serviceQualificationItem) {
    const serviceType = item.serviceSpecification.name;
    const qualificationResult = item.qualificationResult;
    
    const serviceSummary: ServiceQualSummary["services"][0] = {
      type: serviceType,
      qualificationResult,
    };
    
    // Extract specific fields for GPON/XGSPON
    if (serviceType.toLowerCase() === "gpon" || serviceType.toLowerCase() === "xgspon") {
      for (const characteristic of item.service.characteristic) {
        if (characteristic.name === "isDPORequired") {
          serviceSummary.isDPORequired = characteristic.value;
        } else if (characteristic.name === "PathType") {
          serviceSummary.pathType = characteristic.value;
        } else if (characteristic.name === "technologyType") {
          serviceSummary.technologyType = characteristic.value;
        }
      }
    }
    
    summary.services.push(serviceSummary);
  }
  
  return summary;
}

/**
 * Handler that lists available tools.
 * Exposes tools that let clients query service qualification.
 */
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: "service_qual_by_addressId",
        description: "Get service qualification information for a TELUS address ID",
        inputSchema: {
          type: "object",
          properties: {
            addressId: {
              type: "string",
              description: "TELUS Address ID"
            },
            environment: {
              type: "string",
              enum: ["PROD", "NON_PROD"],
              description: "Environment to use (defaults to NON_PROD)"
            }
          },
          required: ["addressId"]
        }
      },
      {
        name: "offnet_service_qual_by_addressId",
        description: "Get Offnet (FTTP) service qualification information for a TELUS address ID",
        inputSchema: {
          type: "object",
          properties: {
            addressId: {
              type: "string",
              description: "TELUS Address ID"
            },
            environment: {
              type: "string",
              enum: ["PROD", "NON_PROD"],
              description: "Environment to use (defaults to NON_PROD)"
            }
          },
          required: ["addressId"]
        }
      }
    ]
  };
});

/**
 * Handler for the service qualification tools.
 * Calls the appropriate TELUS Service Qualification API and returns a summary of the results.
 */
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  // Validate the tool name
  if (request.params.name !== "service_qual_by_addressId" && request.params.name !== "offnet_service_qual_by_addressId") {
    throw new McpError(
      ErrorCode.MethodNotFound,
      `Unknown tool: ${request.params.name}`
    );
  }
  
  // Validate common parameters
  const addressId = String(request.params.arguments?.addressId);
  if (!addressId) {
    throw new McpError(
      ErrorCode.InvalidParams,
      "Address ID is required"
    );
  }
  
  const environment = (request.params.arguments?.environment as Environment) || "NON_PROD";
  if (environment !== "PROD" && environment !== "NON_PROD") {
    throw new McpError(
      ErrorCode.InvalidParams,
      "Environment must be either PROD or NON_PROD"
    );
  }
  
  try {
    // Handle the appropriate tool
    if (request.params.name === "service_qual_by_addressId") {
      const response = await callServiceQualification(addressId, environment);
      const summary = extractServiceQualSummary(addressId, response);
      
      return {
        content: [{
          type: "text",
          text: JSON.stringify(summary, null, 2)
        }]
      };
    } else { // offnet_service_qual_by_addressId
      const response = await callOffnetServiceQualification(addressId, environment);
      const summary = extractOffnetServiceQualSummary(addressId, response);
      
      return {
        content: [{
          type: "text",
          text: JSON.stringify(summary, null, 2)
        }]
      };
    }
  } catch (error) {
    if (error instanceof McpError) {
      throw error;
    }
    
    throw new McpError(
      ErrorCode.InternalError,
      `Error processing request: ${error instanceof Error ? error.message : String(error)}`
    );
  }
});

/**
 * Start the server using stdio transport.
 * This allows the server to communicate via standard input/output streams.
 */
async function main() {
  console.info("[Setup] Initializing TELUS Service Qualification server...");
  
  // Validate environment variables
  if (!process.env.TELUS_CLIENT_ID_NP || !process.env.TELUS_CLIENT_SECRET_NP) {
    console.error("[Setup] Warning: Missing NON_PROD credentials");
  }
  
  if (!process.env.TELUS_CLIENT_ID_PROD || !process.env.TELUS_CLIENT_SECRET_PROD) {
    console.error("[Setup] Warning: Missing PROD credentials");
  }
  
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.info("[Setup] Server started successfully");
}

main().catch((error) => {
  console.error("[Error] Server failed to start:", error);
  process.exit(1);
});
