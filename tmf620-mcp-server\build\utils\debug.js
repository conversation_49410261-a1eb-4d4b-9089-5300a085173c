"use strict";
/**
 * Debug utility for logging with timestamps and categories
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DebugLogger = exports.LogLevel = void 0;
const DEBUG_ENABLED = true;
/**
 * Log levels for different types of messages
 */
var LogLevel;
(function (LogLevel) {
    LogLevel["INFO"] = "INFO";
    LogLevel["DEBUG"] = "DEBUG";
    LogLevel["ERROR"] = "ERROR";
    LogLevel["WARN"] = "WARN";
    LogLevel["TIMING"] = "TIMING";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
/**
 * Get current timestamp in a readable format
 */
const getTimestamp = () => {
    const now = new Date();
    return now.toISOString();
};
/**
 * Calculate time difference in milliseconds
 */
const getTimeDiff = (startTime) => {
    return Date.now() - startTime;
};
/**
 * Format log message with timestamp and category
 */
const formatLog = (level, category, message) => {
    return `[${getTimestamp()}] [${level}] [${category}] ${message}`;
};
/**
 * Debug logger with support for timing operations
 */
class DebugLogger {
    constructor(category) {
        this.timers = new Map();
        this.category = category;
    }
    /**
     * Log an informational message
     */
    info(message, data) {
        if (!DEBUG_ENABLED)
            return;
        console.log(formatLog(LogLevel.INFO, this.category, message));
        if (data)
            console.log(data);
    }
    /**
     * Log a debug message
     */
    debug(message, data) {
        if (!DEBUG_ENABLED)
            return;
        console.log(formatLog(LogLevel.DEBUG, this.category, message));
        if (data)
            console.log(data);
    }
    /**
     * Log an error message
     */
    error(message, error) {
        if (!DEBUG_ENABLED)
            return;
        console.error(formatLog(LogLevel.ERROR, this.category, message));
        if (error)
            console.error(error);
    }
    /**
     * Log a warning message
     */
    warn(message, data) {
        if (!DEBUG_ENABLED)
            return;
        console.warn(formatLog(LogLevel.WARN, this.category, message));
        if (data)
            console.warn(data);
    }
    /**
     * Start timing an operation
     */
    startTimer(operationName) {
        this.timers.set(operationName, Date.now());
        this.debug(`Starting operation: ${operationName}`);
    }
    /**
     * End timing an operation and log the duration
     */
    endTimer(operationName) {
        const startTime = this.timers.get(operationName);
        if (!startTime) {
            this.warn(`No timer found for operation: ${operationName}`);
            return;
        }
        const duration = getTimeDiff(startTime);
        this.timers.delete(operationName);
        console.log(formatLog(LogLevel.TIMING, this.category, `Operation "${operationName}" completed in ${duration}ms`));
        return duration;
    }
    /**
     * Log the current state of all active timers
     */
    logActiveTimers() {
        if (this.timers.size === 0) {
            this.debug('No active timers');
            return;
        }
        this.debug('Active timers:');
        this.timers.forEach((startTime, operationName) => {
            const duration = getTimeDiff(startTime);
            console.log(formatLog(LogLevel.TIMING, this.category, `- ${operationName}: ${duration}ms (still running)`));
        });
    }
}
exports.DebugLogger = DebugLogger;
