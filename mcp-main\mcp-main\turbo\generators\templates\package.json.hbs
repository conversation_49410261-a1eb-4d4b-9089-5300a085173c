{"name": "@telus/mcp-{{name}}", "version": "0.0.1", "description": "{{description}}", "keywords": ["telus", "mcp", "{{name}}"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-{{name}}"}, "license": "MIT", "author": "TELUS", "type": "module", "main": "dist/index.js", "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "prepare": "pnpm run build", "start": "node dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.4.1"}, "devDependencies": {"@types/node": "^22.10.10", "typescript": "^5.3.3"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}