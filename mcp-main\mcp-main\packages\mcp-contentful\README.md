# @telus/mcp-contentful

A Model Context Protocol (MCP) server that provides tools for interacting with Contentful.

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-contentful
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for Claude Desktop):

   ```json
   {
     "mcpServers": {
       "contentful": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-contentful"],
         "env": {
           "CONTENTFUL_SPACE": "[space-id]", // Your Contentful space ID
           "CONTENTFUL_TOKEN": "[access-token]", // Your Contentful access token
           "CONTENTFUL_ENVIRONMENT": "[environment-name]" // Optional: defaults to "master"
         }
       }
     }
   }
   ```

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-contentful
   pnpm install
   ```

2. Build the server:

   ```bash
   pnpm build
   ```

3. Start the server:

   ```bash
   pnpm start
   ```

## Configuration

The following environment variables are required:

- `CONTENTFUL_SPACE`: Your Contentful space ID
- `CONTENTFUL_TOKEN`: Your Contentful access token
- `CONTENTFUL_ENVIRONMENT`: (Optional) Defaults to "master"

You can find these values in your Contentful space settings under "Settings > API keys".

## Available Tools

This MCP server provides the following tools for interacting with Contentful:

1. **get_single_entry**: Retrieves a single entry from Contentful.

   - Description: Gets a single entry from Contentful using its entry ID. Locale is optional (default is english)
   - Example prompt: "Get the Contentful entry with ID 'testID'"
   - Example prompt with local: "Get the Contentful entry with ID 'testID' in french"

2. **get_entries**: Retrieves multiple entries from the Contentful space. Locale is optional (default is english)
   - Description: Gets entries from Contentful, optionally filtered by content type.
   - Example prompt: "Get all entries of content type 'appComponent' from Contentful"
   - Example prompt: "Get all entries of content type 'appComponent' from Contentful in french"

These tools allow you to fetch content from your Contentful space, which can be used to populate websites, applications, or other content-driven systems.

## License

MIT © TELUS
