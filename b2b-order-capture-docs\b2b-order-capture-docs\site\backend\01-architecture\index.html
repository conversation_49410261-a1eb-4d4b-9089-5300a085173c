
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/backend/01-architecture/">
      
      
        <link rel="prev" href="../00-overview/">
      
      
        <link rel="next" href="../02-rest-api-design/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Architecture - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#backend-architecture" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Architecture
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" checked>
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      System Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="System Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#application-layers" class="md-nav__link">
    <span class="md-ellipsis">
      Application Layers
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Component Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#spring-boot-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Spring Boot Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Spring Boot Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#main-application-class" class="md-nav__link">
    <span class="md-ellipsis">
      Main Application Class
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-classes" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Classes
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#multi-module-design" class="md-nav__link">
    <span class="md-ellipsis">
      Multi-Module Design
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Multi-Module Design">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-module-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Module Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#module-dependencies" class="md-nav__link">
    <span class="md-ellipsis">
      Module Dependencies
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#data-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Data Flow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Flow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#request-processing-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Request Processing Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#error-handling-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#order-capture-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Order Capture Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bss-system-integration" class="md-nav__link">
    <span class="md-ellipsis">
      BSS System Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Security Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#authentication-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Security Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#method-level-security" class="md-nav__link">
    <span class="md-ellipsis">
      Method-Level Security
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      System Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="System Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#application-layers" class="md-nav__link">
    <span class="md-ellipsis">
      Application Layers
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Component Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#spring-boot-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Spring Boot Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Spring Boot Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#main-application-class" class="md-nav__link">
    <span class="md-ellipsis">
      Main Application Class
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-classes" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Classes
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#multi-module-design" class="md-nav__link">
    <span class="md-ellipsis">
      Multi-Module Design
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Multi-Module Design">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-module-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Module Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#module-dependencies" class="md-nav__link">
    <span class="md-ellipsis">
      Module Dependencies
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#data-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Data Flow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Flow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#request-processing-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Request Processing Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#error-handling-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#order-capture-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Order Capture Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bss-system-integration" class="md-nav__link">
    <span class="md-ellipsis">
      BSS System Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Security Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#authentication-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Security Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#method-level-security" class="md-nav__link">
    <span class="md-ellipsis">
      Method-Level Security
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="backend-architecture">Backend Architecture<a class="headerlink" href="#backend-architecture" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#system-architecture">System Architecture</a></li>
<li><a href="#spring-boot-architecture">Spring Boot Architecture</a></li>
<li><a href="#multi-module-design">Multi-Module Design</a></li>
<li><a href="#data-flow">Data Flow</a></li>
<li><a href="#integration-architecture">Integration Architecture</a></li>
<li><a href="#security-architecture">Security Architecture</a></li>
</ul>
<h2 id="system-architecture">System Architecture<a class="headerlink" href="#system-architecture" title="Permanent link">&para;</a></h2>
<h3 id="application-layers">Application Layers<a class="headerlink" href="#application-layers" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Presentation Layer&quot;
        A[REST Controllers]
        B[Exception Handlers]
        C[Request/Response DTOs]
    end

    subgraph &quot;Business Logic Layer&quot;
        D[Service Layer]
        E[Validation Layer]
        F[Mapping Layer]
        G[Business Rules]
    end

    subgraph &quot;Integration Layer&quot;
        H[Order Capture Client]
        I[BSS Integration Client]
        J[External API Clients]
        K[Message Handlers]
    end

    subgraph &quot;Infrastructure Layer&quot;
        L[Configuration]
        M[Security]
        N[Logging]
        O[Monitoring]
    end

    subgraph &quot;External Systems&quot;
        P[Order Capture Product]
        Q[TELUS BSS Systems]
        R[CIP Platform]
        S[Frontend Application]
    end

    A --&gt; D
    B --&gt; D
    C --&gt; D
    D --&gt; E
    E --&gt; F
    F --&gt; G
    D --&gt; H
    D --&gt; I
    D --&gt; J
    H --&gt; P
    I --&gt; Q
    J --&gt; R
    S --&gt; A

    L --&gt; A
    M --&gt; A
    N --&gt; D
    O --&gt; D

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style D fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style H fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="component-architecture">Component Architecture<a class="headerlink" href="#component-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;Spring Boot Application&quot;
        A[OrderEntryWebApplication]
        B[Configuration Beans]
        C[Component Scanning]
    end

    subgraph &quot;REST Layer&quot;
        D[QuoteController]
        E[OrderController]
        F[ServiceController]
        G[CustomerController]
    end

    subgraph &quot;Service Layer&quot;
        H[QuoteService]
        I[OrderService]
        J[ValidationService]
        K[MappingService]
    end

    subgraph &quot;Client Layer&quot;
        L[OrderCaptureClient]
        M[BssIntegrationClient]
        N[ExternalApiClient]
    end

    subgraph &quot;Configuration&quot;
        O[SecurityConfig]
        P[JacksonConfig]
        Q[OpenApiConfig]
        R[CorsConfig]
    end

    A --&gt; B
    A --&gt; C

    C --&gt; D
    C --&gt; E
    C --&gt; F
    C --&gt; G

    D --&gt; H
    E --&gt; I
    F --&gt; J
    G --&gt; K

    H --&gt; L
    I --&gt; M
    J --&gt; N

    B --&gt; O
    B --&gt; P
    B --&gt; Q
    B --&gt; R

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style H fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style L fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h2 id="spring-boot-architecture">Spring Boot Architecture<a class="headerlink" href="#spring-boot-architecture" title="Permanent link">&para;</a></h2>
<h3 id="main-application-class">Main Application Class<a class="headerlink" href="#main-application-class" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@SpringBootApplication</span>
<span class="nd">@EnableAutoConfiguration</span>
<span class="nd">@ComponentScan</span><span class="p">(</span><span class="n">basePackages</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s">&quot;com.netcracker.solutions.telus.ordercapture.backend&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s">&quot;com.netcracker.solutions.telus.ordercapture.impl&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s">&quot;com.netcracker.bss&quot;</span>
<span class="p">})</span>
<span class="nd">@EnableConfigurationProperties</span>
<span class="nd">@EnableScheduling</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">OrderEntryWebApplication</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">main</span><span class="p">(</span><span class="n">String</span><span class="o">[]</span><span class="w"> </span><span class="n">args</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">SpringApplication</span><span class="p">.</span><span class="na">run</span><span class="p">(</span><span class="n">OrderEntryWebApplication</span><span class="p">.</span><span class="na">class</span><span class="p">,</span><span class="w"> </span><span class="n">args</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">WebMvcConfigurer</span><span class="w"> </span><span class="nf">corsConfigurer</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">WebMvcConfigurer</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nd">@Override</span>
<span class="w">            </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">addCorsMappings</span><span class="p">(</span><span class="n">CorsRegistry</span><span class="w"> </span><span class="n">registry</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">registry</span><span class="p">.</span><span class="na">addMapping</span><span class="p">(</span><span class="s">&quot;/**&quot;</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">allowedOrigins</span><span class="p">(</span><span class="s">&quot;*&quot;</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">allowedMethods</span><span class="p">(</span><span class="s">&quot;GET&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;POST&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;PUT&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;DELETE&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;OPTIONS&quot;</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">allowedHeaders</span><span class="p">(</span><span class="s">&quot;*&quot;</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">};</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="nd">@Primary</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ObjectMapper</span><span class="w"> </span><span class="nf">objectMapper</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ObjectMapper</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">registerModule</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">JavaTimeModule</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">disable</span><span class="p">(</span><span class="n">SerializationFeature</span><span class="p">.</span><span class="na">WRITE_DATES_AS_TIMESTAMPS</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">setPropertyNamingStrategy</span><span class="p">(</span><span class="n">PropertyNamingStrategies</span><span class="p">.</span><span class="na">SNAKE_CASE</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="configuration-classes">Configuration Classes<a class="headerlink" href="#configuration-classes" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Security Configuration</span>
<span class="nd">@Configuration</span>
<span class="nd">@EnableWebSecurity</span>
<span class="nd">@EnableMethodSecurity</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">SecurityConfig</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">SecurityFilterChain</span><span class="w"> </span><span class="nf">filterChain</span><span class="p">(</span><span class="n">HttpSecurity</span><span class="w"> </span><span class="n">http</span><span class="p">)</span><span class="w"> </span><span class="kd">throws</span><span class="w"> </span><span class="n">Exception</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">http</span>
<span class="w">            </span><span class="p">.</span><span class="na">csrf</span><span class="p">(</span><span class="n">csrf</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">csrf</span><span class="p">.</span><span class="na">disable</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">cors</span><span class="p">(</span><span class="n">cors</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">cors</span><span class="p">.</span><span class="na">configurationSource</span><span class="p">(</span><span class="n">corsConfigurationSource</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">authorizeHttpRequests</span><span class="p">(</span><span class="n">authz</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">authz</span>
<span class="w">                </span><span class="p">.</span><span class="na">requestMatchers</span><span class="p">(</span><span class="s">&quot;/actuator/**&quot;</span><span class="p">).</span><span class="na">permitAll</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">requestMatchers</span><span class="p">(</span><span class="s">&quot;/swagger-ui/**&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;/v3/api-docs/**&quot;</span><span class="p">).</span><span class="na">permitAll</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">requestMatchers</span><span class="p">(</span><span class="n">HttpMethod</span><span class="p">.</span><span class="na">OPTIONS</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;/**&quot;</span><span class="p">).</span><span class="na">permitAll</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">anyRequest</span><span class="p">().</span><span class="na">authenticated</span><span class="p">()</span>
<span class="w">            </span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">oauth2ResourceServer</span><span class="p">(</span><span class="n">oauth2</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">oauth2</span>
<span class="w">                </span><span class="p">.</span><span class="na">jwt</span><span class="p">(</span><span class="n">jwt</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">jwt</span><span class="p">.</span><span class="na">jwtDecoder</span><span class="p">(</span><span class="n">jwtDecoder</span><span class="p">()))</span>
<span class="w">            </span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">http</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">CorsConfigurationSource</span><span class="w"> </span><span class="nf">corsConfigurationSource</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">CorsConfiguration</span><span class="w"> </span><span class="n">configuration</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">CorsConfiguration</span><span class="p">();</span>
<span class="w">        </span><span class="n">configuration</span><span class="p">.</span><span class="na">setAllowedOriginPatterns</span><span class="p">(</span><span class="n">Arrays</span><span class="p">.</span><span class="na">asList</span><span class="p">(</span><span class="s">&quot;*&quot;</span><span class="p">));</span>
<span class="w">        </span><span class="n">configuration</span><span class="p">.</span><span class="na">setAllowedMethods</span><span class="p">(</span><span class="n">Arrays</span><span class="p">.</span><span class="na">asList</span><span class="p">(</span><span class="s">&quot;GET&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;POST&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;PUT&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;DELETE&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;OPTIONS&quot;</span><span class="p">));</span>
<span class="w">        </span><span class="n">configuration</span><span class="p">.</span><span class="na">setAllowedHeaders</span><span class="p">(</span><span class="n">Arrays</span><span class="p">.</span><span class="na">asList</span><span class="p">(</span><span class="s">&quot;*&quot;</span><span class="p">));</span>
<span class="w">        </span><span class="n">configuration</span><span class="p">.</span><span class="na">setAllowCredentials</span><span class="p">(</span><span class="kc">true</span><span class="p">);</span>

<span class="w">        </span><span class="n">UrlBasedCorsConfigurationSource</span><span class="w"> </span><span class="n">source</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">UrlBasedCorsConfigurationSource</span><span class="p">();</span>
<span class="w">        </span><span class="n">source</span><span class="p">.</span><span class="na">registerCorsConfiguration</span><span class="p">(</span><span class="s">&quot;/**&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">configuration</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">source</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="c1">// OpenAPI Configuration</span>
<span class="nd">@Configuration</span>
<span class="nd">@OpenAPIDefinition</span><span class="p">(</span>
<span class="w">    </span><span class="n">info</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nd">@Info</span><span class="p">(</span>
<span class="w">        </span><span class="n">title</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;TELUS B2B Order Capture API&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="n">version</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;1.0.0&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="n">description</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;REST APIs for TELUS B2B order capture and quote management&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="n">contact</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nd">@Contact</span><span class="p">(</span>
<span class="w">            </span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;TELUS Development Team&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="n">email</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;<EMAIL>&quot;</span>
<span class="w">        </span><span class="p">)</span>
<span class="w">    </span><span class="p">),</span>
<span class="w">    </span><span class="n">servers</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nd">@Server</span><span class="p">(</span><span class="n">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;http://localhost:8080&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">description</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;Development Server&quot;</span><span class="p">),</span>
<span class="w">        </span><span class="nd">@Server</span><span class="p">(</span><span class="n">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;https://api-dev.telus.com&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">description</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;Development Environment&quot;</span><span class="p">),</span>
<span class="w">        </span><span class="nd">@Server</span><span class="p">(</span><span class="n">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;https://api.telus.com&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">description</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;Production Environment&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">OpenApiConfig</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">OpenAPI</span><span class="w"> </span><span class="nf">customOpenAPI</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">OpenAPI</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">components</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">Components</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">addSecuritySchemes</span><span class="p">(</span><span class="s">&quot;bearer-jwt&quot;</span><span class="p">,</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">SecurityScheme</span><span class="p">()</span>
<span class="w">                    </span><span class="p">.</span><span class="na">type</span><span class="p">(</span><span class="n">SecurityScheme</span><span class="p">.</span><span class="na">Type</span><span class="p">.</span><span class="na">HTTP</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">scheme</span><span class="p">(</span><span class="s">&quot;bearer&quot;</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">bearerFormat</span><span class="p">(</span><span class="s">&quot;JWT&quot;</span><span class="p">)</span>
<span class="w">                </span><span class="p">)</span>
<span class="w">            </span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">addSecurityItem</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">SecurityRequirement</span><span class="p">().</span><span class="na">addList</span><span class="p">(</span><span class="s">&quot;bearer-jwt&quot;</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="multi-module-design">Multi-Module Design<a class="headerlink" href="#multi-module-design" title="Permanent link">&para;</a></h2>
<h3 id="maven-module-structure">Maven Module Structure<a class="headerlink" href="#maven-module-structure" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Parent POM&quot;
        A[cloud-bss-oc-ui-backend-b2b-service]
    end

    subgraph &quot;Application Module&quot;
        B[cloud-bss-oc-ui-backend-b2b-application]
        C[Main Application Class]
        D[REST Controllers]
        E[Configuration Classes]
    end

    subgraph &quot;Implementation Module&quot;
        F[nc-cloud-bss-oc-ui-be-b2b-impl]
        G[Service Layer]
        H[Integration Clients]
        I[Business Logic]
    end

    subgraph &quot;Resources Module&quot;
        J[cloud-bss-oc-ui-backend-b2b-resources]
        K[Configuration Files]
        L[Static Resources]
        M[Templates]
    end

    A --&gt; B
    A --&gt; F
    A --&gt; J

    B --&gt; C
    B --&gt; D
    B --&gt; E

    F --&gt; G
    F --&gt; H
    F --&gt; I

    J --&gt; K
    J --&gt; L
    J --&gt; M

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style F fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="module-dependencies">Module Dependencies<a class="headerlink" href="#module-dependencies" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">&lt;!-- Parent POM Configuration --&gt;</span>
<span class="nt">&lt;groupId&gt;</span>com.netcracker.telus.test.cloudbss<span class="nt">&lt;/groupId&gt;</span>
<span class="nt">&lt;artifactId&gt;</span>cloud-bss-oc-ui-backend-b2b-service<span class="nt">&lt;/artifactId&gt;</span>
<span class="nt">&lt;version&gt;</span>${revision}<span class="nt">&lt;/version&gt;</span>
<span class="nt">&lt;packaging&gt;</span>pom<span class="nt">&lt;/packaging&gt;</span>

<span class="nt">&lt;modules&gt;</span>
<span class="w">    </span><span class="nt">&lt;module&gt;</span>cloud-bss-oc-ui-backend-b2b-application<span class="nt">&lt;/module&gt;</span>
<span class="w">    </span><span class="nt">&lt;module&gt;</span>cloud-bss-oc-ui-backend-b2b-resources<span class="nt">&lt;/module&gt;</span>
<span class="w">    </span><span class="nt">&lt;module&gt;</span>nc-cloud-bss-oc-ui-be-b2b-impl<span class="nt">&lt;/module&gt;</span>
<span class="nt">&lt;/modules&gt;</span>

<span class="nt">&lt;properties&gt;</span>
<span class="w">    </span><span class="nt">&lt;java.version&gt;</span>17<span class="nt">&lt;/java.version&gt;</span>
<span class="w">    </span><span class="nt">&lt;spring-boot.version&gt;</span>3.1.12<span class="nt">&lt;/spring-boot.version&gt;</span>
<span class="w">    </span><span class="nt">&lt;order-capture-product.version&gt;</span>2023.3.2.63<span class="nt">&lt;/order-capture-product.version&gt;</span>
<span class="w">    </span><span class="nt">&lt;swagger3.version&gt;</span>2.2.7<span class="nt">&lt;/swagger3.version&gt;</span>
<span class="w">    </span><span class="nt">&lt;bss-bom.version&gt;</span>2023.3.2.1<span class="nt">&lt;/bss-bom.version&gt;</span>
<span class="nt">&lt;/properties&gt;</span>

<span class="nt">&lt;dependencyManagement&gt;</span>
<span class="w">    </span><span class="nt">&lt;dependencies&gt;</span>
<span class="w">        </span><span class="nt">&lt;dependency&gt;</span>
<span class="w">            </span><span class="nt">&lt;groupId&gt;</span>org.springframework.boot<span class="nt">&lt;/groupId&gt;</span>
<span class="w">            </span><span class="nt">&lt;artifactId&gt;</span>spring-boot-dependencies<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">            </span><span class="nt">&lt;version&gt;</span>${spring-boot.version}<span class="nt">&lt;/version&gt;</span>
<span class="w">            </span><span class="nt">&lt;type&gt;</span>pom<span class="nt">&lt;/type&gt;</span>
<span class="w">            </span><span class="nt">&lt;scope&gt;</span>import<span class="nt">&lt;/scope&gt;</span>
<span class="w">        </span><span class="nt">&lt;/dependency&gt;</span>
<span class="w">        </span><span class="nt">&lt;dependency&gt;</span>
<span class="w">            </span><span class="nt">&lt;groupId&gt;</span>com.netcracker.bss<span class="nt">&lt;/groupId&gt;</span>
<span class="w">            </span><span class="nt">&lt;artifactId&gt;</span>bss-bom<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">            </span><span class="nt">&lt;version&gt;</span>${bss-bom.version}<span class="nt">&lt;/version&gt;</span>
<span class="w">            </span><span class="nt">&lt;type&gt;</span>pom<span class="nt">&lt;/type&gt;</span>
<span class="w">            </span><span class="nt">&lt;scope&gt;</span>import<span class="nt">&lt;/scope&gt;</span>
<span class="w">        </span><span class="nt">&lt;/dependency&gt;</span>
<span class="w">    </span><span class="nt">&lt;/dependencies&gt;</span>
<span class="nt">&lt;/dependencyManagement&gt;</span>
</code></pre></div>
<h2 id="data-flow">Data Flow<a class="headerlink" href="#data-flow" title="Permanent link">&para;</a></h2>
<h3 id="request-processing-flow">Request Processing Flow<a class="headerlink" href="#request-processing-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Client
    participant Controller
    participant Service
    participant Validator
    participant Mapper
    participant OCClient
    participant OCP

    Client-&gt;&gt;Controller: HTTP Request
    Controller-&gt;&gt;Controller: Validate Request
    Controller-&gt;&gt;Service: Process Business Logic
    Service-&gt;&gt;Validator: Validate Business Rules
    Validator--&gt;&gt;Service: Validation Result
    Service-&gt;&gt;Mapper: Transform Data
    Mapper--&gt;&gt;Service: Mapped Data
    Service-&gt;&gt;OCClient: Call Order Capture
    OCClient-&gt;&gt;OCP: External API Call
    OCP--&gt;&gt;OCClient: Response
    OCClient--&gt;&gt;Service: Processed Response
    Service-&gt;&gt;Mapper: Transform Response
    Mapper--&gt;&gt;Service: Response DTO
    Service--&gt;&gt;Controller: Business Result
    Controller--&gt;&gt;Client: HTTP Response
</code></pre></div>
<h3 id="error-handling-flow">Error Handling Flow<a class="headerlink" href="#error-handling-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    A[HTTP Request] --&gt; B{Validation}
    B --&gt;|Valid| C[Service Processing]
    B --&gt;|Invalid| D[Validation Exception]

    C --&gt; E{Business Logic}
    E --&gt;|Success| F[Response Mapping]
    E --&gt;|Business Error| G[Business Exception]

    F --&gt; H[HTTP Response]

    D --&gt; I[Global Exception Handler]
    G --&gt; I

    I --&gt; J{Exception Type}
    J --&gt;|Validation| K[400 Bad Request]
    J --&gt;|Business| L[422 Unprocessable Entity]
    J --&gt;|Integration| M[502 Bad Gateway]
    J --&gt;|System| N[500 Internal Server Error]

    K --&gt; O[Error Response]
    L --&gt; O
    M --&gt; O
    N --&gt; O

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style I fill:#f44336,stroke:#d32f2f,color:#ffffff
    style O fill:#ff9800,stroke:#f57c00,color:#ffffff
</code></pre></div>
<h2 id="integration-architecture">Integration Architecture<a class="headerlink" href="#integration-architecture" title="Permanent link">&para;</a></h2>
<h3 id="order-capture-integration">Order Capture Integration<a class="headerlink" href="#order-capture-integration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">OrderCaptureClient</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">OrderCaptureService</span><span class="w"> </span><span class="n">orderCaptureService</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">MappingService</span><span class="w"> </span><span class="n">mappingService</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">RetryTemplate</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">OrderCaptureClient</span><span class="p">(</span>
<span class="w">            </span><span class="n">OrderCaptureService</span><span class="w"> </span><span class="n">orderCaptureService</span><span class="p">,</span>
<span class="w">            </span><span class="n">MappingService</span><span class="w"> </span><span class="n">mappingService</span><span class="p">,</span>
<span class="w">            </span><span class="n">RetryTemplate</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">orderCaptureService</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">orderCaptureService</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">mappingService</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">retryTemplate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">getQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Fetching quote from Order Capture: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">.</span><span class="na">execute</span><span class="p">(</span><span class="n">context</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="c1">// Call Order Capture Product API</span>
<span class="w">                </span><span class="n">OrderCaptureQuote</span><span class="w"> </span><span class="n">ocQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">orderCaptureService</span><span class="p">.</span><span class="na">getQuote</span><span class="p">(</span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">                </span><span class="c1">// Transform to internal model</span>
<span class="w">                </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">fromOrderCapture</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">);</span>

<span class="w">                </span><span class="c1">// Apply business transformations</span>
<span class="w">                </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyBusinessRules</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">                </span><span class="c1">// Return response DTO</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">toQuoteResponse</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">            </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">OrderCaptureException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Order Capture API error for quote {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IntegrationException</span><span class="p">(</span><span class="s">&quot;Failed to retrieve quote&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">createQuote</span><span class="p">(</span><span class="n">CreateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Creating quote in Order Capture&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">.</span><span class="na">execute</span><span class="p">(</span><span class="n">context</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="c1">// Transform request to Order Capture format</span>
<span class="w">                </span><span class="n">OrderCaptureQuoteRequest</span><span class="w"> </span><span class="n">ocRequest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">toOrderCaptureRequest</span><span class="p">(</span><span class="n">request</span><span class="p">);</span>

<span class="w">                </span><span class="c1">// Validate request</span>
<span class="w">                </span><span class="n">validateQuoteRequest</span><span class="p">(</span><span class="n">ocRequest</span><span class="p">);</span>

<span class="w">                </span><span class="c1">// Call Order Capture Product API</span>
<span class="w">                </span><span class="n">OrderCaptureQuote</span><span class="w"> </span><span class="n">ocQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">orderCaptureService</span><span class="p">.</span><span class="na">createQuote</span><span class="p">(</span><span class="n">ocRequest</span><span class="p">);</span>

<span class="w">                </span><span class="c1">// Transform response</span>
<span class="w">                </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">fromOrderCapture</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">);</span>

<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">toQuoteResponse</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">            </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">OrderCaptureException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Order Capture API error creating quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IntegrationException</span><span class="p">(</span><span class="s">&quot;Failed to create quote&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Quote</span><span class="w"> </span><span class="nf">applyBusinessRules</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Apply TELUS-specific business rules</span>
<span class="w">        </span><span class="n">quote</span><span class="p">.</span><span class="na">setExpirationDate</span><span class="p">(</span><span class="n">calculateExpirationDate</span><span class="p">(</span><span class="n">quote</span><span class="p">));</span>
<span class="w">        </span><span class="n">quote</span><span class="p">.</span><span class="na">setPricing</span><span class="p">(</span><span class="n">applyDiscounts</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getPricing</span><span class="p">()));</span>
<span class="w">        </span><span class="n">quote</span><span class="p">.</span><span class="na">setTermsAndConditions</span><span class="p">(</span><span class="n">getTelus</span><span class="w"> </span><span class="nf">B2BTerms</span><span class="p">());</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="bss-system-integration">BSS System Integration<a class="headerlink" href="#bss-system-integration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">BssIntegrationClient</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">BssApiClient</span><span class="w"> </span><span class="n">bssApiClient</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">CircuitBreaker</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">CustomerInfo</span><span class="w"> </span><span class="nf">getCustomerInfo</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">customerId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">.</span><span class="na">executeSupplier</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Fetching customer info from BSS: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">customerId</span><span class="p">);</span>

<span class="w">            </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">BssCustomerResponse</span><span class="w"> </span><span class="n">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bssApiClient</span><span class="p">.</span><span class="na">getCustomer</span><span class="p">(</span><span class="n">customerId</span><span class="p">);</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">fromBssCustomer</span><span class="p">(</span><span class="n">response</span><span class="p">);</span>

<span class="w">            </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">BssApiException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;BSS API error for customer {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">customerId</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IntegrationException</span><span class="p">(</span><span class="s">&quot;Failed to retrieve customer info&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ServiceAvailability</span><span class="w"> </span><span class="nf">checkServiceAvailability</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">customerId</span><span class="p">,</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">serviceType</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">.</span><span class="na">executeSupplier</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Checking service availability for customer {} and service {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                     </span><span class="n">customerId</span><span class="p">,</span><span class="w"> </span><span class="n">serviceType</span><span class="p">);</span>

<span class="w">            </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">BssServiceAvailabilityRequest</span><span class="w"> </span><span class="n">request</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">BssServiceAvailabilityRequest</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">                    </span><span class="p">.</span><span class="na">customerId</span><span class="p">(</span><span class="n">customerId</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">serviceType</span><span class="p">(</span><span class="n">serviceType</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">                </span><span class="n">BssServiceAvailabilityResponse</span><span class="w"> </span><span class="n">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bssApiClient</span><span class="p">.</span><span class="na">checkAvailability</span><span class="p">(</span><span class="n">request</span><span class="p">);</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">fromBssAvailability</span><span class="p">(</span><span class="n">response</span><span class="p">);</span>

<span class="w">            </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">BssApiException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;BSS API error checking availability: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IntegrationException</span><span class="p">(</span><span class="s">&quot;Failed to check service availability&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="security-architecture">Security Architecture<a class="headerlink" href="#security-architecture" title="Permanent link">&para;</a></h2>
<h3 id="authentication-flow">Authentication Flow<a class="headerlink" href="#authentication-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Client
    participant Gateway
    participant Backend
    participant AuthService
    participant OCP

    Client-&gt;&gt;Gateway: Request with JWT
    Gateway-&gt;&gt;Gateway: Validate JWT
    Gateway-&gt;&gt;Backend: Forwarded Request
    Backend-&gt;&gt;Backend: Extract User Context
    Backend-&gt;&gt;AuthService: Validate Permissions
    AuthService--&gt;&gt;Backend: Permission Result
    Backend-&gt;&gt;OCP: Authorized Request
    OCP--&gt;&gt;Backend: Response
    Backend--&gt;&gt;Gateway: Response
    Gateway--&gt;&gt;Client: Final Response
</code></pre></div>
<h3 id="security-configuration">Security Configuration<a class="headerlink" href="#security-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Configuration</span>
<span class="nd">@EnableWebSecurity</span>
<span class="nd">@EnableMethodSecurity</span><span class="p">(</span><span class="n">prePostEnabled</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">SecurityConfig</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">SecurityFilterChain</span><span class="w"> </span><span class="nf">filterChain</span><span class="p">(</span><span class="n">HttpSecurity</span><span class="w"> </span><span class="n">http</span><span class="p">)</span><span class="w"> </span><span class="kd">throws</span><span class="w"> </span><span class="n">Exception</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">http</span>
<span class="w">            </span><span class="p">.</span><span class="na">csrf</span><span class="p">(</span><span class="n">csrf</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">csrf</span><span class="p">.</span><span class="na">disable</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">sessionManagement</span><span class="p">(</span><span class="n">session</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">session</span>
<span class="w">                </span><span class="p">.</span><span class="na">sessionCreationPolicy</span><span class="p">(</span><span class="n">SessionCreationPolicy</span><span class="p">.</span><span class="na">STATELESS</span><span class="p">)</span>
<span class="w">            </span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">authorizeHttpRequests</span><span class="p">(</span><span class="n">authz</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">authz</span>
<span class="w">                </span><span class="p">.</span><span class="na">requestMatchers</span><span class="p">(</span><span class="s">&quot;/actuator/health&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;/actuator/info&quot;</span><span class="p">).</span><span class="na">permitAll</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">requestMatchers</span><span class="p">(</span><span class="s">&quot;/swagger-ui/**&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;/v3/api-docs/**&quot;</span><span class="p">).</span><span class="na">permitAll</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">requestMatchers</span><span class="p">(</span><span class="n">HttpMethod</span><span class="p">.</span><span class="na">GET</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;/api/v1/quotes/**&quot;</span><span class="p">).</span><span class="na">hasRole</span><span class="p">(</span><span class="s">&quot;USER&quot;</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">requestMatchers</span><span class="p">(</span><span class="n">HttpMethod</span><span class="p">.</span><span class="na">POST</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;/api/v1/quotes/**&quot;</span><span class="p">).</span><span class="na">hasRole</span><span class="p">(</span><span class="s">&quot;QUOTE_CREATOR&quot;</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">requestMatchers</span><span class="p">(</span><span class="n">HttpMethod</span><span class="p">.</span><span class="na">PUT</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;/api/v1/quotes/**&quot;</span><span class="p">).</span><span class="na">hasRole</span><span class="p">(</span><span class="s">&quot;QUOTE_MODIFIER&quot;</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">requestMatchers</span><span class="p">(</span><span class="n">HttpMethod</span><span class="p">.</span><span class="na">DELETE</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;/api/v1/quotes/**&quot;</span><span class="p">).</span><span class="na">hasRole</span><span class="p">(</span><span class="s">&quot;ADMIN&quot;</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">anyRequest</span><span class="p">().</span><span class="na">authenticated</span><span class="p">()</span>
<span class="w">            </span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">oauth2ResourceServer</span><span class="p">(</span><span class="n">oauth2</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">oauth2</span>
<span class="w">                </span><span class="p">.</span><span class="na">jwt</span><span class="p">(</span><span class="n">jwt</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">jwt</span>
<span class="w">                    </span><span class="p">.</span><span class="na">jwtDecoder</span><span class="p">(</span><span class="n">jwtDecoder</span><span class="p">())</span>
<span class="w">                    </span><span class="p">.</span><span class="na">jwtAuthenticationConverter</span><span class="p">(</span><span class="n">jwtAuthenticationConverter</span><span class="p">())</span>
<span class="w">                </span><span class="p">)</span>
<span class="w">            </span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">http</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">JwtAuthenticationConverter</span><span class="w"> </span><span class="nf">jwtAuthenticationConverter</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">JwtGrantedAuthoritiesConverter</span><span class="w"> </span><span class="n">authoritiesConverter</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">JwtGrantedAuthoritiesConverter</span><span class="p">();</span>
<span class="w">        </span><span class="n">authoritiesConverter</span><span class="p">.</span><span class="na">setAuthorityPrefix</span><span class="p">(</span><span class="s">&quot;ROLE_&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="n">authoritiesConverter</span><span class="p">.</span><span class="na">setAuthoritiesClaimName</span><span class="p">(</span><span class="s">&quot;roles&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="n">JwtAuthenticationConverter</span><span class="w"> </span><span class="n">converter</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">JwtAuthenticationConverter</span><span class="p">();</span>
<span class="w">        </span><span class="n">converter</span><span class="p">.</span><span class="na">setJwtGrantedAuthoritiesConverter</span><span class="p">(</span><span class="n">authoritiesConverter</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">converter</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="method-level-security">Method-Level Security<a class="headerlink" href="#method-level-security" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Service</span>
<span class="nd">@PreAuthorize</span><span class="p">(</span><span class="s">&quot;hasRole(&#39;USER&#39;)&quot;</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QuoteService</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@PreAuthorize</span><span class="p">(</span><span class="s">&quot;hasRole(&#39;QUOTE_CREATOR&#39;) or @securityService.isQuoteOwner(#quoteId, authentication.name)&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">createQuote</span><span class="p">(</span><span class="n">CreateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implementation</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@PreAuthorize</span><span class="p">(</span><span class="s">&quot;hasRole(&#39;QUOTE_MODIFIER&#39;) or @securityService.isQuoteOwner(#quoteId, authentication.name)&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">updateQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">UpdateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implementation</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@PreAuthorize</span><span class="p">(</span><span class="s">&quot;hasRole(&#39;ADMIN&#39;) or @securityService.isQuoteOwner(#quoteId, authentication.name)&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">deleteQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implementation</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../02-rest-api-design/">REST API Design →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>