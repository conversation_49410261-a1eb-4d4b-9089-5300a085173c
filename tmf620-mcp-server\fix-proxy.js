#!/usr/bin/env node
/**
 * TMF620 API Connection Fix Script
 * 
 * This script updates the .env file with the correct API URL and proxy settings.
 * It fixes the API connection issues by:
 * 1. Changing the API URL from the private endpoint to the public endpoint
 * 2. Adding explicit proxy settings to disable proxy for API requests
 */

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Path to .env file
const envFilePath = path.join(__dirname, '.env');
const envBackupPath = path.join(__dirname, '.env.backup');

console.log(`${colors.blue}TMF620 API Connection Fix Script${colors.reset}`);
console.log(`${colors.blue}==============================${colors.reset}`);

// Check if .env file exists
if (!fs.existsSync(envFilePath)) {
  console.error(`${colors.red}Error: .env file not found at ${envFilePath}${colors.reset}`);
  process.exit(1);
}

// Create a backup of the .env file
try {
  fs.copyFileSync(envFilePath, envBackupPath);
  console.log(`${colors.green}Created backup of .env file at ${envBackupPath}${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Error creating backup of .env file: ${error.message}${colors.reset}`);
  process.exit(1);
}

// Read the .env file
let envContent;
try {
  envContent = fs.readFileSync(envFilePath, 'utf8');
  console.log(`${colors.green}Successfully read .env file${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Error reading .env file: ${error.message}${colors.reset}`);
  process.exit(1);
}

// Parse the .env file
const env = dotenv.parse(envContent);

// Check if the API URL is using the private endpoint
const privateEndpointPattern = /apigw-private-nane-np-001\.tsl\.telus\.com/;
let apiUrlFixed = false;
let proxySettingsFixed = false;

// Update the API URLs if they're using the private endpoint
const publicEndpoint = 'https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2';
if (privateEndpointPattern.test(env.DEV_API_URL || '')) {
  env.DEV_API_URL = publicEndpoint;
  apiUrlFixed = true;
}
if (privateEndpointPattern.test(env.TEST_API_URL || '')) {
  env.TEST_API_URL = publicEndpoint;
  apiUrlFixed = true;
}

// Add or update proxy settings
if (env.HTTP_PROXY !== '' || env.HTTPS_PROXY !== '' || env.NO_PROXY !== '*') {
  env.HTTP_PROXY = '';
  env.HTTPS_PROXY = '';
  env.NO_PROXY = '*';
  proxySettingsFixed = true;
}

// Add OAuth token URL if it doesn't exist
if (!env.OAUTH_TOKEN_URL) {
  env.OAUTH_TOKEN_URL = 'https://apigw-st.telus.com/st/token';
  console.log(`${colors.green}Added OAuth token URL${colors.reset}`);
}

// Add OAuth scope if it doesn't exist
if (!env.OAUTH_SCOPE) {
  env.OAUTH_SCOPE = '4823';
  console.log(`${colors.green}Added OAuth scope${colors.reset}`);
}

// Add test quote ID if it doesn't exist
if (!env.TEST_QUOTE_ID) {
  env.TEST_QUOTE_ID = 'a2db6e1a-27ea-419e-a7a5-10575717f8b7';
  console.log(`${colors.green}Added test quote ID${colors.reset}`);
}

// Check if any changes were made
if (!apiUrlFixed && !proxySettingsFixed) {
  console.log(`${colors.yellow}No changes needed. .env file already has the correct API URL and proxy settings.${colors.reset}`);
  process.exit(0);
}

// Generate the new .env file content
let newEnvContent = '';
for (const key in env) {
  newEnvContent += `${key}=${env[key]}\n`;
}

// Write the updated .env file
try {
  fs.writeFileSync(envFilePath, newEnvContent);
  console.log(`${colors.green}Successfully updated .env file${colors.reset}`);
  
  // Log the changes made
  if (apiUrlFixed) {
    console.log(`${colors.green}✓ Fixed API URL: Changed from private endpoint to public endpoint${colors.reset}`);
  }
  if (proxySettingsFixed) {
    console.log(`${colors.green}✓ Fixed proxy settings: Explicitly disabled proxy for API requests${colors.reset}`);
  }
  
  console.log(`\n${colors.blue}Updated .env file:${colors.reset}`);
  console.log(newEnvContent);
  
  console.log(`\n${colors.green}TMF620 API connection fix complete!${colors.reset}`);
  console.log(`${colors.green}You can now restart the server to apply the changes.${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Error writing .env file: ${error.message}${colors.reset}`);
  console.error(`${colors.red}Restoring backup...${colors.reset}`);
  
  try {
    fs.copyFileSync(envBackupPath, envFilePath);
    console.log(`${colors.green}Successfully restored backup of .env file${colors.reset}`);
  } catch (restoreError) {
    console.error(`${colors.red}Error restoring backup of .env file: ${restoreError.message}${colors.reset}`);
    console.error(`${colors.red}Backup file is available at ${envBackupPath}${colors.reset}`);
  }
  
  process.exit(1);
}
