# Technical Context: MCP Vectors Query

## Technology Stack

### Core Technologies
- **TypeScript**: Primary programming language
- **Node.js**: Runtime environment
- **Model Context Protocol (MCP)**: Communication protocol for AI assistants

### Key Dependencies
- **@modelcontextprotocol/sdk**: MCP SDK for server implementation
- **@turbopuffer/turbopuffer**: Vector operations library
- **openai**: OpenAI API client for generating embeddings
- **axios**: HTTP client for API requests
- **dotenv**: Environment configuration management

## Development Environment

### Setup Requirements
1. Node.js (latest LTS version recommended)
2. pnpm package manager
3. TypeScript
4. Access to required APIs (OpenAI, Vectors API)

### Build Process
- TypeScript compilation via `tsc`
- Executable permission setting for the main file
- Watch mode available for development

### Development Commands
- `pnpm build`: Compile TypeScript and set executable permissions
- `pnpm inspector`: Run MCP inspector for debugging
- `pnpm prepare`: Run build before publishing
- `pnpm watch`: Watch for changes and recompile

## External Dependencies

### OpenAI API
- Used for generating vector embeddings from text
- Requires API key and base URL configuration
- Supports multiple embedding models:
  - text-embedding-3-small (1536 dimensions)
  - text-embedding-3-large (3072 dimensions)
  - text-embedding-ada-002 (1536 dimensions, legacy)

### Vectors API / Turbopuffer
- Vector database for storing and querying embeddings
- Requires API key, user ID, namespace, and region
- Supports cosine distance metric for similarity search

## Configuration

### Environment Variables
- **VECTORS_API_BASE**: Base URL for Vectors API requests
- **VECTORS_USER_ID**: User ID for the Vectors API
- **VECTORS_KEY**: API key for the Vectors API
- **OPENAI_API_KEY**: OpenAI API key
- **OPENAI_API_BASE**: Base URL for OpenAI API requests
- **OPENAI_MODEL_ID**: Model ID for generating embeddings
- **TURBOPUFFER_NAMESPACE**: Namespace for Turbopuffer operations
- **TURBOPUFFER_REGION**: Region for Turbopuffer operations

## Technical Constraints

### Read-Only Operations
- The server only supports read operations (search)
- No vector creation, modification, or deletion capabilities

### Model Compatibility
- The embedding model used for queries must match the model used to create the stored vectors
- Mixing different models can lead to poor search results

### API Rate Limits
- Subject to rate limits of the OpenAI API and Vectors API
- No built-in rate limiting or throttling mechanisms

### Error Handling
- Comprehensive error checking for environment variables
- Error handling for API failures
- No automatic retry mechanism for failed requests

## Deployment

### Installation Options
- Global installation via npx
- Local development setup
- Integration with Cline/Claude Desktop

### MCP Configuration
- Server registration in MCP settings file
- Environment variable configuration in MCP settings
