{"name": "@telus/mcp-firestore", "version": "1.0.0", "description": "Handle Firestore database interaction", "keywords": ["telus", "mcp", "firestore"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-firestore"}, "license": "MIT", "author": "TELUS", "type": "module", "main": "dist/index.js", "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "prepare": "pnpm run build", "start": "node dist/index.js"}, "dependencies": {"@google-cloud/firestore": "7.11.0", "@modelcontextprotocol/sdk": "^1.7.0"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}