
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/frontend/02-custom-components/">
      
      
        <link rel="prev" href="../01-architecture/">
      
      
        <link rel="next" href="../03-state-management/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Custom Components - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#custom-telus-components" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Custom Components
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#component-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Component Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Component Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#component-categories" class="md-nav__link">
    <span class="md-ellipsis">
      Component Categories
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-architecture-pattern" class="md-nav__link">
    <span class="md-ellipsis">
      Component Architecture Pattern
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quote-management-components" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Management Components
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Management Components">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#telus-start-quotation" class="md-nav__link">
    <span class="md-ellipsis">
      telus-start-quotation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#telus-quote-view" class="md-nav__link">
    <span class="md-ellipsis">
      telus-quote-view
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#location-services-components" class="md-nav__link">
    <span class="md-ellipsis">
      Location Services Components
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Location Services Components">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#telus-assign-location" class="md-nav__link">
    <span class="md-ellipsis">
      telus-assign-location
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#telus-location-picker" class="md-nav__link">
    <span class="md-ellipsis">
      telus-location-picker
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#cart-and-pricing-components" class="md-nav__link">
    <span class="md-ellipsis">
      Cart and Pricing Components
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Cart and Pricing Components">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#telus-cart-total-prices" class="md-nav__link">
    <span class="md-ellipsis">
      telus-cart-total-prices
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#component-integration-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Component Integration Patterns
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Component Integration Patterns">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#parent-child-communication" class="md-nav__link">
    <span class="md-ellipsis">
      Parent-Child Communication
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#state-management-integration" class="md-nav__link">
    <span class="md-ellipsis">
      State Management Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Development Guidelines
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Guidelines">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#component-creation-checklist" class="md-nav__link">
    <span class="md-ellipsis">
      Component Creation Checklist
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#styling-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Styling Guidelines
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#component-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Component Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Component Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#component-categories" class="md-nav__link">
    <span class="md-ellipsis">
      Component Categories
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-architecture-pattern" class="md-nav__link">
    <span class="md-ellipsis">
      Component Architecture Pattern
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quote-management-components" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Management Components
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Management Components">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#telus-start-quotation" class="md-nav__link">
    <span class="md-ellipsis">
      telus-start-quotation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#telus-quote-view" class="md-nav__link">
    <span class="md-ellipsis">
      telus-quote-view
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#location-services-components" class="md-nav__link">
    <span class="md-ellipsis">
      Location Services Components
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Location Services Components">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#telus-assign-location" class="md-nav__link">
    <span class="md-ellipsis">
      telus-assign-location
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#telus-location-picker" class="md-nav__link">
    <span class="md-ellipsis">
      telus-location-picker
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#cart-and-pricing-components" class="md-nav__link">
    <span class="md-ellipsis">
      Cart and Pricing Components
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Cart and Pricing Components">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#telus-cart-total-prices" class="md-nav__link">
    <span class="md-ellipsis">
      telus-cart-total-prices
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#component-integration-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Component Integration Patterns
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Component Integration Patterns">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#parent-child-communication" class="md-nav__link">
    <span class="md-ellipsis">
      Parent-Child Communication
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#state-management-integration" class="md-nav__link">
    <span class="md-ellipsis">
      State Management Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Development Guidelines
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Guidelines">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#component-creation-checklist" class="md-nav__link">
    <span class="md-ellipsis">
      Component Creation Checklist
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#styling-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Styling Guidelines
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="custom-telus-components">Custom TELUS Components<a class="headerlink" href="#custom-telus-components" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#component-overview">Component Overview</a></li>
<li><a href="#quote-management-components">Quote Management Components</a></li>
<li><a href="#location-services-components">Location Services Components</a></li>
<li><a href="#cart-and-pricing-components">Cart and Pricing Components</a></li>
<li><a href="#service-selection-components">Service Selection Components</a></li>
<li><a href="#component-integration-patterns">Component Integration Patterns</a></li>
<li><a href="#development-guidelines">Development Guidelines</a></li>
</ul>
<h2 id="component-overview">Component Overview<a class="headerlink" href="#component-overview" title="Permanent link">&para;</a></h2>
<p>The frontend application includes <strong>18+ custom TELUS components</strong> specifically designed for B2B telecommunications workflows. These components encapsulate complex business logic, provide consistent user experiences, and integrate seamlessly with the NgRx state management system.</p>
<h3 id="component-categories">Component Categories<a class="headerlink" href="#component-categories" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Custom TELUS Components&quot;
        A[Quote Management]
        B[Location Services]
        C[Cart &amp; Pricing]
        D[Service Selection]
        E[Order Processing]
        F[Customer Management]
    end

    subgraph &quot;Quote Components&quot;
        G[telus-start-quotation]
        H[telus-quote-view]
        I[telus-quote-comparison]
        J[telus-quote-history]
    end

    subgraph &quot;Location Components&quot;
        K[telus-assign-location]
        L[telus-location-picker]
        M[telus-service-availability]
        N[telus-address-validator]
    end

    subgraph &quot;Cart Components&quot;
        O[telus-cart-total-prices]
        P[telus-service-configurator]
        Q[telus-discount-manager]
        R[telus-tax-calculator]
    end

    A --&gt; G
    A --&gt; H
    A --&gt; I
    A --&gt; J

    B --&gt; K
    B --&gt; L
    B --&gt; M
    B --&gt; N

    C --&gt; O
    C --&gt; P
    C --&gt; Q
    C --&gt; R

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style C fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="component-architecture-pattern">Component Architecture Pattern<a class="headerlink" href="#component-architecture-pattern" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Base TELUS Component Pattern</span>
<span class="kd">@Component</span><span class="p">({</span>
<span class="w">  </span><span class="nx">selector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;telus-base-component&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">templateUrl</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;./telus-base-component.component.html&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">styleUrls</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;./telus-base-component.component.less&#39;</span><span class="p">],</span>
<span class="w">  </span><span class="nx">changeDetection</span><span class="o">:</span><span class="w"> </span><span class="kt">ChangeDetectionStrategy.OnPush</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="k">abstract</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">TelusBaseComponent</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">OnInit</span><span class="p">,</span><span class="w"> </span><span class="nx">OnDestroy</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">config</span><span class="o">:</span><span class="w"> </span><span class="kt">ComponentConfig</span><span class="p">;</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">actionCompleted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">ActionResult</span><span class="o">&gt;</span><span class="p">();</span>

<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="nx">destroy$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">Subject</span><span class="o">&lt;</span><span class="ow">void</span><span class="o">&gt;</span><span class="p">();</span>
<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="nx">loading$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">BehaviorSubject</span><span class="o">&lt;</span><span class="kt">boolean</span><span class="o">&gt;</span><span class="p">(</span><span class="kc">false</span><span class="p">);</span>
<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="nx">error$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">BehaviorSubject</span><span class="o">&lt;</span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="o">&gt;</span><span class="p">(</span><span class="kc">null</span><span class="p">);</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">protected</span><span class="w"> </span><span class="nx">store</span><span class="o">:</span><span class="w"> </span><span class="kt">Store</span><span class="o">&lt;</span><span class="nx">AppState</span><span class="o">&gt;</span><span class="p">,</span>
<span class="w">    </span><span class="k">protected</span><span class="w"> </span><span class="nx">cdr</span><span class="o">:</span><span class="w"> </span><span class="kt">ChangeDetectorRef</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="nx">ngOnInit</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">initializeComponent</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">ngOnDestroy</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">destroy$</span><span class="p">.</span><span class="nx">next</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">destroy$</span><span class="p">.</span><span class="nx">complete</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="k">abstract</span><span class="w"> </span><span class="nx">initializeComponent</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="p">;</span>
<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="k">abstract</span><span class="w"> </span><span class="nx">handleError</span><span class="p">(</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">any</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="quote-management-components">Quote Management Components<a class="headerlink" href="#quote-management-components" title="Permanent link">&para;</a></h2>
<h3 id="telus-start-quotation">telus-start-quotation<a class="headerlink" href="#telus-start-quotation" title="Permanent link">&para;</a></h3>
<p><strong>Purpose</strong>: Initiates the quote generation process with service selection and customer information capture.</p>
<div class="highlight"><pre><span></span><code><span class="kd">@Component</span><span class="p">({</span>
<span class="w">  </span><span class="nx">selector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;telus-start-quotation&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">templateUrl</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;./telus-start-quotation.component.html&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">styleUrls</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;./telus-start-quotation.component.less&#39;</span><span class="p">]</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">TelusStartQuotationComponent</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">TelusBaseComponent</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">customerId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">preselectedServices</span><span class="o">:</span><span class="w"> </span><span class="kt">Service</span><span class="p">[]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">quoteStarted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">Quote</span><span class="o">&gt;</span><span class="p">();</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">serviceSelected</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">Service</span><span class="o">&gt;</span><span class="p">();</span>

<span class="w">  </span><span class="nx">quotationForm</span><span class="o">:</span><span class="w"> </span><span class="kt">FormGroup</span><span class="p">;</span>
<span class="w">  </span><span class="nx">availableServices$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectAvailableServices</span><span class="p">);</span>
<span class="w">  </span><span class="nx">serviceCategories$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectServiceCategories</span><span class="p">);</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">fb</span><span class="o">:</span><span class="w"> </span><span class="kt">FormBuilder</span><span class="p">,</span>
<span class="w">    </span><span class="nx">store</span><span class="o">:</span><span class="w"> </span><span class="kt">Store</span><span class="o">&lt;</span><span class="nx">AppState</span><span class="o">&gt;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">cdr</span><span class="o">:</span><span class="w"> </span><span class="kt">ChangeDetectorRef</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">quoteService</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteService</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">super</span><span class="p">(</span><span class="nx">store</span><span class="p">,</span><span class="w"> </span><span class="nx">cdr</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="nx">initializeComponent</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">buildForm</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">loadAvailableServices</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">setupFormValidation</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">buildForm</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">quotationForm</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">fb</span><span class="p">.</span><span class="nx">group</span><span class="p">({</span>
<span class="w">      </span><span class="nx">customerId</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="k">this</span><span class="p">.</span><span class="nx">customerId</span><span class="p">,</span><span class="w"> </span><span class="nx">Validators</span><span class="p">.</span><span class="nx">required</span><span class="p">],</span>
<span class="w">      </span><span class="nx">serviceType</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">Validators</span><span class="p">.</span><span class="nx">required</span><span class="p">],</span>
<span class="w">      </span><span class="nx">location</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">Validators</span><span class="p">.</span><span class="nx">required</span><span class="p">],</span>
<span class="w">      </span><span class="nx">requirements</span><span class="o">:</span><span class="w"> </span><span class="kt">this.fb.group</span><span class="p">({</span>
<span class="w">        </span><span class="nx">bandwidth</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;&#39;</span><span class="p">],</span>
<span class="w">        </span><span class="nx">redundancy</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="kc">false</span><span class="p">],</span>
<span class="w">        </span><span class="nx">sla</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;standard&#39;</span><span class="p">]</span>
<span class="w">      </span><span class="p">}),</span>
<span class="w">      </span><span class="nx">timeline</span><span class="o">:</span><span class="w"> </span><span class="kt">this.fb.group</span><span class="p">({</span>
<span class="w">        </span><span class="nx">startDate</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">Validators</span><span class="p">.</span><span class="nx">required</span><span class="p">],</span>
<span class="w">        </span><span class="nx">endDate</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;&#39;</span><span class="p">]</span>
<span class="w">      </span><span class="p">})</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">startQuotation</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">quotationForm</span><span class="p">.</span><span class="nx">valid</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">quoteRequest</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteRequest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="p">...</span><span class="k">this</span><span class="p">.</span><span class="nx">quotationForm</span><span class="p">.</span><span class="nx">value</span><span class="p">,</span>
<span class="w">        </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">(),</span>
<span class="w">        </span><span class="nx">status</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;draft&#39;</span>
<span class="w">      </span><span class="p">};</span>

<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">dispatch</span><span class="p">(</span><span class="nx">startQuotation</span><span class="p">({</span><span class="w"> </span><span class="nx">quoteRequest</span><span class="w"> </span><span class="p">}));</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">loading$</span><span class="p">.</span><span class="nx">next</span><span class="p">(</span><span class="kc">true</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">selectService</span><span class="p">(</span><span class="nx">service</span><span class="o">:</span><span class="w"> </span><span class="kt">Service</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">serviceSelected</span><span class="p">.</span><span class="nx">emit</span><span class="p">(</span><span class="nx">service</span><span class="p">);</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">quotationForm</span><span class="p">.</span><span class="nx">patchValue</span><span class="p">({</span>
<span class="w">      </span><span class="nx">serviceType</span><span class="o">:</span><span class="w"> </span><span class="kt">service.type</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="telus-quote-view">telus-quote-view<a class="headerlink" href="#telus-quote-view" title="Permanent link">&para;</a></h3>
<p><strong>Purpose</strong>: Displays comprehensive quote information with interactive elements for quote management.</p>
<div class="highlight"><pre><span></span><code><span class="kd">@Component</span><span class="p">({</span>
<span class="w">  </span><span class="nx">selector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;telus-quote-view&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">templateUrl</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;./telus-quote-view.component.html&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">styleUrls</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;./telus-quote-view.component.less&#39;</span><span class="p">]</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">TelusQuoteViewComponent</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">TelusBaseComponent</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="k">readonly</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">quoteUpdated</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">Quote</span><span class="o">&gt;</span><span class="p">();</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">quoteApproved</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">Quote</span><span class="o">&gt;</span><span class="p">();</span>

<span class="w">  </span><span class="nx">quote$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectQuoteById</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">quoteId</span><span class="p">));</span>
<span class="w">  </span><span class="nx">quoteHistory$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectQuoteHistory</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">quoteId</span><span class="p">));</span>
<span class="w">  </span><span class="nx">canEdit$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectCanEditQuote</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">quoteId</span><span class="p">));</span>

<span class="w">  </span><span class="nx">displayColumns</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;service&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;quantity&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;unitPrice&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;totalPrice&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;actions&#39;</span><span class="p">];</span>

<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="nx">initializeComponent</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">loadQuoteDetails</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">setupQuoteSubscriptions</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">loadQuoteDetails</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">dispatch</span><span class="p">(</span><span class="nx">loadQuote</span><span class="p">({</span><span class="w"> </span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">this.quoteId</span><span class="w"> </span><span class="p">}));</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">editQuote</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">dispatch</span><span class="p">(</span><span class="nx">setQuoteEditMode</span><span class="p">({</span><span class="w"> </span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">this.quoteId</span><span class="p">,</span><span class="w"> </span><span class="nx">editMode</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="w"> </span><span class="p">}));</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">approveQuote</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">quote$</span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">take</span><span class="p">(</span><span class="mf">1</span><span class="p">),</span>
<span class="w">      </span><span class="nx">filter</span><span class="p">(</span><span class="nx">quote</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="o">!!</span><span class="nx">quote</span><span class="p">)</span>
<span class="w">    </span><span class="p">).</span><span class="nx">subscribe</span><span class="p">(</span><span class="nx">quote</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">dispatch</span><span class="p">(</span><span class="nx">approveQuote</span><span class="p">({</span><span class="w"> </span><span class="nx">quote</span><span class="w"> </span><span class="p">}));</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">quoteApproved</span><span class="p">.</span><span class="nx">emit</span><span class="p">(</span><span class="nx">quote</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">exportQuote</span><span class="p">(</span><span class="nx">format</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;pdf&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;excel&#39;</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">dispatch</span><span class="p">(</span><span class="nx">exportQuote</span><span class="p">({</span><span class="w"> </span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">this.quoteId</span><span class="p">,</span><span class="w"> </span><span class="nx">format</span><span class="w"> </span><span class="p">}));</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="location-services-components">Location Services Components<a class="headerlink" href="#location-services-components" title="Permanent link">&para;</a></h2>
<h3 id="telus-assign-location">telus-assign-location<a class="headerlink" href="#telus-assign-location" title="Permanent link">&para;</a></h3>
<p><strong>Purpose</strong>: Handles location assignment with address validation and service availability checking.</p>
<div class="highlight"><pre><span></span><code><span class="kd">@Component</span><span class="p">({</span>
<span class="w">  </span><span class="nx">selector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;telus-assign-location&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">templateUrl</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;./telus-assign-location.component.html&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">styleUrls</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;./telus-assign-location.component.less&#39;</span><span class="p">]</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">TelusAssignLocationComponent</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">TelusBaseComponent</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">allowMultipleLocations</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">locationAssigned</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">Location</span><span class="o">&gt;</span><span class="p">();</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">serviceAvailabilityChecked</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">ServiceAvailability</span><span class="o">&gt;</span><span class="p">();</span>

<span class="w">  </span><span class="nx">locationForm</span><span class="o">:</span><span class="w"> </span><span class="kt">FormGroup</span><span class="p">;</span>
<span class="w">  </span><span class="nx">searchResults</span><span class="o">:</span><span class="w"> </span><span class="kt">Location</span><span class="p">[]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>
<span class="w">  </span><span class="nx">selectedLocation</span><span class="o">:</span><span class="w"> </span><span class="kt">Location</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="nx">serviceAvailability</span><span class="o">:</span><span class="w"> </span><span class="kt">ServiceAvailability</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">fb</span><span class="o">:</span><span class="w"> </span><span class="kt">FormBuilder</span><span class="p">,</span>
<span class="w">    </span><span class="nx">store</span><span class="o">:</span><span class="w"> </span><span class="kt">Store</span><span class="o">&lt;</span><span class="nx">AppState</span><span class="o">&gt;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">cdr</span><span class="o">:</span><span class="w"> </span><span class="kt">ChangeDetectorRef</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">locationService</span><span class="o">:</span><span class="w"> </span><span class="kt">LocationService</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">mapsService</span><span class="o">:</span><span class="w"> </span><span class="kt">GoogleMapsService</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">super</span><span class="p">(</span><span class="nx">store</span><span class="p">,</span><span class="w"> </span><span class="nx">cdr</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="nx">initializeComponent</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">buildLocationForm</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">setupAddressSearch</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">setupMapIntegration</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">buildLocationForm</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">locationForm</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">fb</span><span class="p">.</span><span class="nx">group</span><span class="p">({</span>
<span class="w">      </span><span class="nx">address</span><span class="o">:</span><span class="w"> </span><span class="kt">this.fb.group</span><span class="p">({</span>
<span class="w">        </span><span class="nx">street</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">Validators</span><span class="p">.</span><span class="nx">required</span><span class="p">],</span>
<span class="w">        </span><span class="nx">city</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">Validators</span><span class="p">.</span><span class="nx">required</span><span class="p">],</span>
<span class="w">        </span><span class="nx">province</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">Validators</span><span class="p">.</span><span class="nx">required</span><span class="p">],</span>
<span class="w">        </span><span class="nx">postalCode</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">[</span><span class="nx">Validators</span><span class="p">.</span><span class="nx">required</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">postalCodeValidator</span><span class="p">]]</span>
<span class="w">      </span><span class="p">}),</span>
<span class="w">      </span><span class="nx">coordinates</span><span class="o">:</span><span class="w"> </span><span class="kt">this.fb.group</span><span class="p">({</span>
<span class="w">        </span><span class="nx">latitude</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;&#39;</span><span class="p">],</span>
<span class="w">        </span><span class="nx">longitude</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;&#39;</span><span class="p">]</span>
<span class="w">      </span><span class="p">}),</span>
<span class="w">      </span><span class="nx">locationType</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;business&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">Validators</span><span class="p">.</span><span class="nx">required</span><span class="p">],</span>
<span class="w">      </span><span class="nx">accessInstructions</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;&#39;</span><span class="p">]</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">searchAddresses</span><span class="p">(</span><span class="nx">query</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">query</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mf">3</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">locationService</span><span class="p">.</span><span class="nx">searchAddresses</span><span class="p">(</span><span class="nx">query</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">          </span><span class="nx">debounceTime</span><span class="p">(</span><span class="mf">300</span><span class="p">),</span>
<span class="w">          </span><span class="nx">distinctUntilChanged</span><span class="p">(),</span>
<span class="w">          </span><span class="nx">takeUntil</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">destroy$</span><span class="p">)</span>
<span class="w">        </span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="nx">subscribe</span><span class="p">(</span><span class="nx">results</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">this</span><span class="p">.</span><span class="nx">searchResults</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">results</span><span class="p">;</span>
<span class="w">          </span><span class="k">this</span><span class="p">.</span><span class="nx">cdr</span><span class="p">.</span><span class="nx">markForCheck</span><span class="p">();</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">selectAddress</span><span class="p">(</span><span class="nx">address</span><span class="o">:</span><span class="w"> </span><span class="kt">Address</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">locationForm</span><span class="p">.</span><span class="nx">patchValue</span><span class="p">({</span><span class="w"> </span><span class="nx">address</span><span class="w"> </span><span class="p">});</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">validateAddress</span><span class="p">(</span><span class="nx">address</span><span class="p">);</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">checkServiceAvailability</span><span class="p">(</span><span class="nx">address</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">validateAddress</span><span class="p">(</span><span class="nx">address</span><span class="o">:</span><span class="w"> </span><span class="kt">Address</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">mapsService</span><span class="p">.</span><span class="nx">validateAddress</span><span class="p">(</span><span class="nx">address</span><span class="p">)</span>
<span class="w">      </span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span><span class="nx">takeUntil</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">destroy$</span><span class="p">))</span>
<span class="w">      </span><span class="p">.</span><span class="nx">subscribe</span><span class="p">(</span><span class="nx">validation</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">validation</span><span class="p">.</span><span class="nx">isValid</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">this</span><span class="p">.</span><span class="nx">locationForm</span><span class="p">.</span><span class="nx">patchValue</span><span class="p">({</span>
<span class="w">            </span><span class="nx">coordinates</span><span class="o">:</span><span class="w"> </span><span class="kt">validation.coordinates</span>
<span class="w">          </span><span class="p">});</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">this</span><span class="p">.</span><span class="nx">error$</span><span class="p">.</span><span class="nx">next</span><span class="p">(</span><span class="s1">&#39;Address validation failed&#39;</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">checkServiceAvailability</span><span class="p">(</span><span class="nx">address</span><span class="o">:</span><span class="w"> </span><span class="kt">Address</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">locationService</span><span class="p">.</span><span class="nx">checkServiceAvailability</span><span class="p">(</span><span class="nx">address</span><span class="p">)</span>
<span class="w">      </span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span><span class="nx">takeUntil</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">destroy$</span><span class="p">))</span>
<span class="w">      </span><span class="p">.</span><span class="nx">subscribe</span><span class="p">(</span><span class="nx">availability</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">serviceAvailability</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">availability</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">serviceAvailabilityChecked</span><span class="p">.</span><span class="nx">emit</span><span class="p">(</span><span class="nx">availability</span><span class="p">);</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">cdr</span><span class="p">.</span><span class="nx">markForCheck</span><span class="p">();</span>
<span class="w">      </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">assignLocation</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">locationForm</span><span class="p">.</span><span class="nx">valid</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">serviceAvailability</span><span class="o">?</span><span class="p">.</span><span class="nx">available</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">location</span><span class="o">:</span><span class="w"> </span><span class="kt">Location</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="p">...</span><span class="k">this</span><span class="p">.</span><span class="nx">locationForm</span><span class="p">.</span><span class="nx">value</span><span class="p">,</span>
<span class="w">        </span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">this.quoteId</span><span class="p">,</span>
<span class="w">        </span><span class="nx">serviceAvailability</span><span class="o">:</span><span class="w"> </span><span class="kt">this.serviceAvailability</span>
<span class="w">      </span><span class="p">};</span>

<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">dispatch</span><span class="p">(</span><span class="nx">assignLocation</span><span class="p">({</span><span class="w"> </span><span class="nx">location</span><span class="w"> </span><span class="p">}));</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">locationAssigned</span><span class="p">.</span><span class="nx">emit</span><span class="p">(</span><span class="nx">location</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">postalCodeValidator</span><span class="p">(</span><span class="nx">control</span><span class="o">:</span><span class="w"> </span><span class="kt">AbstractControl</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">ValidationErrors</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">postalCode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">control</span><span class="p">.</span><span class="nx">value</span><span class="p">;</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">canadianPostalCodePattern</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="sr">/^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/</span><span class="p">;</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">postalCode</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="o">!</span><span class="nx">canadianPostalCodePattern</span><span class="p">.</span><span class="nx">test</span><span class="p">(</span><span class="nx">postalCode</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">invalidPostalCode</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="w"> </span><span class="p">};</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="telus-location-picker">telus-location-picker<a class="headerlink" href="#telus-location-picker" title="Permanent link">&para;</a></h3>
<p><strong>Purpose</strong>: Interactive map-based location selection with service area visualization.</p>
<div class="highlight"><pre><span></span><code><span class="kd">@Component</span><span class="p">({</span>
<span class="w">  </span><span class="nx">selector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;telus-location-picker&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">templateUrl</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;./telus-location-picker.component.html&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">styleUrls</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;./telus-location-picker.component.less&#39;</span><span class="p">]</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">TelusLocationPickerComponent</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">TelusBaseComponent</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">AfterViewInit</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">@ViewChild</span><span class="p">(</span><span class="s1">&#39;mapContainer&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">static</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="nx">mapContainer</span><span class="o">:</span><span class="w"> </span><span class="kt">ElementRef</span><span class="p">;</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">initialLocation</span><span class="o">:</span><span class="w"> </span><span class="kt">Location</span><span class="p">;</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">serviceAreas</span><span class="o">:</span><span class="w"> </span><span class="kt">ServiceArea</span><span class="p">[]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">locationSelected</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">Location</span><span class="o">&gt;</span><span class="p">();</span>

<span class="w">  </span><span class="nx">map</span><span class="o">:</span><span class="w"> </span><span class="kt">google.maps.Map</span><span class="p">;</span>
<span class="w">  </span><span class="nx">markers</span><span class="o">:</span><span class="w"> </span><span class="kt">google.maps.Marker</span><span class="p">[]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>
<span class="w">  </span><span class="nx">serviceAreaOverlays</span><span class="o">:</span><span class="w"> </span><span class="kt">google.maps.Polygon</span><span class="p">[]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[];</span>

<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="nx">initializeComponent</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">loadGoogleMapsAPI</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">ngAfterViewInit</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">initializeMap</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">initializeMap</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">mapOptions</span><span class="o">:</span><span class="w"> </span><span class="kt">google.maps.MapOptions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">center</span><span class="o">:</span><span class="w"> </span><span class="kt">this.initialLocation?.coordinates</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">lat</span><span class="o">:</span><span class="w"> </span><span class="kt">43.6532</span><span class="p">,</span><span class="w"> </span><span class="nx">lng</span><span class="o">:</span><span class="w"> </span><span class="o">-</span><span class="mf">79.3832</span><span class="w"> </span><span class="p">},</span><span class="w"> </span><span class="c1">// Toronto</span>
<span class="w">      </span><span class="nx">zoom</span><span class="o">:</span><span class="w"> </span><span class="kt">12</span><span class="p">,</span>
<span class="w">      </span><span class="nx">mapTypeId</span><span class="o">:</span><span class="w"> </span><span class="kt">google.maps.MapTypeId.ROADMAP</span>
<span class="w">    </span><span class="p">};</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">map</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">google</span><span class="p">.</span><span class="nx">maps</span><span class="p">.</span><span class="nb">Map</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">mapContainer</span><span class="p">.</span><span class="nx">nativeElement</span><span class="p">,</span><span class="w"> </span><span class="nx">mapOptions</span><span class="p">);</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">setupMapEventListeners</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">displayServiceAreas</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">setupMapEventListeners</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">map</span><span class="p">.</span><span class="nx">addListener</span><span class="p">(</span><span class="s1">&#39;click&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="o">:</span><span class="w"> </span><span class="kt">google.maps.MapMouseEvent</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">handleMapClick</span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">latLng</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">handleMapClick</span><span class="p">(</span><span class="nx">latLng</span><span class="o">:</span><span class="w"> </span><span class="kt">google.maps.LatLng</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">location</span><span class="o">:</span><span class="w"> </span><span class="kt">Location</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">coordinates</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">latitude</span><span class="o">:</span><span class="w"> </span><span class="kt">latLng.lat</span><span class="p">(),</span>
<span class="w">        </span><span class="nx">longitude</span><span class="o">:</span><span class="w"> </span><span class="kt">latLng.lng</span><span class="p">()</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">()</span>
<span class="w">    </span><span class="p">};</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">reverseGeocode</span><span class="p">(</span><span class="nx">latLng</span><span class="p">)</span>
<span class="w">      </span><span class="p">.</span><span class="nx">then</span><span class="p">(</span><span class="nx">address</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">location</span><span class="p">.</span><span class="nx">address</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">address</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">addMarker</span><span class="p">(</span><span class="nx">latLng</span><span class="p">,</span><span class="w"> </span><span class="nx">address</span><span class="p">);</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">locationSelected</span><span class="p">.</span><span class="nx">emit</span><span class="p">(</span><span class="nx">location</span><span class="p">);</span>
<span class="w">      </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">reverseGeocode</span><span class="p">(</span><span class="nx">latLng</span><span class="o">:</span><span class="w"> </span><span class="kt">google.maps.LatLng</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">Address</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">geocoder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">google</span><span class="p">.</span><span class="nx">maps</span><span class="p">.</span><span class="nx">Geocoder</span><span class="p">();</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Promise</span><span class="p">((</span><span class="nx">resolve</span><span class="p">,</span><span class="w"> </span><span class="nx">reject</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">geocoder</span><span class="p">.</span><span class="nx">geocode</span><span class="p">({</span><span class="w"> </span><span class="nx">location</span><span class="o">:</span><span class="w"> </span><span class="kt">latLng</span><span class="w"> </span><span class="p">},</span><span class="w"> </span><span class="p">(</span><span class="nx">results</span><span class="p">,</span><span class="w"> </span><span class="nx">status</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">status</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="s1">&#39;OK&#39;</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">results</span><span class="p">[</span><span class="mf">0</span><span class="p">])</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="kd">const</span><span class="w"> </span><span class="nx">address</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">parseGoogleAddress</span><span class="p">(</span><span class="nx">results</span><span class="p">[</span><span class="mf">0</span><span class="p">]);</span>
<span class="w">          </span><span class="nx">resolve</span><span class="p">(</span><span class="nx">address</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nx">reject</span><span class="p">(</span><span class="s1">&#39;Geocoding failed&#39;</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">});</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">displayServiceAreas</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">serviceAreas</span><span class="p">.</span><span class="nx">forEach</span><span class="p">(</span><span class="nx">area</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">polygon</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">google</span><span class="p">.</span><span class="nx">maps</span><span class="p">.</span><span class="nx">Polygon</span><span class="p">({</span>
<span class="w">        </span><span class="nx">paths</span><span class="o">:</span><span class="w"> </span><span class="kt">area.boundaries</span><span class="p">,</span>
<span class="w">        </span><span class="nx">strokeColor</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;#673ab7&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">strokeOpacity</span><span class="o">:</span><span class="w"> </span><span class="kt">0.8</span><span class="p">,</span>
<span class="w">        </span><span class="nx">strokeWeight</span><span class="o">:</span><span class="w"> </span><span class="kt">2</span><span class="p">,</span>
<span class="w">        </span><span class="nx">fillColor</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;#9c27b0&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">fillOpacity</span><span class="o">:</span><span class="w"> </span><span class="kt">0.2</span>
<span class="w">      </span><span class="p">});</span>

<span class="w">      </span><span class="nx">polygon</span><span class="p">.</span><span class="nx">setMap</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">map</span><span class="p">);</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">serviceAreaOverlays</span><span class="p">.</span><span class="nx">push</span><span class="p">(</span><span class="nx">polygon</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="cart-and-pricing-components">Cart and Pricing Components<a class="headerlink" href="#cart-and-pricing-components" title="Permanent link">&para;</a></h2>
<h3 id="telus-cart-total-prices">telus-cart-total-prices<a class="headerlink" href="#telus-cart-total-prices" title="Permanent link">&para;</a></h3>
<p><strong>Purpose</strong>: Real-time pricing calculations with discount application and tax computation.</p>
<div class="highlight"><pre><span></span><code><span class="kd">@Component</span><span class="p">({</span>
<span class="w">  </span><span class="nx">selector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;telus-cart-total-prices&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">templateUrl</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;./telus-cart-total-prices.component.html&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">styleUrls</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;./telus-cart-total-prices.component.less&#39;</span><span class="p">],</span>
<span class="w">  </span><span class="nx">changeDetection</span><span class="o">:</span><span class="w"> </span><span class="kt">ChangeDetectionStrategy.OnPush</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">TelusCartTotalPricesComponent</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">TelusBaseComponent</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">cartItems</span><span class="o">:</span><span class="w"> </span><span class="kt">CartItem</span><span class="p">[];</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">showDetailedBreakdown</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">priceUpdated</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">PriceCalculation</span><span class="o">&gt;</span><span class="p">();</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">discountApplied</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">Discount</span><span class="o">&gt;</span><span class="p">();</span>

<span class="w">  </span><span class="nx">subtotal$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectCartSubtotal</span><span class="p">);</span>
<span class="w">  </span><span class="nx">discounts$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectAppliedDiscounts</span><span class="p">);</span>
<span class="w">  </span><span class="nx">taxes$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectCalculatedTaxes</span><span class="p">);</span>
<span class="w">  </span><span class="nx">total$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectCartTotal</span><span class="p">);</span>

<span class="w">  </span><span class="nx">discountForm</span><span class="o">:</span><span class="w"> </span><span class="kt">FormGroup</span><span class="p">;</span>
<span class="w">  </span><span class="nx">availableDiscounts$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectAvailableDiscounts</span><span class="p">);</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">fb</span><span class="o">:</span><span class="w"> </span><span class="kt">FormBuilder</span><span class="p">,</span>
<span class="w">    </span><span class="nx">store</span><span class="o">:</span><span class="w"> </span><span class="kt">Store</span><span class="o">&lt;</span><span class="nx">AppState</span><span class="o">&gt;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">cdr</span><span class="o">:</span><span class="w"> </span><span class="kt">ChangeDetectorRef</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">pricingService</span><span class="o">:</span><span class="w"> </span><span class="kt">PricingService</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">super</span><span class="p">(</span><span class="nx">store</span><span class="p">,</span><span class="w"> </span><span class="nx">cdr</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="nx">initializeComponent</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">buildDiscountForm</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">setupPriceCalculations</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">subscribeToCartChanges</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">buildDiscountForm</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">discountForm</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">fb</span><span class="p">.</span><span class="nx">group</span><span class="p">({</span>
<span class="w">      </span><span class="nx">discountCode</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">[</span><span class="nx">Validators</span><span class="p">.</span><span class="nx">required</span><span class="p">,</span><span class="w"> </span><span class="nx">Validators</span><span class="p">.</span><span class="nx">minLength</span><span class="p">(</span><span class="mf">3</span><span class="p">)]]</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">setupPriceCalculations</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">combineLatest</span><span class="p">([</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">subtotal$</span><span class="p">,</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">discounts$</span><span class="p">,</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">taxes$</span>
<span class="w">    </span><span class="p">]).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">takeUntil</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">destroy$</span><span class="p">),</span>
<span class="w">      </span><span class="nx">debounceTime</span><span class="p">(</span><span class="mf">100</span><span class="p">)</span>
<span class="w">    </span><span class="p">).</span><span class="nx">subscribe</span><span class="p">(([</span><span class="nx">subtotal</span><span class="p">,</span><span class="w"> </span><span class="nx">discounts</span><span class="p">,</span><span class="w"> </span><span class="nx">taxes</span><span class="p">])</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">calculation</span><span class="o">:</span><span class="w"> </span><span class="kt">PriceCalculation</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">subtotal</span><span class="p">,</span>
<span class="w">        </span><span class="nx">discounts</span><span class="p">,</span>
<span class="w">        </span><span class="nx">taxes</span><span class="p">,</span>
<span class="w">        </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">this.calculateTotal</span><span class="p">(</span><span class="nx">subtotal</span><span class="p">,</span><span class="w"> </span><span class="nx">discounts</span><span class="p">,</span><span class="w"> </span><span class="nx">taxes</span><span class="p">),</span>
<span class="w">        </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">()</span>
<span class="w">      </span><span class="p">};</span>

<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">priceUpdated</span><span class="p">.</span><span class="nx">emit</span><span class="p">(</span><span class="nx">calculation</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">applyDiscount</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">discountForm</span><span class="p">.</span><span class="nx">valid</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">discountCode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">discountForm</span><span class="p">.</span><span class="nx">value</span><span class="p">.</span><span class="nx">discountCode</span><span class="p">;</span>

<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">dispatch</span><span class="p">(</span><span class="nx">applyDiscount</span><span class="p">({</span><span class="w"> </span><span class="nx">code</span><span class="o">:</span><span class="w"> </span><span class="kt">discountCode</span><span class="w"> </span><span class="p">}));</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">loading$</span><span class="p">.</span><span class="nx">next</span><span class="p">(</span><span class="kc">true</span><span class="p">);</span>

<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectDiscountApplicationResult</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">          </span><span class="nx">filter</span><span class="p">(</span><span class="nx">result</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="o">!!</span><span class="nx">result</span><span class="p">),</span>
<span class="w">          </span><span class="nx">take</span><span class="p">(</span><span class="mf">1</span><span class="p">),</span>
<span class="w">          </span><span class="nx">takeUntil</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">destroy$</span><span class="p">)</span>
<span class="w">        </span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="nx">subscribe</span><span class="p">(</span><span class="nx">result</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">this</span><span class="p">.</span><span class="nx">loading$</span><span class="p">.</span><span class="nx">next</span><span class="p">(</span><span class="kc">false</span><span class="p">);</span>

<span class="w">          </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">success</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">this</span><span class="p">.</span><span class="nx">discountApplied</span><span class="p">.</span><span class="nx">emit</span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">discount</span><span class="p">);</span>
<span class="w">            </span><span class="k">this</span><span class="p">.</span><span class="nx">discountForm</span><span class="p">.</span><span class="nx">reset</span><span class="p">();</span>
<span class="w">          </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">this</span><span class="p">.</span><span class="nx">error$</span><span class="p">.</span><span class="nx">next</span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">error</span><span class="p">);</span>
<span class="w">          </span><span class="p">}</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">removeDiscount</span><span class="p">(</span><span class="nx">discount</span><span class="o">:</span><span class="w"> </span><span class="kt">Discount</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">dispatch</span><span class="p">(</span><span class="nx">removeDiscount</span><span class="p">({</span><span class="w"> </span><span class="nx">discountId</span><span class="o">:</span><span class="w"> </span><span class="kt">discount.id</span><span class="w"> </span><span class="p">}));</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">calculateTotal</span><span class="p">(</span><span class="nx">subtotal</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">,</span><span class="w"> </span><span class="nx">discounts</span><span class="o">:</span><span class="w"> </span><span class="kt">Discount</span><span class="p">[],</span><span class="w"> </span><span class="nx">taxes</span><span class="o">:</span><span class="w"> </span><span class="kt">Tax</span><span class="p">[])</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">discountAmount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">discounts</span><span class="p">.</span><span class="nx">reduce</span><span class="p">((</span><span class="nx">sum</span><span class="p">,</span><span class="w"> </span><span class="nx">discount</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">sum</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="nx">discount</span><span class="p">.</span><span class="nx">amount</span><span class="p">,</span><span class="w"> </span><span class="mf">0</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">taxAmount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">taxes</span><span class="p">.</span><span class="nx">reduce</span><span class="p">((</span><span class="nx">sum</span><span class="p">,</span><span class="w"> </span><span class="nx">tax</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">sum</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="nx">tax</span><span class="p">.</span><span class="nx">amount</span><span class="p">,</span><span class="w"> </span><span class="mf">0</span><span class="p">);</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">subtotal</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nx">discountAmount</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="nx">taxAmount</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">recalculatePrices</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">dispatch</span><span class="p">(</span><span class="nx">recalculateCartPrices</span><span class="p">({</span><span class="w"> </span><span class="nx">cartItems</span><span class="o">:</span><span class="w"> </span><span class="kt">this.cartItems</span><span class="w"> </span><span class="p">}));</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="component-integration-patterns">Component Integration Patterns<a class="headerlink" href="#component-integration-patterns" title="Permanent link">&para;</a></h2>
<h3 id="parent-child-communication">Parent-Child Communication<a class="headerlink" href="#parent-child-communication" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Parent
    participant QuoteComponent
    participant LocationComponent
    participant CartComponent

    Parent-&gt;&gt;QuoteComponent: Initialize Quote
    QuoteComponent-&gt;&gt;Parent: Quote Started Event
    Parent-&gt;&gt;LocationComponent: Assign Location
    LocationComponent-&gt;&gt;Parent: Location Assigned Event
    Parent-&gt;&gt;CartComponent: Add Services
    CartComponent-&gt;&gt;Parent: Price Updated Event
    Parent-&gt;&gt;Parent: Update Overall State
</code></pre></div>
<h3 id="state-management-integration">State Management Integration<a class="headerlink" href="#state-management-integration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Component State Integration Pattern</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">TelusComponentIntegration</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Subscribe to multiple state slices</span>
<span class="w">  </span><span class="nx">componentState$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">combineLatest</span><span class="p">([</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectQuoteState</span><span class="p">),</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectLocationState</span><span class="p">),</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectCartState</span><span class="p">)</span>
<span class="w">  </span><span class="p">]).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">    </span><span class="nx">map</span><span class="p">(([</span><span class="nx">quote</span><span class="p">,</span><span class="w"> </span><span class="nx">location</span><span class="p">,</span><span class="w"> </span><span class="nx">cart</span><span class="p">])</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">      </span><span class="nx">quote</span><span class="p">,</span>
<span class="w">      </span><span class="nx">location</span><span class="p">,</span>
<span class="w">      </span><span class="nx">cart</span><span class="p">,</span>
<span class="w">      </span><span class="nx">isComplete</span><span class="o">:</span><span class="w"> </span><span class="kt">this.isWorkflowComplete</span><span class="p">(</span><span class="nx">quote</span><span class="p">,</span><span class="w"> </span><span class="nx">location</span><span class="p">,</span><span class="w"> </span><span class="nx">cart</span><span class="p">)</span>
<span class="w">    </span><span class="p">}))</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Dispatch coordinated actions</span>
<span class="w">  </span><span class="nx">completeWorkflow</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">dispatch</span><span class="p">(</span><span class="nx">completeQuoteWorkflow</span><span class="p">({</span>
<span class="w">      </span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">this.quoteId</span><span class="p">,</span>
<span class="w">      </span><span class="nx">locationId</span><span class="o">:</span><span class="w"> </span><span class="kt">this.locationId</span><span class="p">,</span>
<span class="w">      </span><span class="nx">cartItems</span><span class="o">:</span><span class="w"> </span><span class="kt">this.cartItems</span>
<span class="w">    </span><span class="p">}));</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">isWorkflowComplete</span><span class="p">(</span><span class="nx">quote</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="p">,</span><span class="w"> </span><span class="nx">location</span><span class="o">:</span><span class="w"> </span><span class="kt">Location</span><span class="p">,</span><span class="w"> </span><span class="nx">cart</span><span class="o">:</span><span class="w"> </span><span class="kt">Cart</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="o">!!</span><span class="p">(</span><span class="nx">quote</span><span class="o">?</span><span class="p">.</span><span class="nx">id</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">location</span><span class="o">?</span><span class="p">.</span><span class="nx">id</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">cart</span><span class="o">?</span><span class="p">.</span><span class="nx">items</span><span class="o">?</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mf">0</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="development-guidelines">Development Guidelines<a class="headerlink" href="#development-guidelines" title="Permanent link">&para;</a></h2>
<h3 id="component-creation-checklist">Component Creation Checklist<a class="headerlink" href="#component-creation-checklist" title="Permanent link">&para;</a></h3>
<ul>
<li>[ ] Extend <code>TelusBaseComponent</code> for consistent patterns</li>
<li>[ ] Implement proper input/output properties</li>
<li>[ ] Use OnPush change detection strategy</li>
<li>[ ] Integrate with NgRx store for state management</li>
<li>[ ] Include comprehensive error handling</li>
<li>[ ] Add loading states and user feedback</li>
<li>[ ] Implement proper cleanup in <code>ngOnDestroy</code></li>
<li>[ ] Follow TELUS design system guidelines</li>
<li>[ ] Include accessibility features (ARIA labels, keyboard navigation)</li>
<li>[ ] Write unit tests with &gt;80% coverage</li>
</ul>
<h3 id="styling-guidelines">Styling Guidelines<a class="headerlink" href="#styling-guidelines" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="o">//</span><span class="w"> </span><span class="nt">TELUS</span><span class="w"> </span><span class="nt">Component</span><span class="w"> </span><span class="nt">Styling</span><span class="w"> </span><span class="nt">Pattern</span>
<span class="p">.</span><span class="nc">telus-component</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="err">@import</span><span class="w"> </span><span class="err">&#39;telus-variables.less&#39;</span><span class="p">;</span>

<span class="w">  </span><span class="err">.component-container</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">padding</span><span class="p">:</span><span class="w"> </span><span class="o">@</span><span class="n">spacing-medium</span><span class="p">;</span>
<span class="w">    </span><span class="k">border-radius</span><span class="p">:</span><span class="w"> </span><span class="o">@</span><span class="k">border-radius</span><span class="o">-</span><span class="kc">default</span><span class="p">;</span>
<span class="w">    </span><span class="k">background-color</span><span class="p">:</span><span class="w"> </span><span class="o">@</span><span class="kc">color</span><span class="o">-</span><span class="k">background</span><span class="o">-</span><span class="n">primary</span><span class="p">;</span>

<span class="w">    </span><span class="err">.component-header</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">color</span><span class="p">:</span><span class="w"> </span><span class="o">@</span><span class="kc">color</span><span class="o">-</span><span class="n">telus-purple</span><span class="p">;</span>
<span class="w">      </span><span class="k">font-weight</span><span class="p">:</span><span class="w"> </span><span class="o">@</span><span class="k">font-weight</span><span class="o">-</span><span class="kc">bold</span><span class="p">;</span>
<span class="w">      </span><span class="k">margin-bottom</span><span class="p">:</span><span class="w"> </span><span class="o">@</span><span class="n">spacing-small</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="err">.component-content</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="err">.form-group</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">margin-bottom</span><span class="p">:</span><span class="w"> </span><span class="o">@</span><span class="n">spacing-medium</span><span class="p">;</span>

<span class="w">        </span><span class="err">.form-control</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">border-color</span><span class="p">:</span><span class="w"> </span><span class="o">@</span><span class="kc">color</span><span class="o">-</span><span class="k">border</span><span class="o">-</span><span class="kc">default</span><span class="p">;</span>

<span class="w">          </span><span class="err">&amp;:focus</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">border-color</span><span class="p">:</span><span class="w"> </span><span class="o">@</span><span class="kc">color</span><span class="o">-</span><span class="n">telus-purple</span><span class="p">;</span>
<span class="w">            </span><span class="k">box-shadow</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="mi">2</span><span class="kt">px</span><span class="w"> </span><span class="nf">fade</span><span class="p">(</span><span class="err">@</span><span class="kc">color</span><span class="nv">-telus-purple</span><span class="p">,</span><span class="w"> </span><span class="mi">20</span><span class="kt">%</span><span class="p">);</span>
<span class="w">          </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="err">.component-actions</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">display</span><span class="p">:</span><span class="w"> </span><span class="kc">flex</span><span class="p">;</span>
<span class="w">      </span><span class="k">justify-content</span><span class="p">:</span><span class="w"> </span><span class="kc">flex-end</span><span class="p">;</span>
<span class="w">      </span><span class="k">gap</span><span class="p">:</span><span class="w"> </span><span class="o">@</span><span class="n">spacing-small</span><span class="p">;</span>
<span class="w">      </span><span class="k">margin-top</span><span class="p">:</span><span class="w"> </span><span class="o">@</span><span class="n">spacing-medium</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../03-state-management/">State Management →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>