
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/bulk-operation-extension/00-overview/">
      
      
        <link rel="prev" href="../../qss-extension/04-development-guide/">
      
      
        <link rel="next" href="../01-nifi-flow-architecture/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Overview - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#bulk-operation-extension-overview" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Overview
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" checked>
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#introduction" class="md-nav__link">
    <span class="md-ellipsis">
      Introduction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Introduction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#purpose-and-scope" class="md-nav__link">
    <span class="md-ellipsis">
      Purpose and Scope
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#high-level-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      High-Level Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#nifi-data-flow-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      NiFi Data Flow Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Core Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#nifi-components" class="md-nav__link">
    <span class="md-ellipsis">
      NiFi Components
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Technologies
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Business Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Business Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#bulk-address-processing" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Address Processing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-capabilities" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Capabilities
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#api-management" class="md-nav__link">
    <span class="md-ellipsis">
      API Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technical-features" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Technical Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Technical Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#apache-nifi-workflows" class="md-nav__link">
    <span class="md-ellipsis">
      Apache NiFi Workflows
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-management" class="md-nav__link">
    <span class="md-ellipsis">
      Data Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-operations" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment &amp; Operations
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#repository-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Repository Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#nifi-flow-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      NiFi Flow Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#available-maven-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Available Maven Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-urls" class="md-nav__link">
    <span class="md-ellipsis">
      Development URLs
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#introduction" class="md-nav__link">
    <span class="md-ellipsis">
      Introduction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Introduction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#purpose-and-scope" class="md-nav__link">
    <span class="md-ellipsis">
      Purpose and Scope
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#high-level-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      High-Level Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#nifi-data-flow-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      NiFi Data Flow Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Core Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#nifi-components" class="md-nav__link">
    <span class="md-ellipsis">
      NiFi Components
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Technologies
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Business Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Business Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#bulk-address-processing" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Address Processing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-capabilities" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Capabilities
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#api-management" class="md-nav__link">
    <span class="md-ellipsis">
      API Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technical-features" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Technical Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Technical Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#apache-nifi-workflows" class="md-nav__link">
    <span class="md-ellipsis">
      Apache NiFi Workflows
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-management" class="md-nav__link">
    <span class="md-ellipsis">
      Data Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-operations" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment &amp; Operations
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#repository-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Repository Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#nifi-flow-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      NiFi Flow Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#available-maven-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Available Maven Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-urls" class="md-nav__link">
    <span class="md-ellipsis">
      Development URLs
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="bulk-operation-extension-overview">Bulk Operation Extension Overview<a class="headerlink" href="#bulk-operation-extension-overview" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#introduction">Introduction</a></li>
<li><a href="#architecture-overview">Architecture Overview</a></li>
<li><a href="#technology-stack">Technology Stack</a></li>
<li><a href="#key-features">Key Features</a></li>
<li><a href="#project-structure">Project Structure</a></li>
<li><a href="#development-environment">Development Environment</a></li>
</ul>
<h2 id="introduction">Introduction<a class="headerlink" href="#introduction" title="Permanent link">&para;</a></h2>
<p>The <strong>telus-bulk-operation-extension-b2b</strong> is a comprehensive Apache NiFi-based solution designed to facilitate bulk operations for TELUS B2B services. This enterprise-grade data processing extension provides automated bulk address uploads, validation workflows, and seamless integration with TELUS BSS (Business Support Systems) through sophisticated NiFi data flows and controller services.</p>
<h3 id="purpose-and-scope">Purpose and Scope<a class="headerlink" href="#purpose-and-scope" title="Permanent link">&para;</a></h3>
<p>The Bulk Operation Extension is designed to:
- <strong>Process Bulk Address Operations</strong>: Automated handling of large-scale address data uploads and validation
- <strong>Integrate with TELUS BSS Systems</strong>: Seamless connectivity to Quote Engine Service (QES) and other BSS components
- <strong>Provide Data Flow Orchestration</strong>: Apache NiFi-based workflow management for complex data processing
- <strong>Enable Scalable Processing</strong>: Handle thousands of bulk operations simultaneously with enterprise reliability
- <strong>Support Multi-Environment Deployment</strong>: Kubernetes-based deployment with Helm charts for different environments
- <strong>Ensure Data Security</strong>: Enterprise-grade security with OAuth2 authentication and sensitive parameter management</p>
<h2 id="architecture-overview">Architecture Overview<a class="headerlink" href="#architecture-overview" title="Permanent link">&para;</a></h2>
<h3 id="high-level-architecture">High-Level Architecture<a class="headerlink" href="#high-level-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Client Layer&quot;
        A[REST API Clients]
        B[Bulk Upload Requests]
        C[Status Monitoring]
    end

    subgraph &quot;Apache NiFi Processing Layer&quot;
        D[HandleHttpRequest]
        E[Authentication Validation]
        F[Request Routing]
        G[Bulk Processing Flows]
    end

    subgraph &quot;Business Logic Layer&quot;
        H[Address Validation]
        I[Market Resolver Integration]
        J[QES Integration]
        K[Error Handling]
    end

    subgraph &quot;Data Storage Layer&quot;
        L[PostgreSQL Database]
        M[Request Status Storage]
        N[Error Logging]
        O[Audit Trail]
    end

    subgraph &quot;External Systems&quot;
        P[TELUS BSS Systems]
        Q[Quote Engine Service]
        R[Identity Provider]
        S[Cloud Integration Platform]
    end

    A --&gt; D
    B --&gt; D
    C --&gt; D

    D --&gt; E
    E --&gt; F
    F --&gt; G

    G --&gt; H
    G --&gt; I
    G --&gt; J
    G --&gt; K

    H --&gt; L
    I --&gt; M
    J --&gt; N
    K --&gt; O

    G --&gt; P
    I --&gt; Q
    E --&gt; R
    J --&gt; S

    style D fill:#673ab7,stroke:#512da8,color:#ffffff
    style G fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style H fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="nifi-data-flow-architecture">NiFi Data Flow Architecture<a class="headerlink" href="#nifi-data-flow-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Client as REST Client
    participant NiFi as Apache NiFi
    participant Auth as Identity Provider
    participant QES as Quote Engine Service
    participant DB as PostgreSQL Database
    participant BSS as TELUS BSS Systems

    Client-&gt;&gt;NiFi: POST /addressManagement/v1/bulkValidate/request
    NiFi-&gt;&gt;Auth: Validate Authorization Token
    Auth--&gt;&gt;NiFi: Token Validation Result

    alt Token Valid
        NiFi-&gt;&gt;NiFi: Parse Request &amp; Extract Data
        NiFi-&gt;&gt;DB: Store Request Status
        NiFi-&gt;&gt;QES: Process Address Validation
        QES--&gt;&gt;NiFi: Validation Results
        NiFi-&gt;&gt;BSS: Update BSS Systems
        BSS--&gt;&gt;NiFi: Update Confirmation
        NiFi-&gt;&gt;DB: Update Request Status
        NiFi--&gt;&gt;Client: HTTP 200 - Request Accepted
    else Token Invalid
        NiFi--&gt;&gt;Client: HTTP 401 - Unauthorized
    end

    Client-&gt;&gt;NiFi: GET /addressManagement/v1/bulkValidate/{id}/status
    NiFi-&gt;&gt;DB: Query Request Status
    DB--&gt;&gt;NiFi: Status Information
    NiFi--&gt;&gt;Client: Status Response

    Client-&gt;&gt;NiFi: GET /addressManagement/v1/bulkValidate/{id}/report
    NiFi-&gt;&gt;DB: Query Processing Results
    DB--&gt;&gt;NiFi: Report Data
    NiFi--&gt;&gt;Client: Report Response
</code></pre></div>
<h2 id="technology-stack">Technology Stack<a class="headerlink" href="#technology-stack" title="Permanent link">&para;</a></h2>
<h3 id="core-technologies">Core Technologies<a class="headerlink" href="#core-technologies" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Technology</th>
<th>Version</th>
<th>Purpose</th>
<th>Key Features</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Apache NiFi</strong></td>
<td>1.19.1</td>
<td>Data Flow Engine</td>
<td>Visual data flow design, real-time processing</td>
</tr>
<tr>
<td><strong>Java</strong></td>
<td>11+</td>
<td>Programming Language</td>
<td>Enterprise reliability, performance</td>
</tr>
<tr>
<td><strong>Maven</strong></td>
<td>3.6+</td>
<td>Build Tool</td>
<td>Dependency management, assembly packaging</td>
</tr>
<tr>
<td><strong>PostgreSQL</strong></td>
<td>13+</td>
<td>Database</td>
<td>Request storage, status tracking</td>
</tr>
<tr>
<td><strong>Docker</strong></td>
<td>20.10+</td>
<td>Containerization</td>
<td>Consistent deployment environments</td>
</tr>
<tr>
<td><strong>Kubernetes</strong></td>
<td>1.20+</td>
<td>Orchestration</td>
<td>Scalable container management</td>
</tr>
</tbody>
</table>
<h3 id="nifi-components">NiFi Components<a class="headerlink" href="#nifi-components" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Component Type</th>
<th>Purpose</th>
<th>Usage</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Processors</strong></td>
<td>Data Processing</td>
<td>HTTP handling, validation, transformation</td>
</tr>
<tr>
<td><strong>Controller Services</strong></td>
<td>Shared Services</td>
<td>Database connections, OAuth2 providers</td>
</tr>
<tr>
<td><strong>Parameter Contexts</strong></td>
<td>Configuration</td>
<td>Environment-specific parameters</td>
</tr>
<tr>
<td><strong>Process Groups</strong></td>
<td>Flow Organization</td>
<td>Logical grouping of related processors</td>
</tr>
<tr>
<td><strong>Connections</strong></td>
<td>Data Flow</td>
<td>Routing and queuing between processors</td>
</tr>
</tbody>
</table>
<h3 id="integration-technologies">Integration Technologies<a class="headerlink" href="#integration-technologies" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;authentication&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;oauth2&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;OAuth2AccessTokenProvider&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;identity_provider&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;NetCracker Identity Provider&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;token_validation&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ValidateAuthToken processor&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;data_processing&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;http_handling&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;HandleHttpRequest/HandleHttpResponse&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;routing&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;RouteOnAttribute processors&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;transformation&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;UpdateAttribute, ReplaceText&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;storage&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;database&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;PostgreSQL with DBaaS connection pooling&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;schema_registry&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Avro schema management&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;parameter_management&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Sensitive parameter contexts&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="key-features">Key Features<a class="headerlink" href="#key-features" title="Permanent link">&para;</a></h2>
<h3 id="business-features">🎯 <strong>Business Features</strong><a class="headerlink" href="#business-features" title="Permanent link">&para;</a></h3>
<h4 id="bulk-address-processing">Bulk Address Processing<a class="headerlink" href="#bulk-address-processing" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Large-Scale Upload Handling</strong>: Process thousands of address records simultaneously</li>
<li><strong>Validation Workflows</strong>: Comprehensive address validation against TELUS standards</li>
<li><strong>Status Tracking</strong>: Real-time status monitoring for bulk operations</li>
<li><strong>Error Management</strong>: Detailed error reporting and recovery mechanisms</li>
<li><strong>Report Generation</strong>: Comprehensive processing reports and audit trails</li>
</ul>
<h4 id="integration-capabilities">Integration Capabilities<a class="headerlink" href="#integration-capabilities" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>QES Integration</strong>: Direct integration with Quote Engine Service for address validation</li>
<li><strong>BSS System Connectivity</strong>: Seamless integration with TELUS Business Support Systems</li>
<li><strong>Market Resolver</strong>: Geographic market resolution for address data</li>
<li><strong>CIP Platform</strong>: Integration with Cloud Integration Platform for extended workflows</li>
</ul>
<h4 id="api-management">API Management<a class="headerlink" href="#api-management" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>RESTful Endpoints</strong>: Standard REST API for bulk operations</li>
<li><strong>Authentication</strong>: OAuth2-based security with token validation</li>
<li><strong>Request Routing</strong>: Intelligent routing based on request type and parameters</li>
<li><strong>Response Handling</strong>: Standardized response formats with error codes</li>
</ul>
<h3 id="technical-features">🔧 <strong>Technical Features</strong><a class="headerlink" href="#technical-features" title="Permanent link">&para;</a></h3>
<h4 id="apache-nifi-workflows">Apache NiFi Workflows<a class="headerlink" href="#apache-nifi-workflows" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Visual Flow Design</strong>: Drag-and-drop workflow creation and management</li>
<li><strong>Real-Time Processing</strong>: Stream processing capabilities for immediate response</li>
<li><strong>Error Handling</strong>: Built-in retry mechanisms and failure routing</li>
<li><strong>Monitoring</strong>: Comprehensive flow monitoring and performance metrics</li>
<li><strong>Scalability</strong>: Horizontal scaling support for high-volume processing</li>
</ul>
<h4 id="data-management">Data Management<a class="headerlink" href="#data-management" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Schema Registry</strong>: Avro schema management for data validation</li>
<li><strong>Database Integration</strong>: PostgreSQL integration with connection pooling</li>
<li><strong>Parameter Management</strong>: Secure handling of sensitive configuration parameters</li>
<li><strong>Audit Logging</strong>: Comprehensive audit trails for compliance requirements</li>
</ul>
<h4 id="deployment-operations">Deployment &amp; Operations<a class="headerlink" href="#deployment-operations" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Containerization</strong>: Docker-based packaging for consistent deployment</li>
<li><strong>Kubernetes Support</strong>: Helm charts for Kubernetes deployment</li>
<li><strong>Environment Management</strong>: Multi-environment configuration support</li>
<li><strong>Health Monitoring</strong>: Built-in health checks and monitoring endpoints</li>
</ul>
<h2 id="project-structure">Project Structure<a class="headerlink" href="#project-structure" title="Permanent link">&para;</a></h2>
<h3 id="repository-organization">Repository Organization<a class="headerlink" href="#repository-organization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>telus-bulk-operation-extension-b2b/
├── nifi/                                              # NiFi configuration and flows
│   ├── versioned-flows/                               # NiFi flow definitions
│   │   ├── Bulk_Address_Upload.json                   # Main bulk address flow
│   │   └── Telus_BSS_Integrations/                    # BSS integration flows
│   ├── controller-services/                           # NiFi controller services
│   │   ├── BulkPriceOverrideDBConnectionPool.json     # Database connection pool
│   │   ├── PostgresPreparedStatementWithArrayProvider.json # SQL provider
│   │   └── StandardHttpContextMap.json                # HTTP context management
│   └── parameter-contexts/                            # Parameter configurations
├── deployments/                                       # Deployment configurations
│   ├── deployment-configuration.json                  # Deployment settings
│   └── charts/                                        # Helm charts
│       └── telus-bulk-operation-extension-b2b/        # Main Helm chart
│           ├── Chart.yaml                             # Chart metadata
│           ├── values.yaml                            # Default values
│           ├── values.schema.json                     # Values validation
│           ├── resource-profiles/                     # Environment profiles
│           └── templates/                             # Kubernetes templates
├── scripts/                                           # Deployment scripts
│   ├── addSqlScript.sh                               # SQL script management
│   ├── beforeImport.sh                               # Pre-import setup
│   ├── afterAllActions.sh                            # Post-deployment actions
│   └── registerRoutes.sh                             # Route registration
├── docs/                                              # Documentation
│   ├── 00-README.md                                  # Overview documentation
│   ├── 01-installation-setup.md                      # Installation guide
│   ├── 02-architecture.md                            # Architecture details
│   ├── 03-configuration.md                           # Configuration guide
│   ├── 04-api-documentation.md                       # API documentation
│   ├── 05-development.md                             # Development guide
│   ├── 06-deployment.md                              # Deployment guide
│   ├── 07-troubleshooting.md                         # Troubleshooting guide
│   └── 08-appendices.md                              # Additional resources
├── descriptors/                                       # Build descriptors
│   └── nifi-assembly-module.xml                      # Maven assembly descriptor
├── pom.xml                                            # Maven configuration
├── Dockerfile                                         # Container definition
└── README-DOCS.md                                     # Documentation setup guide
</code></pre></div>
<h3 id="nifi-flow-architecture">NiFi Flow Architecture<a class="headerlink" href="#nifi-flow-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Bulk Address Upload Flow&quot;
        A[HandleHttpRequest]
        B[Get Request ID and Type]
        C[ValidateAuthToken]
        D[Delete Extra HTTP Attributes]
        E[Check Path]
    end

    subgraph &quot;Request Processing&quot;
        F[Route by Request Type]
        G[Bulk Address Request]
        H[Status Check]
        I[Report Generation]
        J[Error Handling]
    end

    subgraph &quot;Response Management&quot;
        K[Set HTTP Status Code]
        L[Generate Response]
        M[HandleHttpResponse]
        N[Log Messages]
    end

    subgraph &quot;Controller Services&quot;
        O[DB Connection Pool]
        P[OAuth2 Token Provider]
        Q[HTTP Context Map]
        R[Schema Registry]
    end

    A --&gt; B
    B --&gt; C
    C --&gt; D
    D --&gt; E

    E --&gt; F
    F --&gt; G
    F --&gt; H
    F --&gt; I
    F --&gt; J

    G --&gt; K
    H --&gt; K
    I --&gt; K
    J --&gt; K

    K --&gt; L
    L --&gt; M
    M --&gt; N

    C --&gt; P
    A --&gt; Q
    G --&gt; O
    G --&gt; R

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style F fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style O fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h2 id="development-environment">Development Environment<a class="headerlink" href="#development-environment" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Required Software Versions</span>
Java:<span class="w"> </span>OpenJDK<span class="w"> </span><span class="m">11</span><span class="w"> </span>or<span class="w"> </span>higher
Maven:<span class="w"> </span><span class="m">3</span>.6+<span class="w"> </span>
Docker:<span class="w"> </span><span class="m">20</span>.10+
Kubernetes:<span class="w"> </span><span class="m">1</span>.20+<span class="w"> </span><span class="o">(</span>minikube,<span class="w"> </span>kind,<span class="w"> </span>or<span class="w"> </span>k3s<span class="w"> </span><span class="k">for</span><span class="w"> </span><span class="nb">local</span><span class="w"> </span>development<span class="o">)</span>
Helm:<span class="w"> </span><span class="m">3</span>.x<span class="w"> </span>or<span class="w"> </span>higher
Apache<span class="w"> </span>NiFi:<span class="w"> </span><span class="m">1</span>.19.1<span class="w"> </span>or<span class="w"> </span>higher
PostgreSQL:<span class="w"> </span><span class="m">13</span>+<span class="w"> </span><span class="o">(</span><span class="k">for</span><span class="w"> </span><span class="nb">local</span><span class="w"> </span>development<span class="o">)</span>
Git:<span class="w"> </span><span class="m">2</span>.x<span class="w"> </span>or<span class="w"> </span>higher
</code></pre></div>
<h3 id="environment-setup">Environment Setup<a class="headerlink" href="#environment-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Clone repository</span>
git<span class="w"> </span>clone<span class="w"> </span>&lt;repository-url&gt;
<span class="nb">cd</span><span class="w"> </span>telus-bulk-operation-extension-b2b

<span class="c1"># 2. Verify prerequisites</span>
java<span class="w"> </span>-version
mvn<span class="w"> </span>-version
docker<span class="w"> </span>--version
kubectl<span class="w"> </span>version<span class="w"> </span>--client
helm<span class="w"> </span>version

<span class="c1"># 3. Set up environment variables</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">JAVA_HOME</span><span class="o">=</span>/path/to/java11
<span class="nb">export</span><span class="w"> </span><span class="nv">MAVEN_HOME</span><span class="o">=</span>/path/to/maven
<span class="nb">export</span><span class="w"> </span><span class="nv">PATH</span><span class="o">=</span><span class="nv">$JAVA_HOME</span>/bin:<span class="nv">$MAVEN_HOME</span>/bin:<span class="nv">$PATH</span>

<span class="c1"># 4. Build the extension</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package

<span class="c1"># 5. Validate assembly</span>
mvn<span class="w"> </span>verify
</code></pre></div>
<h3 id="local-development-setup">Local Development Setup<a class="headerlink" href="#local-development-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Start local PostgreSQL (using Docker)</span>
docker<span class="w"> </span>run<span class="w"> </span>--name<span class="w"> </span>postgres-bulk-ops<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-e<span class="w"> </span><span class="nv">POSTGRES_DB</span><span class="o">=</span>bulk_operations<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-e<span class="w"> </span><span class="nv">POSTGRES_USER</span><span class="o">=</span>nifi<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-e<span class="w"> </span><span class="nv">POSTGRES_PASSWORD</span><span class="o">=</span>nifi<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-p<span class="w"> </span><span class="m">5432</span>:5432<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span>postgres:13

<span class="c1"># 2. Start local NiFi instance</span>
docker<span class="w"> </span>run<span class="w"> </span>--name<span class="w"> </span>nifi-bulk-ops<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-p<span class="w"> </span><span class="m">8080</span>:8080<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-p<span class="w"> </span><span class="m">8443</span>:8443<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span>apache/nifi:1.19.1

<span class="c1"># 3. Wait for NiFi to start (check logs)</span>
docker<span class="w"> </span>logs<span class="w"> </span>-f<span class="w"> </span>nifi-bulk-ops

<span class="c1"># 4. Access NiFi UI</span>
<span class="c1"># http://localhost:8080/nifi</span>
<span class="c1"># Default credentials: admin/admin (generated on first start)</span>

<span class="c1"># 5. Import NiFi flows</span>
<span class="c1"># Use the NiFi UI to import flows from nifi/versioned-flows/</span>
</code></pre></div>
<h3 id="configuration-management">Configuration Management<a class="headerlink" href="#configuration-management" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Parameter Context Configuration</span>
<span class="nt">bulk_address_upload</span><span class="p">:</span>
<span class="w">  </span><span class="nt">parameters</span><span class="p">:</span>
<span class="w">    </span><span class="nt">src</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;T&quot;</span><span class="w">                                    </span><span class="c1"># Source identifier</span>
<span class="w">    </span><span class="nt">maxResults</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;5&quot;</span><span class="w">                             </span><span class="c1"># Maximum results per query</span>
<span class="w">    </span><span class="nt">searchType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;match&quot;</span><span class="w">                         </span><span class="c1"># Search type for validation</span>
<span class="w">    </span><span class="nt">internal-gw</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://cloud-integration-platform-engine:8080/&quot;</span>
<span class="w">    </span><span class="nt">qes-gw</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://quotation-engine-service:8080/&quot;</span>
<span class="w">    </span><span class="nt">action</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;query&quot;</span><span class="w">                             </span><span class="c1"># Default action type</span>

<span class="c1"># Controller Service Configuration</span>
<span class="nt">database_connection</span><span class="p">:</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;DBaaSAwareConnectionPoolService&quot;</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">database_url</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;************************************************&quot;</span>
<span class="w">    </span><span class="nt">username</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;nifi&quot;</span>
<span class="w">    </span><span class="nt">password</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${DB_PASSWORD}&quot;</span><span class="w">                  </span><span class="c1"># From sensitive parameters</span>
<span class="w">    </span><span class="nt">max_connections</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;10&quot;</span>
<span class="w">    </span><span class="nt">validation_query</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;SELECT</span><span class="nv"> </span><span class="s">1&quot;</span>

<span class="nt">oauth2_provider</span><span class="p">:</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;OAuth2AccessTokenProviderImpl&quot;</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">grant_type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Client</span><span class="nv"> </span><span class="s">Credentials&quot;</span>
<span class="w">    </span><span class="nt">identity_provider_url</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://identity-provider:8080/auth&quot;</span>
<span class="w">    </span><span class="nt">realm</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;cloud-common&quot;</span>
<span class="w">    </span><span class="nt">client_id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${OAUTH2_CLIENT_ID}&quot;</span><span class="w">           </span><span class="c1"># From sensitive parameters</span>
<span class="w">    </span><span class="nt">client_secret_path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/etc/secret/&quot;</span>
</code></pre></div>
<h3 id="available-maven-commands">Available Maven Commands<a class="headerlink" href="#available-maven-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Build commands</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile<span class="w">                    </span><span class="c1"># Compile source code</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w">                    </span><span class="c1"># Package as assembly</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w">                    </span><span class="c1"># Install to local repository</span>
mvn<span class="w"> </span>clean<span class="w"> </span>deploy<span class="w">                     </span><span class="c1"># Deploy to remote repository</span>

<span class="c1"># Assembly commands</span>
mvn<span class="w"> </span>assembly:single<span class="w">                  </span><span class="c1"># Create NiFi assembly package</span>
mvn<span class="w"> </span>assembly:help<span class="w">                    </span><span class="c1"># Show assembly plugin help</span>

<span class="c1"># Validation commands</span>
mvn<span class="w"> </span>validate<span class="w">                         </span><span class="c1"># Validate project structure</span>
mvn<span class="w"> </span>verify<span class="w">                          </span><span class="c1"># Run integration tests</span>

<span class="c1"># Development commands</span>
mvn<span class="w"> </span>dependency:tree<span class="w">                  </span><span class="c1"># Show dependency tree</span>
mvn<span class="w"> </span>help:effective-pom<span class="w">              </span><span class="c1"># Show effective POM configuration</span>
mvn<span class="w"> </span>versions:display-dependency-updates<span class="w">  </span><span class="c1"># Check for dependency updates</span>

<span class="c1"># Docker commands</span>
docker<span class="w"> </span>build<span class="w"> </span>-t<span class="w"> </span>telus-bulk-ops<span class="w"> </span>.<span class="w">    </span><span class="c1"># Build Docker image</span>
docker<span class="w"> </span>run<span class="w"> </span>-p<span class="w"> </span><span class="m">8080</span>:8080<span class="w"> </span>telus-bulk-ops<span class="w">  </span><span class="c1"># Run container locally</span>
</code></pre></div>
<h3 id="development-urls">Development URLs<a class="headerlink" href="#development-urls" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Environment</th>
<th>URL</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Local NiFi UI</strong></td>
<td>http://localhost:8080/nifi</td>
<td>NiFi flow development interface</td>
</tr>
<tr>
<td><strong>Bulk Operations API</strong></td>
<td>http://localhost:21014/addressManagement/v1/bulkValidate</td>
<td>REST API endpoint</td>
</tr>
<tr>
<td><strong>Health Check</strong></td>
<td>http://localhost:8080/nifi-api/system-diagnostics</td>
<td>NiFi system diagnostics</td>
</tr>
<tr>
<td><strong>PostgreSQL</strong></td>
<td>************************************************</td>
<td>Local database connection</td>
</tr>
<tr>
<td><strong>Documentation</strong></td>
<td>http://localhost:8000</td>
<td>Local documentation server</td>
</tr>
</tbody>
</table>
<hr />
<p><strong>Next</strong>: <a href="../01-nifi-flow-architecture/">NiFi Flow Architecture →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>