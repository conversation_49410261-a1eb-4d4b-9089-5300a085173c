# @telus/mcp-github-actions

An MCP server for interacting with GitHub Actions workflows through the GitHub API.

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-github-actions
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for Claude Desktop):

   ```json
   {
     "mcpServers": {
       "github-actions": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-github-actions"],
         "env": {
           "GITHUB_TOKEN": "ghp_your_personal_access_token"
         }
       }
     }
   }
   ```

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-github-actions
   pnpm install
   ```

2. Build the server:

   ```bash
   pnpm build
   ```

3. Start the server:

   ```bash
   pnpm start
   ```

   For development with auto-rebuild:

   ```bash
   pnpm watch
   ```

## Configuration

Required Environment Variables:

- `GITHUB_TOKEN`: GitHub Personal Access Token (must start with 'ghp\_')
  - Required permissions:
    - `actions:read` - For listing workflows and viewing runs
    - `actions:write` - For triggering workflows
    - `repo` - For accessing repository resources

## Available Tools

- `list_workflows` - List workflows for a repository

  - Required parameters:
    - `owner`: String - Repository owner
    - `repo`: String - Repository name

- `get_workflow_runs` - Get workflow runs for a specific workflow

  - Required parameters:
    - `owner`: String - Repository owner
    - `repo`: String - Repository name
    - `workflow_id`: String - ID of the workflow to get runs for

- `trigger_workflow` - Trigger a workflow dispatch event

  - Required parameters:
    - `owner`: String - Repository owner
    - `repo`: String - Repository name
    - `workflow_id`: String - ID of the workflow to trigger
    - `ref`: String - Git reference (branch/tag) to run the workflow on

- `get_run_logs` - Get logs for a specific workflow run
  - Required parameters:
    - `owner`: String - Repository owner
    - `repo`: String - Repository name
    - `run_id`: Number - ID of the run to get logs for

## Debugging

Since MCP servers communicate over stdio, debugging can be challenging. The MCP Inspector is available as a package script:

```bash
pnpm inspector
```

The Inspector will provide a URL to access debugging tools in your browser.
