{"name": "@telus/mcp-confluence-server", "version": "1.0.0", "description": "Confluence Cloud MCP server for TELUS", "keywords": ["telus", "mcp", "confluence", "server"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-confluence-server"}, "license": "MIT", "author": "TELUS", "type": "module", "main": "dist/index.js", "bin": {"confluence-server": "./dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node --eval \"import('fs').then(fs => fs.chmodSync('dist/index.js', '755'))\"", "inspector": "pnpm dlx @modelcontextprotocol/inspector dist/index.js", "prepare": "pnpm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "axios": "^1.8.4", "tunnel": "^0.0.6"}, "devDependencies": {"@types/node": "^22.13.10", "@types/tunnel": "^0.0.6", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}