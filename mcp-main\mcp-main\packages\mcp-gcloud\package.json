{"name": "@telus/mcp-gcloud", "version": "0.1.0", "description": "MCP server for Google Cloud Secret Manager", "keywords": ["telus", "mcp", "gcloud", "secret-manager"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-gcloud"}, "license": "MIT", "author": "<PERSON> (@raphberube)", "type": "module", "main": "dist/index.js", "bin": {"mcp-gcloud": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "start": "node dist/index.js"}, "dependencies": {"@google-cloud/secret-manager": "^5.0.1", "@modelcontextprotocol/sdk": "^1.7.0"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}