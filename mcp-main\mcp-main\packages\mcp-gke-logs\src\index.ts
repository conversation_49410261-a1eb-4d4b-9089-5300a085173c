#!/usr/bin/env node
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from "@modelcontextprotocol/sdk/types.js";
import { Logging } from "@google-cloud/logging";

// Environment variables will be provided in MCP settings
const PROJECT_ID = process.env.GCP_PROJECT_ID;
const LOCATION = process.env.GCP_LOCATION;
const NAMESPACE = process.env.K8S_NAMESPACE;
const POD_NAME = process.env.K8S_POD_NAME; // Optional
const APP_NAME = process.env.K8S_APP_NAME; // Optional
const BUCKET_PROJECT_ID = process.env.GCP_BUCKET_PROJECT_ID; // Optional
const BUCKET_NAME = process.env.GCP_BUCKET_NAME; // Optional

if (!PROJECT_ID || !LOCATION || !NAMESPACE) {
  throw new Error(
    "GCP_PROJECT_ID, GCP_LOCATION, and K8S_NAMESPACE environment variables are required",
  );
}

class GcpLogsServer {
  private server: Server;
  private logging: Logging;

  constructor() {
    this.server = new Server(
      {
        name: "gcp-logs-server",
        version: "0.1.0",
      },
      {
        capabilities: {
          tools: {},
        },
      },
    );

    // Initialize Google Cloud Logging client using Application Default Credentials
    this.logging = new Logging({
      projectId: PROJECT_ID,
    });

    this.setupToolHandlers();

    // Error handling
    this.server.onerror = (error) => console.error("[MCP Error]", error);
    process.on("SIGINT", async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: "search_logs",
          description:
            "Search logs by keyword and time range for the configured Kubernetes namespace",
          inputSchema: {
            type: "object",
            properties: {
              keyword: {
                type: "string",
                description: "Keyword or text to search for in logs",
              },
              podName: {
                type: "string",
                description:
                  "Optional: Specific pod name to filter logs from. Overrides K8S_POD_NAME environment variable if provided.",
              },
              appName: {
                type: "string",
                description:
                  "Optional: Specific app/workload name to filter logs from. Overrides K8S_APP_NAME and ignores K8S_POD_NAME environment variables if provided.",
              },
              bucket: {
                type: "string",
                description:
                  "Optional: Specific bucket name to filter logs from. Overrides BUCKET_NAME environment variable if provided.",
              },
              startTime: {
                type: "string",
                description:
                  "Start time in ISO format (e.g. 2024-01-20T10:00:00Z) or relative time (e.g. -1h, -30m)",
              },
              endTime: {
                type: "string",
                description:
                  "End time in ISO format (e.g. 2024-01-20T11:00:00Z) or relative time (e.g. now)",
              },
              maxResults: {
                type: "number",
                description: "Maximum number of log entries to return",
                minimum: 1,
                maximum: 1000,
                default: 100,
              },
              filter: {
                type: "string",
                description:
                  "Additional filter criteria in GCP log filter syntax",
              },
            },
            required: ["keyword"],
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      if (request.params.name !== "search_logs") {
        throw new McpError(
          ErrorCode.MethodNotFound,
          `Unknown tool: ${request.params.name}`,
        );
      }
      return await this.handleSearchLogs(request.params.arguments);
    });
  }

  private parseTimeInput(
    timeStr: string | undefined,
    defaultValue: Date,
  ): Date {
    if (!timeStr) return defaultValue;

    // Handle relative time formats
    if (timeStr === "now") return new Date();

    const relativeMatch = timeStr.match(/^-(\d+)(h|m)$/);
    if (relativeMatch) {
      const [, amount, unit] = relativeMatch;
      const ms =
        unit === "h" ? parseInt(amount) * 3600000 : parseInt(amount) * 60000;
      return new Date(Date.now() - ms);
    }

    // Handle ISO format
    const date = new Date(timeStr);
    if (isNaN(date.getTime())) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid time format: ${timeStr}`,
      );
    }
    return date;
  }

  private async handleSearchLogs(args: any) {
    // Validate required parameters
    if (typeof args.keyword !== "string") {
      throw new McpError(ErrorCode.InvalidParams, "Invalid keyword parameter");
    }

    const maxResults = args.maxResults || 100;
    const now = new Date();
    const defaultStartTime = new Date(now.getTime() - 3600000); // Default to last hour

    try {
      const startTime = this.parseTimeInput(args.startTime, defaultStartTime);
      const endTime = this.parseTimeInput(args.endTime, now);

      // Start with base filter including configured namespace
      let filter = `resource.type="k8s_container" AND resource.labels.namespace_name="${NAMESPACE}"`;

      // Add app or pod name filter if provided in input or configured in env
      const appNameToUse = args.appName || APP_NAME;
      const podNameToUse = args.podName || POD_NAME;
      if (appNameToUse) {
        filter += ` AND labels.k8s-pod/app="${appNameToUse}"`;
      } else if (podNameToUse) {
        filter += ` AND resource.labels.pod_name="${podNameToUse}"`;
      }

      // Add user's custom filter if provided
      if (args.filter) {
        filter += ` AND (${args.filter})`;
      }

      // Add timestamp constraints
      filter += ` AND timestamp >= "${startTime.toISOString()}" AND timestamp <= "${endTime.toISOString()}"`;

      // Add keyword search using GCP's SEARCH operator
      if (args.keyword) {
        filter += ` AND SEARCH("${args.keyword}")`;
      }

      filter = filter.replace(/\s+/g, " ").trim();

      // Add bucket name if provided in input or configured in env
      const bucketToUse = args.bucket || BUCKET_NAME;
      // Use the bucket to build the resource name if has bucketToUse
      const resourceNames = BUCKET_PROJECT_ID && bucketToUse
        ? [`projects/${BUCKET_PROJECT_ID}/locations/${LOCATION}/buckets/${bucketToUse}/views/_AllLogs`]
        : undefined

      const [entries] = await this.logging.getEntries({
        filter,
        orderBy: "timestamp desc",
        pageSize: maxResults,
        resourceNames
      });

      const formattedLogs = entries.map((entry) => ({
        timestamp: entry.metadata.timestamp,
        message: entry.data,
        severity: entry.metadata.severity,
        labels: entry.metadata.labels || {},
        resource: {
          type: entry.metadata.resource?.type,
          labels: entry.metadata.resource?.labels || {},
        },
      }));

      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(formattedLogs, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error getting trace logs: ${
              error instanceof Error ? error.message : String(error)
            }`,
          },
        ],
        isError: true,
      };
    }
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error("GCP Logs MCP server running on stdio");
  }
}

const server = new GcpLogsServer();
server.run().catch(console.error);