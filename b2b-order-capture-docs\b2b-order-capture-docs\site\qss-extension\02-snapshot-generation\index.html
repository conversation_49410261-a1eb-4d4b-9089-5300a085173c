
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/qss-extension/02-snapshot-generation/">
      
      
        <link rel="prev" href="../01-plugin-architecture/">
      
      
        <link rel="next" href="../03-storage-management/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Snapshot Generation - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#quote-snapshot-generation" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Snapshot Generation
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" checked>
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#snapshot-generation-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Snapshot Generation Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Snapshot Generation Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#snapshot-generation-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Snapshot Generation Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#snapshot-generation-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Snapshot Generation Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#business-rules-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Business Rules Engine
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Business Rules Engine">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-business-rules" class="md-nav__link">
    <span class="md-ellipsis">
      Core Business Rules
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#rule-decision-model" class="md-nav__link">
    <span class="md-ellipsis">
      Rule Decision Model
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#prime-quote-detection" class="md-nav__link">
    <span class="md-ellipsis">
      Prime Quote Detection
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Prime Quote Detection">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prime-quote-logic" class="md-nav__link">
    <span class="md-ellipsis">
      Prime Quote Logic
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#change-detection-logic" class="md-nav__link">
    <span class="md-ellipsis">
      Change Detection Logic
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Change Detection Logic">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-change-analyzer" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Change Analyzer
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Optimization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Optimization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#caching-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Caching Strategy
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#monitoring-and-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring and Metrics
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Monitoring and Metrics">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#metrics-collection" class="md-nav__link">
    <span class="md-ellipsis">
      Metrics Collection
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#snapshot-generation-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Snapshot Generation Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Snapshot Generation Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#snapshot-generation-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Snapshot Generation Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#snapshot-generation-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Snapshot Generation Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#business-rules-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Business Rules Engine
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Business Rules Engine">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-business-rules" class="md-nav__link">
    <span class="md-ellipsis">
      Core Business Rules
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#rule-decision-model" class="md-nav__link">
    <span class="md-ellipsis">
      Rule Decision Model
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#prime-quote-detection" class="md-nav__link">
    <span class="md-ellipsis">
      Prime Quote Detection
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Prime Quote Detection">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prime-quote-logic" class="md-nav__link">
    <span class="md-ellipsis">
      Prime Quote Logic
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#change-detection-logic" class="md-nav__link">
    <span class="md-ellipsis">
      Change Detection Logic
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Change Detection Logic">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-change-analyzer" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Change Analyzer
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Optimization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Optimization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#caching-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Caching Strategy
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#monitoring-and-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring and Metrics
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Monitoring and Metrics">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#metrics-collection" class="md-nav__link">
    <span class="md-ellipsis">
      Metrics Collection
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="quote-snapshot-generation">Quote Snapshot Generation<a class="headerlink" href="#quote-snapshot-generation" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#snapshot-generation-overview">Snapshot Generation Overview</a></li>
<li><a href="#business-rules-engine">Business Rules Engine</a></li>
<li><a href="#prime-quote-detection">Prime Quote Detection</a></li>
<li><a href="#change-detection-logic">Change Detection Logic</a></li>
<li><a href="#performance-optimization">Performance Optimization</a></li>
<li><a href="#monitoring-and-metrics">Monitoring and Metrics</a></li>
</ul>
<h2 id="snapshot-generation-overview">Snapshot Generation Overview<a class="headerlink" href="#snapshot-generation-overview" title="Permanent link">&para;</a></h2>
<h3 id="snapshot-generation-architecture">Snapshot Generation Architecture<a class="headerlink" href="#snapshot-generation-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Quote Change Event&quot;
        A[Quote Update Request]
        B[Quote Delta]
        C[Change Context]
        D[Tenant Information]
    end

    subgraph &quot;Snapshot Decision Engine&quot;
        E[Business Rules Evaluator]
        F[Prime Quote Detector]
        G[Change Analyzer]
        H[Value Assessor]
    end

    subgraph &quot;Decision Logic&quot;
        I[isPrime Check]
        J[Description Change Check]
        K[High Value Check]
        L[Enterprise Customer Check]
        M[Special Quote Type Check]
    end

    subgraph &quot;Snapshot Actions&quot;
        N[Generate Snapshot]
        O[Skip Snapshot]
        P[Audit Log]
        Q[Metrics Update]
    end

    A --&gt; B
    B --&gt; C
    C --&gt; D

    B --&gt; E
    E --&gt; F
    E --&gt; G
    E --&gt; H

    F --&gt; I
    G --&gt; J
    H --&gt; K
    H --&gt; L
    H --&gt; M

    I --&gt; N
    J --&gt; N
    K --&gt; N
    L --&gt; N
    M --&gt; N

    I --&gt; O
    J --&gt; O
    K --&gt; O
    L --&gt; O
    M --&gt; O

    N --&gt; P
    O --&gt; P
    P --&gt; Q

    style E fill:#673ab7,stroke:#512da8,color:#ffffff
    style N fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style I fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="snapshot-generation-flow">Snapshot Generation Flow<a class="headerlink" href="#snapshot-generation-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant QES as Quote Engine Service
    participant QSS as Quote Storage Service
    participant Plugin as QSS Extension Plugin
    participant Rules as Business Rules Engine
    participant Storage as Snapshot Storage
    participant Audit as Audit Service

    QES-&gt;&gt;QSS: Quote Change Event
    QSS-&gt;&gt;Plugin: Evaluate Snapshot Generation
    Plugin-&gt;&gt;Rules: Apply Business Rules

    Rules-&gt;&gt;Rules: Check isPrime Attribute
    Rules-&gt;&gt;Rules: Check Description Changes
    Rules-&gt;&gt;Rules: Check Deal Value
    Rules-&gt;&gt;Rules: Check Customer Segment
    Rules-&gt;&gt;Rules: Check Quote Type

    alt Any Rule Matches
        Rules--&gt;&gt;Plugin: Generate Snapshot = true
        Plugin--&gt;&gt;QSS: Snapshot Required
        QSS-&gt;&gt;Storage: Create Quote Snapshot
        Storage--&gt;&gt;QSS: Snapshot ID
        QSS-&gt;&gt;Audit: Log Snapshot Creation
        Audit--&gt;&gt;QSS: Audit Entry Created
        QSS--&gt;&gt;QES: Snapshot Generated Successfully
    else No Rules Match
        Rules--&gt;&gt;Plugin: Generate Snapshot = false
        Plugin--&gt;&gt;QSS: No Snapshot Required
        QSS-&gt;&gt;Audit: Log Skip Decision
        Audit--&gt;&gt;QSS: Audit Entry Created
        QSS--&gt;&gt;QES: No Snapshot Generated
    end
</code></pre></div>
<h2 id="business-rules-engine">Business Rules Engine<a class="headerlink" href="#business-rules-engine" title="Permanent link">&para;</a></h2>
<h3 id="core-business-rules">Core Business Rules<a class="headerlink" href="#core-business-rules" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Business rules for quote snapshot generation</span>
<span class="cm"> */</span>
<span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">SnapshotGenerationRulesEngine</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="c1">// Configuration constants</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">IS_PRIME</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;isPrime&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">QUOTE_TYPE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;quoteType&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">CUSTOMER_SEGMENT</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;customerSegment&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">DEAL_VALUE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;dealValue&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">QUOTE_STATUS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;status&quot;</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.rules.high-value-threshold:100000.0}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">double</span><span class="w"> </span><span class="n">highValueThreshold</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.rules.enterprise-segments}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">enterpriseSegments</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.rules.special-quote-types}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">specialQuoteTypes</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Evaluate all business rules for snapshot generation</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">SnapshotDecision</span><span class="w"> </span><span class="nf">evaluateSnapshotRules</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteChangeContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Evaluating snapshot rules for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>

<span class="w">        </span><span class="n">SnapshotDecision</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">decisionBuilder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">SnapshotDecision</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">quoteId</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">timestamp</span><span class="p">(</span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Rule 1: Prime Quote Rule</span>
<span class="w">        </span><span class="n">RuleResult</span><span class="w"> </span><span class="n">primeResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">evaluatePrimeQuoteRule</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">        </span><span class="n">decisionBuilder</span><span class="p">.</span><span class="na">addRuleResult</span><span class="p">(</span><span class="s">&quot;PRIME_QUOTE&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">primeResult</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Rule 2: Description Change Rule</span>
<span class="w">        </span><span class="n">RuleResult</span><span class="w"> </span><span class="n">descriptionResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">evaluateDescriptionChangeRule</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">        </span><span class="n">decisionBuilder</span><span class="p">.</span><span class="na">addRuleResult</span><span class="p">(</span><span class="s">&quot;DESCRIPTION_CHANGE&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">descriptionResult</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Rule 3: High Value Quote Rule</span>
<span class="w">        </span><span class="n">RuleResult</span><span class="w"> </span><span class="n">highValueResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">evaluateHighValueQuoteRule</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">        </span><span class="n">decisionBuilder</span><span class="p">.</span><span class="na">addRuleResult</span><span class="p">(</span><span class="s">&quot;HIGH_VALUE&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">highValueResult</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Rule 4: Enterprise Customer Rule</span>
<span class="w">        </span><span class="n">RuleResult</span><span class="w"> </span><span class="n">enterpriseResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">evaluateEnterpriseCustomerRule</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">        </span><span class="n">decisionBuilder</span><span class="p">.</span><span class="na">addRuleResult</span><span class="p">(</span><span class="s">&quot;ENTERPRISE_CUSTOMER&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">enterpriseResult</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Rule 5: Special Quote Type Rule</span>
<span class="w">        </span><span class="n">RuleResult</span><span class="w"> </span><span class="n">specialTypeResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">evaluateSpecialQuoteTypeRule</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">        </span><span class="n">decisionBuilder</span><span class="p">.</span><span class="na">addRuleResult</span><span class="p">(</span><span class="s">&quot;SPECIAL_QUOTE_TYPE&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">specialTypeResult</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Rule 6: Status Change Rule</span>
<span class="w">        </span><span class="n">RuleResult</span><span class="w"> </span><span class="n">statusResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">evaluateStatusChangeRule</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">);</span>
<span class="w">        </span><span class="n">decisionBuilder</span><span class="p">.</span><span class="na">addRuleResult</span><span class="p">(</span><span class="s">&quot;STATUS_CHANGE&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">statusResult</span><span class="p">);</span>

<span class="w">        </span><span class="n">SnapshotDecision</span><span class="w"> </span><span class="n">decision</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">decisionBuilder</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Snapshot decision for quote {}: {} (triggered by: {})&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span>
<span class="w">                </span><span class="n">decision</span><span class="p">.</span><span class="na">shouldGenerateSnapshot</span><span class="p">(),</span><span class="w"> </span>
<span class="w">                </span><span class="n">decision</span><span class="p">.</span><span class="na">getTriggeredRules</span><span class="p">());</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">decision</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Rule 1: Prime Quote Detection</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">RuleResult</span><span class="w"> </span><span class="nf">evaluatePrimeQuoteRule</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kt">boolean</span><span class="w"> </span><span class="n">isPrime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">isPrimeQuote</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">RuleResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">ruleName</span><span class="p">(</span><span class="s">&quot;PRIME_QUOTE&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">triggered</span><span class="p">(</span><span class="n">isPrime</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">reason</span><span class="p">(</span><span class="n">isPrime</span><span class="w"> </span><span class="o">?</span><span class="w"> </span><span class="s">&quot;Quote is marked as prime&quot;</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Quote is not prime&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">priority</span><span class="p">(</span><span class="n">RulePriority</span><span class="p">.</span><span class="na">HIGH</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Rule 2: Description Change Detection</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">RuleResult</span><span class="w"> </span><span class="nf">evaluateDescriptionChangeRule</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kt">boolean</span><span class="w"> </span><span class="n">hasDescriptionChange</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">hasDescriptionChanged</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">RuleResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">ruleName</span><span class="p">(</span><span class="s">&quot;DESCRIPTION_CHANGE&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">triggered</span><span class="p">(</span><span class="n">hasDescriptionChange</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">reason</span><span class="p">(</span><span class="n">hasDescriptionChange</span><span class="w"> </span><span class="o">?</span><span class="w"> </span><span class="s">&quot;Quote description was modified&quot;</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s">&quot;No description change&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">priority</span><span class="p">(</span><span class="n">RulePriority</span><span class="p">.</span><span class="na">MEDIUM</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Rule 3: High Value Quote Detection</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">RuleResult</span><span class="w"> </span><span class="nf">evaluateHighValueQuoteRule</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kt">boolean</span><span class="w"> </span><span class="n">isHighValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">isHighValueQuote</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">        </span><span class="kt">double</span><span class="w"> </span><span class="n">dealValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getDealValue</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">RuleResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">ruleName</span><span class="p">(</span><span class="s">&quot;HIGH_VALUE&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">triggered</span><span class="p">(</span><span class="n">isHighValue</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">reason</span><span class="p">(</span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;Deal value: %.2f (threshold: %.2f)&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">dealValue</span><span class="p">,</span><span class="w"> </span><span class="n">highValueThreshold</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">priority</span><span class="p">(</span><span class="n">RulePriority</span><span class="p">.</span><span class="na">HIGH</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">metadata</span><span class="p">(</span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;dealValue&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">dealValue</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;threshold&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">highValueThreshold</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Rule 4: Enterprise Customer Detection</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">RuleResult</span><span class="w"> </span><span class="nf">evaluateEnterpriseCustomerRule</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kt">boolean</span><span class="w"> </span><span class="n">isEnterprise</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">isEnterpriseCustomer</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">segment</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getCustomerSegment</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">RuleResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">ruleName</span><span class="p">(</span><span class="s">&quot;ENTERPRISE_CUSTOMER&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">triggered</span><span class="p">(</span><span class="n">isEnterprise</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">reason</span><span class="p">(</span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;Customer segment: %s&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">segment</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">priority</span><span class="p">(</span><span class="n">RulePriority</span><span class="p">.</span><span class="na">MEDIUM</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">metadata</span><span class="p">(</span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;customerSegment&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">segment</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Rule 5: Special Quote Type Detection</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">RuleResult</span><span class="w"> </span><span class="nf">evaluateSpecialQuoteTypeRule</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kt">boolean</span><span class="w"> </span><span class="n">isSpecialType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">isSpecialQuoteType</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">quoteType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getQuoteType</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">RuleResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">ruleName</span><span class="p">(</span><span class="s">&quot;SPECIAL_QUOTE_TYPE&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">triggered</span><span class="p">(</span><span class="n">isSpecialType</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">reason</span><span class="p">(</span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;Quote type: %s&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteType</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">priority</span><span class="p">(</span><span class="n">RulePriority</span><span class="p">.</span><span class="na">MEDIUM</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">metadata</span><span class="p">(</span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;quoteType&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteType</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Rule 6: Status Change Detection</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">RuleResult</span><span class="w"> </span><span class="nf">evaluateStatusChangeRule</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteChangeContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kt">boolean</span><span class="w"> </span><span class="n">hasStatusChange</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">hasSignificantStatusChange</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">);</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">currentStatus</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getQuoteStatus</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">previousStatus</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getPreviousStatus</span><span class="p">();</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">RuleResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">ruleName</span><span class="p">(</span><span class="s">&quot;STATUS_CHANGE&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">triggered</span><span class="p">(</span><span class="n">hasStatusChange</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">reason</span><span class="p">(</span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;Status change: %s -&gt; %s&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">previousStatus</span><span class="p">,</span><span class="w"> </span><span class="n">currentStatus</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">priority</span><span class="p">(</span><span class="n">RulePriority</span><span class="p">.</span><span class="na">LOW</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">metadata</span><span class="p">(</span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;currentStatus&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">currentStatus</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;previousStatus&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">previousStatus</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="rule-decision-model">Rule Decision Model<a class="headerlink" href="#rule-decision-model" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Snapshot generation decision model</span>
<span class="cm"> */</span>
<span class="nd">@Data</span>
<span class="nd">@Builder</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">SnapshotDecision</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Instant</span><span class="w"> </span><span class="n">timestamp</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">RuleResult</span><span class="o">&gt;</span><span class="w"> </span><span class="n">ruleResults</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">shouldGenerateSnapshot</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">triggeredRules</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">SnapshotPriority</span><span class="w"> </span><span class="n">priority</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">metadata</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">class</span> <span class="nc">SnapshotDecisionBuilder</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">RuleResult</span><span class="o">&gt;</span><span class="w"> </span><span class="n">ruleResults</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HashMap</span><span class="o">&lt;&gt;</span><span class="p">();</span>

<span class="w">        </span><span class="kd">public</span><span class="w"> </span><span class="n">SnapshotDecisionBuilder</span><span class="w"> </span><span class="nf">addRuleResult</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">ruleName</span><span class="p">,</span><span class="w"> </span><span class="n">RuleResult</span><span class="w"> </span><span class="n">result</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">this</span><span class="p">.</span><span class="na">ruleResults</span><span class="p">.</span><span class="na">put</span><span class="p">(</span><span class="n">ruleName</span><span class="p">,</span><span class="w"> </span><span class="n">result</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="kd">public</span><span class="w"> </span><span class="n">SnapshotDecision</span><span class="w"> </span><span class="nf">build</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Determine if snapshot should be generated</span>
<span class="w">            </span><span class="kt">boolean</span><span class="w"> </span><span class="n">shouldGenerate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ruleResults</span><span class="p">.</span><span class="na">values</span><span class="p">().</span><span class="na">stream</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">anyMatch</span><span class="p">(</span><span class="n">RuleResult</span><span class="p">::</span><span class="n">isTriggered</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Get triggered rules</span>
<span class="w">            </span><span class="n">List</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">triggered</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ruleResults</span><span class="p">.</span><span class="na">values</span><span class="p">().</span><span class="na">stream</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">filter</span><span class="p">(</span><span class="n">RuleResult</span><span class="p">::</span><span class="n">isTriggered</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">map</span><span class="p">(</span><span class="n">RuleResult</span><span class="p">::</span><span class="n">getRuleName</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">collect</span><span class="p">(</span><span class="n">Collectors</span><span class="p">.</span><span class="na">toList</span><span class="p">());</span>

<span class="w">            </span><span class="c1">// Determine priority</span>
<span class="w">            </span><span class="n">SnapshotPriority</span><span class="w"> </span><span class="n">priority</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ruleResults</span><span class="p">.</span><span class="na">values</span><span class="p">().</span><span class="na">stream</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">filter</span><span class="p">(</span><span class="n">RuleResult</span><span class="p">::</span><span class="n">isTriggered</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">map</span><span class="p">(</span><span class="n">RuleResult</span><span class="p">::</span><span class="n">getPriority</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">max</span><span class="p">(</span><span class="n">Comparator</span><span class="p">.</span><span class="na">comparing</span><span class="p">(</span><span class="n">RulePriority</span><span class="p">::</span><span class="n">getLevel</span><span class="p">))</span>
<span class="w">                </span><span class="p">.</span><span class="na">map</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">mapToSnapshotPriority</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">orElse</span><span class="p">(</span><span class="n">SnapshotPriority</span><span class="p">.</span><span class="na">LOW</span><span class="p">);</span>

<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">SnapshotDecision</span><span class="p">(</span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">timestamp</span><span class="p">,</span><span class="w"> </span><span class="n">ruleResults</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                      </span><span class="n">shouldGenerate</span><span class="p">,</span><span class="w"> </span><span class="n">triggered</span><span class="p">,</span><span class="w"> </span><span class="n">priority</span><span class="p">,</span><span class="w"> </span><span class="n">metadata</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">SnapshotPriority</span><span class="w"> </span><span class="nf">mapToSnapshotPriority</span><span class="p">(</span><span class="n">RulePriority</span><span class="w"> </span><span class="n">rulePriority</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">rulePriority</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">case</span><span class="w"> </span><span class="n">HIGH</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">SnapshotPriority</span><span class="p">.</span><span class="na">CRITICAL</span><span class="p">;</span>
<span class="w">                </span><span class="k">case</span><span class="w"> </span><span class="n">MEDIUM</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">SnapshotPriority</span><span class="p">.</span><span class="na">HIGH</span><span class="p">;</span>
<span class="w">                </span><span class="k">case</span><span class="w"> </span><span class="n">LOW</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">SnapshotPriority</span><span class="p">.</span><span class="na">NORMAL</span><span class="p">;</span>
<span class="w">            </span><span class="p">};</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Individual rule result</span>
<span class="cm"> */</span>
<span class="nd">@Data</span>
<span class="nd">@Builder</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">RuleResult</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">ruleName</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">triggered</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">reason</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">RulePriority</span><span class="w"> </span><span class="n">priority</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">metadata</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Instant</span><span class="w"> </span><span class="n">evaluationTime</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Rule priority levels</span>
<span class="cm"> */</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">enum</span><span class="w"> </span><span class="n">RulePriority</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">HIGH</span><span class="p">(</span><span class="mi">3</span><span class="p">),</span><span class="w"> </span><span class="n">MEDIUM</span><span class="p">(</span><span class="mi">2</span><span class="p">),</span><span class="w"> </span><span class="n">LOW</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">level</span><span class="p">;</span>

<span class="w">    </span><span class="n">RulePriority</span><span class="p">(</span><span class="kt">int</span><span class="w"> </span><span class="n">level</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">level</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">level</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="nf">getLevel</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="n">level</span><span class="p">;</span><span class="w"> </span><span class="p">}</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Snapshot priority levels</span>
<span class="cm"> */</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">enum</span><span class="w"> </span><span class="n">SnapshotPriority</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">CRITICAL</span><span class="p">,</span><span class="w"> </span><span class="n">HIGH</span><span class="p">,</span><span class="w"> </span><span class="n">NORMAL</span><span class="p">,</span><span class="w"> </span><span class="n">LOW</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="prime-quote-detection">Prime Quote Detection<a class="headerlink" href="#prime-quote-detection" title="Permanent link">&para;</a></h2>
<h3 id="prime-quote-logic">Prime Quote Logic<a class="headerlink" href="#prime-quote-logic" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Prime quote detection and handling</span>
<span class="cm"> */</span>
<span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">PrimeQuoteDetector</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">IS_PRIME</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;isPrime&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">PRIME_REASON</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;primeReason&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">PRIME_TIMESTAMP</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;primeTimestamp&quot;</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Detect if quote is marked as prime</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">PrimeQuoteResult</span><span class="w"> </span><span class="nf">detectPrimeQuote</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Detecting prime quote status for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">PrimeQuoteResult</span><span class="p">.</span><span class="na">notPrime</span><span class="p">(</span><span class="s">&quot;No attributes found&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">Object</span><span class="w"> </span><span class="n">isPrimeValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="n">IS_PRIME</span><span class="p">);</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isPrimeValue</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">PrimeQuoteResult</span><span class="p">.</span><span class="na">notPrime</span><span class="p">(</span><span class="s">&quot;isPrime attribute not set&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="kt">boolean</span><span class="w"> </span><span class="n">isPrime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">evaluatePrimeValue</span><span class="p">(</span><span class="n">isPrimeValue</span><span class="p">);</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">isPrime</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">PrimeQuoteResult</span><span class="p">.</span><span class="na">notPrime</span><span class="p">(</span><span class="s">&quot;isPrime attribute is false&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Extract additional prime quote metadata</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">primeReason</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractPrimeReason</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">        </span><span class="n">Instant</span><span class="w"> </span><span class="n">primeTimestamp</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractPrimeTimestamp</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">PrimeQuoteResult</span><span class="p">.</span><span class="na">prime</span><span class="p">(</span><span class="n">primeReason</span><span class="p">,</span><span class="w"> </span><span class="n">primeTimestamp</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Evaluate prime value with multiple format support</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">evaluatePrimeValue</span><span class="p">(</span><span class="n">Object</span><span class="w"> </span><span class="n">isPrimeValue</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isPrimeValue</span><span class="w"> </span><span class="k">instanceof</span><span class="w"> </span><span class="n">Boolean</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="p">(</span><span class="n">Boolean</span><span class="p">)</span><span class="w"> </span><span class="n">isPrimeValue</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isPrimeValue</span><span class="w"> </span><span class="k">instanceof</span><span class="w"> </span><span class="n">String</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">String</span><span class="w"> </span><span class="n">strValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">((</span><span class="n">String</span><span class="p">)</span><span class="w"> </span><span class="n">isPrimeValue</span><span class="p">).</span><span class="na">trim</span><span class="p">().</span><span class="na">toLowerCase</span><span class="p">();</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="s">&quot;true&quot;</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">strValue</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s">&quot;yes&quot;</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">strValue</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s">&quot;1&quot;</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">strValue</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isPrimeValue</span><span class="w"> </span><span class="k">instanceof</span><span class="w"> </span><span class="n">Number</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="p">((</span><span class="n">Number</span><span class="p">)</span><span class="w"> </span><span class="n">isPrimeValue</span><span class="p">).</span><span class="na">intValue</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">warn</span><span class="p">(</span><span class="s">&quot;Unexpected isPrime value type: {} with value: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                </span><span class="n">isPrimeValue</span><span class="p">.</span><span class="na">getClass</span><span class="p">(),</span><span class="w"> </span><span class="n">isPrimeValue</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Extract prime reason from quote attributes</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="nf">extractPrimeReason</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Object</span><span class="w"> </span><span class="n">reasonObj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="n">PRIME_REASON</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">reasonObj</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">?</span><span class="w"> </span><span class="n">reasonObj</span><span class="p">.</span><span class="na">toString</span><span class="p">()</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Prime quote - reason not specified&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Extract prime timestamp from quote attributes</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Instant</span><span class="w"> </span><span class="nf">extractPrimeTimestamp</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Object</span><span class="w"> </span><span class="n">timestampObj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="n">PRIME_TIMESTAMP</span><span class="p">);</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">timestampObj</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">timestampObj</span><span class="w"> </span><span class="k">instanceof</span><span class="w"> </span><span class="n">String</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="n">Instant</span><span class="p">.</span><span class="na">parse</span><span class="p">((</span><span class="n">String</span><span class="p">)</span><span class="w"> </span><span class="n">timestampObj</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">timestampObj</span><span class="w"> </span><span class="k">instanceof</span><span class="w"> </span><span class="n">Long</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="n">Instant</span><span class="p">.</span><span class="na">ofEpochMilli</span><span class="p">((</span><span class="n">Long</span><span class="p">)</span><span class="w"> </span><span class="n">timestampObj</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">timestampObj</span><span class="w"> </span><span class="k">instanceof</span><span class="w"> </span><span class="n">Instant</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="p">(</span><span class="n">Instant</span><span class="p">)</span><span class="w"> </span><span class="n">timestampObj</span><span class="p">;</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">warn</span><span class="p">(</span><span class="s">&quot;Failed to parse prime timestamp: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">timestampObj</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Prime quote detection result</span>
<span class="cm"> */</span>
<span class="nd">@Data</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">PrimeQuoteResult</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">isPrime</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">reason</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Instant</span><span class="w"> </span><span class="n">timestamp</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">metadata</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="n">PrimeQuoteResult</span><span class="w"> </span><span class="nf">prime</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">reason</span><span class="p">,</span><span class="w"> </span><span class="n">Instant</span><span class="w"> </span><span class="n">timestamp</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">PrimeQuoteResult</span><span class="p">(</span><span class="kc">true</span><span class="p">,</span><span class="w"> </span><span class="n">reason</span><span class="p">,</span><span class="w"> </span><span class="n">timestamp</span><span class="p">,</span><span class="w"> </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="n">PrimeQuoteResult</span><span class="w"> </span><span class="nf">notPrime</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">reason</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">PrimeQuoteResult</span><span class="p">(</span><span class="kc">false</span><span class="p">,</span><span class="w"> </span><span class="n">reason</span><span class="p">,</span><span class="w"> </span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">(),</span><span class="w"> </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="change-detection-logic">Change Detection Logic<a class="headerlink" href="#change-detection-logic" title="Permanent link">&para;</a></h2>
<h3 id="quote-change-analyzer">Quote Change Analyzer<a class="headerlink" href="#quote-change-analyzer" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Comprehensive quote change detection and analysis</span>
<span class="cm"> */</span>
<span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QuoteChangeAnalyzer</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Analyze quote changes to determine snapshot necessity</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteChangeAnalysis</span><span class="w"> </span><span class="nf">analyzeChanges</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteChangeContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Analyzing changes for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>

<span class="w">        </span><span class="n">QuoteChangeAnalysis</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">analysisBuilder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">QuoteChangeAnalysis</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">quoteId</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">analysisTimestamp</span><span class="p">(</span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Analyze different types of changes</span>
<span class="w">        </span><span class="n">analyzeDescriptionChanges</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">analysisBuilder</span><span class="p">);</span>
<span class="w">        </span><span class="n">analyzeAttributeChanges</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">analysisBuilder</span><span class="p">);</span>
<span class="w">        </span><span class="n">analyzePricingChanges</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">analysisBuilder</span><span class="p">);</span>
<span class="w">        </span><span class="n">analyzeStatusChanges</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">analysisBuilder</span><span class="p">);</span>
<span class="w">        </span><span class="n">analyzeLineItemChanges</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">analysisBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="n">QuoteChangeAnalysis</span><span class="w"> </span><span class="n">analysis</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">analysisBuilder</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Change analysis for quote {}: {} significant changes detected&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">analysis</span><span class="p">.</span><span class="na">getSignificantChanges</span><span class="p">().</span><span class="na">size</span><span class="p">());</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">analysis</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Analyze description changes</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">analyzeDescriptionChanges</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteChangeContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                         </span><span class="n">QuoteChangeAnalysis</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">builder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">currentDescription</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getDescription</span><span class="p">();</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">previousDescription</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getPreviousDescription</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">hasSignificantDescriptionChange</span><span class="p">(</span><span class="n">currentDescription</span><span class="p">,</span><span class="w"> </span><span class="n">previousDescription</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">builder</span><span class="p">.</span><span class="na">addSignificantChange</span><span class="p">(</span><span class="n">ChangeType</span><span class="p">.</span><span class="na">DESCRIPTION</span><span class="p">,</span><span class="w"> </span>
<span class="w">                </span><span class="s">&quot;Description modified&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;previous&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">previousDescription</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;current&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">currentDescription</span><span class="p">));</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Analyze attribute changes</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">analyzeAttributeChanges</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteChangeContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                       </span><span class="n">QuoteChangeAnalysis</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">builder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">currentAttributes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">();</span>
<span class="w">        </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">previousAttributes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getPreviousAttributes</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">currentAttributes</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="n">currentAttributes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">();</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">previousAttributes</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="n">previousAttributes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Check for new attributes</span>
<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">key</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">currentAttributes</span><span class="p">.</span><span class="na">keySet</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">previousAttributes</span><span class="p">.</span><span class="na">containsKey</span><span class="p">(</span><span class="n">key</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">builder</span><span class="p">.</span><span class="na">addSignificantChange</span><span class="p">(</span><span class="n">ChangeType</span><span class="p">.</span><span class="na">ATTRIBUTE_ADDED</span><span class="p">,</span><span class="w"> </span>
<span class="w">                    </span><span class="s">&quot;New attribute: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">key</span><span class="p">,</span><span class="w"> </span>
<span class="w">                    </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;attribute&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">key</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;value&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">currentAttributes</span><span class="p">.</span><span class="na">get</span><span class="p">(</span><span class="n">key</span><span class="p">)));</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Check for modified attributes</span>
<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">key</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">currentAttributes</span><span class="p">.</span><span class="na">keySet</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">previousAttributes</span><span class="p">.</span><span class="na">containsKey</span><span class="p">(</span><span class="n">key</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">Object</span><span class="w"> </span><span class="n">currentValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">currentAttributes</span><span class="p">.</span><span class="na">get</span><span class="p">(</span><span class="n">key</span><span class="p">);</span>
<span class="w">                </span><span class="n">Object</span><span class="w"> </span><span class="n">previousValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">previousAttributes</span><span class="p">.</span><span class="na">get</span><span class="p">(</span><span class="n">key</span><span class="p">);</span>

<span class="w">                </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">Objects</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">currentValue</span><span class="p">,</span><span class="w"> </span><span class="n">previousValue</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">builder</span><span class="p">.</span><span class="na">addSignificantChange</span><span class="p">(</span><span class="n">ChangeType</span><span class="p">.</span><span class="na">ATTRIBUTE_MODIFIED</span><span class="p">,</span><span class="w"> </span>
<span class="w">                        </span><span class="s">&quot;Modified attribute: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">key</span><span class="p">,</span><span class="w"> </span>
<span class="w">                        </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;attribute&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">key</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;previous&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">previousValue</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;current&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">currentValue</span><span class="p">));</span>
<span class="w">                </span><span class="p">}</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Check for removed attributes</span>
<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">key</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">previousAttributes</span><span class="p">.</span><span class="na">keySet</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">currentAttributes</span><span class="p">.</span><span class="na">containsKey</span><span class="p">(</span><span class="n">key</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">builder</span><span class="p">.</span><span class="na">addSignificantChange</span><span class="p">(</span><span class="n">ChangeType</span><span class="p">.</span><span class="na">ATTRIBUTE_REMOVED</span><span class="p">,</span><span class="w"> </span>
<span class="w">                    </span><span class="s">&quot;Removed attribute: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">key</span><span class="p">,</span><span class="w"> </span>
<span class="w">                    </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;attribute&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">key</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;previousValue&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">previousAttributes</span><span class="p">.</span><span class="na">get</span><span class="p">(</span><span class="n">key</span><span class="p">)));</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Check if description change is significant</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">hasSignificantDescriptionChange</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">current</span><span class="p">,</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">previous</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">current</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">previous</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">current</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">previous</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>

<span class="w">        </span><span class="c1">// Normalize whitespace and compare</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">normalizedCurrent</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">current</span><span class="p">.</span><span class="na">trim</span><span class="p">().</span><span class="na">replaceAll</span><span class="p">(</span><span class="s">&quot;\\s+&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot; &quot;</span><span class="p">);</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">normalizedPrevious</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">previous</span><span class="p">.</span><span class="na">trim</span><span class="p">().</span><span class="na">replaceAll</span><span class="p">(</span><span class="s">&quot;\\s+&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot; &quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="o">!</span><span class="n">normalizedCurrent</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">normalizedPrevious</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Quote change analysis result</span>
<span class="cm"> */</span>
<span class="nd">@Data</span>
<span class="nd">@Builder</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QuoteChangeAnalysis</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Instant</span><span class="w"> </span><span class="n">analysisTimestamp</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">SignificantChange</span><span class="o">&gt;</span><span class="w"> </span><span class="n">significantChanges</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ChangeImpact</span><span class="w"> </span><span class="n">overallImpact</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">metadata</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">hasSignificantChanges</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="o">!</span><span class="n">significantChanges</span><span class="p">.</span><span class="na">isEmpty</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QuoteChangeAnalysisBuilder</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">SignificantChange</span><span class="o">&gt;</span><span class="w"> </span><span class="n">significantChanges</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ArrayList</span><span class="o">&lt;&gt;</span><span class="p">();</span>

<span class="w">        </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteChangeAnalysisBuilder</span><span class="w"> </span><span class="nf">addSignificantChange</span><span class="p">(</span><span class="n">ChangeType</span><span class="w"> </span><span class="n">type</span><span class="p">,</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">description</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                                             </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">details</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">this</span><span class="p">.</span><span class="na">significantChanges</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">SignificantChange</span><span class="p">(</span><span class="n">type</span><span class="p">,</span><span class="w"> </span><span class="n">description</span><span class="p">,</span><span class="w"> </span><span class="n">details</span><span class="p">,</span><span class="w"> </span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">()));</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Significant change record</span>
<span class="cm"> */</span>
<span class="nd">@Data</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">SignificantChange</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ChangeType</span><span class="w"> </span><span class="n">type</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">description</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">details</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Instant</span><span class="w"> </span><span class="n">timestamp</span><span class="p">;</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Types of quote changes</span>
<span class="cm"> */</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">enum</span><span class="w"> </span><span class="n">ChangeType</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">DESCRIPTION</span><span class="p">,</span><span class="w"> </span><span class="n">ATTRIBUTE_ADDED</span><span class="p">,</span><span class="w"> </span><span class="n">ATTRIBUTE_MODIFIED</span><span class="p">,</span><span class="w"> </span><span class="n">ATTRIBUTE_REMOVED</span><span class="p">,</span>
<span class="w">    </span><span class="n">PRICING</span><span class="p">,</span><span class="w"> </span><span class="n">STATUS</span><span class="p">,</span><span class="w"> </span><span class="n">LINE_ITEM_ADDED</span><span class="p">,</span><span class="w"> </span><span class="n">LINE_ITEM_MODIFIED</span><span class="p">,</span><span class="w"> </span><span class="n">LINE_ITEM_REMOVED</span>
<span class="p">}</span>

<span class="cm">/**</span>
<span class="cm"> * Change impact levels</span>
<span class="cm"> */</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">enum</span><span class="w"> </span><span class="n">ChangeImpact</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">CRITICAL</span><span class="p">,</span><span class="w"> </span><span class="n">HIGH</span><span class="p">,</span><span class="w"> </span><span class="n">MEDIUM</span><span class="p">,</span><span class="w"> </span><span class="n">LOW</span><span class="p">,</span><span class="w"> </span><span class="n">NONE</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="performance-optimization">Performance Optimization<a class="headerlink" href="#performance-optimization" title="Permanent link">&para;</a></h2>
<h3 id="caching-strategy">Caching Strategy<a class="headerlink" href="#caching-strategy" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Performance optimization for snapshot generation</span>
<span class="cm"> */</span>
<span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">SnapshotGenerationOptimizer</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">CacheManager</span><span class="w"> </span><span class="n">cacheManager</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.performance.cache-enabled:true}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">cacheEnabled</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.performance.batch-size:100}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">batchSize</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Optimized snapshot decision with caching</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="nd">@Cacheable</span><span class="p">(</span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;snapshotDecisions&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">key</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;#quote.id + &#39;_&#39; + #quote.version&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">               </span><span class="n">condition</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;#root.target.cacheEnabled&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">SnapshotDecision</span><span class="w"> </span><span class="nf">getOptimizedSnapshotDecision</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteChangeContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Computing snapshot decision for quote: {} (cache miss)&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Perform expensive computation</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">computeSnapshotDecision</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Batch processing for multiple quotes</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">SnapshotDecision</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">processBatchSnapshotDecisions</span><span class="p">(</span>
<span class="w">            </span><span class="n">List</span><span class="o">&lt;</span><span class="n">QuoteChangeRequest</span><span class="o">&gt;</span><span class="w"> </span><span class="n">requests</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Processing batch of {} snapshot decisions&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">requests</span><span class="p">.</span><span class="na">size</span><span class="p">());</span>

<span class="w">        </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">SnapshotDecision</span><span class="o">&gt;</span><span class="w"> </span><span class="n">results</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ConcurrentHashMap</span><span class="o">&lt;&gt;</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Process in batches to avoid memory issues</span>
<span class="w">        </span><span class="n">Lists</span><span class="p">.</span><span class="na">partition</span><span class="p">(</span><span class="n">requests</span><span class="p">,</span><span class="w"> </span><span class="n">batchSize</span><span class="p">).</span><span class="na">parallelStream</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">forEach</span><span class="p">(</span><span class="n">batch</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">processBatch</span><span class="p">(</span><span class="n">batch</span><span class="p">,</span><span class="w"> </span><span class="n">results</span><span class="p">));</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Completed batch processing: {} decisions generated&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">results</span><span class="p">.</span><span class="na">size</span><span class="p">());</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">results</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">processBatch</span><span class="p">(</span><span class="n">List</span><span class="o">&lt;</span><span class="n">QuoteChangeRequest</span><span class="o">&gt;</span><span class="w"> </span><span class="n">batch</span><span class="p">,</span><span class="w"> </span>
<span class="w">                            </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">SnapshotDecision</span><span class="o">&gt;</span><span class="w"> </span><span class="n">results</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">batch</span><span class="p">.</span><span class="na">parallelStream</span><span class="p">().</span><span class="na">forEach</span><span class="p">(</span><span class="n">request</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">SnapshotDecision</span><span class="w"> </span><span class="n">decision</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getOptimizedSnapshotDecision</span><span class="p">(</span>
<span class="w">                    </span><span class="n">request</span><span class="p">.</span><span class="na">getQuote</span><span class="p">(),</span><span class="w"> </span><span class="n">request</span><span class="p">.</span><span class="na">getContext</span><span class="p">());</span>
<span class="w">                </span><span class="n">results</span><span class="p">.</span><span class="na">put</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getQuote</span><span class="p">().</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">decision</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error processing snapshot decision for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                         </span><span class="n">request</span><span class="p">.</span><span class="na">getQuote</span><span class="p">().</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="monitoring-and-metrics">Monitoring and Metrics<a class="headerlink" href="#monitoring-and-metrics" title="Permanent link">&para;</a></h2>
<h3 id="metrics-collection">Metrics Collection<a class="headerlink" href="#metrics-collection" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Metrics collection for snapshot generation</span>
<span class="cm"> */</span>
<span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">SnapshotGenerationMetrics</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">MeterRegistry</span><span class="w"> </span><span class="n">meterRegistry</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Counter</span><span class="w"> </span><span class="n">snapshotsGenerated</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Counter</span><span class="w"> </span><span class="n">snapshotsSkipped</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Timer</span><span class="w"> </span><span class="n">decisionTime</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Gauge</span><span class="w"> </span><span class="n">activeDecisions</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">SnapshotGenerationMetrics</span><span class="p">(</span><span class="n">MeterRegistry</span><span class="w"> </span><span class="n">meterRegistry</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">meterRegistry</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">meterRegistry</span><span class="p">;</span>

<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">snapshotsGenerated</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Counter</span><span class="p">.</span><span class="na">builder</span><span class="p">(</span><span class="s">&quot;qss.snapshots.generated&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">description</span><span class="p">(</span><span class="s">&quot;Number of snapshots generated&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">tag</span><span class="p">(</span><span class="s">&quot;component&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;qss-extension&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">);</span>

<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">snapshotsSkipped</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Counter</span><span class="p">.</span><span class="na">builder</span><span class="p">(</span><span class="s">&quot;qss.snapshots.skipped&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">description</span><span class="p">(</span><span class="s">&quot;Number of snapshots skipped&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">tag</span><span class="p">(</span><span class="s">&quot;component&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;qss-extension&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">);</span>

<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">decisionTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Timer</span><span class="p">.</span><span class="na">builder</span><span class="p">(</span><span class="s">&quot;qss.decision.duration&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">description</span><span class="p">(</span><span class="s">&quot;Time taken to make snapshot decisions&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">tag</span><span class="p">(</span><span class="s">&quot;component&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;qss-extension&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">);</span>

<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">activeDecisions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Gauge</span><span class="p">.</span><span class="na">builder</span><span class="p">(</span><span class="s">&quot;qss.decisions.active&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">description</span><span class="p">(</span><span class="s">&quot;Number of active snapshot decisions&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">tag</span><span class="p">(</span><span class="s">&quot;component&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;qss-extension&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">,</span><span class="w"> </span><span class="n">SnapshotGenerationMetrics</span><span class="p">::</span><span class="n">getActiveDecisionCount</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">recordSnapshotGenerated</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">reason</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">snapshotsGenerated</span><span class="p">.</span><span class="na">increment</span><span class="p">(</span><span class="n">Tags</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;reason&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">reason</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">recordSnapshotSkipped</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">reason</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">snapshotsSkipped</span><span class="p">.</span><span class="na">increment</span><span class="p">(</span><span class="n">Tags</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;reason&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">reason</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Timer</span><span class="p">.</span><span class="na">Sample</span><span class="w"> </span><span class="nf">startDecisionTimer</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">Timer</span><span class="p">.</span><span class="na">start</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">recordDecisionTime</span><span class="p">(</span><span class="n">Timer</span><span class="p">.</span><span class="na">Sample</span><span class="w"> </span><span class="n">sample</span><span class="p">,</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">outcome</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">sample</span><span class="p">.</span><span class="na">stop</span><span class="p">(</span><span class="n">Timer</span><span class="p">.</span><span class="na">builder</span><span class="p">(</span><span class="s">&quot;qss.decision.duration&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">tag</span><span class="p">(</span><span class="s">&quot;outcome&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">outcome</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">double</span><span class="w"> </span><span class="nf">getActiveDecisionCount</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implementation to get active decision count</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"> </span><span class="c1">// Placeholder</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../03-storage-management/">Storage Management →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>