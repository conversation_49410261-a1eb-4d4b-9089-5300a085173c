# Typescript API Project Guidelines
## Goal
Generate a typescript REST API project with a sample controller demonstrating the typical CRUD operations for a task list.
The task list is a collection in memory and the controller class provides access to manage tasks.

## Rules
- Rule: All code must be properly documented
- Must: Follow the coding style guide
- MUST: Follow all required instructions.
- Shall: Write unit tests for new features

## Recommendations
- Should: Use meaningful variable names
- Recommended: Break down complex functions
- Should: Review code before submitting

## Requirements
- Required: use node ^20 and typescript ^5 and ts-node ^10.
- Required: the project you create must use winston logger console transport, do not log to file.
- Required: the project you create must use express and cors. 
- Required: the project must include .env file including:
HOST=http://localhost
PORT=8080
CORS_ORIGIN=*
- Required: The project must have a config folder with configuration files for each environment: default, dev, st, and pr.
- Required: before generating the project code, ask user to enter a GCP Project ID. Use the provided project id as value for the environment variable GCP_PROJECT_ID.
- Required: All code files must have introduction comments and include "Property of TELUS" at the top.
- Required: Use TypeScript for all new code
- Needs: Proper error handling
- Required: Follow Git commit message conventions
- Required: use swagger-jsdoc and swagger-ui-express to generate swagger ui for testing the api controller endpoints.
- Required: add /api-docs route to access the swagger ui.
- Required: include a brief description of the code and provide instructions to run and test the application.
- Required: the project must have the following folder structure:
   - root\README.md
   - root\DOCKERFILE
   - root\.gitignore
   - root\api\swagger.oas.json
   - root\app\.env
   - root\app\eslint.config
   - root\app\package.json
   - root\app\swaggerGenerate.ts
   - root\app\swaggerSpec.ts
   - root\app\tsconfig.json
   - root\app\tsconfig.test.json
   - root\app\src\index.ts
   - root\app\src\server.ts
   - root\app\src\config\
   - root\app\src\controllers\
   - root\app\src\models\
   - root\app\src\routes\
   - root\app\src\services\
   - root\app\src\tests\
   - root\app\src\utils\
