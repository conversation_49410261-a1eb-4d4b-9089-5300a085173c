{"name": "@telus/mcp-api-marketplace", "version": "0.0.7", "description": "API Marketplace MCP server for TELUS", "keywords": ["telus", "mcp", "api", "marketplace"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-api-marketplace"}, "license": "MIT", "author": "<PERSON> (@isand3r)", "type": "module", "main": "dist/index.js", "bin": {"amp": "./dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node --eval \"import('fs').then(fs => fs.chmodSync('dist/index.js', '755'))\"", "inspector": "pnpm dlx @modelcontextprotocol/inspector dist/index.js", "prepare": "pnpm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "@telus/sdk-kong-auth": "^3.1.0", "node-cache": "^5.1.2", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}