import * as contentful from "contentful";

const CONTENTFUL_CLIENT = contentful.createClient({
  space: process.env.CONTENTFUL_SPACE!,
  accessToken: process.env.CONTENTFUL_TOKEN!,
  environment: process.env.CONTENTFUL_ENVIRONMENT || "master",
});

export async function getSingleEntry(id: string, locale?: string) {
  const entry = await CONTENTFUL_CLIENT.getEntry(id, {
    include: 0,
    ...(locale && { locale }),
  });

  return entry.fields;
}

export async function getEntries(query?: Record<string, any>) {
  const res = await CONTENTFUL_CLIENT.getEntries({
    ...query,
    include: 0,
    ...(query?.locale && { locale: query.locale }),
  });

  return {
    items: res.items.map((item) => item.fields),
  };
}
