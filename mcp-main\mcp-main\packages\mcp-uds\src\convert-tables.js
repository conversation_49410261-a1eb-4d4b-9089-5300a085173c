/**
 * Convert Tables Script
 *
 * This script converts PropsTable and TokensTable JSX components in MDX files
 * to Markdown tables for better compatibility and rendering.
 *
 * It extracts component props from React components and generates
 * documentation tables for props and design tokens.
 */

const fs = require("fs");
const path = require("path");
const PropTypes = require("prop-types");

// Import the components
const componentsWeb = require("@telus-uds/components-web");
const componentsBase = require("@telus-uds/components-base");

// Configuration
const PATHS = {
  docsDir: path.join(
    __dirname,
    "packages/docusaurus-plugin-component-docs-pages/docs",
  ),
  componentsTokensPath: path.join(
    __dirname,
    "packages/system-theme-tokens/src/components.js",
  ),
};

// Markdown table templates
const TABLE_TEMPLATES = {
  props: `| Name | Type | Platform | Default | Description |
|------|------|----------|---------|-------------|`,
  tokens: `| Prop Name | Token Property | Token Type |
|-----------|----------------|---------|`,
};

/**
 * Load component tokens from the system-theme-tokens package
 * @returns {Object} Component tokens object
 */
function loadComponentTokens() {
  try {
    const content = fs.readFileSync(PATHS.componentsTokensPath, "utf8");
    // Convert export default to module.exports for Node.js compatibility
    return eval(`(${content.replace("export default", "module.exports =")})`);
  } catch (error) {
    console.error(`Error loading component tokens: ${error.message}`);
    return {};
  }
}

/**
 * Map PropTypes to string type names
 * @param {PropType} propType - React PropType
 * @returns {string} Type name
 */
function mapPropTypeToString(propType) {
  const propTypeMap = {
    [PropTypes.string]: "string",
    [PropTypes.bool]: "boolean",
    [PropTypes.number]: "number",
    [PropTypes.func]: "function",
    [PropTypes.node]: "node",
    [PropTypes.element]: "element",
    [PropTypes.array]: "array",
    [PropTypes.object]: "object",
    [PropTypes.shape]: "object",
    [PropTypes.oneOf]: "enum",
    [PropTypes.oneOfType]: "union",
  };

  return propTypeMap[propType] || "any";
}

/**
 * Extract props from a component
 * @param {Object} component - React component
 * @param {string} componentName - Component name
 * @returns {Array|null} Array of prop objects or null if no props
 */
function extractPropsFromComponent(component, componentName) {
  if (!component?.propTypes) {
    return null;
  }

  const props = [];
  const { propTypes, defaultProps = {} } = component;

  Object.entries(propTypes).forEach(([propName, propType]) => {
    // Get default value if available
    const defaultValue = Object.prototype.hasOwnProperty.call(
      defaultProps,
      propName,
    )
      ? String(defaultProps[propName])
      : "";

    props.push({
      name: propName,
      type: mapPropTypeToString(propType),
      platform: "all",
      defaultValue,
      description: `${componentName} ${propName}`,
    });
  });

  return props.length > 0 ? props : null;
}

/**
 * Extract props from all components in the provided libraries
 * @returns {Object} Map of component names to their props
 */
function extractAllComponentProps() {
  const componentProps = {};

  // Process components-web library
  Object.entries(componentsWeb).forEach(([componentName, component]) => {
    const props = extractPropsFromComponent(component, componentName);
    if (props) {
      componentProps[componentName] = props;
    }
  });

  // Process components-base library (only if not already processed)
  Object.entries(componentsBase).forEach(([componentName, component]) => {
    if (!componentProps[componentName]) {
      const props = extractPropsFromComponent(component, componentName);
      if (props) {
        componentProps[componentName] = props;
      }
    }
  });

  console.log(
    `Extracted props for ${Object.keys(componentProps).length} components`,
  );
  return componentProps;
}

/**
 * Get default props for a component when no specific props are found
 * @param {string} componentName - Component name
 * @returns {Array} Default props
 */
function getDefaultComponentProps(componentName) {
  return [
    {
      name: "children",
      type: "node",
      platform: "all",
      defaultValue: "",
      description: `${componentName} content`,
    },
    {
      name: "tokens",
      type: "object",
      platform: "all",
      defaultValue: "",
      description: `${componentName} tokens`,
    },
    {
      name: "variant",
      type: "object",
      platform: "all",
      defaultValue: "",
      description: `${componentName} variant`,
    },
    {
      name: "dataSet",
      type: "object",
      platform: "all",
      defaultValue: "",
      description: `Data set for the ${componentName}`,
    },
  ];
}

/**
 * Extract component name from PropsTable tag
 * @param {string} propsTableTag - PropsTable JSX tag
 * @returns {string|null} Component name or null
 */
function extractComponentNameFromPropsTable(propsTableTag) {
  const webMatch = propsTableTag.match(/web=\{(\w+)Docs\}/);
  const baseMatch = propsTableTag.match(/base=\{(\w+)Docs\}/);

  const componentName =
    (webMatch && webMatch[1]) || (baseMatch && baseMatch[1]) || null;

  // If the component name is "Web" or "Base", it's likely the actual component name
  // is in the import statement, so we'll use the file name instead
  return componentName === "Web" || componentName === "Base"
    ? null
    : componentName;
}

/**
 * Extract component name from TokensTable tag
 * @param {string} tokensTableTag - TokensTable JSX tag
 * @returns {string|null} Component name or null
 */
function extractComponentNameFromTokensTable(tokensTableTag) {
  const componentMatch = tokensTableTag.match(/component="(\w+)"/);
  return (componentMatch && componentMatch[1]) || null;
}

/**
 * Convert kebab-case to CamelCase
 * @param {string} kebabCase - Kebab-case string
 * @returns {string} CamelCase string
 */
function kebabToCamelCase(kebabCase) {
  return kebabCase
    .split("-")
    .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
    .join("");
}

/**
 * Find component in tokens with case-insensitive search
 * @param {Object} tokens - Component tokens object
 * @param {string} componentName - Component name to find
 * @returns {string|null} Found component name or null
 */
function findComponentInTokens(tokens, componentName) {
  const componentKey = Object.keys(tokens).find(
    (key) => key.toLowerCase() === componentName.toLowerCase(),
  );

  return componentKey || null;
}

// Load component tokens
const componentsTokens = loadComponentTokens();

// Extract props for all components
const componentProps = extractAllComponentProps();

/**
 * Get props for a component
 * @param {string} componentName - Component name
 * @returns {Array} Component props
 */
function getComponentProps(componentName) {
  return (
    componentProps[componentName] || getDefaultComponentProps(componentName)
  );
}

/**
 * Convert PropsTable to Markdown table
 * @param {string} componentName - Component name
 * @returns {string} Markdown table
 */
function convertPropsTableToMarkdown(componentName) {
  const props = getComponentProps(componentName);
  let markdown = TABLE_TEMPLATES.props;

  props.forEach((prop) => {
    markdown += `\n| ${prop.name} | ${prop.type} | ${prop.platform} | ${prop.defaultValue} | ${prop.description} |`;
  });

  return markdown;
}

/**
 * Convert TokensTable to Markdown table
 * @param {string} componentName - Component name
 * @returns {string} Markdown table
 */
function convertTokensTableToMarkdown(componentName) {
  // Try to find the component in tokens
  const foundComponentName = findComponentInTokens(
    componentsTokens,
    componentName,
  );

  if (!foundComponentName) {
    console.log(`No tokens found for component: ${componentName}`);
    console.log(
      `Available components: ${Object.keys(componentsTokens).join(", ")}`,
    );
    return `${TABLE_TEMPLATES.tokens}\n| No tokens available for ${componentName} |  |  |`;
  }

  const tokens = componentsTokens[foundComponentName];
  let markdown = TABLE_TEMPLATES.tokens;

  Object.entries(tokens).forEach(([tokenProperty, tokenType], index) => {
    if (index === 0) {
      markdown += `\n| tokens | ${tokenProperty} | ${tokenType} |`;
    } else {
      markdown += `\n|  | ${tokenProperty} | ${tokenType} |`;
    }
  });

  return markdown;
}

/**
 * Process an MDX file to convert JSX tables to Markdown
 * @param {string} filePath - Path to MDX file
 */
function processMdxFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, "utf8");

    // Extract the component name from the file name
    const fileComponentName = path.basename(filePath, ".mdx");
    const componentName = kebabToCamelCase(fileComponentName);

    // Special debug for TextInput
    if (fileComponentName === "text-input") {
      console.log(`Processing TextInput component`);
      console.log(`Capitalized name: ${componentName}`);
      console.log(
        `Tokens available: ${componentsTokens["TextInput"] ? "Yes" : "No"}`,
      );
      console.log(
        `Keys in componentsTokens: ${Object.keys(componentsTokens).join(", ")}`,
      );
    }

    let updatedContent = content;

    // Replace PropsTable tags
    const propsTableRegex = /<PropsTable[^>]*\/>/g;
    updatedContent = updatedContent.replace(propsTableRegex, () =>
      convertPropsTableToMarkdown(componentName),
    );

    // Replace TokensTable tags
    const tokensTableRegex = /<TokensTable[^>]*\/>/g;
    updatedContent = updatedContent.replace(tokensTableRegex, () =>
      convertTokensTableToMarkdown(componentName),
    );

    // Write the updated content back to the file
    fs.writeFileSync(filePath, updatedContent, "utf8");

    console.log(`Processed ${filePath}`);
  } catch (error) {
    console.error(`Error processing file ${filePath}: ${error.message}`);
  }
}

/**
 * Process all MDX files in the docs directory
 */
function processAllMdxFiles() {
  try {
    const files = fs.readdirSync(PATHS.docsDir);

    const mdxFiles = files.filter((file) => file.endsWith(".mdx"));
    console.log(`Found ${mdxFiles.length} MDX files to process`);

    mdxFiles.forEach((file) => {
      processMdxFile(path.join(PATHS.docsDir, file));
    });

    console.log("All MDX files processed successfully!");
  } catch (error) {
    console.error(`Error processing MDX files: ${error.message}`);
  }
}

// Execute the script
processAllMdxFiles();
