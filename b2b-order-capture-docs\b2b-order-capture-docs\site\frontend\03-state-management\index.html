
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/frontend/03-state-management/">
      
      
        <link rel="prev" href="../02-custom-components/">
      
      
        <link rel="next" href="../04-api-integration/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>State Management - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#state-management-with-ngrx" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              State Management
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ngrx-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      NgRx Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="NgRx Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#store-architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Store Architecture Overview
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-flow-pattern" class="md-nav__link">
    <span class="md-ellipsis">
      Data Flow Pattern
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#state-structure" class="md-nav__link">
    <span class="md-ellipsis">
      State Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="State Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#application-state-interface" class="md-nav__link">
    <span class="md-ellipsis">
      Application State Interface
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#state-initialization" class="md-nav__link">
    <span class="md-ellipsis">
      State Initialization
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#actions-and-action-creators" class="md-nav__link">
    <span class="md-ellipsis">
      Actions and Action Creators
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Actions and Action Creators">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-actions" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Actions
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cart-actions" class="md-nav__link">
    <span class="md-ellipsis">
      Cart Actions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#reducers" class="md-nav__link">
    <span class="md-ellipsis">
      Reducers
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Reducers">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-reducer" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Reducer
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cart-reducer-with-complex-logic" class="md-nav__link">
    <span class="md-ellipsis">
      Cart Reducer with Complex Logic
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#effects" class="md-nav__link">
    <span class="md-ellipsis">
      Effects
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Effects">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-effects" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Effects
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cart-effects-with-complex-business-logic" class="md-nav__link">
    <span class="md-ellipsis">
      Cart Effects with Complex Business Logic
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#selectors" class="md-nav__link">
    <span class="md-ellipsis">
      Selectors
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Selectors">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#feature-selectors" class="md-nav__link">
    <span class="md-ellipsis">
      Feature Selectors
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#memoized-selectors" class="md-nav__link">
    <span class="md-ellipsis">
      Memoized Selectors
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#state-management-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      State Management Guidelines
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ngrx-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      NgRx Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="NgRx Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#store-architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Store Architecture Overview
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-flow-pattern" class="md-nav__link">
    <span class="md-ellipsis">
      Data Flow Pattern
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#state-structure" class="md-nav__link">
    <span class="md-ellipsis">
      State Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="State Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#application-state-interface" class="md-nav__link">
    <span class="md-ellipsis">
      Application State Interface
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#state-initialization" class="md-nav__link">
    <span class="md-ellipsis">
      State Initialization
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#actions-and-action-creators" class="md-nav__link">
    <span class="md-ellipsis">
      Actions and Action Creators
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Actions and Action Creators">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-actions" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Actions
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cart-actions" class="md-nav__link">
    <span class="md-ellipsis">
      Cart Actions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#reducers" class="md-nav__link">
    <span class="md-ellipsis">
      Reducers
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Reducers">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-reducer" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Reducer
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cart-reducer-with-complex-logic" class="md-nav__link">
    <span class="md-ellipsis">
      Cart Reducer with Complex Logic
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#effects" class="md-nav__link">
    <span class="md-ellipsis">
      Effects
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Effects">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-effects" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Effects
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cart-effects-with-complex-business-logic" class="md-nav__link">
    <span class="md-ellipsis">
      Cart Effects with Complex Business Logic
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#selectors" class="md-nav__link">
    <span class="md-ellipsis">
      Selectors
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Selectors">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#feature-selectors" class="md-nav__link">
    <span class="md-ellipsis">
      Feature Selectors
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#memoized-selectors" class="md-nav__link">
    <span class="md-ellipsis">
      Memoized Selectors
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#state-management-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      State Management Guidelines
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="state-management-with-ngrx">State Management with NgRx<a class="headerlink" href="#state-management-with-ngrx" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#ngrx-architecture">NgRx Architecture</a></li>
<li><a href="#state-structure">State Structure</a></li>
<li><a href="#actions-and-action-creators">Actions and Action Creators</a></li>
<li><a href="#reducers">Reducers</a></li>
<li><a href="#effects">Effects</a></li>
<li><a href="#selectors">Selectors</a></li>
<li><a href="#best-practices">Best Practices</a></li>
</ul>
<h2 id="ngrx-architecture">NgRx Architecture<a class="headerlink" href="#ngrx-architecture" title="Permanent link">&para;</a></h2>
<h3 id="store-architecture-overview">Store Architecture Overview<a class="headerlink" href="#store-architecture-overview" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;NgRx Store Architecture&quot;
        A[Components] --&gt; B[Actions]
        B --&gt; C[Reducers]
        C --&gt; D[State]
        D --&gt; E[Selectors]
        E --&gt; A

        B --&gt; F[Effects]
        F --&gt; G[Services]
        G --&gt; H[HTTP APIs]
        H --&gt; I[Backend]

        F --&gt; B
    end

    subgraph &quot;State Slices&quot;
        J[Quote State]
        K[Cart State]
        L[User State]
        M[Service State]
        N[Location State]
        O[UI State]
    end

    D --&gt; J
    D --&gt; K
    D --&gt; L
    D --&gt; M
    D --&gt; N
    D --&gt; O

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style D fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style I fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="data-flow-pattern">Data Flow Pattern<a class="headerlink" href="#data-flow-pattern" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Component
    participant Store
    participant Reducer
    participant Effect
    participant Service
    participant API

    Component-&gt;&gt;Store: Dispatch Action
    Store-&gt;&gt;Reducer: Process Action
    Reducer-&gt;&gt;Store: Update State
    Store-&gt;&gt;Component: Notify State Change

    Store-&gt;&gt;Effect: Trigger Side Effect
    Effect-&gt;&gt;Service: Call Service
    Service-&gt;&gt;API: HTTP Request
    API--&gt;&gt;Service: Response
    Service--&gt;&gt;Effect: Processed Data
    Effect-&gt;&gt;Store: Dispatch Success/Error Action
    Store-&gt;&gt;Reducer: Process Result Action
    Reducer-&gt;&gt;Store: Update State
    Store-&gt;&gt;Component: Notify Final State
</code></pre></div>
<h2 id="state-structure">State Structure<a class="headerlink" href="#state-structure" title="Permanent link">&para;</a></h2>
<h3 id="application-state-interface">Application State Interface<a class="headerlink" href="#application-state-interface" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Root Application State</span>
<span class="k">export</span><span class="w"> </span><span class="kd">interface</span><span class="w"> </span><span class="nx">AppState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">cart</span><span class="o">:</span><span class="w"> </span><span class="kt">CartState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">user</span><span class="o">:</span><span class="w"> </span><span class="kt">UserState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">services</span><span class="o">:</span><span class="w"> </span><span class="kt">ServiceState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">locations</span><span class="o">:</span><span class="w"> </span><span class="kt">LocationState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">ui</span><span class="o">:</span><span class="w"> </span><span class="kt">UIState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">router</span><span class="o">:</span><span class="w"> </span><span class="kt">RouterReducerState</span><span class="o">&lt;</span><span class="nx">any</span><span class="o">&gt;</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// Quote State Slice</span>
<span class="k">export</span><span class="w"> </span><span class="kd">interface</span><span class="w"> </span><span class="nx">QuoteState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">selectedQuote</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="nx">quoteHistory</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteHistoryEntry</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="nx">filters</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteFilters</span><span class="p">;</span>
<span class="w">  </span><span class="nx">pagination</span><span class="o">:</span><span class="w"> </span><span class="kt">Pagination</span><span class="p">;</span>
<span class="w">  </span><span class="nx">searchQuery</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">sortBy</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteSortOption</span><span class="p">;</span>
<span class="w">  </span><span class="nx">editMode</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// Cart State Slice</span>
<span class="k">export</span><span class="w"> </span><span class="kd">interface</span><span class="w"> </span><span class="nx">CartState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">items</span><span class="o">:</span><span class="w"> </span><span class="kt">CartItem</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">subtotal</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">discounts</span><span class="o">:</span><span class="w"> </span><span class="kt">Discount</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">appliedDiscounts</span><span class="o">:</span><span class="w"> </span><span class="kt">AppliedDiscount</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">taxes</span><span class="o">:</span><span class="w"> </span><span class="kt">Tax</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="nx">lastUpdated</span><span class="o">:</span><span class="w"> </span><span class="kt">Date</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="nx">validationErrors</span><span class="o">:</span><span class="w"> </span><span class="kt">ValidationError</span><span class="p">[];</span>
<span class="p">}</span>

<span class="c1">// Service State Slice</span>
<span class="k">export</span><span class="w"> </span><span class="kd">interface</span><span class="w"> </span><span class="nx">ServiceState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">availableServices</span><span class="o">:</span><span class="w"> </span><span class="kt">Service</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">selectedServices</span><span class="o">:</span><span class="w"> </span><span class="kt">Service</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">serviceCategories</span><span class="o">:</span><span class="w"> </span><span class="kt">ServiceCategory</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">serviceDetails</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="p">[</span><span class="nx">serviceId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">]</span><span class="o">:</span><span class="w"> </span><span class="nx">ServiceDetail</span><span class="w"> </span><span class="p">};</span>
<span class="w">  </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="nx">filters</span><span class="o">:</span><span class="w"> </span><span class="kt">ServiceFilters</span><span class="p">;</span>
<span class="w">  </span><span class="nx">searchResults</span><span class="o">:</span><span class="w"> </span><span class="kt">Service</span><span class="p">[];</span>
<span class="p">}</span>

<span class="c1">// Location State Slice</span>
<span class="k">export</span><span class="w"> </span><span class="kd">interface</span><span class="w"> </span><span class="nx">LocationState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">searchResults</span><span class="o">:</span><span class="w"> </span><span class="kt">Location</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">selectedLocation</span><span class="o">:</span><span class="w"> </span><span class="kt">Location</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="nx">serviceAreas</span><span class="o">:</span><span class="w"> </span><span class="kt">ServiceArea</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">addressValidation</span><span class="o">:</span><span class="w"> </span><span class="kt">AddressValidation</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="nx">serviceAvailability</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="p">[</span><span class="nx">locationId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">]</span><span class="o">:</span><span class="w"> </span><span class="nx">ServiceAvailability</span><span class="w"> </span><span class="p">};</span>
<span class="w">  </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="nx">mapState</span><span class="o">:</span><span class="w"> </span><span class="kt">MapState</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// UI State Slice</span>
<span class="k">export</span><span class="w"> </span><span class="kd">interface</span><span class="w"> </span><span class="nx">UIState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">sidebarOpen</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">theme</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;light&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;dark&#39;</span><span class="p">;</span>
<span class="w">  </span><span class="nx">notifications</span><span class="o">:</span><span class="w"> </span><span class="kt">Notification</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">modals</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="p">[</span><span class="nx">modalId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">]</span><span class="o">:</span><span class="w"> </span><span class="nx">ModalState</span><span class="w"> </span><span class="p">};</span>
<span class="w">  </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="p">[</span><span class="nx">key</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">]</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="p">};</span>
<span class="w">  </span><span class="nx">errors</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="p">[</span><span class="nx">key</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">]</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">};</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="state-initialization">State Initialization<a class="headerlink" href="#state-initialization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Initial State Definitions</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">initialQuoteState</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="p">[],</span>
<span class="w">  </span><span class="nx">selectedQuote</span><span class="o">:</span><span class="w"> </span><span class="kt">null</span><span class="p">,</span>
<span class="w">  </span><span class="nx">quoteHistory</span><span class="o">:</span><span class="w"> </span><span class="p">[],</span>
<span class="w">  </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="p">,</span>
<span class="w">  </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">null</span><span class="p">,</span>
<span class="w">  </span><span class="nx">filters</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">status</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;all&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">dateRange</span><span class="o">:</span><span class="w"> </span><span class="kt">null</span><span class="p">,</span>
<span class="w">    </span><span class="nx">customerId</span><span class="o">:</span><span class="w"> </span><span class="kt">null</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nx">pagination</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">page</span><span class="o">:</span><span class="w"> </span><span class="kt">1</span><span class="p">,</span>
<span class="w">    </span><span class="nx">pageSize</span><span class="o">:</span><span class="w"> </span><span class="kt">10</span><span class="p">,</span>
<span class="w">    </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">0</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nx">searchQuery</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">sortBy</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;dateCreated&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">editMode</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span>
<span class="p">};</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">initialCartState</span><span class="o">:</span><span class="w"> </span><span class="kt">CartState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">items</span><span class="o">:</span><span class="w"> </span><span class="p">[],</span>
<span class="w">  </span><span class="nx">subtotal</span><span class="o">:</span><span class="w"> </span><span class="kt">0</span><span class="p">,</span>
<span class="w">  </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">0</span><span class="p">,</span>
<span class="w">  </span><span class="nx">discounts</span><span class="o">:</span><span class="w"> </span><span class="p">[],</span>
<span class="w">  </span><span class="nx">appliedDiscounts</span><span class="o">:</span><span class="w"> </span><span class="p">[],</span>
<span class="w">  </span><span class="nx">taxes</span><span class="o">:</span><span class="w"> </span><span class="p">[],</span>
<span class="w">  </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="p">,</span>
<span class="w">  </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">null</span><span class="p">,</span>
<span class="w">  </span><span class="nx">lastUpdated</span><span class="o">:</span><span class="w"> </span><span class="kt">null</span><span class="p">,</span>
<span class="w">  </span><span class="nx">validationErrors</span><span class="o">:</span><span class="w"> </span><span class="p">[]</span>
<span class="p">};</span>
</code></pre></div>
<h2 id="actions-and-action-creators">Actions and Action Creators<a class="headerlink" href="#actions-and-action-creators" title="Permanent link">&para;</a></h2>
<h3 id="quote-actions">Quote Actions<a class="headerlink" href="#quote-actions" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Quote Action Types</span>
<span class="k">export</span><span class="w"> </span><span class="kd">enum</span><span class="w"> </span><span class="nx">QuoteActionTypes</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Load Actions</span>
<span class="w">  </span><span class="nx">LOAD_QUOTES</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Load Quotes&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">LOAD_QUOTES_SUCCESS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Load Quotes Success&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">LOAD_QUOTES_FAILURE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Load Quotes Failure&#39;</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// CRUD Actions</span>
<span class="w">  </span><span class="nx">CREATE_QUOTE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Create Quote&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">CREATE_QUOTE_SUCCESS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Create Quote Success&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">CREATE_QUOTE_FAILURE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Create Quote Failure&#39;</span><span class="p">,</span>

<span class="w">  </span><span class="nx">UPDATE_QUOTE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Update Quote&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">UPDATE_QUOTE_SUCCESS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Update Quote Success&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">UPDATE_QUOTE_FAILURE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Update Quote Failure&#39;</span><span class="p">,</span>

<span class="w">  </span><span class="nx">DELETE_QUOTE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Delete Quote&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">DELETE_QUOTE_SUCCESS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Delete Quote Success&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">DELETE_QUOTE_FAILURE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Delete Quote Failure&#39;</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Selection Actions</span>
<span class="w">  </span><span class="nx">SELECT_QUOTE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Select Quote&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">CLEAR_SELECTED_QUOTE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Clear Selected Quote&#39;</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Filter Actions</span>
<span class="w">  </span><span class="nx">SET_QUOTE_FILTERS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Set Filters&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">CLEAR_QUOTE_FILTERS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Clear Filters&#39;</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Search Actions</span>
<span class="w">  </span><span class="nx">SEARCH_QUOTES</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Search Quotes&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">SET_SEARCH_QUERY</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;[Quote] Set Search Query&#39;</span>
<span class="p">}</span>

<span class="c1">// Action Creators using createAction</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">loadQuotes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="nx">QuoteActionTypes</span><span class="p">.</span><span class="nx">LOAD_QUOTES</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">filters?</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteFilters</span><span class="p">;</span><span class="w"> </span><span class="nx">pagination?</span><span class="o">:</span><span class="w"> </span><span class="kt">Pagination</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">loadQuotesSuccess</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="nx">QuoteActionTypes</span><span class="p">.</span><span class="nx">LOAD_QUOTES_SUCCESS</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="p">[];</span><span class="w"> </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">loadQuotesFailure</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="nx">QuoteActionTypes</span><span class="p">.</span><span class="nx">LOAD_QUOTES_FAILURE</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">createQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="nx">QuoteActionTypes</span><span class="p">.</span><span class="nx">CREATE_QUOTE</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">quoteRequest</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteRequest</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">createQuoteSuccess</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="nx">QuoteActionTypes</span><span class="p">.</span><span class="nx">CREATE_QUOTE_SUCCESS</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">quote</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="nx">QuoteActionTypes</span><span class="p">.</span><span class="nx">SELECT_QUOTE</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">setQuoteFilters</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="nx">QuoteActionTypes</span><span class="p">.</span><span class="nx">SET_QUOTE_FILTERS</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">filters</span><span class="o">:</span><span class="w"> </span><span class="kt">Partial</span><span class="o">&lt;</span><span class="nx">QuoteFilters</span><span class="o">&gt;</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>
</code></pre></div>
<h3 id="cart-actions">Cart Actions<a class="headerlink" href="#cart-actions" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Cart Action Creators</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">addToCart</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Cart] Add To Cart&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">service</span><span class="o">:</span><span class="w"> </span><span class="kt">Service</span><span class="p">;</span><span class="w"> </span><span class="nx">quantity</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w"> </span><span class="nx">configuration?</span><span class="o">:</span><span class="w"> </span><span class="kt">ServiceConfiguration</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">removeFromCart</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Cart] Remove From Cart&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">itemId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">updateCartItem</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Cart] Update Cart Item&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">itemId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span><span class="w"> </span><span class="nx">updates</span><span class="o">:</span><span class="w"> </span><span class="kt">Partial</span><span class="o">&lt;</span><span class="nx">CartItem</span><span class="o">&gt;</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">clearCart</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span><span class="s1">&#39;[Cart] Clear Cart&#39;</span><span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">calculateCartTotal</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Cart] Calculate Total&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">cartItems</span><span class="o">:</span><span class="w"> </span><span class="kt">CartItem</span><span class="p">[]</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">applyDiscount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Cart] Apply Discount&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">discountCode</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">removeDiscount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Cart] Remove Discount&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">discountId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">validateCart</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span><span class="s1">&#39;[Cart] Validate Cart&#39;</span><span class="p">);</span>
</code></pre></div>
<h2 id="reducers">Reducers<a class="headerlink" href="#reducers" title="Permanent link">&para;</a></h2>
<h3 id="quote-reducer">Quote Reducer<a class="headerlink" href="#quote-reducer" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Quote Reducer Implementation</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">quoteReducer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createReducer</span><span class="p">(</span>
<span class="w">  </span><span class="nx">initialQuoteState</span><span class="p">,</span>

<span class="w">  </span><span class="c1">// Load Quotes</span>
<span class="w">  </span><span class="nx">on</span><span class="p">(</span><span class="nx">loadQuotes</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">null</span>
<span class="w">  </span><span class="p">})),</span>

<span class="w">  </span><span class="nx">on</span><span class="p">(</span><span class="nx">loadQuotesSuccess</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">quotes</span><span class="p">,</span><span class="w"> </span><span class="nx">total</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">quotes</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="p">,</span>
<span class="w">    </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">null</span><span class="p">,</span>
<span class="w">    </span><span class="nx">pagination</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="p">...</span><span class="nx">state</span><span class="p">.</span><span class="nx">pagination</span><span class="p">,</span>
<span class="w">      </span><span class="nx">total</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">})),</span>

<span class="w">  </span><span class="nx">on</span><span class="p">(</span><span class="nx">loadQuotesFailure</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">error</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="p">,</span>
<span class="w">    </span><span class="nx">error</span>
<span class="w">  </span><span class="p">})),</span>

<span class="w">  </span><span class="c1">// Create Quote</span>
<span class="w">  </span><span class="nx">on</span><span class="p">(</span><span class="nx">createQuote</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span>
<span class="w">    </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">null</span>
<span class="w">  </span><span class="p">})),</span>

<span class="w">  </span><span class="nx">on</span><span class="p">(</span><span class="nx">createQuoteSuccess</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">quote</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="nx">quote</span><span class="p">,</span><span class="w"> </span><span class="p">...</span><span class="nx">state</span><span class="p">.</span><span class="nx">quotes</span><span class="p">],</span>
<span class="w">    </span><span class="nx">selectedQuote</span><span class="o">:</span><span class="w"> </span><span class="kt">quote</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="p">,</span>
<span class="w">    </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">null</span>
<span class="w">  </span><span class="p">})),</span>

<span class="w">  </span><span class="c1">// Select Quote</span>
<span class="w">  </span><span class="nx">on</span><span class="p">(</span><span class="nx">selectQuote</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">quoteId</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">selectedQuote</span><span class="o">:</span><span class="w"> </span><span class="kt">state.quotes.find</span><span class="p">(</span><span class="nx">q</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">q</span><span class="p">.</span><span class="nx">id</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="nx">quoteId</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="kc">null</span>
<span class="w">  </span><span class="p">})),</span>

<span class="w">  </span><span class="c1">// Filters</span>
<span class="w">  </span><span class="nx">on</span><span class="p">(</span><span class="nx">setQuoteFilters</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">filters</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">filters</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="p">...</span><span class="nx">state</span><span class="p">.</span><span class="nx">filters</span><span class="p">,</span>
<span class="w">      </span><span class="p">...</span><span class="nx">filters</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">})),</span>

<span class="w">  </span><span class="nx">on</span><span class="p">(</span><span class="nx">clearQuoteFilters</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">filters</span><span class="o">:</span><span class="w"> </span><span class="kt">initialQuoteState.filters</span>
<span class="w">  </span><span class="p">}))</span>
<span class="p">);</span>
</code></pre></div>
<h3 id="cart-reducer-with-complex-logic">Cart Reducer with Complex Logic<a class="headerlink" href="#cart-reducer-with-complex-logic" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Cart Reducer with Advanced State Management</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">cartReducer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createReducer</span><span class="p">(</span>
<span class="w">  </span><span class="nx">initialCartState</span><span class="p">,</span>

<span class="w">  </span><span class="nx">on</span><span class="p">(</span><span class="nx">addToCart</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">service</span><span class="p">,</span><span class="w"> </span><span class="nx">quantity</span><span class="p">,</span><span class="w"> </span><span class="nx">configuration</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">existingItemIndex</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">items</span><span class="p">.</span><span class="nx">findIndex</span><span class="p">(</span>
<span class="w">      </span><span class="nx">item</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">item</span><span class="p">.</span><span class="nx">serviceId</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="nx">service</span><span class="p">.</span><span class="nx">id</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>
<span class="w">               </span><span class="nb">JSON</span><span class="p">.</span><span class="nx">stringify</span><span class="p">(</span><span class="nx">item</span><span class="p">.</span><span class="nx">configuration</span><span class="p">)</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="nb">JSON</span><span class="p">.</span><span class="nx">stringify</span><span class="p">(</span><span class="nx">configuration</span><span class="p">)</span>
<span class="w">    </span><span class="p">);</span>

<span class="w">    </span><span class="kd">let</span><span class="w"> </span><span class="nx">updatedItems</span><span class="o">:</span><span class="w"> </span><span class="kt">CartItem</span><span class="p">[];</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">existingItemIndex</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mf">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Update existing item</span>
<span class="w">      </span><span class="nx">updatedItems</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">items</span><span class="p">.</span><span class="nx">map</span><span class="p">((</span><span class="nx">item</span><span class="p">,</span><span class="w"> </span><span class="nx">index</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">        </span><span class="nx">index</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="nx">existingItemIndex</span>
<span class="w">          </span><span class="o">?</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="p">...</span><span class="nx">item</span><span class="p">,</span><span class="w"> </span><span class="nx">quantity</span><span class="o">:</span><span class="w"> </span><span class="kt">item.quantity</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="nx">quantity</span><span class="w"> </span><span class="p">}</span>
<span class="w">          </span><span class="o">:</span><span class="w"> </span><span class="nx">item</span>
<span class="w">      </span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Add new item</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">newItem</span><span class="o">:</span><span class="w"> </span><span class="kt">CartItem</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">generateId</span><span class="p">(),</span>
<span class="w">        </span><span class="nx">serviceId</span><span class="o">:</span><span class="w"> </span><span class="kt">service.id</span><span class="p">,</span>
<span class="w">        </span><span class="nx">service</span><span class="p">,</span>
<span class="w">        </span><span class="nx">quantity</span><span class="p">,</span>
<span class="w">        </span><span class="nx">configuration</span><span class="p">,</span>
<span class="w">        </span><span class="nx">unitPrice</span><span class="o">:</span><span class="w"> </span><span class="kt">service.price</span><span class="p">,</span>
<span class="w">        </span><span class="nx">totalPrice</span><span class="o">:</span><span class="w"> </span><span class="kt">service.price</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="nx">quantity</span><span class="p">,</span>
<span class="w">        </span><span class="nx">addedAt</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">()</span>
<span class="w">      </span><span class="p">};</span>
<span class="w">      </span><span class="nx">updatedItems</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[...</span><span class="nx">state</span><span class="p">.</span><span class="nx">items</span><span class="p">,</span><span class="w"> </span><span class="nx">newItem</span><span class="p">];</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">      </span><span class="nx">items</span><span class="o">:</span><span class="w"> </span><span class="kt">updatedItems</span><span class="p">,</span>
<span class="w">      </span><span class="nx">lastUpdated</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">()</span>
<span class="w">    </span><span class="p">};</span>
<span class="w">  </span><span class="p">}),</span>

<span class="w">  </span><span class="nx">on</span><span class="p">(</span><span class="nx">removeFromCart</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">itemId</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">items</span><span class="o">:</span><span class="w"> </span><span class="kt">state.items.filter</span><span class="p">(</span><span class="nx">item</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">item</span><span class="p">.</span><span class="nx">id</span><span class="w"> </span><span class="o">!==</span><span class="w"> </span><span class="nx">itemId</span><span class="p">),</span>
<span class="w">    </span><span class="nx">lastUpdated</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">()</span>
<span class="w">  </span><span class="p">})),</span>

<span class="w">  </span><span class="nx">on</span><span class="p">(</span><span class="nx">calculateCartTotalSuccess</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">calculation</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">subtotal</span><span class="o">:</span><span class="w"> </span><span class="kt">calculation.subtotal</span><span class="p">,</span>
<span class="w">    </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">calculation.total</span><span class="p">,</span>
<span class="w">    </span><span class="nx">taxes</span><span class="o">:</span><span class="w"> </span><span class="kt">calculation.taxes</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="p">,</span>
<span class="w">    </span><span class="nx">lastUpdated</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">()</span>
<span class="w">  </span><span class="p">})),</span>

<span class="w">  </span><span class="nx">on</span><span class="p">(</span><span class="nx">applyDiscountSuccess</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">discount</span><span class="p">,</span><span class="w"> </span><span class="nx">appliedDiscount</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">discounts</span><span class="o">:</span><span class="w"> </span><span class="p">[...</span><span class="nx">state</span><span class="p">.</span><span class="nx">discounts</span><span class="p">,</span><span class="w"> </span><span class="nx">discount</span><span class="p">],</span>
<span class="w">    </span><span class="nx">appliedDiscounts</span><span class="o">:</span><span class="w"> </span><span class="p">[...</span><span class="nx">state</span><span class="p">.</span><span class="nx">appliedDiscounts</span><span class="p">,</span><span class="w"> </span><span class="nx">appliedDiscount</span><span class="p">],</span>
<span class="w">    </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span>
<span class="w">  </span><span class="p">})),</span>

<span class="w">  </span><span class="nx">on</span><span class="p">(</span><span class="nx">validateCartSuccess</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">validationResult</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">    </span><span class="nx">validationErrors</span><span class="o">:</span><span class="w"> </span><span class="kt">validationResult.errors</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span>
<span class="w">  </span><span class="p">}))</span>
<span class="p">);</span>
</code></pre></div>
<h2 id="effects">Effects<a class="headerlink" href="#effects" title="Permanent link">&para;</a></h2>
<h3 id="quote-effects">Quote Effects<a class="headerlink" href="#quote-effects" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="kd">@Injectable</span><span class="p">()</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">QuoteEffects</span><span class="w"> </span><span class="p">{</span>

<span class="w">  </span><span class="c1">// Load Quotes Effect</span>
<span class="w">  </span><span class="nx">loadQuotes$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createEffect</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">actions$</span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">ofType</span><span class="p">(</span><span class="nx">loadQuotes</span><span class="p">),</span>
<span class="w">      </span><span class="nx">switchMap</span><span class="p">(({</span><span class="w"> </span><span class="nx">filters</span><span class="p">,</span><span class="w"> </span><span class="nx">pagination</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">quoteService</span><span class="p">.</span><span class="nx">getQuotes</span><span class="p">(</span><span class="nx">filters</span><span class="p">,</span><span class="w"> </span><span class="nx">pagination</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">          </span><span class="nx">map</span><span class="p">(</span><span class="nx">response</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">loadQuotesSuccess</span><span class="p">({</span>
<span class="w">            </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="kt">response.quotes</span><span class="p">,</span>
<span class="w">            </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">response.total</span>
<span class="w">          </span><span class="p">})),</span>
<span class="w">          </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">of</span><span class="p">(</span><span class="nx">loadQuotesFailure</span><span class="p">({</span>
<span class="w">            </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">error.message</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">&#39;Failed to load quotes&#39;</span>
<span class="w">          </span><span class="p">})))</span>
<span class="w">        </span><span class="p">)</span>
<span class="w">      </span><span class="p">)</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Create Quote Effect</span>
<span class="w">  </span><span class="nx">createQuote$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createEffect</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">actions$</span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">ofType</span><span class="p">(</span><span class="nx">createQuote</span><span class="p">),</span>
<span class="w">      </span><span class="nx">switchMap</span><span class="p">(({</span><span class="w"> </span><span class="nx">quoteRequest</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">quoteService</span><span class="p">.</span><span class="nx">createQuote</span><span class="p">(</span><span class="nx">quoteRequest</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">          </span><span class="nx">map</span><span class="p">(</span><span class="nx">quote</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">createQuoteSuccess</span><span class="p">({</span><span class="w"> </span><span class="nx">quote</span><span class="w"> </span><span class="p">})),</span>
<span class="w">          </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">of</span><span class="p">(</span><span class="nx">createQuoteFailure</span><span class="p">({</span>
<span class="w">            </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">error.message</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">&#39;Failed to create quote&#39;</span>
<span class="w">          </span><span class="p">})))</span>
<span class="w">        </span><span class="p">)</span>
<span class="w">      </span><span class="p">)</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Navigate After Quote Creation</span>
<span class="w">  </span><span class="nx">navigateAfterQuoteCreation$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createEffect</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">actions$</span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">ofType</span><span class="p">(</span><span class="nx">createQuoteSuccess</span><span class="p">),</span>
<span class="w">      </span><span class="nx">tap</span><span class="p">(({</span><span class="w"> </span><span class="nx">quote</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">router</span><span class="p">.</span><span class="nx">navigate</span><span class="p">([</span><span class="s1">&#39;/quotes&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">quote</span><span class="p">.</span><span class="nx">id</span><span class="p">]);</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">notificationService</span><span class="p">.</span><span class="nx">showSuccess</span><span class="p">(</span><span class="s1">&#39;Quote created successfully&#39;</span><span class="p">);</span>
<span class="w">      </span><span class="p">})</span>
<span class="w">    </span><span class="p">),</span>
<span class="w">    </span><span class="p">{</span><span class="w"> </span><span class="nx">dispatch</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="w"> </span><span class="p">}</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Search Quotes Effect</span>
<span class="w">  </span><span class="nx">searchQuotes$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createEffect</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">actions$</span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">ofType</span><span class="p">(</span><span class="nx">setSearchQuery</span><span class="p">),</span>
<span class="w">      </span><span class="nx">debounceTime</span><span class="p">(</span><span class="mf">300</span><span class="p">),</span>
<span class="w">      </span><span class="nx">distinctUntilChanged</span><span class="p">(),</span>
<span class="w">      </span><span class="nx">switchMap</span><span class="p">(({</span><span class="w"> </span><span class="nx">query</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">quoteService</span><span class="p">.</span><span class="nx">searchQuotes</span><span class="p">(</span><span class="nx">query</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">          </span><span class="nx">map</span><span class="p">(</span><span class="nx">quotes</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">loadQuotesSuccess</span><span class="p">({</span>
<span class="w">            </span><span class="nx">quotes</span><span class="p">,</span>
<span class="w">            </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">quotes.length</span>
<span class="w">          </span><span class="p">})),</span>
<span class="w">          </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">of</span><span class="p">(</span><span class="nx">loadQuotesFailure</span><span class="p">({</span>
<span class="w">            </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">error.message</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">&#39;Search failed&#39;</span>
<span class="w">          </span><span class="p">})))</span>
<span class="w">        </span><span class="p">)</span>
<span class="w">      </span><span class="p">)</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">actions$</span><span class="o">:</span><span class="w"> </span><span class="kt">Actions</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">quoteService</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteService</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">router</span><span class="o">:</span><span class="w"> </span><span class="kt">Router</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">notificationService</span><span class="o">:</span><span class="w"> </span><span class="kt">NotificationService</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="cart-effects-with-complex-business-logic">Cart Effects with Complex Business Logic<a class="headerlink" href="#cart-effects-with-complex-business-logic" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="kd">@Injectable</span><span class="p">()</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">CartEffects</span><span class="w"> </span><span class="p">{</span>

<span class="w">  </span><span class="c1">// Calculate Cart Total Effect</span>
<span class="w">  </span><span class="nx">calculateCartTotal$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createEffect</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">actions$</span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">ofType</span><span class="p">(</span><span class="nx">addToCart</span><span class="p">,</span><span class="w"> </span><span class="nx">removeFromCart</span><span class="p">,</span><span class="w"> </span><span class="nx">updateCartItem</span><span class="p">,</span><span class="w"> </span><span class="nx">applyDiscount</span><span class="p">,</span><span class="w"> </span><span class="nx">removeDiscount</span><span class="p">),</span>
<span class="w">      </span><span class="nx">withLatestFrom</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectCartItems</span><span class="p">)),</span>
<span class="w">      </span><span class="nx">switchMap</span><span class="p">(([</span><span class="nx">action</span><span class="p">,</span><span class="w"> </span><span class="nx">cartItems</span><span class="p">])</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">pricingService</span><span class="p">.</span><span class="nx">calculateTotal</span><span class="p">(</span><span class="nx">cartItems</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">          </span><span class="nx">map</span><span class="p">(</span><span class="nx">calculation</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">calculateCartTotalSuccess</span><span class="p">({</span><span class="w"> </span><span class="nx">calculation</span><span class="w"> </span><span class="p">})),</span>
<span class="w">          </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">of</span><span class="p">(</span><span class="nx">calculateCartTotalFailure</span><span class="p">({</span>
<span class="w">            </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">error.message</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">&#39;Failed to calculate total&#39;</span>
<span class="w">          </span><span class="p">})))</span>
<span class="w">        </span><span class="p">)</span>
<span class="w">      </span><span class="p">)</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Apply Discount Effect</span>
<span class="w">  </span><span class="nx">applyDiscount$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createEffect</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">actions$</span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">ofType</span><span class="p">(</span><span class="nx">applyDiscount</span><span class="p">),</span>
<span class="w">      </span><span class="nx">withLatestFrom</span><span class="p">(</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectCartItems</span><span class="p">),</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectCartSubtotal</span><span class="p">)</span>
<span class="w">      </span><span class="p">),</span>
<span class="w">      </span><span class="nx">switchMap</span><span class="p">(([{</span><span class="w"> </span><span class="nx">discountCode</span><span class="w"> </span><span class="p">},</span><span class="w"> </span><span class="nx">cartItems</span><span class="p">,</span><span class="w"> </span><span class="nx">subtotal</span><span class="p">])</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">discountService</span><span class="p">.</span><span class="nx">validateAndApplyDiscount</span><span class="p">(</span><span class="nx">discountCode</span><span class="p">,</span><span class="w"> </span><span class="nx">cartItems</span><span class="p">,</span><span class="w"> </span><span class="nx">subtotal</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">          </span><span class="nx">map</span><span class="p">(</span><span class="nx">result</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">valid</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="k">return</span><span class="w"> </span><span class="nx">applyDiscountSuccess</span><span class="p">({</span>
<span class="w">                </span><span class="nx">discount</span><span class="o">:</span><span class="w"> </span><span class="kt">result.discount</span><span class="p">,</span>
<span class="w">                </span><span class="nx">appliedDiscount</span><span class="o">:</span><span class="w"> </span><span class="kt">result.appliedDiscount</span>
<span class="w">              </span><span class="p">});</span>
<span class="w">            </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="k">return</span><span class="w"> </span><span class="nx">applyDiscountFailure</span><span class="p">({</span>
<span class="w">                </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">result.error</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">&#39;Invalid discount code&#39;</span>
<span class="w">              </span><span class="p">});</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">          </span><span class="p">}),</span>
<span class="w">          </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">of</span><span class="p">(</span><span class="nx">applyDiscountFailure</span><span class="p">({</span>
<span class="w">            </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">error.message</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">&#39;Failed to apply discount&#39;</span>
<span class="w">          </span><span class="p">})))</span>
<span class="w">        </span><span class="p">)</span>
<span class="w">      </span><span class="p">)</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Validate Cart Effect</span>
<span class="w">  </span><span class="nx">validateCart$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createEffect</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">actions$</span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">ofType</span><span class="p">(</span><span class="nx">validateCart</span><span class="p">),</span>
<span class="w">      </span><span class="nx">withLatestFrom</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectCartItems</span><span class="p">)),</span>
<span class="w">      </span><span class="nx">switchMap</span><span class="p">(([</span><span class="nx">action</span><span class="p">,</span><span class="w"> </span><span class="nx">cartItems</span><span class="p">])</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">validationService</span><span class="p">.</span><span class="nx">validateCart</span><span class="p">(</span><span class="nx">cartItems</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">          </span><span class="nx">map</span><span class="p">(</span><span class="nx">validationResult</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">validateCartSuccess</span><span class="p">({</span><span class="w"> </span><span class="nx">validationResult</span><span class="w"> </span><span class="p">})),</span>
<span class="w">          </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">of</span><span class="p">(</span><span class="nx">validateCartFailure</span><span class="p">({</span>
<span class="w">            </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">error.message</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">&#39;Cart validation failed&#39;</span>
<span class="w">          </span><span class="p">})))</span>
<span class="w">        </span><span class="p">)</span>
<span class="w">      </span><span class="p">)</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="c1">// Auto-save Cart Effect</span>
<span class="w">  </span><span class="nx">autoSaveCart$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createEffect</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">actions$</span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">ofType</span><span class="p">(</span><span class="nx">addToCart</span><span class="p">,</span><span class="w"> </span><span class="nx">removeFromCart</span><span class="p">,</span><span class="w"> </span><span class="nx">updateCartItem</span><span class="p">),</span>
<span class="w">      </span><span class="nx">debounceTime</span><span class="p">(</span><span class="mf">1000</span><span class="p">),</span>
<span class="w">      </span><span class="nx">withLatestFrom</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectCartState</span><span class="p">)),</span>
<span class="w">      </span><span class="nx">switchMap</span><span class="p">(([</span><span class="nx">action</span><span class="p">,</span><span class="w"> </span><span class="nx">cartState</span><span class="p">])</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">cartService</span><span class="p">.</span><span class="nx">saveCart</span><span class="p">(</span><span class="nx">cartState</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">          </span><span class="nx">map</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">saveCartSuccess</span><span class="p">()),</span>
<span class="w">          </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">of</span><span class="p">(</span><span class="nx">saveCartFailure</span><span class="p">({</span>
<span class="w">            </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">error.message</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">&#39;Failed to save cart&#39;</span>
<span class="w">          </span><span class="p">})))</span>
<span class="w">        </span><span class="p">)</span>
<span class="w">      </span><span class="p">)</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">actions$</span><span class="o">:</span><span class="w"> </span><span class="kt">Actions</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">store</span><span class="o">:</span><span class="w"> </span><span class="kt">Store</span><span class="o">&lt;</span><span class="nx">AppState</span><span class="o">&gt;</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">pricingService</span><span class="o">:</span><span class="w"> </span><span class="kt">PricingService</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">discountService</span><span class="o">:</span><span class="w"> </span><span class="kt">DiscountService</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">validationService</span><span class="o">:</span><span class="w"> </span><span class="kt">ValidationService</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">cartService</span><span class="o">:</span><span class="w"> </span><span class="kt">CartService</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="selectors">Selectors<a class="headerlink" href="#selectors" title="Permanent link">&para;</a></h2>
<h3 id="feature-selectors">Feature Selectors<a class="headerlink" href="#feature-selectors" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Feature State Selectors</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectQuoteState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createFeatureSelector</span><span class="o">&lt;</span><span class="nx">QuoteState</span><span class="o">&gt;</span><span class="p">(</span><span class="s1">&#39;quotes&#39;</span><span class="p">);</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectCartState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createFeatureSelector</span><span class="o">&lt;</span><span class="nx">CartState</span><span class="o">&gt;</span><span class="p">(</span><span class="s1">&#39;cart&#39;</span><span class="p">);</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectServiceState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createFeatureSelector</span><span class="o">&lt;</span><span class="nx">ServiceState</span><span class="o">&gt;</span><span class="p">(</span><span class="s1">&#39;services&#39;</span><span class="p">);</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectLocationState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createFeatureSelector</span><span class="o">&lt;</span><span class="nx">LocationState</span><span class="o">&gt;</span><span class="p">(</span><span class="s1">&#39;locations&#39;</span><span class="p">);</span>
</code></pre></div>
<h3 id="memoized-selectors">Memoized Selectors<a class="headerlink" href="#memoized-selectors" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Quote Selectors</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectAllQuotes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createSelector</span><span class="p">(</span>
<span class="w">  </span><span class="nx">selectQuoteState</span><span class="p">,</span>
<span class="w">  </span><span class="p">(</span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteState</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">quotes</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectSelectedQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createSelector</span><span class="p">(</span>
<span class="w">  </span><span class="nx">selectQuoteState</span><span class="p">,</span>
<span class="w">  </span><span class="p">(</span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteState</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">selectedQuote</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectQuoteById</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">createSelector</span><span class="p">(</span>
<span class="w">  </span><span class="nx">selectAllQuotes</span><span class="p">,</span>
<span class="w">  </span><span class="p">(</span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="p">[])</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">quotes</span><span class="p">.</span><span class="nx">find</span><span class="p">(</span><span class="nx">quote</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">quote</span><span class="p">.</span><span class="nx">id</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="nx">quoteId</span><span class="p">)</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectQuotesLoading</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createSelector</span><span class="p">(</span>
<span class="w">  </span><span class="nx">selectQuoteState</span><span class="p">,</span>
<span class="w">  </span><span class="p">(</span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteState</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">loading</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectFilteredQuotes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createSelector</span><span class="p">(</span>
<span class="w">  </span><span class="nx">selectAllQuotes</span><span class="p">,</span>
<span class="w">  </span><span class="nx">selectQuoteState</span><span class="p">,</span>
<span class="w">  </span><span class="p">(</span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="p">[],</span><span class="w"> </span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteState</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">let</span><span class="w"> </span><span class="nx">filtered</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">quotes</span><span class="p">;</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">.</span><span class="nx">filters</span><span class="p">.</span><span class="nx">status</span><span class="w"> </span><span class="o">!==</span><span class="w"> </span><span class="s1">&#39;all&#39;</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">filtered</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">filtered</span><span class="p">.</span><span class="nx">filter</span><span class="p">(</span><span class="nx">quote</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">quote</span><span class="p">.</span><span class="nx">status</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">filters</span><span class="p">.</span><span class="nx">status</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">.</span><span class="nx">filters</span><span class="p">.</span><span class="nx">customerId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">filtered</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">filtered</span><span class="p">.</span><span class="nx">filter</span><span class="p">(</span><span class="nx">quote</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">quote</span><span class="p">.</span><span class="nx">customerId</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">filters</span><span class="p">.</span><span class="nx">customerId</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">.</span><span class="nx">searchQuery</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">query</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">searchQuery</span><span class="p">.</span><span class="nx">toLowerCase</span><span class="p">();</span>
<span class="w">      </span><span class="nx">filtered</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">filtered</span><span class="p">.</span><span class="nx">filter</span><span class="p">(</span><span class="nx">quote</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">        </span><span class="nx">quote</span><span class="p">.</span><span class="nx">id</span><span class="p">.</span><span class="nx">toLowerCase</span><span class="p">().</span><span class="nx">includes</span><span class="p">(</span><span class="nx">query</span><span class="p">)</span><span class="w"> </span><span class="o">||</span>
<span class="w">        </span><span class="nx">quote</span><span class="p">.</span><span class="nx">customerName</span><span class="p">.</span><span class="nx">toLowerCase</span><span class="p">().</span><span class="nx">includes</span><span class="p">(</span><span class="nx">query</span><span class="p">)</span>
<span class="w">      </span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">filtered</span><span class="p">.</span><span class="nx">sort</span><span class="p">((</span><span class="nx">a</span><span class="p">,</span><span class="w"> </span><span class="nx">b</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">.</span><span class="nx">sortBy</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;dateCreated&#39;</span><span class="o">:</span>
<span class="w">          </span><span class="k">return</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">(</span><span class="nx">b</span><span class="p">.</span><span class="nx">createdAt</span><span class="p">).</span><span class="nx">getTime</span><span class="p">()</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">(</span><span class="nx">a</span><span class="p">.</span><span class="nx">createdAt</span><span class="p">).</span><span class="nx">getTime</span><span class="p">();</span>
<span class="w">        </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;total&#39;</span><span class="o">:</span>
<span class="w">          </span><span class="k">return</span><span class="w"> </span><span class="nx">b</span><span class="p">.</span><span class="nx">total</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nx">a</span><span class="p">.</span><span class="nx">total</span><span class="p">;</span>
<span class="w">        </span><span class="nx">default</span><span class="o">:</span>
<span class="w">          </span><span class="kt">return</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">);</span>

<span class="c1">// Cart Selectors</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectCartItems</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createSelector</span><span class="p">(</span>
<span class="w">  </span><span class="nx">selectCartState</span><span class="p">,</span>
<span class="w">  </span><span class="p">(</span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="kt">CartState</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">items</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectCartSubtotal</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createSelector</span><span class="p">(</span>
<span class="w">  </span><span class="nx">selectCartState</span><span class="p">,</span>
<span class="w">  </span><span class="p">(</span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="kt">CartState</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">subtotal</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectCartTotal</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createSelector</span><span class="p">(</span>
<span class="w">  </span><span class="nx">selectCartState</span><span class="p">,</span>
<span class="w">  </span><span class="p">(</span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="kt">CartState</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">total</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectCartItemCount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createSelector</span><span class="p">(</span>
<span class="w">  </span><span class="nx">selectCartItems</span><span class="p">,</span>
<span class="w">  </span><span class="p">(</span><span class="nx">items</span><span class="o">:</span><span class="w"> </span><span class="kt">CartItem</span><span class="p">[])</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">items</span><span class="p">.</span><span class="nx">reduce</span><span class="p">((</span><span class="nx">count</span><span class="p">,</span><span class="w"> </span><span class="nx">item</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">count</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="nx">item</span><span class="p">.</span><span class="nx">quantity</span><span class="p">,</span><span class="w"> </span><span class="mf">0</span><span class="p">)</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectAppliedDiscounts</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createSelector</span><span class="p">(</span>
<span class="w">  </span><span class="nx">selectCartState</span><span class="p">,</span>
<span class="w">  </span><span class="p">(</span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="kt">CartState</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">appliedDiscounts</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectCartTotalWithBreakdown</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createSelector</span><span class="p">(</span>
<span class="w">  </span><span class="nx">selectCartState</span><span class="p">,</span>
<span class="w">  </span><span class="p">(</span><span class="nx">state</span><span class="o">:</span><span class="w"> </span><span class="kt">CartState</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="nx">subtotal</span><span class="o">:</span><span class="w"> </span><span class="kt">state.subtotal</span><span class="p">,</span>
<span class="w">    </span><span class="nx">discounts</span><span class="o">:</span><span class="w"> </span><span class="kt">state.appliedDiscounts</span><span class="p">,</span>
<span class="w">    </span><span class="nx">taxes</span><span class="o">:</span><span class="w"> </span><span class="kt">state.taxes</span><span class="p">,</span>
<span class="w">    </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">state.total</span><span class="p">,</span>
<span class="w">    </span><span class="nx">savings</span><span class="o">:</span><span class="w"> </span><span class="kt">state.appliedDiscounts.reduce</span><span class="p">((</span><span class="nx">sum</span><span class="p">,</span><span class="w"> </span><span class="nx">discount</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">sum</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="nx">discount</span><span class="p">.</span><span class="nx">amount</span><span class="p">,</span><span class="w"> </span><span class="mf">0</span><span class="p">)</span>
<span class="w">  </span><span class="p">})</span>
<span class="p">);</span>
</code></pre></div>
<h2 id="best-practices">Best Practices<a class="headerlink" href="#best-practices" title="Permanent link">&para;</a></h2>
<h3 id="state-management-guidelines">State Management Guidelines<a class="headerlink" href="#state-management-guidelines" title="Permanent link">&para;</a></h3>
<ol>
<li>
<p><strong>Immutable State Updates</strong>
   <div class="highlight"><pre><span></span><code><span class="c1">// ✅ Good - Immutable update</span>
<span class="nx">on</span><span class="p">(</span><span class="nx">updateQuote</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">quote</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">  </span><span class="p">...</span><span class="nx">state</span><span class="p">,</span>
<span class="w">  </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="kt">state.quotes.map</span><span class="p">(</span><span class="nx">q</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">q</span><span class="p">.</span><span class="nx">id</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="nx">quote</span><span class="p">.</span><span class="nx">id</span><span class="w"> </span><span class="o">?</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="p">...</span><span class="nx">q</span><span class="p">,</span><span class="w"> </span><span class="p">...</span><span class="nx">quote</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="o">:</span><span class="w"> </span><span class="nx">q</span><span class="p">)</span>
<span class="p">}))</span>

<span class="c1">// ❌ Bad - Mutating state</span>
<span class="nx">on</span><span class="p">(</span><span class="nx">updateQuote</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">state</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">quote</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">index</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">quotes</span><span class="p">.</span><span class="nx">findIndex</span><span class="p">(</span><span class="nx">q</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">q</span><span class="p">.</span><span class="nx">id</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="nx">quote</span><span class="p">.</span><span class="nx">id</span><span class="p">);</span>
<span class="w">  </span><span class="nx">state</span><span class="p">.</span><span class="nx">quotes</span><span class="p">[</span><span class="nx">index</span><span class="p">]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">quote</span><span class="p">;</span><span class="w"> </span><span class="c1">// Mutation!</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">state</span><span class="p">;</span>
<span class="p">})</span>
</code></pre></div></p>
</li>
<li>
<p><strong>Normalized State Structure</strong>
   <div class="highlight"><pre><span></span><code><span class="c1">// ✅ Good - Normalized structure</span>
<span class="kd">interface</span><span class="w"> </span><span class="nx">QuoteState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">entities</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="p">[</span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">]</span><span class="o">:</span><span class="w"> </span><span class="nx">Quote</span><span class="w"> </span><span class="p">};</span>
<span class="w">  </span><span class="nx">ids</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">selectedId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// ❌ Bad - Nested arrays</span>
<span class="kd">interface</span><span class="w"> </span><span class="nx">QuoteState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">selectedQuote</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div></p>
</li>
<li>
<p><strong>Selector Composition</strong>
   <div class="highlight"><pre><span></span><code><span class="c1">// ✅ Good - Composed selectors</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">selectQuoteWithServices</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createSelector</span><span class="p">(</span>
<span class="w">  </span><span class="nx">selectQuoteById</span><span class="p">,</span>
<span class="w">  </span><span class="nx">selectAllServices</span><span class="p">,</span>
<span class="w">  </span><span class="p">(</span><span class="nx">quote</span><span class="p">,</span><span class="w"> </span><span class="nx">services</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">    </span><span class="p">...</span><span class="nx">quote</span><span class="p">,</span>
<span class="w">    </span><span class="nx">services</span><span class="o">:</span><span class="w"> </span><span class="kt">quote?.serviceIds?.map</span><span class="p">(</span><span class="nx">id</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">services</span><span class="p">.</span><span class="nx">find</span><span class="p">(</span><span class="nx">s</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">s</span><span class="p">.</span><span class="nx">id</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="nx">id</span><span class="p">))</span>
<span class="w">  </span><span class="p">})</span>
<span class="p">);</span>
</code></pre></div></p>
</li>
<li>
<p><strong>Effect Error Handling</strong>
   <div class="highlight"><pre><span></span><code><span class="c1">// ✅ Good - Comprehensive error handling</span>
<span class="nx">loadQuotes$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createEffect</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">  </span><span class="k">this</span><span class="p">.</span><span class="nx">actions$</span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">    </span><span class="nx">ofType</span><span class="p">(</span><span class="nx">loadQuotes</span><span class="p">),</span>
<span class="w">    </span><span class="nx">switchMap</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">quoteService</span><span class="p">.</span><span class="nx">getQuotes</span><span class="p">().</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">        </span><span class="nx">map</span><span class="p">(</span><span class="nx">quotes</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">loadQuotesSuccess</span><span class="p">({</span><span class="w"> </span><span class="nx">quotes</span><span class="w"> </span><span class="p">})),</span>
<span class="w">        </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">this</span><span class="p">.</span><span class="nx">logger</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Failed to load quotes&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">);</span>
<span class="w">          </span><span class="k">this</span><span class="p">.</span><span class="nx">notificationService</span><span class="p">.</span><span class="nx">showError</span><span class="p">(</span><span class="s1">&#39;Failed to load quotes&#39;</span><span class="p">);</span>
<span class="w">          </span><span class="k">return</span><span class="w"> </span><span class="k">of</span><span class="p">(</span><span class="nx">loadQuotesFailure</span><span class="p">({</span><span class="w"> </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">error.message</span><span class="w"> </span><span class="p">}));</span>
<span class="w">        </span><span class="p">})</span>
<span class="w">      </span><span class="p">)</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">)</span>
<span class="p">);</span>
</code></pre></div></p>
</li>
</ol>
<hr />
<p><strong>Next</strong>: <a href="../04-api-integration/">API Integration →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>