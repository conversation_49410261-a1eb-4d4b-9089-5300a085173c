{"name": "@telus/mcp-pagerduty", "version": "0.1.4", "description": "mcp server focus on pagerduty incident data", "keywords": ["telus", "mcp", "<PERSON><PERSON><PERSON><PERSON>"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-pagerduty"}, "license": "MIT", "author": "<PERSON> (@chengli-telus)", "type": "module", "main": "dist/index.js", "bin": {"mcp-pagerduty": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "prepare": "pnpm run build", "start": "node dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "dotenv": "~16.4.7", "node-fetch": "~3.3.2", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}