

⚠️ **DEPRECATION NOTICE** ⚠️

This MCP is being deprecated in favour of the new Unified Change Calendar view at [go/unifiedChangeCalendar](https://go.telus.com/unifiedChangeCalendar), and due to the relatively tight controls placed around ITSM and eChange API keys.

---

# Change Query MCP Server

This MCP (Model Context Protocol) server provides a tool for retrieving and managing changes from multiple sources including GitHub issues, ITSM (IT Service Management), and eChange within a specified time frame. It's designed to work with TELUS Digital systems and integrates with the MCP system.

## Features

- Fetch changes from multiple sources:
  - GitHub issues within a specified date range
  - ITSM (IT Service Management) changes
  - eChange system changes
- Filter and process changes by date range (required for GitHub, optional for ITSM/eChange)
- Unified interface for querying changes across all systems
- Ability to query specific sources or aggregate from all sources

## Setup

1. Clone this repository
2. Install dependencies: `npm install`
3. Set up environment variables (see Configuration section)
4. Build the project: `npm run build`
5. Start the server: `npm start`

## Configuration

This server requires various credentials for accessing different systems:

### GitHub Configuration
A fine-grained GitHub Personal Access Token with the following scopes:

- `repo`: Full control of private repositories
  - `repo:status`: Access commit status
  - `repo_deployment`: Access deployment status
  - `public_repo`: Access public repositories
  - `repo:invite`: Access repository invitations
  - `security_events`: Read and write security events

To set up the GitHub token:

1. Go to GitHub Settings > Developer settings > Personal access tokens > Fine-grained tokens
2. Generate a new token with the above scopes
3. Add the token to your environment variables or MCP settings file

### MCP Settings Configuration

Add the following to your MCP settings file:

```json
{
  "mcpServers": {
    "change-management": {
      "command": "node",
      "args": ["/path/to/build/index.js"],
      "env": {
        "GITHUB_TOKEN": "your_token_here",
        "GITHUB_OWNER": "telus",
        "GITHUB_REPO": "auto-crq",
        "ITSM_CLIENT_ID": "kong_client_id_here",
        "ITSM_CLIENT_SECRET": "kong_client_secret_here",
        "ITSM_API_URL": "https://environment_url/enterprise/changeManagement/v1",
        "ITSM_TOKEN_URL": "https://environment_url/token"
      },
    }
  }
}
```

## Usage

Once the server is running, you can use the `get_changes` tool to retrieve changes. The tool supports the following parameters:

- `startDate` (optional*): Start date in ISO 8601 format (YYYY-MM-DD)
- `endDate` (optional*): End date in ISO 8601 format (YYYY-MM-DD)
- `source` (optional): Source of changes ('github', 'itsm', 'echange', or 'all', defaults to 'all')

*Note: startDate and endDate are required when fetching GitHub changes

Examples:

```javascript
// Fetch changes from all sources
<use_mcp_tool>
<server_name>change-management</server_name>
<tool_name>get_changes</tool_name>
<arguments>
{
  "source": "all",
  "startDate": "2025-01-22",
  "endDate": "2025-01-29"
}
</arguments>
</use_mcp_tool>

// Fetch only ITSM changes (dates optional)
<use_mcp_tool>
<server_name>change-management</server_name>
<tool_name>get_changes</tool_name>
<arguments>
{
  "source": "itsm"
}
</arguments>
</use_mcp_tool>
```

The tool will return a JSON object containing changes from the requested sources, organized by source type.

## Contributing

Please read our contributing guidelines before submitting pull requests.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
