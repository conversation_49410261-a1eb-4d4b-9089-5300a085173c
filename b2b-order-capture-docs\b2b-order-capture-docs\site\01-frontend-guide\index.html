
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/01-frontend-guide/">
      
      
      
      
      <link rel="icon" href="../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Frontend Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#frontend-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Frontend Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href=".." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#angular-application-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Angular Application Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Angular Application Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#source-code-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Source Code Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Build Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#custom-telus-components" class="md-nav__link">
    <span class="md-ellipsis">
      Custom TELUS Components
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Custom TELUS Components">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-order-components" class="md-nav__link">
    <span class="md-ellipsis">
      Core Order Components
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Core Order Components">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-telus-start-quotation" class="md-nav__link">
    <span class="md-ellipsis">
      1. telus-start-quotation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-telus-assign-location" class="md-nav__link">
    <span class="md-ellipsis">
      2. telus-assign-location
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-telus-cart-total-prices" class="md-nav__link">
    <span class="md-ellipsis">
      3. telus-cart-total-prices
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#quote-management-components" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Management Components
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Management Components">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#4-telus-quote-view" class="md-nav__link">
    <span class="md-ellipsis">
      4. telus-quote-view
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#5-telus-quote-view-header" class="md-nav__link">
    <span class="md-ellipsis">
      5. telus-quote-view-header
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#6-telus-quote-view-parameter" class="md-nav__link">
    <span class="md-ellipsis">
      6. telus-quote-view-parameter
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-configuration-components" class="md-nav__link">
    <span class="md-ellipsis">
      Service Configuration Components
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Configuration Components">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#7-telus-eligibility-parameters" class="md-nav__link">
    <span class="md-ellipsis">
      7. telus-eligibility-parameters
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#8-telus-quote-item-card-tree-container" class="md-nav__link">
    <span class="md-ellipsis">
      8. telus-quote-item-card-tree-container
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#9-telus-information-items" class="md-nav__link">
    <span class="md-ellipsis">
      9. telus-information-items
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pricing-components" class="md-nav__link">
    <span class="md-ellipsis">
      Pricing Components
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Pricing Components">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#10-telus-prices" class="md-nav__link">
    <span class="md-ellipsis">
      10. telus-prices
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#11-telus-product-base-price" class="md-nav__link">
    <span class="md-ellipsis">
      11. telus-product-base-price
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#12-telus-quote-item-discount" class="md-nav__link">
    <span class="md-ellipsis">
      12. telus-quote-item-discount
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#additional-components" class="md-nav__link">
    <span class="md-ellipsis">
      Additional Components
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Additional Components">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#13-18-specialized-components" class="md-nav__link">
    <span class="md-ellipsis">
      13-18. Specialized Components
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Component Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#state-management" class="md-nav__link">
    <span class="md-ellipsis">
      State Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="State Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#ngrx-store-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      NgRx Store Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#state-modules" class="md-nav__link">
    <span class="md-ellipsis">
      State Modules
    </span>
  </a>
  
    <nav class="md-nav" aria-label="State Modules">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-state" class="md-nav__link">
    <span class="md-ellipsis">
      Quote State
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cart-state" class="md-nav__link">
    <span class="md-ellipsis">
      Cart State
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#actions-effects" class="md-nav__link">
    <span class="md-ellipsis">
      Actions &amp; Effects
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#routing-navigation" class="md-nav__link">
    <span class="md-ellipsis">
      Routing &amp; Navigation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Routing &amp; Navigation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#route-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Route Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#custom-route-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Custom Route Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Development Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-process" class="md-nav__link">
    <span class="md-ellipsis">
      Build Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#code-quality" class="md-nav__link">
    <span class="md-ellipsis">
      Code Quality
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategy
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="frontend-guide">Frontend Guide<a class="headerlink" href="#frontend-guide" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#angular-application-overview">Angular Application Overview</a></li>
<li><a href="#project-structure">Project Structure</a></li>
<li><a href="#custom-telus-components">Custom TELUS Components</a></li>
<li><a href="#state-management">State Management</a></li>
<li><a href="#routing--navigation">Routing &amp; Navigation</a></li>
<li><a href="#development-workflow">Development Workflow</a></li>
</ul>
<h2 id="angular-application-overview">Angular Application Overview<a class="headerlink" href="#angular-application-overview" title="Permanent link">&para;</a></h2>
<p>The frontend is built with <strong>Angular 15.2.9</strong> and provides a modern, responsive user interface for TELUS B2B order capture and quote generation.</p>
<h3 id="key-features">Key Features<a class="headerlink" href="#key-features" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Custom TELUS Components</strong>: 18+ specialized components for B2B workflows</li>
<li><strong>NgRx State Management</strong>: Centralized application state</li>
<li><strong>Responsive Design</strong>: Mobile-first approach with TELUS branding</li>
<li><strong>Real-time Updates</strong>: Live quote calculations and validation</li>
<li><strong>Accessibility</strong>: WCAG 2.1 AA compliance</li>
</ul>
<h3 id="technology-stack">Technology Stack<a class="headerlink" href="#technology-stack" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;framework&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Angular 15.2.9&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;language&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;TypeScript 4.9.5&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;stateManagement&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;NgRx 15.4.0&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;styling&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;LESS 3.13.1&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;uiLibrary&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;@netcracker/ux-ng2 2023.3.9&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;buildTool&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Angular CLI 15.2.8&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;bundler&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Webpack (via Angular CLI)&quot;</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="project-structure">Project Structure<a class="headerlink" href="#project-structure" title="Permanent link">&para;</a></h2>
<h3 id="source-code-organization">Source Code Organization<a class="headerlink" href="#source-code-organization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>src/
├── app/
│   ├── custom/                    # Custom TELUS components
│   │   ├── telus-assign-location/
│   │   ├── telus-cart-total-prices/
│   │   ├── telus-quote-view/
│   │   └── [15 more components...]
│   ├── modules/                   # Feature modules
│   ├── store/                     # NgRx state management
│   ├── interceptors/              # HTTP interceptors
│   ├── environments/              # Environment configurations
│   ├── configuration/             # App configuration
│   ├── app.component.ts           # Root component
│   ├── app.module.ts              # Root module
│   └── app-routing.module.ts      # Main routing
├── assets/                        # Static assets
├── index.html                     # Main HTML template
├── main.ts                        # Application bootstrap
└── polyfills.ts                   # Browser compatibility
</code></pre></div>
<h3 id="build-configuration">Build Configuration<a class="headerlink" href="#build-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;scripts&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;serve:dev&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ng serve --port=4200 --configuration development&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;serve:proxy&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ng serve --proxy-config demo/proxy.conf.js&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;build&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ng build --configuration production&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;build:dev&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ng build --configuration development&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;mock:server&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;tsc-watch --watch ./demo/server/server.ts&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;styles:watch&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;gulp styles:watch&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="custom-telus-components">Custom TELUS Components<a class="headerlink" href="#custom-telus-components" title="Permanent link">&para;</a></h2>
<p>The application includes 18+ custom components specifically designed for TELUS B2B workflows:</p>
<h3 id="core-order-components">Core Order Components<a class="headerlink" href="#core-order-components" title="Permanent link">&para;</a></h3>
<h4 id="1-telus-start-quotation">1. <strong>telus-start-quotation</strong><a class="headerlink" href="#1-telus-start-quotation" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Purpose</strong>: Initiates the quote generation process</li>
<li><strong>Features</strong>: Service selection, customer information capture</li>
<li><strong>Integration</strong>: Connects to backend quote APIs</li>
</ul>
<h4 id="2-telus-assign-location">2. <strong>telus-assign-location</strong><a class="headerlink" href="#2-telus-assign-location" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Purpose</strong>: Location assignment and validation</li>
<li><strong>Features</strong>: Address autocomplete, geocoding integration</li>
<li><strong>Dependencies</strong>: Google Maps API, TELUS location services</li>
</ul>
<h4 id="3-telus-cart-total-prices">3. <strong>telus-cart-total-prices</strong><a class="headerlink" href="#3-telus-cart-total-prices" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Purpose</strong>: Shopping cart with pricing calculations</li>
<li><strong>Features</strong>: Real-time price updates, discount application</li>
<li><strong>State</strong>: Managed through NgRx store</li>
</ul>
<h3 id="quote-management-components">Quote Management Components<a class="headerlink" href="#quote-management-components" title="Permanent link">&para;</a></h3>
<h4 id="4-telus-quote-view">4. <strong>telus-quote-view</strong><a class="headerlink" href="#4-telus-quote-view" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Purpose</strong>: Comprehensive quote display</li>
<li><strong>Features</strong>: Detailed pricing breakdown, service summaries</li>
<li><strong>Export</strong>: PDF generation, email sharing</li>
</ul>
<h4 id="5-telus-quote-view-header">5. <strong>telus-quote-view-header</strong><a class="headerlink" href="#5-telus-quote-view-header" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Purpose</strong>: Quote header information</li>
<li><strong>Features</strong>: Quote ID, status, expiration date</li>
<li><strong>Actions</strong>: Edit, duplicate, export options</li>
</ul>
<h4 id="6-telus-quote-view-parameter">6. <strong>telus-quote-view-parameter</strong><a class="headerlink" href="#6-telus-quote-view-parameter" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Purpose</strong>: Quote parameter display and editing</li>
<li><strong>Features</strong>: Service parameters, configuration options</li>
<li><strong>Validation</strong>: Real-time parameter validation</li>
</ul>
<h3 id="service-configuration-components">Service Configuration Components<a class="headerlink" href="#service-configuration-components" title="Permanent link">&para;</a></h3>
<h4 id="7-telus-eligibility-parameters">7. <strong>telus-eligibility-parameters</strong><a class="headerlink" href="#7-telus-eligibility-parameters" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Purpose</strong>: Service eligibility checking</li>
<li><strong>Features</strong>: Location-based eligibility, service availability</li>
<li><strong>Integration</strong>: TELUS service catalog APIs</li>
</ul>
<h4 id="8-telus-quote-item-card-tree-container">8. <strong>telus-quote-item-card-tree-container</strong><a class="headerlink" href="#8-telus-quote-item-card-tree-container" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Purpose</strong>: Hierarchical service display</li>
<li><strong>Features</strong>: Tree view of services, drag-and-drop reordering</li>
<li><strong>Performance</strong>: Virtual scrolling for large datasets</li>
</ul>
<h4 id="9-telus-information-items">9. <strong>telus-information-items</strong><a class="headerlink" href="#9-telus-information-items" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Purpose</strong>: Service information display</li>
<li><strong>Features</strong>: Rich text content, expandable sections</li>
<li><strong>Accessibility</strong>: Screen reader support, keyboard navigation</li>
</ul>
<h3 id="pricing-components">Pricing Components<a class="headerlink" href="#pricing-components" title="Permanent link">&para;</a></h3>
<h4 id="10-telus-prices">10. <strong>telus-prices</strong><a class="headerlink" href="#10-telus-prices" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Purpose</strong>: Price display and formatting</li>
<li><strong>Features</strong>: Currency formatting, tax calculations</li>
<li><strong>Localization</strong>: Multi-currency support</li>
</ul>
<h4 id="11-telus-product-base-price">11. <strong>telus-product-base-price</strong><a class="headerlink" href="#11-telus-product-base-price" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Purpose</strong>: Base product pricing</li>
<li><strong>Features</strong>: Tiered pricing, volume discounts</li>
<li><strong>Calculation</strong>: Real-time price updates</li>
</ul>
<h4 id="12-telus-quote-item-discount">12. <strong>telus-quote-item-discount</strong><a class="headerlink" href="#12-telus-quote-item-discount" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Purpose</strong>: Discount application and display</li>
<li><strong>Features</strong>: Percentage and fixed discounts, promotional codes</li>
<li><strong>Validation</strong>: Discount eligibility checking</li>
</ul>
<h3 id="additional-components">Additional Components<a class="headerlink" href="#additional-components" title="Permanent link">&para;</a></h3>
<h4 id="13-18-specialized-components">13-18. <strong>Specialized Components</strong><a class="headerlink" href="#13-18-specialized-components" title="Permanent link">&para;</a></h4>
<ul>
<li><code>telus-assign-quote-cancellation</code>: Quote cancellation workflow</li>
<li><code>telus-confirmed-order-screen</code>: Order confirmation display</li>
<li><code>telus-error-page</code>: Error handling and display</li>
<li><code>telus-google-autocomplete-address-input</code>: Address input with autocomplete</li>
<li><code>telus-quote-item-information</code>: Detailed item information</li>
<li><code>telus-quote-item-price</code>: Individual item pricing</li>
<li><code>telus-quote-list-card-attributes</code>: Quote list attributes</li>
</ul>
<h3 id="component-architecture">Component Architecture<a class="headerlink" href="#component-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Example component structure</span>
<span class="kd">@Component</span><span class="p">({</span>
<span class="w">  </span><span class="nx">selector</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;telus-quote-view&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">templateUrl</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;./telus-quote-view.component.html&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">styleUrls</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;./telus-quote-view.component.less&#39;</span><span class="p">],</span>
<span class="w">  </span><span class="nx">changeDetection</span><span class="o">:</span><span class="w"> </span><span class="kt">ChangeDetectionStrategy.OnPush</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">TelusQuoteViewComponent</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">OnInit</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">@Input</span><span class="p">()</span><span class="w"> </span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="kd">@Output</span><span class="p">()</span><span class="w"> </span><span class="nx">quoteUpdated</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">EventEmitter</span><span class="o">&lt;</span><span class="nx">Quote</span><span class="o">&gt;</span><span class="p">();</span>

<span class="w">  </span><span class="nx">quote$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">store</span><span class="p">.</span><span class="nx">select</span><span class="p">(</span><span class="nx">selectQuoteById</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">quoteId</span><span class="p">));</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">store</span><span class="o">:</span><span class="w"> </span><span class="kt">Store</span><span class="o">&lt;</span><span class="nx">AppState</span><span class="o">&gt;</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">quoteService</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteService</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="state-management">State Management<a class="headerlink" href="#state-management" title="Permanent link">&para;</a></h2>
<h3 id="ngrx-store-architecture">NgRx Store Architecture<a class="headerlink" href="#ngrx-store-architecture" title="Permanent link">&para;</a></h3>
<p>The application uses NgRx for centralized state management:</p>
<div class="highlight"><pre><span></span><code><span class="c1">// Store structure</span>
<span class="kd">interface</span><span class="w"> </span><span class="nx">AppState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">cart</span><span class="o">:</span><span class="w"> </span><span class="kt">CartState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">user</span><span class="o">:</span><span class="w"> </span><span class="kt">UserState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">services</span><span class="o">:</span><span class="w"> </span><span class="kt">ServiceState</span><span class="p">;</span>
<span class="w">  </span><span class="nx">locations</span><span class="o">:</span><span class="w"> </span><span class="kt">LocationState</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="state-modules">State Modules<a class="headerlink" href="#state-modules" title="Permanent link">&para;</a></h3>
<h4 id="quote-state">Quote State<a class="headerlink" href="#quote-state" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="kd">interface</span><span class="w"> </span><span class="nx">QuoteState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">selectedQuote</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">  </span><span class="nx">loading</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="cart-state">Cart State<a class="headerlink" href="#cart-state" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="kd">interface</span><span class="w"> </span><span class="nx">CartState</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">items</span><span class="o">:</span><span class="w"> </span><span class="kt">CartItem</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">totalPrice</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">discounts</span><span class="o">:</span><span class="w"> </span><span class="kt">Discount</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">taxes</span><span class="o">:</span><span class="w"> </span><span class="kt">Tax</span><span class="p">[];</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="actions-effects">Actions &amp; Effects<a class="headerlink" href="#actions-effects" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Quote actions</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">loadQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Quote] Load Quote&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">loadQuoteSuccess</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createAction</span><span class="p">(</span>
<span class="w">  </span><span class="s1">&#39;[Quote] Load Quote Success&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">props</span><span class="o">&lt;</span><span class="p">{</span><span class="w"> </span><span class="nx">quote</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">()</span>
<span class="p">);</span>

<span class="c1">// Quote effects</span>
<span class="kd">@Injectable</span><span class="p">()</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">QuoteEffects</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">loadQuote$</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">createEffect</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">actions$</span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">ofType</span><span class="p">(</span><span class="nx">loadQuote</span><span class="p">),</span>
<span class="w">      </span><span class="nx">switchMap</span><span class="p">(</span><span class="nx">action</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">quoteService</span><span class="p">.</span><span class="nx">getQuote</span><span class="p">(</span><span class="nx">action</span><span class="p">.</span><span class="nx">quoteId</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">          </span><span class="nx">map</span><span class="p">(</span><span class="nx">quote</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">loadQuoteSuccess</span><span class="p">({</span><span class="w"> </span><span class="nx">quote</span><span class="w"> </span><span class="p">})),</span>
<span class="w">          </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">of</span><span class="p">(</span><span class="nx">loadQuoteFailure</span><span class="p">({</span><span class="w"> </span><span class="nx">error</span><span class="w"> </span><span class="p">})))</span>
<span class="w">        </span><span class="p">)</span>
<span class="w">      </span><span class="p">)</span>
<span class="w">    </span><span class="p">)</span>
<span class="w">  </span><span class="p">);</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="routing-navigation">Routing &amp; Navigation<a class="headerlink" href="#routing-navigation" title="Permanent link">&para;</a></h2>
<h3 id="route-configuration">Route Configuration<a class="headerlink" href="#route-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="kd">const</span><span class="w"> </span><span class="nx">routes</span><span class="o">:</span><span class="w"> </span><span class="kt">Routes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nx">path</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;quotes&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loadChildren</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">import</span><span class="p">(</span><span class="s1">&#39;./modules/quotes/quotes.module&#39;</span><span class="p">).</span><span class="nx">then</span><span class="p">(</span><span class="nx">m</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">m</span><span class="p">.</span><span class="nx">QuotesModule</span><span class="p">)</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nx">path</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;orders&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loadChildren</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">import</span><span class="p">(</span><span class="s1">&#39;./modules/orders/orders.module&#39;</span><span class="p">).</span><span class="nx">then</span><span class="p">(</span><span class="nx">m</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">m</span><span class="p">.</span><span class="nx">OrdersModule</span><span class="p">)</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nx">path</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;services&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">loadChildren</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">import</span><span class="p">(</span><span class="s1">&#39;./modules/services/services.module&#39;</span><span class="p">).</span><span class="nx">then</span><span class="p">(</span><span class="nx">m</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">m</span><span class="p">.</span><span class="nx">ServicesModule</span><span class="p">)</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">];</span>
</code></pre></div>
<h3 id="custom-route-configuration">Custom Route Configuration<a class="headerlink" href="#custom-route-configuration" title="Permanent link">&para;</a></h3>
<p>The application includes custom routing defined in <code>custom-app-routes.ts</code>:</p>
<div class="highlight"><pre><span></span><code><span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">customRoutes</span><span class="o">:</span><span class="w"> </span><span class="kt">Routes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nx">path</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;telus/quote/:id&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">component</span><span class="o">:</span><span class="w"> </span><span class="kt">TelusQuoteViewComponent</span><span class="p">,</span>
<span class="w">    </span><span class="nx">canActivate</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="nx">AuthGuard</span><span class="p">]</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nx">path</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;telus/start-quotation&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">component</span><span class="o">:</span><span class="w"> </span><span class="kt">TelusStartQuotationComponent</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">];</span>
</code></pre></div>
<h2 id="development-workflow">Development Workflow<a class="headerlink" href="#development-workflow" title="Permanent link">&para;</a></h2>
<h3 id="local-development-setup">Local Development Setup<a class="headerlink" href="#local-development-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Install dependencies</span>
npm<span class="w"> </span>install

<span class="c1"># Start development server</span>
npm<span class="w"> </span>run<span class="w"> </span>serve:dev

<span class="c1"># Start with proxy (for backend integration)</span>
npm<span class="w"> </span>run<span class="w"> </span>serve:proxy

<span class="c1"># Watch styles</span>
npm<span class="w"> </span>run<span class="w"> </span>styles:watch
</code></pre></div>
<h3 id="build-process">Build Process<a class="headerlink" href="#build-process" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Development build</span>
npm<span class="w"> </span>run<span class="w"> </span>build:dev

<span class="c1"># Production build</span>
npm<span class="w"> </span>run<span class="w"> </span>build

<span class="c1"># Analyze bundle</span>
npm<span class="w"> </span>run<span class="w"> </span>report
</code></pre></div>
<h3 id="code-quality">Code Quality<a class="headerlink" href="#code-quality" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Linting</span>
npm<span class="w"> </span>run<span class="w"> </span>lint

<span class="c1"># Pre-commit hooks (Husky)</span>
npm<span class="w"> </span>run<span class="w"> </span>pre-commit

<span class="c1"># Prettier formatting</span>
npx<span class="w"> </span>prettier<span class="w"> </span>--write<span class="w"> </span><span class="s2">&quot;src/**/*.{ts,html,less}&quot;</span>
</code></pre></div>
<h3 id="testing-strategy">Testing Strategy<a class="headerlink" href="#testing-strategy" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Unit tests</span>
ng<span class="w"> </span><span class="nb">test</span>

<span class="c1"># E2E tests</span>
ng<span class="w"> </span>e2e

<span class="c1"># Coverage report</span>
ng<span class="w"> </span><span class="nb">test</span><span class="w"> </span>--code-coverage
</code></pre></div>
<hr />
<p><strong>Next</strong>: Explore the <a href="../02-backend-guide/">Backend Guide</a> for Spring Boot development details.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "..", "features": [], "search": "../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>