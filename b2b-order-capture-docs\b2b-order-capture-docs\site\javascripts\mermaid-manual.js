// Manual Mermaid initialization - converts code blocks to diagrams
console.log('=== MANUAL MERMAID SCRIPT LOADED ===');

function initMermaid() {
    console.log('Checking for Mermaid library...');
    
    if (typeof window.mermaid === 'undefined') {
        console.log('Mermaid not loaded yet, retrying in 500ms...');
        setTimeout(initMermaid, 500);
        return;
    }
    
    console.log('Mermaid library found! Initializing...');
    
    // Initialize Mermaid
    window.mermaid.initialize({
        startOnLoad: false,
        theme: 'default',
        securityLevel: 'loose',
        themeVariables: {
            primaryColor: '#673ab7',
            primaryTextColor: '#ffffff',
            primaryBorderColor: '#512da8',
            lineColor: '#757575'
        },
        flowchart: {
            useMaxWidth: true,
            htmlLabels: true
        },
        sequence: {
            useMaxWidth: true,
            showSequenceNumbers: false
        }
    });
    
    console.log('Mermaid initialized. Looking for code blocks...');
    
    // Find all code blocks that contain mermaid
    const codeBlocks = document.querySelectorAll('pre code');
    console.log('Found', codeBlocks.length, 'code blocks total');
    
    let mermaidCount = 0;
    
    codeBlocks.forEach((codeBlock, index) => {
        const text = codeBlock.textContent.trim();
        
        // Check if this looks like a mermaid diagram
        if (text.startsWith('graph ') || 
            text.startsWith('sequenceDiagram') || 
            text.startsWith('flowchart ') ||
            text.startsWith('classDiagram') ||
            text.startsWith('gantt') ||
            text.startsWith('gitgraph') ||
            text.includes('-->') ||
            text.includes('->>')) {
            
            console.log(`Found potential mermaid diagram ${mermaidCount + 1}:`, text.substring(0, 50) + '...');
            
            try {
                // Create a new div for the mermaid diagram
                const mermaidDiv = document.createElement('div');
                mermaidDiv.className = 'mermaid';
                mermaidDiv.textContent = text;
                mermaidDiv.style.textAlign = 'center';
                mermaidDiv.style.margin = '20px 0';
                
                // Replace the pre/code block with the mermaid div
                const preElement = codeBlock.parentElement;
                if (preElement && preElement.tagName === 'PRE') {
                    preElement.parentNode.replaceChild(mermaidDiv, preElement);
                } else {
                    codeBlock.parentNode.replaceChild(mermaidDiv, codeBlock);
                }
                
                // Render the diagram
                window.mermaid.init(undefined, mermaidDiv);
                
                mermaidCount++;
                console.log(`Successfully rendered mermaid diagram ${mermaidCount}`);
                
            } catch (error) {
                console.error(`Error rendering mermaid diagram ${mermaidCount + 1}:`, error);
                
                // Show error message
                const errorDiv = document.createElement('div');
                errorDiv.innerHTML = `
                    <div style="color: red; border: 1px solid red; padding: 15px; margin: 10px 0; background: #ffe6e6; border-radius: 5px;">
                        <strong>⚠️ Mermaid Diagram Error</strong><br>
                        ${error.message}<br>
                        <details style="margin-top: 10px;">
                            <summary>Show diagram code</summary>
                            <pre style="background: #f5f5f5; padding: 10px; margin-top: 5px; border-radius: 3px;">${text}</pre>
                        </details>
                    </div>
                `;
                
                const preElement = codeBlock.parentElement;
                if (preElement && preElement.tagName === 'PRE') {
                    preElement.parentNode.replaceChild(errorDiv, preElement);
                } else {
                    codeBlock.parentNode.replaceChild(errorDiv, codeBlock);
                }
            }
        }
    });
    
    console.log(`Processed ${mermaidCount} mermaid diagrams`);
    
    if (mermaidCount === 0) {
        console.log('No mermaid diagrams found. Looking for existing .mermaid divs...');
        
        const existingMermaidDivs = document.querySelectorAll('.mermaid');
        console.log('Found', existingMermaidDivs.length, 'existing .mermaid divs');
        
        if (existingMermaidDivs.length > 0) {
            try {
                window.mermaid.init(undefined, existingMermaidDivs);
                console.log('Rendered existing mermaid divs');
            } catch (error) {
                console.error('Error rendering existing mermaid divs:', error);
            }
        }
    }
}

// Start when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initMermaid);
} else {
    initMermaid();
}

// Also try when window loads
window.addEventListener('load', initMermaid);

// Handle navigation changes (for SPA-like behavior)
const observer = new MutationObserver(function(mutations) {
    let shouldReinit = false;
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            shouldReinit = true;
        }
    });
    if (shouldReinit) {
        setTimeout(initMermaid, 100);
    }
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});
