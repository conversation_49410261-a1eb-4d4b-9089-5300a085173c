// Simple Mermaid initialization
console.log('=== SIMPLE MERMAID SCRIPT LOADED ===');

function initMermaid() {
    console.log('Checking for Mermaid library...');

    if (typeof window.mermaid === 'undefined') {
        console.log('Mermaid not loaded yet, retrying in 1000ms...');
        setTimeout(initMermaid, 1000);
        return;
    }

    console.log('Mermaid library found! Version:', window.mermaid.version || 'unknown');

    try {
        // Initialize Mermaid with simple config
        window.mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose'
        });

        console.log('Mermaid initialized successfully');

        // Force render any existing mermaid elements
        setTimeout(() => {
            const mermaidElements = document.querySelectorAll('.mermaid');
            console.log('Found', mermaidElements.length, 'existing .mermaid elements');

            if (mermaidElements.length > 0) {
                window.mermaid.init(undefined, mermaidElements);
                console.log('Rendered existing mermaid elements');
            }
        }, 500);

    } catch (error) {
        console.error('Error initializing Mermaid:', error);
    }
}

// Start when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initMermaid);
} else {
    initMermaid();
}

// Also try when window loads
window.addEventListener('load', initMermaid);

// Handle navigation changes (for SPA-like behavior)
document.addEventListener('DOMContentLoaded', function() {
    // Re-initialize on navigation changes
    const observer = new MutationObserver(function(mutations) {
        let shouldReinit = false;
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                shouldReinit = true;
            }
        });
        if (shouldReinit) {
            setTimeout(initMermaid, 500);
        }
    });

    if (document.body) {
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
});
