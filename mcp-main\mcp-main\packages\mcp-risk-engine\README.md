# @telus/mcp-risk-engine

An MCP server that enables direct triggering of Risk Engine assessments, providing immediate risk analysis results for repository changes without requiring GitHub interactions.

## Project Structure

The project has been refactored into a modular structure for better maintainability:

- `src/index.ts`: Main entry point that initializes the server
- `src/constants.ts`: Contains configuration constants
- `src/server/index.ts`: Server setup and configuration
- `src/tools/index.ts`: Tool definitions
- `src/tools/triggerAssessment.ts`: Implementation of the assessment trigger functionality
- `src/types/assessment.ts`: TypeScript interfaces for assessment-related data

## Requirements

To use MCP Risk Engine, you need to:
1. Host your repository on GitHub.
2. Include the [Risk Engine configuration file](https://github.com/telus/sre-risk-engine/blob/main/docs/general/risk-config-file-setup.md) in your repository.

## Installation and Build

There are two ways to use this MCP server:

### Option 1: Configure Cline to use the pre-built package (Recommended)

For most users who just want to use the MCP server as a tool, you only need to add the configuration to your `cline_mcp_settings.json` file as described in the [Configuration](#configuration) section below. The Cline extension will automatically start the server when needed.

### Option 2: Build from source (For contributors)

If you're developing or modifying the MCP server itself, follow these steps:

1. Clone the repository:
   ```sh
   git clone https://github.com/telus/mcp-risk-engine.git
   cd mcp-risk-engine
   ```

2. Install dependencies:
   ```sh
   pnpm install
   # or if using npm
   npm install
   ```

   > **Note:** This project is configured to use pnpm as the preferred package manager. pnpm offers better disk space efficiency and faster installation times compared to npm by using a content-addressable store and symlinks to avoid package duplication.

3. Build the project:
   ```sh
   pnpm run build
   # or if using npm
   npm run build
   ```

The build process compiles TypeScript to JavaScript and sets the executable permissions for the main file.

When contributing changes, you don't need to manually start the server. Instead, update your Cline configuration to point to your local build and let Cline manage the server lifecycle.

## Configuration

Add the following configuration to your Cline MCP settings file (`cline_mcp_settings.json`):

```json
{
  "mcpServers": {
    "mcp-risk-engine": {
      "command": "node",
      "args": ["/path/to/mcp-risk-engine/dist/index.js"], // Adjust path as needed
      "transportType": "stdio",
      "timeout": 60, // Optional: Timeout in seconds for tool calls
      "disabled": false,
      "autoApprove": [
        "trigger_assessment"
      ] // Optional: Tools to auto-approve
    }
    // ... other servers
  }
}
```

**Note:**
*   Ensure the `args` path points to the correct location of the built `index.js` file for this MCP server.

## Usage

This server provides the `trigger_assessment` tool.

### Available Tools

#### `trigger_assessment`

Triggers a risk engine assessment by sending an `mcp_trigger` event to the Risk Engine dispatcher and returns the assessment result directly.

**Example Usage**

This example relies on the defaults for `owner` ("telus") and `branch` ("main").

```xml
<use_mcp_tool>
<server_name>mcp-risk-engine</server_name>
<tool_name>trigger_assessment</tool_name>
<arguments>
{
  "repo": "risk-engine-test-repo-2",
  "sha": "f8b0e43bc17bb82b469df62306150c4bfb3c195a"
}
</arguments>
</use_mcp_tool>
```

**Input Schema**

```json
{
  "type": "object",
  "properties": {
    "owner": {
      "type": "string",
      "description": "Repository owner (optional, defaults to 'telus')"
    },
    "repo": {
      "type": "string",
      "description": "Repository name"
    },
    "branch": {
      "type": "string",
      "description": "Branch name (optional, defaults to 'main')"
    },
    "sha": {
      "type": "string",
      "description": "Commit SHA"
    },
    "commit_message": {
      "type": "string",
      "description": "Commit message (optional)"
    }
  },
  "required": [
    "repo",
    "sha"
  ]
}
```

**Output**

Returns a JSON object containing the assessment result with the following structure:

```json
{
  "metadata": {
    "owner": "telus",
    "repo": "risk-engine-test-repo-2",
    "branch": "main",
    "gitCommitId": "f8b0e43bc17bb82b469df62306150c4bfb3c195a",
    "riskThreshold": 40
  },
  "assessmentDbRecord": {
    "id": 10401,
    "createdAt": "2025-05-08T18:48:33.251Z"
  },
  "categories": {
    "security": {
      "categoryTotalRisk": 0,
      "plugins": {
        "secretScanningAlerts": 0,
        "vulnerabilityAlerts": 0,
        "codeAnalysisAlerts": 0,
        "dynatraceVulnerabilityAlerts": 0
      }
    },
    "informational": {
      "categoryTotalRisk": 0,
      "plugins": {
        "innersource": 0,
        "semanticCommit": 100,
        "githubAdminTeam": 0,
        "incidentInfo": 0
      }
    },
    "applicationStatus": {
      "categoryTotalRisk": 21,
      "plugins": {
        "linesChanged": 0,
        "fontChecker": 0,
        "gitBranchProtection": 40,
        "gkeTemplateHealth": 100,
        "testCoverage": 0,
        "versionsChecker": 0,
        "changedFiles": 5
      }
    },
    "pluginRollout": {
      "categoryTotalRisk": 0,
      "plugins": {
        "dynatraceEnablementCheck": 100,
        "sccFindings": 100
      }
    },
    "additive": {
      "categoryTotalRisk": 0,
      "plugins": {
        "embargo": 0,
        "riskConfigHealth": 0,
        "dockerFileHealth": 0,
        "maintenanceWindow": 0
      }
    }
  },
  "assessmentSummary": {
    "totalRisk": 21,
    "callToAction": {
      "infoMessage": "Total risk is below the repository risk threshold and the repository is approved for automated CRQs.",
      "isPassing": true,
      "actionMessage": "This change can be deployed.",
      "isApprovedToProceed": true
    }
  }
}
```

This detailed output format provides:
1. The overall metadata in the `metadata` object
2. Assessment database record information
3. Risk categories with their total risk scores and individual plugin risk scores
4. An assessment summary with the total risk score and call to action
