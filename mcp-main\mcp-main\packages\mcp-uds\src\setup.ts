import { exec } from "child_process";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

import {
  REPOSITORY_URL,
  DIRECTORY_PATH,
  SSH_REPOSITORY_URL,
  HTTPS_REPOSITORY_URL,
} from "./config.js";

// Get the current file path and directory (ES modules replacement for __dirname)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const execPromise = (
  command: string,
  options: { cwd?: string; timeout?: number } = {},
) => {
  return new Promise<void>((resolve, reject) => {
    exec(command, options, (error, stdout) => {
      if (error) {
        return reject(new Error(`${error.message} error`));
      }
      console.log(stdout);
      resolve();
    });
  });
};

/**
 * Attempts to clone a repository with fallback from SSH to HTTPS if needed
 * @param repoUrl Initial repository URL to try (likely SSH based on config)
 * @param directory Target directory for the clone
 * @param options Command options
 * @returns Promise that resolves when clone succeeds
 */
const cloneWithFallback = async (
  repoUrl: string,
  directory: string,
  options: { timeout?: number } = {},
): Promise<void> => {
  try {
    // First attempt with the provided URL (likely SSH if SSH keys detected)
    console.log(`Attempting to clone repository using: ${repoUrl}`);
    await execPromise(`git clone ${repoUrl} ${directory}`, options);
  } catch (error) {
    // If the first attempt fails and we were using SSH, try HTTPS
    if (repoUrl === SSH_REPOSITORY_URL) {
      console.log("SSH clone failed, falling back to HTTPS...");
      try {
        await execPromise(
          `git clone ${HTTPS_REPOSITORY_URL} ${directory}`,
          options,
        );
      } catch (httpsError) {
        throw new Error(`Failed to clone repository using both SSH and HTTPS. 
          SSH error: ${error}
          HTTPS error: ${httpsError}`);
      }
    } else {
      // If we weren't using SSH or both failed, rethrow the original error
      throw error;
    }
  }
};

/**
 * Copies the convert-tables.js file to the cloned repository and runs it
 * @param directoryPath Path to the cloned repository
 */
const copyAndRunConvertTables = async (
  directoryPath: string,
): Promise<void> => {
  try {
    // Get the path to the convert-tables.js file (in the same directory as this file)
    const convertTablesPath = path.join(__dirname, "convert-tables.js");

    // Destination path in the cloned repository
    const destPath = path.join(directoryPath, "convert-tables.js");

    console.log(`Copying convert-tables.js to ${destPath}...`);

    // Copy the file
    fs.copyFileSync(convertTablesPath, destPath);

    console.log("File copied successfully. Running convert-tables.js...");

    // Run the script using Node.js
    await execPromise(`node ${destPath}`, {
      cwd: directoryPath,
      timeout: 120000, // 2 minutes timeout
    });

    console.log("convert-tables.js executed successfully.");
  } catch (error) {
    console.error("Error copying or running convert-tables.js:", error);
    throw error;
  }
};

/**
 * Runs the init.sh bash script in the cloned repository
 * @param directoryPath Path to the cloned repository
 */
const runInitScript = async (directoryPath: string): Promise<void> => {
  try {
    console.log("Running init.sh script...");

    // Run the bash script
    await execPromise(`bash ./init.sh`, {
      cwd: directoryPath,
      timeout: 300000, // 5 minutes timeout
    });

    console.log("init.sh executed successfully.");
  } catch (error) {
    console.error("Error running init.sh script:", error);
    throw error;
  }
};

/**
 * Checks if the repository is a valid git repository with a remote
 * @param directoryPath Path to the repository
 * @returns Promise that resolves to true if it's a valid git repository, false otherwise
 */
const isValidGitRepo = async (directoryPath: string): Promise<boolean> => {
  try {
    // Check if git status works (basic check for valid git repo)
    await execPromise(`git status`, {
      cwd: directoryPath,
      timeout: 30000, // 30 seconds timeout
    });

    // Check if the repository has a remote configured
    await execPromise(`git remote -v`, {
      cwd: directoryPath,
      timeout: 30000, // 30 seconds timeout
    });

    return true;
  } catch (error) {
    console.error("Not a valid git repository or no remote configured:", error);
    return false;
  }
};

/**
 * Checks if the repository has updates using git status
 * @param directoryPath Path to the repository
 * @returns Promise that resolves to true if there are updates, false otherwise
 */
const hasUpdates = async (directoryPath: string): Promise<boolean> => {
  try {
    console.log("Checking for repository updates...");

    // Update the remote refs
    await execPromise(`git remote update`, {
      cwd: directoryPath,
      timeout: 60000, // 60 seconds timeout
    });

    // Run git status to check if we're behind the remote
    return new Promise((resolve) => {
      const statusProcess = exec(`git status -uno`, { cwd: directoryPath });
      let output = "";

      statusProcess.stdout?.on("data", (data) => {
        output += data.toString();
      });

      statusProcess.on("close", () => {
        // Check if the output contains indicators that we're behind
        const isBehind =
          output.includes("behind") ||
          output.includes("Your branch is behind") ||
          output.includes("pull");

        console.log(
          `Repository status checked. Updates available: ${isBehind}`,
        );
        resolve(isBehind);
      });
    });
  } catch (error) {
    console.error("Error checking for updates:", error);
    // If there's an error, assume there are updates to be safe
    return true;
  }
};

const setup = async () => {
  try {
    let repositoryUpdated = false;

    if (
      fs.existsSync(DIRECTORY_PATH) &&
      fs.lstatSync(DIRECTORY_PATH).isDirectory()
    ) {
      const files = fs.readdirSync(DIRECTORY_PATH);

      if (files.length === 0) {
        // Directory exists but is empty, clone the repository
        console.log("Directory is empty, cloning repository...");
        await cloneWithFallback(REPOSITORY_URL, DIRECTORY_PATH, {
          timeout: 60000,
        }); // 60 seconds timeout
        repositoryUpdated = true;
      } else {
        // Directory exists and is not empty, check if it's a valid git repository
        const isGitRepo = fs.existsSync(path.join(DIRECTORY_PATH, ".git"));

        if (isGitRepo) {
          // Check if it's a valid git repository with a remote
          const isValid = await isValidGitRepo(DIRECTORY_PATH);

          if (isValid) {
            // First clean the repository to remove any uncommitted changes
            // This ensures that git status will give accurate results
            console.log("Cleaning repository before checking for updates...");
            await execPromise(`git checkout -- .`, {
              cwd: DIRECTORY_PATH,
              timeout: 30000, // 30 seconds timeout
            });

            await execPromise(`git clean -fd`, {
              cwd: DIRECTORY_PATH,
              timeout: 30000, // 30 seconds timeout
            });

            // Now check for updates
            const updates = await hasUpdates(DIRECTORY_PATH);

            if (updates) {
              // There are updates, pull the latest changes
              console.log("Updates available, pulling latest changes...");
              await execPromise(`git pull`, {
                cwd: DIRECTORY_PATH,
                timeout: 60000, // 60 seconds timeout
              });
              repositoryUpdated = true;
            } else {
              console.log("Repository is up to date, no need to clone.");
            }
          } else {
            // Not a valid git repository despite having .git folder
            console.log(
              "Directory has .git folder but is not a valid git repository, deleting and cloning...",
            );
            fs.rmSync(DIRECTORY_PATH, { recursive: true, force: true });
            fs.mkdirSync(DIRECTORY_PATH, { recursive: true });
            await cloneWithFallback(REPOSITORY_URL, DIRECTORY_PATH, {
              timeout: 60000,
            }); // 60 seconds timeout
            repositoryUpdated = true;
          }
        } else {
          // Directory exists but is not a git repository, delete and clone
          console.log(
            "Directory is not a git repository, deleting and cloning...",
          );
          fs.rmSync(DIRECTORY_PATH, { recursive: true, force: true });
          fs.mkdirSync(DIRECTORY_PATH, { recursive: true });
          await cloneWithFallback(REPOSITORY_URL, DIRECTORY_PATH, {
            timeout: 60000,
          }); // 60 seconds timeout
          repositoryUpdated = true;
        }
      }
    } else {
      // Directory does not exist, create it and clone the repository
      console.log(
        "Directory does not exist, creating and cloning repository...",
      );
      fs.mkdirSync(DIRECTORY_PATH, { recursive: true });
      await cloneWithFallback(REPOSITORY_URL, DIRECTORY_PATH, {
        timeout: 60000,
      }); // 60 seconds timeout
      repositoryUpdated = true;
    }

    // After cloning or updating the repository, copy and run convert-tables.js
    // and then run the init.sh script
    if (repositoryUpdated) {
      await runInitScript(DIRECTORY_PATH);
    }
    await copyAndRunConvertTables(DIRECTORY_PATH);
  } catch (error) {
    if (error instanceof Error) {
      throw new Error("Failed to setup UDS repository: " + error.message);
    } else {
      throw new Error("Failed to setup UDS repository: " + String(error));
    }
  }
};

export default setup;
