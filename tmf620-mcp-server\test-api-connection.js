#!/usr/bin/env node
/**
 * TMF620 API Connection Test Script
 * 
 * This script tests the API connection to the TMF620 API using the updated settings.
 * It verifies that the OAuth authentication is working and that the API is accessible.
 */

// Load environment variables from .env file
const path = require('path');
const fs = require('fs');
const axios = require('axios');
const dotenvResult = require('dotenv').config({ path: path.resolve(__dirname, '.env') });
if (dotenvResult.error) {
  console.error(`Error loading .env file: ${dotenvResult.error.message}`);
  process.exit(1);
}
console.log('Environment variables loaded from .env file');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

console.log(`${colors.blue}TMF620 API Connection Test Script${colors.reset}`);
console.log(`${colors.blue}===============================${colors.reset}`);

// Check if the required environment variables are set
const requiredEnvVars = [
  'DEV_API_URL',
  'OAUTH_TOKEN_URL',
  'OAUTH_SCOPE',
  'OAUTH_CLIENT_ID',
  'OAUTH_CLIENT_SECRET'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missingEnvVars.length > 0) {
  console.error(`${colors.red}Error: Missing required environment variables: ${missingEnvVars.join(', ')}${colors.reset}`);
  console.error(`${colors.red}Please set these variables in the .env file or environment.${colors.reset}`);
  process.exit(1);
}

// Log the environment variables (redacting sensitive information)
console.log(`${colors.blue}Environment Variables:${colors.reset}`);
console.log(`${colors.blue}DEV_API_URL: ${process.env.DEV_API_URL}${colors.reset}`);
console.log(`${colors.blue}OAUTH_TOKEN_URL: ${process.env.OAUTH_TOKEN_URL}${colors.reset}`);
console.log(`${colors.blue}OAUTH_SCOPE: ${process.env.OAUTH_SCOPE}${colors.reset}`);
console.log(`${colors.blue}OAUTH_CLIENT_ID: ${process.env.OAUTH_CLIENT_ID ? '[REDACTED]' : 'Not set'}${colors.reset}`);
console.log(`${colors.blue}OAUTH_CLIENT_SECRET: ${process.env.OAUTH_CLIENT_SECRET ? '[REDACTED]' : 'Not set'}${colors.reset}`);
console.log(`${colors.blue}HTTP_PROXY: ${process.env.HTTP_PROXY || 'Not set'}${colors.reset}`);
console.log(`${colors.blue}HTTPS_PROXY: ${process.env.HTTPS_PROXY || 'Not set'}${colors.reset}`);
console.log(`${colors.blue}NO_PROXY: ${process.env.NO_PROXY || 'Not set'}${colors.reset}`);

// Function to get an OAuth token
async function getOAuthToken() {
  console.log(`\n${colors.blue}Getting OAuth token...${colors.reset}`);
  
  const tokenUrl = process.env.OAUTH_TOKEN_URL;
  const clientId = process.env.OAUTH_CLIENT_ID;
  const clientSecret = process.env.OAUTH_CLIENT_SECRET;
  const scope = process.env.OAUTH_SCOPE;
  
  const params = new URLSearchParams();
  params.append('grant_type', 'client_credentials');
  params.append('scope', scope);
  
  try {
    console.log(`${colors.blue}Requesting token from ${tokenUrl}${colors.reset}`);
    
    const response = await axios.post(tokenUrl, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`
      },
      proxy: false
    });
    
    console.log(`${colors.green}Successfully obtained OAuth token${colors.reset}`);
    return response.data.access_token;
  } catch (error) {
    console.error(`${colors.red}Error getting OAuth token:${colors.reset}`);
    if (error.response) {
      console.error(`${colors.red}Status: ${error.response.status}${colors.reset}`);
      console.error(`${colors.red}Data: ${JSON.stringify(error.response.data, null, 2)}${colors.reset}`);
    } else if (error.request) {
      console.error(`${colors.red}No response received from server${colors.reset}`);
      console.error(`${colors.red}Request: ${JSON.stringify(error.request, null, 2)}${colors.reset}`);
    } else {
      console.error(`${colors.red}Error: ${error.message}${colors.reset}`);
    }
    throw error;
  }
}

// Function to test the API connection
async function testApiConnection(token) {
  console.log(`\n${colors.blue}Testing API connection...${colors.reset}`);
  
  // Use the TEST_QUOTE_ID to get a specific product offering
  const quoteId = process.env.TEST_QUOTE_ID;
  const apiUrl = `${process.env.DEV_API_URL}/${quoteId}`;
  
  try {
    console.log(`${colors.blue}Requesting data from ${apiUrl}${colors.reset}`);
    console.log(`${colors.blue}Using TEST_QUOTE_ID: ${quoteId}${colors.reset}`);
    
    const response = await axios.get(apiUrl, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
        'env': 'itn05'  // Add the environment parameter
      },
      proxy: false
    });
    
    console.log(`${colors.green}Successfully connected to the API${colors.reset}`);
    console.log(`${colors.green}Status: ${response.status}${colors.reset}`);
    console.log(`${colors.green}Data: ${JSON.stringify(response.data, null, 2).substring(0, 500)}...${colors.reset}`);
    return response.data;
  } catch (error) {
    console.error(`${colors.red}Error connecting to the API:${colors.reset}`);
    if (error.response) {
      console.error(`${colors.red}Status: ${error.response.status}${colors.reset}`);
      console.error(`${colors.red}Data: ${JSON.stringify(error.response.data, null, 2)}${colors.reset}`);
    } else if (error.request) {
      console.error(`${colors.red}No response received from server${colors.reset}`);
      console.error(`${colors.red}Request: ${JSON.stringify(error.request, null, 2)}${colors.reset}`);
    } else {
      console.error(`${colors.red}Error: ${error.message}${colors.reset}`);
    }
    throw error;
  }
}

// Main function to run the tests
async function main() {
  try {
    // Get an OAuth token
    const token = await getOAuthToken();
    
    // Test the API connection
    const data = await testApiConnection(token);
    
    console.log(`\n${colors.green}API connection test successful!${colors.reset}`);
    
    // Save the test results to a file
    const testResultsPath = path.join(__dirname, 'api-connection-test-results.json');
    fs.writeFileSync(testResultsPath, JSON.stringify(data, null, 2));
    console.log(`${colors.green}Test results saved to ${testResultsPath}${colors.reset}`);
    
    process.exit(0);
  } catch (error) {
    console.error(`\n${colors.red}API connection test failed!${colors.reset}`);
    process.exit(1);
  }
}

// Run the main function
main();
