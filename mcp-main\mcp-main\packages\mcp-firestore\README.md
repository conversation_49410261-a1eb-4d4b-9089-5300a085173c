# @telus/mcp-firestore

Handle Firestore database interaction

## Installation

```bash
pnpm add @telus/mcp-firestore
```

## Usage

This MCP server can be used with the Model Context Protocol (MCP) to provide additional capabilities to AI models.

To start the server:

```bash
pnpm start
```

## Environment Variables

The following environment variables are used:

- `GOOGLE_APPLICATION_CREDENTIALS`: (Required) Path to the Google Cloud service account key file (JSON) with Firestore permissions.
- `GCP_PROJECT_ID`: (Required) The Google Cloud Project ID that hosts your Firestore database.
- `DEFAULT_DATABASE_ID`: (Optional) Default Firestore database ID to use when not specified in the tool arguments.
- `DEFAULT_COLLECTION_ID`: (Optional) Default Firestore collection ID to use when not specified in the tool arguments.

## MCP Configuration

Add the following configuration to your MCP settings file:

```json
{
  "mcpServers": {
    "firestore": {
      "command": "npx",
      "args": ["@telus/mcp-firestore"],
      "env": {
        "GOOGLE_APPLICATION_CREDENTIALS": "/path/to/your/service-account-key.json",
        "GCP_PROJECT_ID": "your-gcp-project-id",
        "DEFAULT_DATABASE_ID": "your-default-database-id",
        "DEFAULT_COLLECTION_ID": "your-default-collection-id"
      },
      "disabled": false,
      "autoApprove": []
    }
  }
}
```

Replace `/path/to/mcp-firestore/dist/index.js` with the actual path to the compiled index.js file, `/path/to/your/service-account-key.json` with the path to your Google Cloud service account key file, `your-gcp-project-id` with your Google Cloud Project ID, and set appropriate values for `DEFAULT_DATABASE_ID` and `DEFAULT_COLLECTION_ID` if you want to use default values.

## Usage

When using the Firestore MCP tools, you can now omit the `databaseId` and `collectionId` parameters if you've set the `DEFAULT_DATABASE_ID` and `DEFAULT_COLLECTION_ID` environment variables. The server will use these default values when the parameters are not provided in the tool arguments.

For example, to query a collection using default values:

```json
{
  "name": "query_collection",
  "arguments": {
    "limit": 10
  }
}
```

If you need to override the default values, you can still provide `databaseId` and `collectionId` in the tool arguments:

```json
{
  "name": "query_collection",
  "arguments": {
    "databaseId": "custom-database-id",
    "collectionId": "custom-collection-id",
    "limit": 10
  }
}
```

## Available Tools

The following tools are available for interacting with Firestore:

### query_collection
Query documents from a collection with optional filtering and limit.
```json
{
  "databaseId": "your-database-id",
  "collectionId": "your-collection-id",
  "whereClause": {
    "field": "fieldName",
    "operator": "==",
    "value": "value"
  },
  "limit": 20
}
```
Supported operators: `==`, `!=`, `>`, `>=`, `<`, `<=`, `array-contains`, `array-contains-any`, `in`, `not-in`

### create_collection
Create a new collection.
```json
{
  "databaseId": "your-database-id",
  "collectionId": "your-collection-id"
}
```

### add_document
Add a new document to a collection.
```json
{
  "databaseId": "your-database-id",
  "collectionId": "your-collection-id",
  "documentData": {
    "field1": "value1",
    "field2": "value2"
  }
}
```

### get_document
Get a specific document by ID.
```json
{
  "databaseId": "your-database-id",
  "collectionId": "your-collection-id",
  "documentId": "your-document-id"
}
```

### update_document
Update an existing document.
```json
{
  "databaseId": "your-database-id",
  "collectionId": "your-collection-id",
  "documentId": "your-document-id",
  "documentData": {
    "field1": "new-value1",
    "field2": "new-value2"
  }
}
```

### delete_document
Delete a document.
```json
{
  "databaseId": "your-database-id",
  "collectionId": "your-collection-id",
  "documentId": "your-document-id"
}
```

## Development

To build the project:

```bash
pnpm build
```

To run tests:

```bash
pnpm test
```

## License

MIT © TELUS
