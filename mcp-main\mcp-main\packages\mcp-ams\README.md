# @telus/mcp-ams

TELUS Address Management Service (AMS) MCP server for address search and validation.

## Overview

This MCP server provides integration with the TELUS Address Management Service (AMS) API, allowing AI models to search for and validate addresses using the "smart match" functionality. It supports both Production and Non-Production environments.

## Features

- Address search using AMS "smart match" functionality
- Support for both PROD and NON-PROD environments
- OAuth 2.0 authentication with client credentials flow
- Comprehensive error handling and logging

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-ams
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for <PERSON>):

   ```json
   {
     "mcpServers": {
       "ams": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-ams"],
         "env": {
           "CLIENT_ID": "your_client_id_here",
           "CLIENT_SECRET_PROD": "your_prod_client_secret_here",
           "CLIENT_SECRET_NONPROD": "your_nonprod_client_secret_here",
           "ENVIRONMENT": "NON_PROD",
           "SCOPE": "15"
         },
         "disabled": false,
         "autoApprove": []
       }
     }
   }
   ```

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp
   pnpm install
   ```

2. Create a file named `.env` in the `packages/mcp-ams` directory and add your configuration:

   ```sh
   CLIENT_ID=your_client_id_here
   CLIENT_SECRET_PROD=your_prod_client_secret_here
   CLIENT_SECRET_NONPROD=your_nonprod_client_secret_here
   ENVIRONMENT=NON_PROD
   SCOPE=15
   ```

3. Build the server:

   ```bash
   pnpm build
   ```

4. Start the server:

   ```bash
   pnpm start
   ```

## Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| CLIENT_ID | OAuth client ID | Yes | - |
| CLIENT_SECRET_PROD | OAuth client secret for PROD environment | Required for PROD | - |
| CLIENT_SECRET_NONPROD | OAuth client secret for NON_PROD environment | Required for NON_PROD | - |
| ENVIRONMENT | API environment (PROD or NON_PROD) | No | NON_PROD |
| SCOPE | OAuth scope | No | 15 |

## Available Tools

### ams_smart_search_address

Searches for an address using TELUS AMS smart match functionality.

**Input Schema:**

```typescript
{
  address: string,      // Formatted address (e.g., "2457 BROADMOOR BLVD, SHERWOOD PARK AB")
  environment?: string  // Optional environment override ("PROD" or "NON_PROD")
}
```

**Example:**

```json
{
  "address": "2457 BROADMOOR BLVD, SHERWOOD PARK AB"
}
```

**Response:**

The tool returns an array of matching addresses with details such as:
- Formatted address
- Street number
- Street name
- City
- Province
- Postal code
- Confidence score
We are filtering to keep only some important fields

## Error Handling

Common error messages and their solutions:

- "CLIENT_ID environment variable is required": Add this to your .env file or MCP settings
- "CLIENT_SECRET_PROD environment variable is required for PROD environment": Add this when using PROD environment
- "CLIENT_SECRET_NONPROD environment variable is required for NON_PROD environment": Add this when using NON_PROD environment
- "OAuth authentication failed": Verify your client ID and secrets are correct
- "AMS API rate limit exceeded": Wait before making more requests
- "AMS API request failed": Check the address format or API availability

## Development

To build the project:

```bash
pnpm build
```

## License

MIT © TELUS
