
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/07-reference/">
      
      
        <link rel="prev" href="../06-deployment-guide/">
      
      
        <link rel="next" href="../DOCUMENTATION_SUMMARY/">
      
      
      <link rel="icon" href="../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Reference - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#reference-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Reference
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href=".." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#api-reference" class="md-nav__link">
    <span class="md-ellipsis">
      API Reference
    </span>
  </a>
  
    <nav class="md-nav" aria-label="API Reference">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-management-apis" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Management APIs
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Management APIs">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#create-quote" class="md-nav__link">
    <span class="md-ellipsis">
      Create Quote
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#get-quote" class="md-nav__link">
    <span class="md-ellipsis">
      Get Quote
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#update-quote" class="md-nav__link">
    <span class="md-ellipsis">
      Update Quote
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#submit-quote" class="md-nav__link">
    <span class="md-ellipsis">
      Submit Quote
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#order-management-apis" class="md-nav__link">
    <span class="md-ellipsis">
      Order Management APIs
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Order Management APIs">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#create-order-from-quote" class="md-nav__link">
    <span class="md-ellipsis">
      Create Order from Quote
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#get-order-status" class="md-nav__link">
    <span class="md-ellipsis">
      Get Order Status
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#location-services-apis" class="md-nav__link">
    <span class="md-ellipsis">
      Location Services APIs
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Location Services APIs">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#validate-address" class="md-nav__link">
    <span class="md-ellipsis">
      Validate Address
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bulk-address-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Address Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-parameters" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Parameters
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Parameters">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Frontend Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#environment-variables" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Variables
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#angular-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Angular Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Backend Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#application-properties" class="md-nav__link">
    <span class="md-ellipsis">
      Application Properties
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Extensions Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#qes-plugin-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      QES Plugin Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#component-inventory" class="md-nav__link">
    <span class="md-ellipsis">
      Component Inventory
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Component Inventory">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-components" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Components
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-services" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Services
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-plugins" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Plugins
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-codes" class="md-nav__link">
    <span class="md-ellipsis">
      Error Codes
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Codes">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#http-status-codes" class="md-nav__link">
    <span class="md-ellipsis">
      HTTP Status Codes
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#application-error-codes" class="md-nav__link">
    <span class="md-ellipsis">
      Application Error Codes
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Metrics
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Metrics">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Metrics
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Metrics
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Metrics
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#external-resources" class="md-nav__link">
    <span class="md-ellipsis">
      External Resources
    </span>
  </a>
  
    <nav class="md-nav" aria-label="External Resources">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#documentation-links" class="md-nav__link">
    <span class="md-ellipsis">
      Documentation Links
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#telus-internal-resources" class="md-nav__link">
    <span class="md-ellipsis">
      TELUS Internal Resources
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Development Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#api-reference" class="md-nav__link">
    <span class="md-ellipsis">
      API Reference
    </span>
  </a>
  
    <nav class="md-nav" aria-label="API Reference">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-management-apis" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Management APIs
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Management APIs">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#create-quote" class="md-nav__link">
    <span class="md-ellipsis">
      Create Quote
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#get-quote" class="md-nav__link">
    <span class="md-ellipsis">
      Get Quote
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#update-quote" class="md-nav__link">
    <span class="md-ellipsis">
      Update Quote
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#submit-quote" class="md-nav__link">
    <span class="md-ellipsis">
      Submit Quote
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#order-management-apis" class="md-nav__link">
    <span class="md-ellipsis">
      Order Management APIs
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Order Management APIs">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#create-order-from-quote" class="md-nav__link">
    <span class="md-ellipsis">
      Create Order from Quote
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#get-order-status" class="md-nav__link">
    <span class="md-ellipsis">
      Get Order Status
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#location-services-apis" class="md-nav__link">
    <span class="md-ellipsis">
      Location Services APIs
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Location Services APIs">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#validate-address" class="md-nav__link">
    <span class="md-ellipsis">
      Validate Address
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bulk-address-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Address Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-parameters" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Parameters
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Parameters">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Frontend Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#environment-variables" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Variables
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#angular-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Angular Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Backend Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#application-properties" class="md-nav__link">
    <span class="md-ellipsis">
      Application Properties
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Extensions Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#qes-plugin-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      QES Plugin Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#component-inventory" class="md-nav__link">
    <span class="md-ellipsis">
      Component Inventory
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Component Inventory">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-components" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Components
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-services" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Services
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-plugins" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Plugins
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-codes" class="md-nav__link">
    <span class="md-ellipsis">
      Error Codes
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Codes">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#http-status-codes" class="md-nav__link">
    <span class="md-ellipsis">
      HTTP Status Codes
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#application-error-codes" class="md-nav__link">
    <span class="md-ellipsis">
      Application Error Codes
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Metrics
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Metrics">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Metrics
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Metrics
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Metrics
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#external-resources" class="md-nav__link">
    <span class="md-ellipsis">
      External Resources
    </span>
  </a>
  
    <nav class="md-nav" aria-label="External Resources">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#documentation-links" class="md-nav__link">
    <span class="md-ellipsis">
      Documentation Links
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#telus-internal-resources" class="md-nav__link">
    <span class="md-ellipsis">
      TELUS Internal Resources
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Development Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="reference-guide">Reference Guide<a class="headerlink" href="#reference-guide" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#api-reference">API Reference</a></li>
<li><a href="#configuration-parameters">Configuration Parameters</a></li>
<li><a href="#component-inventory">Component Inventory</a></li>
<li><a href="#error-codes">Error Codes</a></li>
<li><a href="#performance-metrics">Performance Metrics</a></li>
<li><a href="#external-resources">External Resources</a></li>
</ul>
<h2 id="api-reference">API Reference<a class="headerlink" href="#api-reference" title="Permanent link">&para;</a></h2>
<h3 id="quote-management-apis">Quote Management APIs<a class="headerlink" href="#quote-management-apis" title="Permanent link">&para;</a></h3>
<h4 id="create-quote">Create Quote<a class="headerlink" href="#create-quote" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="err">POST /api/v1/quotes</span>
<span class="err">Content-Type: application/json</span>
<span class="err">Authorization: Bearer {token}</span>

<span class="err">{</span>
<span class="err">  &quot;customerId&quot;: &quot;string&quot;,</span>
<span class="err">  &quot;customerType&quot;: &quot;ENTERPRISE|GOVERNMENT|STANDARD&quot;,</span>
<span class="err">  &quot;items&quot;: [</span>
<span class="err">    {</span>
<span class="err">      &quot;productId&quot;: &quot;string&quot;,</span>
<span class="err">      &quot;quantity&quot;: 1,</span>
<span class="err">      &quot;parameters&quot;: [</span>
<span class="err">        {</span>
<span class="err">          &quot;name&quot;: &quot;string&quot;,</span>
<span class="err">          &quot;value&quot;: &quot;string&quot;</span>
<span class="err">        }</span>
<span class="err">      ]</span>
<span class="err">    }</span>
<span class="err">  ],</span>
<span class="err">  &quot;location&quot;: {</span>
<span class="err">    &quot;address&quot;: &quot;string&quot;,</span>
<span class="err">    &quot;city&quot;: &quot;string&quot;,</span>
<span class="err">    &quot;province&quot;: &quot;string&quot;,</span>
<span class="err">    &quot;postalCode&quot;: &quot;string&quot;</span>
<span class="err">  }</span>
<span class="err">}</span>
</code></pre></div>
<p><strong>Response:</strong>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;quoteId&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;customerId&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;DRAFT|SUBMITTED|APPROVED|EXPIRED|CANCELLED&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;items&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;itemId&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;productId&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;quantity&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;unitPrice&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">100.00</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;totalPrice&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">100.00</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;attributes&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;pricing&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;subtotal&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">100.00</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;taxes&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">13.00</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;discounts&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.00</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;total&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">113.00</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;currency&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CAD&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;createdDate&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-01T00:00:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;expirationDate&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-02-01T00:00:00Z&quot;</span>
<span class="p">}</span>
</code></pre></div></p>
<h4 id="get-quote">Get Quote<a class="headerlink" href="#get-quote" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="err">GET /api/v1/quotes/{quoteId}</span>
<span class="err">Authorization: Bearer {token}</span>
</code></pre></div>
<h4 id="update-quote">Update Quote<a class="headerlink" href="#update-quote" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="err">PUT /api/v1/quotes/{quoteId}</span>
<span class="err">Content-Type: application/json</span>
<span class="err">Authorization: Bearer {token}</span>

<span class="err">{</span>
<span class="err">  &quot;items&quot;: [...],</span>
<span class="err">  &quot;location&quot;: {...}</span>
<span class="err">}</span>
</code></pre></div>
<h4 id="submit-quote">Submit Quote<a class="headerlink" href="#submit-quote" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="err">POST /api/v1/quotes/{quoteId}/submit</span>
<span class="err">Authorization: Bearer {token}</span>
</code></pre></div>
<h3 id="order-management-apis">Order Management APIs<a class="headerlink" href="#order-management-apis" title="Permanent link">&para;</a></h3>
<h4 id="create-order-from-quote">Create Order from Quote<a class="headerlink" href="#create-order-from-quote" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="err">POST /api/v1/orders</span>
<span class="err">Content-Type: application/json</span>
<span class="err">Authorization: Bearer {token}</span>

<span class="err">{</span>
<span class="err">  &quot;quoteId&quot;: &quot;string&quot;,</span>
<span class="err">  &quot;customerPurchaseOrder&quot;: &quot;string&quot;,</span>
<span class="err">  &quot;requestedDeliveryDate&quot;: &quot;2024-01-15T00:00:00Z&quot;</span>
<span class="err">}</span>
</code></pre></div>
<h4 id="get-order-status">Get Order Status<a class="headerlink" href="#get-order-status" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="err">GET /api/v1/orders/{orderId}</span>
<span class="err">Authorization: Bearer {token}</span>
</code></pre></div>
<h3 id="location-services-apis">Location Services APIs<a class="headerlink" href="#location-services-apis" title="Permanent link">&para;</a></h3>
<h4 id="validate-address">Validate Address<a class="headerlink" href="#validate-address" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="err">POST /api/v1/locations/validate</span>
<span class="err">Content-Type: application/json</span>
<span class="err">Authorization: Bearer {token}</span>

<span class="err">{</span>
<span class="err">  &quot;address&quot;: &quot;string&quot;,</span>
<span class="err">  &quot;city&quot;: &quot;string&quot;,</span>
<span class="err">  &quot;province&quot;: &quot;string&quot;,</span>
<span class="err">  &quot;postalCode&quot;: &quot;string&quot;</span>
<span class="err">}</span>
</code></pre></div>
<p><strong>Response:</strong>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;isValid&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;formattedAddress&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;latitude&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">43.6532</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;longitude&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">-79.3832</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;serviceAvailable&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;availableServices&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;serviceType&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;INTERNET&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;maxBandwidth&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;1000 Mbps&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</code></pre></div></p>
<h4 id="bulk-address-validation">Bulk Address Validation<a class="headerlink" href="#bulk-address-validation" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="err">POST /api/v1/locations/validate-bulk</span>
<span class="err">Content-Type: application/json</span>
<span class="err">Authorization: Bearer {token}</span>

<span class="err">{</span>
<span class="err">  &quot;addresses&quot;: [</span>
<span class="err">    {</span>
<span class="err">      &quot;id&quot;: &quot;string&quot;,</span>
<span class="err">      &quot;address&quot;: &quot;string&quot;,</span>
<span class="err">      &quot;city&quot;: &quot;string&quot;,</span>
<span class="err">      &quot;province&quot;: &quot;string&quot;,</span>
<span class="err">      &quot;postalCode&quot;: &quot;string&quot;</span>
<span class="err">    }</span>
<span class="err">  ]</span>
<span class="err">}</span>
</code></pre></div>
<h2 id="configuration-parameters">Configuration Parameters<a class="headerlink" href="#configuration-parameters" title="Permanent link">&para;</a></h2>
<h3 id="frontend-configuration">Frontend Configuration<a class="headerlink" href="#frontend-configuration" title="Permanent link">&para;</a></h3>
<h4 id="environment-variables">Environment Variables<a class="headerlink" href="#environment-variables" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="kd">interface</span><span class="w"> </span><span class="nx">EnvironmentConfig</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">production</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">apiBaseUrl</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">googleMapsApiKey</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">authConfig</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">clientId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">    </span><span class="nx">authority</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">    </span><span class="nx">redirectUri</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="p">};</span>
<span class="w">  </span><span class="nx">features</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">enableBulkOperations</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">    </span><span class="nx">enableRealTimeUpdates</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">    </span><span class="nx">enableAdvancedPricing</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="p">};</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="angular-configuration">Angular Configuration<a class="headerlink" href="#angular-configuration" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;projects&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;nc-cloud-bss-oc-ui-frontend-b2b&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;architect&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;build&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;configurations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;production&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;budgets&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">                </span><span class="p">{</span>
<span class="w">                  </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;initial&quot;</span><span class="p">,</span>
<span class="w">                  </span><span class="nt">&quot;maximumWarning&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2mb&quot;</span><span class="p">,</span>
<span class="w">                  </span><span class="nt">&quot;maximumError&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;5mb&quot;</span>
<span class="w">                </span><span class="p">}</span>
<span class="w">              </span><span class="p">],</span>
<span class="w">              </span><span class="nt">&quot;outputHashing&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;all&quot;</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="nt">&quot;development&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;buildOptimizer&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;optimization&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;vendorChunk&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;extractLicenses&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;sourceMap&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;namedChunks&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">          </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="backend-configuration">Backend Configuration<a class="headerlink" href="#backend-configuration" title="Permanent link">&para;</a></h3>
<h4 id="application-properties">Application Properties<a class="headerlink" href="#application-properties" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># application.yml</span>
<span class="nt">server</span><span class="p">:</span>
<span class="w">  </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8080</span>
<span class="w">  </span><span class="nt">servlet</span><span class="p">:</span>
<span class="w">    </span><span class="nt">context-path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/api/v1</span>

<span class="nt">spring</span><span class="p">:</span>
<span class="w">  </span><span class="nt">application</span><span class="p">:</span>
<span class="w">    </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-order-capture-backend</span>
<span class="w">  </span><span class="nt">profiles</span><span class="p">:</span>
<span class="w">    </span><span class="nt">active</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${SPRING_PROFILES_ACTIVE:development}</span>

<span class="w">  </span><span class="nt">datasource</span><span class="p">:</span>
<span class="w">    </span><span class="nt">url</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${DATABASE_URL:******************************************}</span>
<span class="w">    </span><span class="nt">username</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${DATABASE_USERNAME:telus_user}</span>
<span class="w">    </span><span class="nt">password</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${DATABASE_PASSWORD:telus_password}</span>
<span class="w">    </span><span class="nt">driver-class-name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">org.postgresql.Driver</span>

<span class="w">  </span><span class="nt">jpa</span><span class="p">:</span>
<span class="w">    </span><span class="nt">hibernate</span><span class="p">:</span>
<span class="w">      </span><span class="nt">ddl-auto</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">validate</span>
<span class="w">    </span><span class="nt">show-sql</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>
<span class="w">    </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">      </span><span class="nt">hibernate</span><span class="p">:</span>
<span class="w">        </span><span class="nt">dialect</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">org.hibernate.dialect.PostgreSQLDialect</span>
<span class="w">        </span><span class="nt">format_sql</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">  </span><span class="nt">cache</span><span class="p">:</span>
<span class="w">    </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">redis</span>
<span class="w">    </span><span class="nt">redis</span><span class="p">:</span>
<span class="w">      </span><span class="nt">host</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${REDIS_HOST:localhost}</span>
<span class="w">      </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${REDIS_PORT:6379}</span>
<span class="w">      </span><span class="nt">password</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${REDIS_PASSWORD:}</span>

<span class="nt">telus</span><span class="p">:</span>
<span class="w">  </span><span class="nt">order-capture</span><span class="p">:</span>
<span class="w">    </span><span class="nt">base-url</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${OCP_BASE_URL:https://order-capture.telus.com/api/v2}</span>
<span class="w">    </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${OCP_TIMEOUT:30s}</span>
<span class="w">    </span><span class="nt">authentication</span><span class="p">:</span>
<span class="w">      </span><span class="nt">client-id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${OCP_CLIENT_ID}</span>
<span class="w">      </span><span class="nt">client-secret</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${OCP_CLIENT_SECRET}</span>
<span class="w">      </span><span class="nt">token-url</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${OCP_TOKEN_URL:https://auth.telus.com/oauth/token}</span>

<span class="w">  </span><span class="nt">geocoding</span><span class="p">:</span>
<span class="w">    </span><span class="nt">provider</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">google</span>
<span class="w">    </span><span class="nt">api-key</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${GEOCODING_API_KEY}</span>
<span class="w">    </span><span class="nt">cache-ttl</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">24h</span>

<span class="w">  </span><span class="nt">bulk-operations</span><span class="p">:</span>
<span class="w">    </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${BULK_OPERATIONS_ENABLED:true}</span>
<span class="w">    </span><span class="nt">base-url</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${BULK_OPERATIONS_URL:http://bulk-operations-service:8080}</span>
<span class="w">    </span><span class="nt">max-batch-size</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>

<span class="nt">logging</span><span class="p">:</span>
<span class="w">  </span><span class="nt">level</span><span class="p">:</span>
<span class="w">    </span><span class="nt">com.netcracker.solutions.telus</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${LOG_LEVEL:INFO}</span>
<span class="w">    </span><span class="nt">org.springframework.web</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">WARN</span>
<span class="w">    </span><span class="nt">org.hibernate.SQL</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">WARN</span>
<span class="w">  </span><span class="nt">pattern</span><span class="p">:</span>
<span class="w">    </span><span class="nt">console</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;%d{yyyy-MM-dd</span><span class="nv"> </span><span class="s">HH:mm:ss}</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">%msg%n&quot;</span>
<span class="w">    </span><span class="nt">file</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;%d{yyyy-MM-dd</span><span class="nv"> </span><span class="s">HH:mm:ss}</span><span class="nv"> </span><span class="s">[%thread]</span><span class="nv"> </span><span class="s">%-5level</span><span class="nv"> </span><span class="s">%logger{36}</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">%msg%n&quot;</span>

<span class="nt">management</span><span class="p">:</span>
<span class="w">  </span><span class="nt">endpoints</span><span class="p">:</span>
<span class="w">    </span><span class="nt">web</span><span class="p">:</span>
<span class="w">      </span><span class="nt">exposure</span><span class="p">:</span>
<span class="w">        </span><span class="nt">include</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">health,info,metrics,prometheus</span>
<span class="w">  </span><span class="nt">endpoint</span><span class="p">:</span>
<span class="w">    </span><span class="nt">health</span><span class="p">:</span>
<span class="w">      </span><span class="nt">show-details</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">always</span>
<span class="w">  </span><span class="nt">metrics</span><span class="p">:</span>
<span class="w">    </span><span class="nt">export</span><span class="p">:</span>
<span class="w">      </span><span class="nt">prometheus</span><span class="p">:</span>
<span class="w">        </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</code></pre></div>
<h3 id="extensions-configuration">Extensions Configuration<a class="headerlink" href="#extensions-configuration" title="Permanent link">&para;</a></h3>
<h4 id="qes-plugin-configuration">QES Plugin Configuration<a class="headerlink" href="#qes-plugin-configuration" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># qes-plugin-config.yml</span>
<span class="nt">qes</span><span class="p">:</span>
<span class="w">  </span><span class="nt">plugins</span><span class="p">:</span>
<span class="w">    </span><span class="nt">telus-b2b</span><span class="p">:</span>
<span class="w">      </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.2.0&quot;</span>
<span class="w">      </span><span class="nt">configuration</span><span class="p">:</span>
<span class="w">        </span><span class="nt">bandwidth-validation</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">strict-mode</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">validators</span><span class="p">:</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;WAN_L2_CIR&quot;</span>
<span class="w">              </span><span class="nt">min-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">              </span><span class="nt">max-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10000</span>
<span class="w">              </span><span class="nt">unit</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Mbps&quot;</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;WAN_L2_EVC&quot;</span>
<span class="w">              </span><span class="nt">min-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">              </span><span class="nt">max-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
<span class="w">              </span><span class="nt">unit</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Mbps&quot;</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;WAN_L3_IP_QOS&quot;</span>
<span class="w">              </span><span class="nt">min-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">              </span><span class="nt">max-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
<span class="w">              </span><span class="nt">unit</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Mbps&quot;</span>

<span class="w">        </span><span class="nt">quote-modification</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">rules</span><span class="p">:</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;volume-discount&quot;</span>
<span class="w">              </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;totalPrice</span><span class="nv"> </span><span class="s">&gt;</span><span class="nv"> </span><span class="s">100000&quot;</span>
<span class="w">              </span><span class="nt">action</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;apply-discount&quot;</span>
<span class="w">              </span><span class="nt">parameters</span><span class="p">:</span>
<span class="w">                </span><span class="nt">discount-percentage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;platinum-customer-discount&quot;</span>
<span class="w">              </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;customerTier</span><span class="nv"> </span><span class="s">==</span><span class="nv"> </span><span class="s">&#39;PLATINUM&#39;&quot;</span>
<span class="w">              </span><span class="nt">action</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;apply-discount&quot;</span>
<span class="w">              </span><span class="nt">parameters</span><span class="p">:</span>
<span class="w">                </span><span class="nt">discount-percentage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>

<span class="w">        </span><span class="nt">expiration-policy</span><span class="p">:</span>
<span class="w">          </span><span class="nt">default-days</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30</span>
<span class="w">          </span><span class="nt">enterprise-days</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">60</span>
<span class="w">          </span><span class="nt">government-days</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">90</span>
<span class="w">          </span><span class="nt">max-extensions</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
</code></pre></div>
<h2 id="component-inventory">Component Inventory<a class="headerlink" href="#component-inventory" title="Permanent link">&para;</a></h2>
<h3 id="frontend-components">Frontend Components<a class="headerlink" href="#frontend-components" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Component Name</th>
<th>Type</th>
<th>Purpose</th>
<th>Dependencies</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>telus-start-quotation</code></td>
<td>Feature</td>
<td>Quote initiation</td>
<td>QuoteService, LocationService</td>
</tr>
<tr>
<td><code>telus-assign-location</code></td>
<td>Feature</td>
<td>Location assignment</td>
<td>GoogleMapsService, GeocodingService</td>
</tr>
<tr>
<td><code>telus-cart-total-prices</code></td>
<td>Display</td>
<td>Cart pricing</td>
<td>PricingService, NgRx Store</td>
</tr>
<tr>
<td><code>telus-quote-view</code></td>
<td>Feature</td>
<td>Quote display</td>
<td>QuoteService, NgRx Store</td>
</tr>
<tr>
<td><code>telus-quote-view-header</code></td>
<td>Display</td>
<td>Quote header</td>
<td>DatePipe, CurrencyPipe</td>
</tr>
<tr>
<td><code>telus-eligibility-parameters</code></td>
<td>Feature</td>
<td>Service eligibility</td>
<td>EligibilityService</td>
</tr>
<tr>
<td><code>telus-confirmed-order-screen</code></td>
<td>Display</td>
<td>Order confirmation</td>
<td>OrderService, NotificationService</td>
</tr>
<tr>
<td><code>telus-error-page</code></td>
<td>Display</td>
<td>Error handling</td>
<td>ErrorService, Router</td>
</tr>
</tbody>
</table>
<h3 id="backend-services">Backend Services<a class="headerlink" href="#backend-services" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Service Name</th>
<th>Type</th>
<th>Purpose</th>
<th>Dependencies</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>QuoteService</code></td>
<td>Business</td>
<td>Quote management</td>
<td>QuoteRepository, OrderCaptureClient</td>
</tr>
<tr>
<td><code>OrderService</code></td>
<td>Business</td>
<td>Order processing</td>
<td>OrderRepository, QuoteService</td>
</tr>
<tr>
<td><code>LocationService</code></td>
<td>Business</td>
<td>Location validation</td>
<td>GeocodingClient, ServiceAvailabilityService</td>
</tr>
<tr>
<td><code>PricingService</code></td>
<td>Business</td>
<td>Price calculation</td>
<td>PricingEngine, DiscountService</td>
</tr>
<tr>
<td><code>CustomerService</code></td>
<td>Business</td>
<td>Customer management</td>
<td>CustomerRepository, AuthService</td>
</tr>
<tr>
<td><code>NotificationService</code></td>
<td>Infrastructure</td>
<td>Event notifications</td>
<td>WebSocketService, EmailService</td>
</tr>
</tbody>
</table>
<h3 id="extension-plugins">Extension Plugins<a class="headerlink" href="#extension-plugins" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Plugin Name</th>
<th>Type</th>
<th>Purpose</th>
<th>Interfaces</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>TelusQuoteDeltaModificatorImpl</code></td>
<td>Business</td>
<td>Quote modifications</td>
<td>QuoteDeltaModificator</td>
</tr>
<tr>
<td><code>TelusQuoteExpirationPeriodProvider</code></td>
<td>Business</td>
<td>Quote lifecycle</td>
<td>QuoteExpirationPeriodProvider</td>
</tr>
<tr>
<td><code>TelusStateMachineExtensionValidator</code></td>
<td>Validation</td>
<td>State transitions</td>
<td>StateMachineExtensionValidator</td>
</tr>
<tr>
<td><code>TelusWanL2CirBandwidthValidator</code></td>
<td>Validation</td>
<td>L2 CIR validation</td>
<td>BandwidthValidator</td>
</tr>
<tr>
<td><code>TelusWanL2EvcBandwidthValidator</code></td>
<td>Validation</td>
<td>L2 EVC validation</td>
<td>BandwidthValidator</td>
</tr>
<tr>
<td><code>TelusWanL3IpQosBandwidthValidator</code></td>
<td>Validation</td>
<td>L3 QoS validation</td>
<td>BandwidthValidator</td>
</tr>
</tbody>
</table>
<h2 id="error-codes">Error Codes<a class="headerlink" href="#error-codes" title="Permanent link">&para;</a></h2>
<h3 id="http-status-codes">HTTP Status Codes<a class="headerlink" href="#http-status-codes" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Code</th>
<th>Status</th>
<th>Description</th>
<th>Resolution</th>
</tr>
</thead>
<tbody>
<tr>
<td>400</td>
<td>Bad Request</td>
<td>Invalid request format or parameters</td>
<td>Check request body and parameters</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized</td>
<td>Missing or invalid authentication token</td>
<td>Refresh authentication token</td>
</tr>
<tr>
<td>403</td>
<td>Forbidden</td>
<td>Insufficient permissions</td>
<td>Contact administrator for access</td>
</tr>
<tr>
<td>404</td>
<td>Not Found</td>
<td>Resource not found</td>
<td>Verify resource ID exists</td>
</tr>
<tr>
<td>409</td>
<td>Conflict</td>
<td>Resource state conflict</td>
<td>Check resource state and retry</td>
</tr>
<tr>
<td>422</td>
<td>Unprocessable Entity</td>
<td>Business validation failed</td>
<td>Review validation errors</td>
</tr>
<tr>
<td>429</td>
<td>Too Many Requests</td>
<td>Rate limit exceeded</td>
<td>Implement exponential backoff</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error</td>
<td>Server-side error</td>
<td>Check logs and contact support</td>
</tr>
<tr>
<td>502</td>
<td>Bad Gateway</td>
<td>External service unavailable</td>
<td>Check external service status</td>
</tr>
<tr>
<td>503</td>
<td>Service Unavailable</td>
<td>Service temporarily unavailable</td>
<td>Retry after delay</td>
</tr>
</tbody>
</table>
<h3 id="application-error-codes">Application Error Codes<a class="headerlink" href="#application-error-codes" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Code</th>
<th>Category</th>
<th>Description</th>
<th>Example</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>QUOTE_001</code></td>
<td>Validation</td>
<td>Quote validation failed</td>
<td>Invalid customer ID</td>
</tr>
<tr>
<td><code>QUOTE_002</code></td>
<td>Business</td>
<td>Quote expired</td>
<td>Quote past expiration date</td>
</tr>
<tr>
<td><code>QUOTE_003</code></td>
<td>State</td>
<td>Invalid state transition</td>
<td>Cannot approve cancelled quote</td>
</tr>
<tr>
<td><code>ORDER_001</code></td>
<td>Validation</td>
<td>Order validation failed</td>
<td>Missing required fields</td>
</tr>
<tr>
<td><code>ORDER_002</code></td>
<td>Business</td>
<td>Order processing failed</td>
<td>Insufficient inventory</td>
</tr>
<tr>
<td><code>LOC_001</code></td>
<td>Validation</td>
<td>Address validation failed</td>
<td>Invalid postal code format</td>
</tr>
<tr>
<td><code>LOC_002</code></td>
<td>Service</td>
<td>Service not available</td>
<td>No service at location</td>
</tr>
<tr>
<td><code>BW_001</code></td>
<td>Validation</td>
<td>Bandwidth validation failed</td>
<td>Bandwidth exceeds limits</td>
</tr>
<tr>
<td><code>BW_002</code></td>
<td>Configuration</td>
<td>Invalid bandwidth configuration</td>
<td>EIR less than CIR</td>
</tr>
</tbody>
</table>
<h2 id="performance-metrics">Performance Metrics<a class="headerlink" href="#performance-metrics" title="Permanent link">&para;</a></h2>
<h3 id="frontend-metrics">Frontend Metrics<a class="headerlink" href="#frontend-metrics" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Metric</th>
<th>Target</th>
<th>Measurement</th>
</tr>
</thead>
<tbody>
<tr>
<td>First Contentful Paint</td>
<td>&lt; 1.5s</td>
<td>Lighthouse</td>
</tr>
<tr>
<td>Largest Contentful Paint</td>
<td>&lt; 2.5s</td>
<td>Lighthouse</td>
</tr>
<tr>
<td>Cumulative Layout Shift</td>
<td>&lt; 0.1</td>
<td>Lighthouse</td>
</tr>
<tr>
<td>Time to Interactive</td>
<td>&lt; 3.0s</td>
<td>Lighthouse</td>
</tr>
<tr>
<td>Bundle Size</td>
<td>&lt; 2MB</td>
<td>Webpack Bundle Analyzer</td>
</tr>
</tbody>
</table>
<h3 id="backend-metrics">Backend Metrics<a class="headerlink" href="#backend-metrics" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Metric</th>
<th>Target</th>
<th>Measurement</th>
</tr>
</thead>
<tbody>
<tr>
<td>Response Time (95th percentile)</td>
<td>&lt; 500ms</td>
<td>Prometheus</td>
</tr>
<tr>
<td>Throughput</td>
<td>&gt; 1000 req/sec</td>
<td>Prometheus</td>
</tr>
<tr>
<td>Error Rate</td>
<td>&lt; 0.1%</td>
<td>Prometheus</td>
</tr>
<tr>
<td>CPU Utilization</td>
<td>&lt; 70%</td>
<td>Kubernetes Metrics</td>
</tr>
<tr>
<td>Memory Utilization</td>
<td>&lt; 80%</td>
<td>Kubernetes Metrics</td>
</tr>
<tr>
<td>Database Connection Pool</td>
<td>&lt; 80%</td>
<td>HikariCP Metrics</td>
</tr>
</tbody>
</table>
<h3 id="extension-metrics">Extension Metrics<a class="headerlink" href="#extension-metrics" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Metric</th>
<th>Target</th>
<th>Measurement</th>
</tr>
</thead>
<tbody>
<tr>
<td>Plugin Execution Time</td>
<td>&lt; 100ms</td>
<td>QES Framework</td>
</tr>
<tr>
<td>Validation Success Rate</td>
<td>&gt; 99.9%</td>
<td>Custom Metrics</td>
</tr>
<tr>
<td>Quote Modification Rate</td>
<td>Variable</td>
<td>Business Metrics</td>
</tr>
</tbody>
</table>
<h2 id="external-resources">External Resources<a class="headerlink" href="#external-resources" title="Permanent link">&para;</a></h2>
<h3 id="documentation-links">Documentation Links<a class="headerlink" href="#documentation-links" title="Permanent link">&para;</a></h3>
<ul>
<li><a href="https://angular.io/docs">Angular Documentation</a></li>
<li><a href="https://spring.io/projects/spring-boot">Spring Boot Documentation</a></li>
<li><a href="https://kubernetes.io/docs/">Kubernetes Documentation</a></li>
<li><a href="https://helm.sh/docs/">Helm Documentation</a></li>
</ul>
<h3 id="telus-internal-resources">TELUS Internal Resources<a class="headerlink" href="#telus-internal-resources" title="Permanent link">&para;</a></h3>
<ul>
<li><a href="https://internal.telus.com/ocp-docs">Order Capture Product Documentation</a></li>
<li><a href="https://internal.telus.com/bss-integration">TELUS BSS Integration Guide</a></li>
<li><a href="https://internal.telus.com/auth-service">TELUS Authentication Service</a></li>
<li><a href="https://internal.telus.com/api-gateway">TELUS API Gateway</a></li>
</ul>
<h3 id="development-tools">Development Tools<a class="headerlink" href="#development-tools" title="Permanent link">&para;</a></h3>
<ul>
<li><a href="https://gitlab.telus.com">GitLab CI/CD</a></li>
<li><a href="https://artifactory.telus.com">Artifactory</a></li>
<li><a href="https://monitoring.telus.com">Monitoring Dashboard</a></li>
<li><a href="https://logs.telus.com">Log Aggregation</a></li>
</ul>
<hr />
<p><strong>Documentation Complete!</strong> </p>
<p>This comprehensive guide covers all aspects of the TELUS B2B Order Capture ecosystem. For additional support or questions, please contact the TELUS Cloud BSS team.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "..", "features": [], "search": "../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>