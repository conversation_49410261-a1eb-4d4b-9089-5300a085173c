#!/usr/bin/env node
const fs = require('fs');
const path = require('path');

// Define paths
const tmf620ApiPath = path.join(__dirname, 'src', 'tmf620Api.ts');
const buildApiPath = path.join(__dirname, 'build', 'tmf620Api.js');

console.log('Fixing API URL in TMF620 server code...');

// Function to update the getApiUrl method in the TypeScript file
function updateTypeScriptFile() {
  if (!fs.existsSync(tmf620ApiPath)) {
    console.error(`TypeScript file not found: ${tmf620ApiPath}`);
    return false;
  }

  let content = fs.readFileSync(tmf620ApiPath, 'utf8');
  
  // Find the getApiUrl method
  const getApiUrlRegex = /private\s+getApiUrl\s*\(\s*environment\s*:\s*Environment\s*\)\s*:\s*string\s*{[^}]*}/gs;
  const match = content.match(getApiUrlRegex);
  
  if (!match) {
    console.error('Could not find getApiUrl method in TypeScript file');
    return false;
  }
  
  // Replace the method with the fixed version
  const fixedMethod = `private getApiUrl(environment: Environment): string {
    logger.debug(\`Getting API URL for environment: \${environment}\`);
    // Get the base URL from environment variables each time to ensure we use the latest value
    let baseUrl = '';
    switch (environment) {
      case 'dev':
        baseUrl = process.env.DEV_API_URL || this.apiConfig.devApiUrl;
        break;
      case 'test':
        baseUrl = process.env.TEST_API_URL || this.apiConfig.testApiUrl;
        break;
      case 'prod':
        baseUrl = process.env.PROD_API_URL || this.apiConfig.prodApiUrl;
        break;
      default:
        throw new Error(\`Invalid environment: \${environment}\`);
    }
    
    // Remove trailing '/productOffering' if it exists to prevent duplication
    if (baseUrl.endsWith('/productOffering')) {
      baseUrl = baseUrl.substring(0, baseUrl.length - '/productOffering'.length);
    }
    
    logger.debug(\`Using API URL: \${baseUrl} for environment: \${environment}\`);
    return baseUrl;
  }`;
  
  content = content.replace(getApiUrlRegex, fixedMethod);
  
  // Write the updated content back to the file
  fs.writeFileSync(tmf620ApiPath, content, 'utf8');
  console.log('Updated TypeScript file successfully');
  return true;
}

// Function to update the getApiUrl method in the JavaScript build file
function updateJavaScriptFile() {
  if (!fs.existsSync(buildApiPath)) {
    console.error(`JavaScript build file not found: ${buildApiPath}`);
    return false;
  }

  let content = fs.readFileSync(buildApiPath, 'utf8');
  
  // Find the getApiUrl method
  const getApiUrlRegex = /getApiUrl\s*\(\s*environment\s*\)\s*{[^}]*}/gs;
  const match = content.match(getApiUrlRegex);
  
  if (!match) {
    console.error('Could not find getApiUrl method in JavaScript file');
    return false;
  }
  
  // Replace the method with the fixed version
  const fixedMethod = `getApiUrl(environment) {
        logger.debug(\`Getting API URL for environment: \${environment}\`);
        // Get the base URL from environment variables each time to ensure we use the latest value
        let baseUrl = '';
        switch (environment) {
            case 'dev':
                baseUrl = process.env.DEV_API_URL || this.apiConfig.devApiUrl;
                break;
            case 'test':
                baseUrl = process.env.TEST_API_URL || this.apiConfig.testApiUrl;
                break;
            case 'prod':
                baseUrl = process.env.PROD_API_URL || this.apiConfig.prodApiUrl;
                break;
            default:
                throw new Error(\`Invalid environment: \${environment}\`);
        }
        // Remove trailing '/productOffering' if it exists to prevent duplication
        if (baseUrl.endsWith('/productOffering')) {
            baseUrl = baseUrl.substring(0, baseUrl.length - '/productOffering'.length);
        }
        logger.debug(\`Using API URL: \${baseUrl} for environment: \${environment}\`);
        return baseUrl;
    }`;
  
  content = content.replace(getApiUrlRegex, fixedMethod);
  
  // Write the updated content back to the file
  fs.writeFileSync(buildApiPath, content, 'utf8');
  console.log('Updated JavaScript build file successfully');
  return true;
}

// Update both files
const tsUpdated = updateTypeScriptFile();
const jsUpdated = updateJavaScriptFile();

if (tsUpdated && jsUpdated) {
  console.log('API URL fix completed successfully!');
  console.log('Please rebuild the project and restart the server to apply the changes.');
} else {
  console.error('Failed to update one or more files.');
  console.log('Please check the error messages above and try again.');
}
