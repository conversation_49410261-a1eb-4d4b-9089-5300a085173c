# 🤖 How to install and configure Cline in VS Code

## Installation Steps

1. Install [Cline extension in VS Code](https://marketplace.visualstudio.com/items?itemName=saoudrizwan.claude-dev)

2. Open the Cline Extension settings in VS Code and configure these settings:

   ### Option 1: Using Copilot-routed models
   - API Provider: `VS Code LM API`
   - Language Model: `copilot-claude-3-5-sonnet` (or the one you prefer)
     > NOTE: You will be asked to allow usage by GitHub; accept it, and you should be good to go
     ![VS Code Proxy Settings](assets/vscode-proxy-settings.png)

   ### Option 2: Using FueliX API (if you feel limited by what the Copilot integrations offer)

   - Navigate to [https://app.fuelix.ai](https://app.fuelix.ai)
      - Log in with your TELUS credentials
      - Navigate to your Profile
      - Scroll down and copy the API Key
     > If you don't see the API Key items, you need to ask <PERSON> to activate it in your account

   - API Provider: `OpenAI Compatible`
   - Base URL: [https://proxy.fuelix.ai](https://proxy.fuelix.ai)
   - API Key: `[yourAPIkey]`
   - Model ID: `claude-3-5-sonnet` (or any other model supported by FueliX; Claude Sonnet 3.5 seems to give the best results for coding as of this date)
   ![FueliX Profile Settings](assets/fuelix-profile.png)

3. In the Cline chat session, there are additional options you can configure according to your preference:
   - I have all options checked except `Edit Files`, as I prefer to Accept/Reject file modifications manually
   ![FueliX Login](assets/fuelix-login.png)

## 🔒 How to use VS Code (and its extensions) with TELUS VPN

1. Navigate to VS Code Extension Marketplace and install [proxy-toggle](https://marketplace.visualstudio.com/items?itemName=leunardo.proxy-toggle) (Thanks @kevin)

2. Go to the extension settings

3. You'll have 2 profiles (Home and Office):
   - Home (VPN Disconnected): Leave blank
   - Office: `http://198.161.14.25:8080`
   ![VS Code Proxy Toggle Settings](assets/vscode-proxy-toggle.png)

4. Close the settings and return to VS Code

5. In the VS Code bottom menu, you can toggle between the two proxy modes
![Proxy Toggle Demo](assets/proxy-toggle-demo.gif)

## Extension Details
📦 [marketplace.visualstudio.com](https://marketplace.visualstudio.com)  
[Cline - Visual Studio Marketplace](https://marketplace.visualstudio.com/items?itemName=saoudrizwan.claude-dev)

Extension for Visual Studio Code - Autonomous coding agent right in your IDE, capable of creating/editing files, running commands, using the browser, and more with your permission every step of the way.
