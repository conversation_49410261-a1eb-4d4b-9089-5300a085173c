
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/cpq-config/01-configuration-management/">
      
      
        <link rel="prev" href="../00-overview/">
      
      
        <link rel="next" href="../02-integration-chains/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Configuration Management - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#configuration-management" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Configuration Management
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" checked>
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-orchestrator" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Orchestrator
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Orchestrator">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#main-configuration-file" class="md-nav__link">
    <span class="md-ellipsis">
      Main Configuration File
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-deployment-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Deployment Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-actions-sequence" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Actions Sequence
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#variable-management" class="md-nav__link">
    <span class="md-ellipsis">
      Variable Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Variable Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-variables-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Common Variables Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#secured-variables-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Secured Variables Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#variable-injection-process" class="md-nav__link">
    <span class="md-ellipsis">
      Variable Injection Process
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Environment Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#environment-specific-configmaps" class="md-nav__link">
    <span class="md-ellipsis">
      Environment-Specific ConfigMaps
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-values-files" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Values Files
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Security Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#tls-certificate-management" class="md-nav__link">
    <span class="md-ellipsis">
      TLS Certificate Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#secured-variables-secret" class="md-nav__link">
    <span class="md-ellipsis">
      Secured Variables Secret
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#helm-chart-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Helm Chart Deployment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-validation-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Validation Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Validation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Validation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#validation-rules" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Rules
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-orchestrator" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Orchestrator
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Orchestrator">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#main-configuration-file" class="md-nav__link">
    <span class="md-ellipsis">
      Main Configuration File
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-deployment-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Deployment Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-actions-sequence" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Actions Sequence
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#variable-management" class="md-nav__link">
    <span class="md-ellipsis">
      Variable Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Variable Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-variables-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Common Variables Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#secured-variables-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Secured Variables Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#variable-injection-process" class="md-nav__link">
    <span class="md-ellipsis">
      Variable Injection Process
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Environment Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#environment-specific-configmaps" class="md-nav__link">
    <span class="md-ellipsis">
      Environment-Specific ConfigMaps
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-values-files" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Values Files
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Security Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#tls-certificate-management" class="md-nav__link">
    <span class="md-ellipsis">
      TLS Certificate Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#secured-variables-secret" class="md-nav__link">
    <span class="md-ellipsis">
      Secured Variables Secret
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#helm-chart-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Helm Chart Deployment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-validation-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Validation Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Validation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Validation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#validation-rules" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Rules
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="configuration-management">Configuration Management<a class="headerlink" href="#configuration-management" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#configuration-orchestrator">Configuration Orchestrator</a></li>
<li><a href="#variable-management">Variable Management</a></li>
<li><a href="#environment-configuration">Environment Configuration</a></li>
<li><a href="#security-configuration">Security Configuration</a></li>
<li><a href="#configuration-deployment">Configuration Deployment</a></li>
<li><a href="#configuration-validation">Configuration Validation</a></li>
</ul>
<h2 id="configuration-orchestrator">Configuration Orchestrator<a class="headerlink" href="#configuration-orchestrator" title="Permanent link">&para;</a></h2>
<h3 id="main-configuration-file">Main Configuration File<a class="headerlink" href="#main-configuration-file" title="Permanent link">&para;</a></h3>
<p>The <code>content/config.json</code> file serves as the central orchestrator for the entire configuration package deployment process.</p>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;52306f9b-2481-4880-a016-39395df81974&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Telus cloud-integration-service configuration package&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;VENDOR&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;version&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;0&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;actions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Sequence of actions&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Multiple&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;args&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;actions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;1. Get namespace from config-server&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Request&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;args&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;method&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;GET&quot;</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/api/v1/config-server/global/default&quot;</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;headers&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="nt">&quot;Content-Type&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;application/json&quot;</span><span class="p">]</span>
<span class="w">              </span><span class="p">},</span>
<span class="w">              </span><span class="nt">&quot;expectedResponseStatuses&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="mi">200</span><span class="p">]</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="nt">&quot;resultReferences&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;NAMESPACE&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;$.propertySources[0].source[&#39;spring.application.namespace&#39;]&quot;</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">          </span><span class="p">}</span>
<span class="w">        </span><span class="p">]</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="configuration-deployment-flow">Configuration Deployment Flow<a class="headerlink" href="#configuration-deployment-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant CP as Configuration Package
    participant CS as Config Server
    participant K8s as Kubernetes
    participant CIP as CIP Engine
    participant VS as Variable Store

    CP-&gt;&gt;CS: Get namespace configuration
    CS--&gt;&gt;CP: Return namespace info

    CP-&gt;&gt;K8s: Deploy common variables ConfigMap
    CP-&gt;&gt;K8s: Deploy secured variables Secret
    CP-&gt;&gt;K8s: Deploy TLS certificates

    CP-&gt;&gt;CIP: Import service catalog
    CP-&gt;&gt;CIP: Import integration chains
    CP-&gt;&gt;CIP: Apply variable mappings

    CIP-&gt;&gt;VS: Load variables from ConfigMaps
    CIP-&gt;&gt;VS: Load secrets from Kubernetes Secrets
    CIP-&gt;&gt;CIP: Validate configuration integrity

    CIP-&gt;&gt;CP: Configuration deployment complete
    CP-&gt;&gt;K8s: Cleanup temporary resources
</code></pre></div>
<h3 id="configuration-actions-sequence">Configuration Actions Sequence<a class="headerlink" href="#configuration-actions-sequence" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Configuration deployment sequence</span>
<span class="nt">deployment-sequence</span><span class="p">:</span>
<span class="w">  </span><span class="nt">1-namespace-discovery</span><span class="p">:</span>
<span class="w">    </span><span class="nt">action</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Get</span><span class="nv"> </span><span class="s">namespace</span><span class="nv"> </span><span class="s">from</span><span class="nv"> </span><span class="s">config-server&quot;</span>
<span class="w">    </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Request&quot;</span>
<span class="w">    </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;GET&quot;</span>
<span class="w">    </span><span class="nt">url</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/api/v1/config-server/global/default&quot;</span>
<span class="w">    </span><span class="nt">result</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;NAMESPACE&quot;</span>

<span class="w">  </span><span class="nt">2-variable-deployment</span><span class="p">:</span>
<span class="w">    </span><span class="nt">action</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Deploy</span><span class="nv"> </span><span class="s">common</span><span class="nv"> </span><span class="s">variables&quot;</span>
<span class="w">    </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;ConfigMap&quot;</span>
<span class="w">    </span><span class="nt">source</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;content/variables/common-variables.yaml&quot;</span>
<span class="w">    </span><span class="nt">target</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;cip-common-variables&quot;</span>

<span class="w">  </span><span class="nt">3-secret-deployment</span><span class="p">:</span>
<span class="w">    </span><span class="nt">action</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Deploy</span><span class="nv"> </span><span class="s">secured</span><span class="nv"> </span><span class="s">variables&quot;</span>
<span class="w">    </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Secret&quot;</span>
<span class="w">    </span><span class="nt">source</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;content/variables/secured-variables.yaml&quot;</span>
<span class="w">    </span><span class="nt">target</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;cip-secured-variables&quot;</span>

<span class="w">  </span><span class="nt">4-tls-deployment</span><span class="p">:</span>
<span class="w">    </span><span class="nt">action</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Deploy</span><span class="nv"> </span><span class="s">TLS</span><span class="nv"> </span><span class="s">certificates&quot;</span>
<span class="w">    </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TlsDef&quot;</span>
<span class="w">    </span><span class="nt">source</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TLS-config/cert/{environment}/&quot;</span>
<span class="w">    </span><span class="nt">target</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;telus-itf-tls-config&quot;</span>

<span class="w">  </span><span class="nt">5-service-import</span><span class="p">:</span>
<span class="w">    </span><span class="nt">action</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Import</span><span class="nv"> </span><span class="s">service</span><span class="nv"> </span><span class="s">catalog&quot;</span>
<span class="w">    </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;ServiceImport&quot;</span>
<span class="w">    </span><span class="nt">source</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;content/services/&quot;</span>
<span class="w">    </span><span class="nt">target</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;CIP</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Registry&quot;</span>

<span class="w">  </span><span class="nt">6-chain-import</span><span class="p">:</span>
<span class="w">    </span><span class="nt">action</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Import</span><span class="nv"> </span><span class="s">integration</span><span class="nv"> </span><span class="s">chains&quot;</span>
<span class="w">    </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;ChainImport&quot;</span>
<span class="w">    </span><span class="nt">source</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;content/chains/&quot;</span>
<span class="w">    </span><span class="nt">target</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;CIP</span><span class="nv"> </span><span class="s">Chain</span><span class="nv"> </span><span class="s">Repository&quot;</span>

<span class="w">  </span><span class="nt">7-cleanup</span><span class="p">:</span>
<span class="w">    </span><span class="nt">action</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Clear</span><span class="nv"> </span><span class="s">temporary</span><span class="nv"> </span><span class="s">variables&quot;</span>
<span class="w">    </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Cleanup&quot;</span>
<span class="w">    </span><span class="nt">target</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Temporary</span><span class="nv"> </span><span class="s">configuration</span><span class="nv"> </span><span class="s">variables&quot;</span>
</code></pre></div>
<h2 id="variable-management">Variable Management<a class="headerlink" href="#variable-management" title="Permanent link">&para;</a></h2>
<h3 id="common-variables-configuration">Common Variables Configuration<a class="headerlink" href="#common-variables-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># content/variables/common-variables.yaml</span>
<span class="nn">---</span>
<span class="c1"># Service Configuration</span>
<span class="nt">BUNDLEOFFER_TELUS_SERVICETIERID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;7500&quot;</span>
<span class="nt">BUNDLEOFFER_TELUS_PRICETIERID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;7000&quot;</span>
<span class="nt">SERVICE_SUPPORT_FEE_TYPE_CHAR_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1d749ec9-69a9-4b48-a22b-9f3e1379d7f0&quot;</span>
<span class="nt">PHONE_NUMBER_CHAR_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;e12a3c66-5d54-48ab-badd-be9465385807&quot;</span>
<span class="nt">ICCID_CHAR_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;119caf63-d41c-4946-9cbd-8f3fd0787fc6&quot;</span>

<span class="c1"># Brand Configuration</span>
<span class="nt">BRAND_TELUS_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;01fdb423-f3d5-46da-8597-789ef250d3e2&quot;</span>
<span class="nt">BRAND_KOODO_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1d9320bd-baa6-468e-9d15-5ea61b3922a3&quot;</span>

<span class="c1"># Integration Configuration</span>
<span class="nt">orgchart-url</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;org-chart-backend-app:8080&quot;</span>
<span class="nt">om.utm.callback-url</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://cloud-integration-platform-engine:8080/routes&quot;</span>
<span class="nt">om-decomposition-som-urls</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;&quot;</span>

<span class="c1"># Timing Configuration</span>
<span class="nt">CIP_APPLY_SSF_INITIAL_DELAY_START</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;0&quot;</span>
<span class="nt">CIP_SM_API_RETRY_DELAY</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;3000&quot;</span>
<span class="nt">CIP_POST_TASK_BAN_LOCK_RETRY_DELAY_START</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1000&quot;</span>
<span class="nt">CIP_POST_TASK_BAN_LOCK_RETRY_DELAY_END</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;5000&quot;</span>

<span class="c1"># Scope Configuration</span>
<span class="nt">AMS_SCOPE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718&quot;</span>
<span class="nt">promoCodeAPI_v2.scope</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718&quot;</span>
<span class="nt">om.ePubSubEventManagement.scope</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718&quot;</span>
<span class="nt">om.eso.tmf640.scope</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718&quot;</span>
<span class="nt">om.tmf622.scope</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718&quot;</span>
<span class="nt">om.altima.tmf641.scope</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718&quot;</span>

<span class="c1"># Bill Credit Configuration</span>
<span class="nt">BILLCREDIT_TELUS_OTHERPROMOTIONID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1021558&quot;</span>
<span class="nt">BILLCREDIT_KOODO_OTHERPROMOTIONPERSPECTIVETS</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;6/30/2023</span><span class="nv"> </span><span class="s">13:55:14&quot;</span>
<span class="nt">BUNDLEOFFER_KOODO_PHONEPRICEDISCOUNTIND</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;FALSE&quot;</span>

<span class="c1"># Event Management</span>
<span class="nt">om.ePubSubEventManagement.LoyaltyManagement.topicName</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;&quot;</span>
</code></pre></div>
<h3 id="secured-variables-configuration">Secured Variables Configuration<a class="headerlink" href="#secured-variables-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># content/variables/secured-variables.yaml (encrypted)</span>
<span class="nn">---</span>
<span class="c1"># Authentication Credentials</span>
<span class="nt">CIP_KONG_AUTH_CLIENT_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${KONG_CLIENT_ID}&quot;</span>
<span class="nt">CIP_KONG_AUTH_CLIENT_SECRET</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${KONG_CLIENT_SECRET}&quot;</span>
<span class="nt">APPLY_BILL_CREDIT_AUTH_USERNAME</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${BILL_CREDIT_USERNAME}&quot;</span>
<span class="nt">APPLY_BILL_CREDIT_AUTH_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${BILL_CREDIT_PASSWORD}&quot;</span>

<span class="c1"># Kafka Configuration</span>
<span class="nt">KAFKA_CLIENT_USERNAME</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${KAFKA_USERNAME}&quot;</span>
<span class="nt">KAFKA_CLIENT_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${KAFKA_PASSWORD}&quot;</span>
<span class="nt">CIP_OM_KAFKA_SASL_JAAS_CONFIG</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${KAFKA_SASL_JAAS_CONFIG}&quot;</span>
<span class="nt">CIP_OM_KAFKA_SASL_MECHANISM</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${KAFKA_SASL_MECHANISM}&quot;</span>
<span class="nt">CIP_OM_KAFKA_SECURITY_PROTOCOL</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${KAFKA_SECURITY_PROTOCOL}&quot;</span>

<span class="c1"># SFDC Integration</span>
<span class="nt">SFDC_CLIENT_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${SFDC_CLIENT_ID}&quot;</span>
<span class="nt">SFDC_CLIENT_SECRET</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${SFDC_CLIENT_SECRET}&quot;</span>
<span class="nt">SFDC_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${SFDC_PASSWORD}&quot;</span>
<span class="nt">SFDC_USERNAME</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${SFDC_USERNAME}&quot;</span>
<span class="nt">SFDC_SECURITY_TOKEN</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${SFDC_SECURITY_TOKEN}&quot;</span>

<span class="c1"># External System Credentials</span>
<span class="nt">EXTERNAL_API_KEY</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${EXTERNAL_API_KEY}&quot;</span>
<span class="nt">EXTERNAL_API_SECRET</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${EXTERNAL_API_SECRET}&quot;</span>
<span class="nt">DATABASE_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${DATABASE_PASSWORD}&quot;</span>
<span class="nt">ENCRYPTION_KEY</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${ENCRYPTION_KEY}&quot;</span>
</code></pre></div>
<h3 id="variable-injection-process">Variable Injection Process<a class="headerlink" href="#variable-injection-process" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;Variable Sources&quot;
        A[Common Variables YAML]
        B[Secured Variables YAML]
        C[Environment Values]
        D[Kubernetes Secrets]
    end

    subgraph &quot;Processing&quot;
        E[Variable Processor]
        F[Template Engine]
        G[Encryption Handler]
        H[Validation Engine]
    end

    subgraph &quot;Deployment Targets&quot;
        I[ConfigMaps]
        J[Secrets]
        K[CIP Variables]
        L[Chain Scripts]
    end

    A --&gt; E
    B --&gt; G
    C --&gt; F
    D --&gt; G

    E --&gt; F
    G --&gt; F
    F --&gt; H

    H --&gt; I
    H --&gt; J
    H --&gt; K
    H --&gt; L

    style E fill:#673ab7,stroke:#512da8,color:#ffffff
    style F fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style H fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h2 id="environment-configuration">Environment Configuration<a class="headerlink" href="#environment-configuration" title="Permanent link">&para;</a></h2>
<h3 id="environment-specific-configmaps">Environment-Specific ConfigMaps<a class="headerlink" href="#environment-specific-configmaps" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># deployments/charts/telus-cpq-config-package/templates/cpq-common-variables-configmap.yaml</span>
<span class="nn">---</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ConfigMap</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">v1</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cpq-common-variables</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.NAMESPACE</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="nt">data</span><span class="p">:</span>
<span class="w">  </span><span class="c1"># Dynamic variables from Helm values</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.NAMESPACE</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">om.ePubSubEventManagement.LoyaltyManagement.topicName</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.OM_EPUBSUBMANAGEMENT_TOPIC</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">om.ePubSubEventManagement.scope</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.OM_EPUBSUBMANAGEMENT_SCOPE</span><span class="nv"> </span><span class="s">}}&#39;</span>

<span class="w">  </span><span class="c1"># Timing configuration</span>
<span class="w">  </span><span class="nt">CIP_APPLY_SSF_INITIAL_DELAY_START</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.CIP_APPLY_SSF_INITIAL_DELAY_START</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">CIP_APPLY_SSF_INITIAL_DELAY_END</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.CIP_APPLY_SSF_INITIAL_DELAY_END</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">CIP_POST_TASK_BAN_LOCK_RETRY_DELAY_START</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.CIP_POST_TASK_BAN_LOCK_RETRY_DELAY_START</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">CIP_POST_TASK_BAN_LOCK_RETRY_DELAY_END</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.CIP_POST_TASK_BAN_LOCK_RETRY_DELAY_END</span><span class="nv"> </span><span class="s">}}&#39;</span>

<span class="w">  </span><span class="c1"># Integration configuration</span>
<span class="w">  </span><span class="nt">CIP_CREATE_PROFILE_EXTERNAL_ENV</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.CIP_CREATE_PROFILE_EXTERNAL_ENV</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">om-decomposition-som-urls</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.CIP_DECOMPOSITION_SOM_URLS</span><span class="nv"> </span><span class="s">}}&#39;</span>

<span class="w">  </span><span class="c1"># Scope configuration</span>
<span class="w">  </span><span class="nt">om.eso.manager.env</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.OM_ESO_MANAGER_ENV</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">om.eso.tmf640.scope</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.OM_ESO_TMF640_SCOPE</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">om.tmf622.scope</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.OM_TMF622_SCOPE</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">om.altima.tmf641.scope</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.OM_ALTIMA_TMF641_SCOPE</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">promoCodeAPI_v2.scope</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.PROMO_CODE_API_V2_SCOPE</span><span class="nv"> </span><span class="s">}}&#39;</span>

<span class="w">  </span><span class="c1"># Constants (environment-independent)</span>
<span class="w">  </span><span class="nt">ICCID_CHAR_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;119caf63-d41c-4946-9cbd-8f3fd0787fc6&#39;</span>
<span class="w">  </span><span class="nt">PHONE_NUMBER_CHAR_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;e12a3c66-5d54-48ab-badd-be9465385807&quot;</span>
<span class="w">  </span><span class="nt">BRAND_TELUS_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;01fdb423-f3d5-46da-8597-789ef250d3e2&quot;</span>
<span class="w">  </span><span class="nt">BRAND_KOODO_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1d9320bd-baa6-468e-9d15-5ea61b3922a3&quot;</span>
<span class="w">  </span><span class="nt">SERVICE_SUPPORT_FEE_TYPE_CHAR_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1d749ec9-69a9-4b48-a22b-9f3e1379d7f0&quot;</span>
</code></pre></div>
<h3 id="environment-values-files">Environment Values Files<a class="headerlink" href="#environment-values-files" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># deployments/charts/telus-cpq-config-package/values-qa.yaml</span>
<span class="nn">---</span>
<span class="c1"># QA Environment Configuration</span>
<span class="nt">NAMESPACE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;cpq-config-qa&quot;</span>
<span class="nt">ENVIRONMENT</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;qa&quot;</span>

<span class="c1"># Integration Endpoints</span>
<span class="nt">OM_EPUBSUBMANAGEMENT_TOPIC</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;loyalty-management-qa&quot;</span>
<span class="nt">OM_EPUBSUBMANAGEMENT_SCOPE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718-qa&quot;</span>
<span class="nt">CIP_DECOMPOSITION_SOM_URLS</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://som-service-qa:8080&quot;</span>

<span class="c1"># Timing Configuration (Relaxed for QA)</span>
<span class="nt">CIP_APPLY_SSF_INITIAL_DELAY_START</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;0&quot;</span>
<span class="nt">CIP_APPLY_SSF_INITIAL_DELAY_END</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1000&quot;</span>
<span class="nt">CIP_POST_TASK_BAN_LOCK_RETRY_DELAY_START</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;500&quot;</span>
<span class="nt">CIP_POST_TASK_BAN_LOCK_RETRY_DELAY_END</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;2000&quot;</span>

<span class="c1"># External Environment Configuration</span>
<span class="nt">CIP_CREATE_PROFILE_EXTERNAL_ENV</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;qa&quot;</span>
<span class="nt">OM_ESO_MANAGER_ENV</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;qa&quot;</span>

<span class="c1"># Scope Configuration</span>
<span class="nt">OM_ESO_TMF640_SCOPE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718-qa&quot;</span>
<span class="nt">OM_TMF622_SCOPE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718-qa&quot;</span>
<span class="nt">OM_ALTIMA_TMF641_SCOPE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718-qa&quot;</span>
<span class="nt">PROMO_CODE_API_V2_SCOPE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718-qa&quot;</span>

<span class="c1"># Kafka Configuration</span>
<span class="nt">KAFKA_SASL_MECHANISM</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;PLAIN&quot;</span>
<span class="nt">KAFKA_SECURITY_PROTOCOL</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;SASL_SSL&quot;</span>

<span class="c1"># Resource Configuration</span>
<span class="nt">resources</span><span class="p">:</span>
<span class="w">  </span><span class="nt">requests</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;100m&quot;</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;256Mi&quot;</span>
<span class="w">  </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;500m&quot;</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;512Mi&quot;</span>

<span class="c1"># Replica Configuration</span>
<span class="nt">replicaCount</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>

<span class="c1"># Ingress Configuration</span>
<span class="nt">ingress</span><span class="p">:</span>
<span class="w">  </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">  </span><span class="nt">host</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;cpq-config-qa.telus.com&quot;</span>
<span class="w">  </span><span class="nt">tls</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</code></pre></div>
<div class="highlight"><pre><span></span><code><span class="c1"># deployments/charts/telus-cpq-config-package/values-production.yaml</span>
<span class="nn">---</span>
<span class="c1"># Production Environment Configuration</span>
<span class="nt">NAMESPACE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;cpq-config-prod&quot;</span>
<span class="nt">ENVIRONMENT</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;production&quot;</span>

<span class="c1"># Integration Endpoints</span>
<span class="nt">OM_EPUBSUBMANAGEMENT_TOPIC</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;loyalty-management-prod&quot;</span>
<span class="nt">OM_EPUBSUBMANAGEMENT_SCOPE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718&quot;</span>
<span class="nt">CIP_DECOMPOSITION_SOM_URLS</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://som-service-prod:8080&quot;</span>

<span class="c1"># Timing Configuration (Production optimized)</span>
<span class="nt">CIP_APPLY_SSF_INITIAL_DELAY_START</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1000&quot;</span>
<span class="nt">CIP_APPLY_SSF_INITIAL_DELAY_END</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;5000&quot;</span>
<span class="nt">CIP_POST_TASK_BAN_LOCK_RETRY_DELAY_START</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;2000&quot;</span>
<span class="nt">CIP_POST_TASK_BAN_LOCK_RETRY_DELAY_END</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;10000&quot;</span>

<span class="c1"># External Environment Configuration</span>
<span class="nt">CIP_CREATE_PROFILE_EXTERNAL_ENV</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;production&quot;</span>
<span class="nt">OM_ESO_MANAGER_ENV</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;production&quot;</span>

<span class="c1"># Scope Configuration</span>
<span class="nt">OM_ESO_TMF640_SCOPE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718&quot;</span>
<span class="nt">OM_TMF622_SCOPE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718&quot;</span>
<span class="nt">OM_ALTIMA_TMF641_SCOPE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718&quot;</span>
<span class="nt">PROMO_CODE_API_V2_SCOPE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1718&quot;</span>

<span class="c1"># Kafka Configuration</span>
<span class="nt">KAFKA_SASL_MECHANISM</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;SCRAM-SHA-512&quot;</span>
<span class="nt">KAFKA_SECURITY_PROTOCOL</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;SASL_SSL&quot;</span>

<span class="c1"># Resource Configuration</span>
<span class="nt">resources</span><span class="p">:</span>
<span class="w">  </span><span class="nt">requests</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;200m&quot;</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;512Mi&quot;</span>
<span class="w">  </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1000m&quot;</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1Gi&quot;</span>

<span class="c1"># Replica Configuration</span>
<span class="nt">replicaCount</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>

<span class="c1"># Ingress Configuration</span>
<span class="nt">ingress</span><span class="p">:</span>
<span class="w">  </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">  </span><span class="nt">host</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;cpq-config.telus.com&quot;</span>
<span class="w">  </span><span class="nt">tls</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="c1"># High Availability Configuration</span>
<span class="nt">affinity</span><span class="p">:</span>
<span class="w">  </span><span class="nt">podAntiAffinity</span><span class="p">:</span>
<span class="w">    </span><span class="nt">requiredDuringSchedulingIgnoredDuringExecution</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">labelSelector</span><span class="p">:</span>
<span class="w">        </span><span class="nt">matchExpressions</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">key</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">app</span>
<span class="w">          </span><span class="nt">operator</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">In</span>
<span class="w">          </span><span class="nt">values</span><span class="p">:</span>
<span class="w">          </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cpq-config</span>
<span class="w">      </span><span class="nt">topologyKey</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">kubernetes.io/hostname</span>
</code></pre></div>
<h2 id="security-configuration">Security Configuration<a class="headerlink" href="#security-configuration" title="Permanent link">&para;</a></h2>
<h3 id="tls-certificate-management">TLS Certificate Management<a class="headerlink" href="#tls-certificate-management" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// TLS-config/cert/gateway/QA/atp-itf-tls-def.json</span>
<span class="p">[</span>
<span class="w">  </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;apiVersion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;nc.core.mesh/v3&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;kind&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;TlsDef&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;metadata&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">null</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;spec&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;telus-itf-tls-config-gw&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;trustedForGateways&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;egress-gateway&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;tls&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;insecure&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;caCertificates&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/etc/ssl/certs/ca-bundle.pem&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;privateKey&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/etc/ssl/private/gateway-key.pem&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;serverCertificate&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/etc/ssl/certs/gateway-cert.pem&quot;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">]</span>
</code></pre></div>
<h3 id="secured-variables-secret">Secured Variables Secret<a class="headerlink" href="#secured-variables-secret" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># deployments/charts/telus-cip-config-package/templates/cip-secured-variables-configmap.yaml</span>
<span class="nn">---</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ConfigMap</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">v1</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cip-secured-variables</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.NAMESPACE</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="nt">data</span><span class="p">:</span>
<span class="w">  </span><span class="c1"># Authentication credentials (values injected from Kubernetes Secrets)</span>
<span class="w">  </span><span class="nt">CIP_KONG_AUTH_CLIENT_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.CIP_KONG_AUTH_CLIENT_ID</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">CIP_KONG_AUTH_CLIENT_SECRET</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.CIP_KONG_AUTH_CLIENT_SECRET</span><span class="nv"> </span><span class="s">}}&#39;</span>

<span class="w">  </span><span class="c1"># Billing system credentials</span>
<span class="w">  </span><span class="nt">APPLY_BILL_CREDIT_AUTH_USERNAME</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.APPLY_BILL_CREDIT_AUTH_USERNAME</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">APPLY_BILL_CREDIT_AUTH_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.APPLY_BILL_CREDIT_AUTH_PASSWORD</span><span class="nv"> </span><span class="s">}}&#39;</span>

<span class="w">  </span><span class="c1"># Kafka credentials</span>
<span class="w">  </span><span class="nt">KAFKA_CLIENT_USERNAME</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.KAFKA_CLIENT_USERNAME</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">KAFKA_CLIENT_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.KAFKA_CLIENT_PASSWORD</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">CIP_OM_KAFKA_SASL_JAAS_CONFIG</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.KAFKA_SASL_JAAS_CONFIG</span><span class="nv"> </span><span class="s">}}&#39;</span>

<span class="w">  </span><span class="c1"># SFDC credentials</span>
<span class="w">  </span><span class="nt">SFDC_CLIENT_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.SFDC_CLIENT_ID</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">SFDC_CLIENT_SECRET</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.SFDC_CLIENT_SECRET</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">SFDC_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.SFDC_PASSWORD</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">SFDC_USERNAME</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.SFDC_USERNAME</span><span class="nv"> </span><span class="s">}}&#39;</span>
<span class="w">  </span><span class="nt">SFDC_SECURITY_TOKEN</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;{{</span><span class="nv"> </span><span class="s">.Values.SFDC_SECURITY_TOKEN</span><span class="nv"> </span><span class="s">}}&#39;</span>
</code></pre></div>
<h2 id="configuration-deployment">Configuration Deployment<a class="headerlink" href="#configuration-deployment" title="Permanent link">&para;</a></h2>
<h3 id="helm-chart-deployment">Helm Chart Deployment<a class="headerlink" href="#helm-chart-deployment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Deploy to QA environment</span>
helm<span class="w"> </span>upgrade<span class="w"> </span>--install<span class="w"> </span>cip-config-qa<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>deployments/charts/telus-cip-config-package<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--values<span class="w"> </span>deployments/charts/telus-cip-config-package/values-qa.yaml<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--namespace<span class="w"> </span>cip-config-qa<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--create-namespace

<span class="c1"># Deploy to Production environment</span>
helm<span class="w"> </span>upgrade<span class="w"> </span>--install<span class="w"> </span>cip-config-prod<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>deployments/charts/telus-cip-config-package<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--values<span class="w"> </span>deployments/charts/telus-cip-config-package/values-production.yaml<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--namespace<span class="w"> </span>cip-config-prod<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--create-namespace
</code></pre></div>
<h3 id="configuration-validation-commands">Configuration Validation Commands<a class="headerlink" href="#configuration-validation-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Validate configuration syntax</span>
mvn<span class="w"> </span>validate

<span class="c1"># Validate Helm chart</span>
helm<span class="w"> </span>lint<span class="w"> </span>deployments/charts/telus-cip-config-package

<span class="c1"># Validate Kubernetes manifests</span>
helm<span class="w"> </span>template<span class="w"> </span>cip-config<span class="w"> </span>deployments/charts/telus-cip-config-package<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--values<span class="w"> </span>deployments/charts/telus-cip-config-package/values-qa.yaml<span class="w"> </span><span class="se">\</span>
<span class="w">  </span><span class="p">|</span><span class="w"> </span>kubectl<span class="w"> </span>apply<span class="w"> </span>--dry-run<span class="o">=</span>client<span class="w"> </span>-f<span class="w"> </span>-

<span class="c1"># Validate TLS configuration</span>
openssl<span class="w"> </span>verify<span class="w"> </span>-CAfile<span class="w"> </span>TLS-config/cert/gateway/QA/ca-bundle.pem<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>TLS-config/cert/gateway/QA/gateway-cert.pem
</code></pre></div>
<h2 id="configuration-validation">Configuration Validation<a class="headerlink" href="#configuration-validation" title="Permanent link">&para;</a></h2>
<h3 id="validation-rules">Validation Rules<a class="headerlink" href="#validation-rules" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># validation-rules.yaml</span>
<span class="nt">validation</span><span class="p">:</span>
<span class="w">  </span><span class="nt">variables</span><span class="p">:</span>
<span class="w">    </span><span class="nt">required-variables</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">NAMESPACE</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">BRAND_TELUS_ID</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">BRAND_KOODO_ID</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">SERVICE_SUPPORT_FEE_TYPE_CHAR_ID</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">PHONE_NUMBER_CHAR_ID</span>

<span class="w">    </span><span class="nt">format-validation</span><span class="p">:</span>
<span class="w">      </span><span class="nt">NAMESPACE</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;^[a-z0-9-]+$&quot;</span>
<span class="w">      </span><span class="nt">BRAND_TELUS_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$&quot;</span>
<span class="w">      </span><span class="nt">PHONE_NUMBER_CHAR_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$&quot;</span>

<span class="w">    </span><span class="nt">environment-consistency</span><span class="p">:</span>
<span class="w">      </span><span class="nt">qa</span><span class="p">:</span>
<span class="w">        </span><span class="nt">ENVIRONMENT</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;qa&quot;</span>
<span class="w">        </span><span class="nt">CIP_CREATE_PROFILE_EXTERNAL_ENV</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;qa&quot;</span>
<span class="w">      </span><span class="nt">production</span><span class="p">:</span>
<span class="w">        </span><span class="nt">ENVIRONMENT</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;production&quot;</span>
<span class="w">        </span><span class="nt">CIP_CREATE_PROFILE_EXTERNAL_ENV</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;production&quot;</span>

<span class="w">  </span><span class="nt">security</span><span class="p">:</span>
<span class="w">    </span><span class="nt">tls-validation</span><span class="p">:</span>
<span class="w">      </span><span class="nt">certificate-expiry-check</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">certificate-chain-validation</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">private-key-validation</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">    </span><span class="nt">secret-validation</span><span class="p">:</span>
<span class="w">      </span><span class="nt">required-secrets</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">SFDC_CLIENT_SECRET</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">KAFKA_CLIENT_PASSWORD</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">CIP_KONG_AUTH_CLIENT_SECRET</span>

<span class="w">      </span><span class="nt">secret-format-validation</span><span class="p">:</span>
<span class="w">        </span><span class="nt">SFDC_CLIENT_ID</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;^[A-Za-z0-9._]+$&quot;</span>
<span class="w">        </span><span class="nt">KAFKA_CLIENT_USERNAME</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;^[A-Za-z0-9._-]+$&quot;</span>

<span class="w">  </span><span class="nt">deployment</span><span class="p">:</span>
<span class="w">    </span><span class="nt">resource-validation</span><span class="p">:</span>
<span class="w">      </span><span class="nt">cpu-limits</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1000m&quot;</span>
<span class="w">      </span><span class="nt">memory-limits</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1Gi&quot;</span>
<span class="w">      </span><span class="nt">replica-count-max</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>

<span class="w">    </span><span class="nt">namespace-validation</span><span class="p">:</span>
<span class="w">      </span><span class="nt">allowed-namespaces</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cip-config-qa&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cip-config-staging&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cip-config-prod&quot;</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../02-integration-chains/">Integration Chains →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>