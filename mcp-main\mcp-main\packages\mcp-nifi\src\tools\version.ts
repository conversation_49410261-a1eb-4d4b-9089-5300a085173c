import { NiFiClient } from '../nifi-client.js';
import { z } from 'zod';

type ToolRequest = {
  method: string;
  params?: {
    arguments?: Record<string, unknown>;
  };
};

type ToolResponse = {
  content: Array<{ type: string; text: string }>;
  isError?: boolean;
};

const toolSchema = z.object({
  method: z.string(),
  params: z.object({
    arguments: z.record(z.unknown())
  }).optional()
});

export const tools = {
  manage_version: {
    description: 'Manage version control for process groups',
    inputSchema: {
      type: 'object',
      properties: {
        processGroupId: {
          type: 'string',
          description: 'ID of the process group'
        },
        action: {
          type: 'string',
          enum: ['get_info'],
          description: 'Version control action to perform'
        }
      },
      required: ['processGroupId', 'action']
    }
  }
};

export const manageVersion = (client: NiFiClient) => async (request: ToolRequest): Promise<ToolResponse> => {
  const { processGroupId, action } = (request.params?.arguments || {}) as {
    processGroupId: string;
    action: 'get_info';
  };

  try {
    let response;
    if (action !== 'get_info') {
      throw new Error(`Invalid action: ${action}`);
    }
    response = await client.get(`/versions/process-groups/${processGroupId}`);

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2)
        }
      ]
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      content: [
        {
          type: 'text',
          text: `Error managing version: ${errorMessage}`
        }
      ],
      isError: true
    };
  }
};
