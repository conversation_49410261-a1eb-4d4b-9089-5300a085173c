# @telus/mcp-gcp-log-rest

This package provides a Model Context Protocol (MCP) server for interacting with Google Cloud Platform (GCP) Logging REST API. It allows you to search and retrieve logs from log sinks for any GCP project.   It uses the [Cloud Logging API](https://cloud.google.com/logging/docs/reference/v2/rest/v2/entries/list) and you can use that link to test your resourceNames for formatting.

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-gcp-log-rest
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for Claude Desktop):

   ```json
   {
     "mcpServers": {
       "gcp-log-rest": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-gcp-log-rest"],
         "env": {
           "RESOURCE_NAMES": "[\"projects/my-project\", \"projects/my-project/locations/us-central1/buckets/my-bucket\"]"
         }
       }
     }
   }
   ```

   The `RESOURCE_NAMES` environment variable should be set to a JSON array of resource names you want to search logs for. Here are examples of valid resource names:

   - Projects: `["projects/my-project"]`
   - Locations: `["locations/us-central1"]`
   - Log Buckets: `["projects/my-project/locations/us-central1/buckets/my-bucket"]`
   - Sinks: `["projects/my-project/locations/us-central1/sinks/my-sink"]`
   - Log Entries: `["projects/my-project/locations/us-central1/logs/my-log"]`
   - Monitored Resource Types: `["monitored_resource_types/gce_instance"]`

   You can include multiple resource types in the array. For example:



   Make sure to replace the resource names with your actual GCP resource names.

3. Install the Google Cloud SDK if you haven't already: [https://cloud.google.com/sdk/docs/install](https://cloud.google.com/sdk/docs/install)

4. Run the following command to authenticate:
   ```bash
   gcloud auth login
   ```

   Follow the prompts to log in with your Google account that has access to the GCP project. The MCP server will automatically use the generated token file for authentication.

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-gcp-log-rest
   npm install
   ```

2. Create a `.env` file in the root directory with the following variable:

   ```
   RESOURCE_NAMES=["projects/my-project"]
   ```

3. Build the server:

   ```bash
   npm run build
   ```

4. Start the server:

   ```bash
   npm start
   ```

## Features

- Search logs from GCP log sinks
- Filter logs by various criteria such as time range, severity, and custom labels
- Retrieve logs for specific trace IDs
- Verbose logging for easier debugging and monitoring

## Prerequisites

- Node.js (version 14 or higher recommended)
- Google Cloud SDK
- A Google Cloud Platform account with access to Cloud Logging

## Available Tools

| Tool Name | Description | Input | Output |
|-----------|-------------|-------|--------|
| `search_logs` | Search logs by keyword and time range from any GCP log source | `keyword` (string), `trace_id` (optional string), `startTime` (string), `endTime` (string), `maxResults` (optional number), `filter` (optional string), `pod_name` (optional string), `namespace_name` (optional string) | Array of log entries matching the search criteria |

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

MIT © TELUS
