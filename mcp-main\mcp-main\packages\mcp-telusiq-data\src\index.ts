import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import { getConfig } from './config.js';
import { DatabaseConnection } from './connection.js';
import { PortForwardManager } from './port-forward.js';

class TelusIQDataServer {
  private server: Server;
  private dbConnection: DatabaseConnection;
  private portForwardManager: PortForwardManager;

  constructor() {
    const config = getConfig();
    this.dbConnection = new DatabaseConnection(config);
    this.portForwardManager = new PortForwardManager(config);

    this.server = new Server(
      {
        name: 'telusiq-data-server',
        version: '0.1.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    
    this.server.onerror = (error) => console.error('[MCP Error]', error);
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'execute_query',
          description: 'Execute a read-only SQL query on the Google Cloud SQL PostgreSQL database',
          inputSchema: {
            type: 'object',
            properties: {
              query: { type: 'string', description: 'SQL query to execute' },
              params: { type: 'array', items: { type: 'any' }, description: 'Query parameters' },
            },
            required: ['query'],
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      if (request.params.name !== 'execute_query') {
        throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${request.params.name}`);
      }

      const { query, params } = request.params.arguments as { query: string; params?: any[] };

      try {
        const result = await this.dbConnection.query(query, params);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result.rows, null, 2),
            },
          ],
        };
      } catch (error: unknown) {
        console.error('[Query Error]', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        throw new McpError(ErrorCode.InternalError, `Query execution failed: ${errorMessage}`);
      }
    });
  }

  async start() {
    try {
      // Check if port forward is already established
      const isPortInUse = await this.portForwardManager.isPortInUse();
      
      if (!isPortInUse) {
        console.error('[Server] Starting port forwarding...');
        await this.portForwardManager.start();
        
        // Wait a moment for port forwarding to stabilize
        await new Promise(resolve => setTimeout(resolve, 2000));
      } else {
        console.error('[Server] Using existing port forward');
      }
      
      console.error('[Server] Connecting to database...');
      await this.dbConnection.connect();

      const transport = new StdioServerTransport();
      await this.server.connect(transport);
      console.error('[Server] TelusIQ Data MCP server running on stdio');
    } catch (error) {
      console.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  async stop() {
    await this.dbConnection.disconnect();
    await this.server.close();
  }
}

const server = new TelusIQDataServer();
server.start().catch(console.error);

process.on('SIGINT', async () => {
  console.error('Shutting down...');
  await server.stop();
  process.exit(0);
});
