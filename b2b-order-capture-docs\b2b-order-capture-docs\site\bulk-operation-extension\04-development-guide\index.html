
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/bulk-operation-extension/04-development-guide/">
      
      
        <link rel="prev" href="../03-deployment-configuration/">
      
      
        <link rel="next" href="../../cip-config/00-overview/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Development Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#development-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Development Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" checked>
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#docker-compose-development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Docker Compose Development Environment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#nifi-flow-development" class="md-nav__link">
    <span class="md-ellipsis">
      NiFi Flow Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="NiFi Flow Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#flow-development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Flow Development Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#nifi-flow-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      NiFi Flow Best Practices
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#processor-configuration-templates" class="md-nav__link">
    <span class="md-ellipsis">
      Processor Configuration Templates
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#parameter-context-management" class="md-nav__link">
    <span class="md-ellipsis">
      Parameter Context Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-strategies" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategies
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Strategies">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing Framework
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Testing
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#build-and-packaging" class="md-nav__link">
    <span class="md-ellipsis">
      Build and Packaging
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Build and Packaging">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-build-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Build Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-scripts" class="md-nav__link">
    <span class="md-ellipsis">
      Build Scripts
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#dockerfile" class="md-nav__link">
    <span class="md-ellipsis">
      Dockerfile
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#debugging-and-troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging and Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Debugging and Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues-and-solutions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues and Solutions
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#debugging-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Development Best Practices
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Security Best Practices
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#docker-compose-development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Docker Compose Development Environment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#nifi-flow-development" class="md-nav__link">
    <span class="md-ellipsis">
      NiFi Flow Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="NiFi Flow Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#flow-development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Flow Development Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#nifi-flow-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      NiFi Flow Best Practices
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#processor-configuration-templates" class="md-nav__link">
    <span class="md-ellipsis">
      Processor Configuration Templates
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#parameter-context-management" class="md-nav__link">
    <span class="md-ellipsis">
      Parameter Context Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-strategies" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategies
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Strategies">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing Framework
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Testing
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#build-and-packaging" class="md-nav__link">
    <span class="md-ellipsis">
      Build and Packaging
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Build and Packaging">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-build-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Build Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-scripts" class="md-nav__link">
    <span class="md-ellipsis">
      Build Scripts
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#dockerfile" class="md-nav__link">
    <span class="md-ellipsis">
      Dockerfile
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#debugging-and-troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging and Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Debugging and Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues-and-solutions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues and Solutions
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#debugging-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Development Best Practices
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Security Best Practices
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="development-guide">Development Guide<a class="headerlink" href="#development-guide" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#development-environment-setup">Development Environment Setup</a></li>
<li><a href="#nifi-flow-development">NiFi Flow Development</a></li>
<li><a href="#testing-strategies">Testing Strategies</a></li>
<li><a href="#build-and-packaging">Build and Packaging</a></li>
<li><a href="#debugging-and-troubleshooting">Debugging and Troubleshooting</a></li>
<li><a href="#best-practices">Best Practices</a></li>
</ul>
<h2 id="development-environment-setup">Development Environment Setup<a class="headerlink" href="#development-environment-setup" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Required Software Versions</span>
Java:<span class="w"> </span>OpenJDK<span class="w"> </span><span class="m">11</span><span class="w"> </span>or<span class="w"> </span>higher
Maven:<span class="w"> </span><span class="m">3</span>.6+
Docker:<span class="w"> </span><span class="m">20</span>.10+
Docker<span class="w"> </span>Compose:<span class="w"> </span><span class="m">1</span>.29+
Kubernetes:<span class="w"> </span><span class="m">1</span>.20+<span class="w"> </span><span class="o">(</span>minikube,<span class="w"> </span>kind,<span class="w"> </span>or<span class="w"> </span>k3s<span class="w"> </span><span class="k">for</span><span class="w"> </span><span class="nb">local</span><span class="w"> </span>development<span class="o">)</span>
Helm:<span class="w"> </span><span class="m">3</span>.x<span class="w"> </span>or<span class="w"> </span>higher
Apache<span class="w"> </span>NiFi:<span class="w"> </span><span class="m">1</span>.19.1<span class="w"> </span>or<span class="w"> </span>higher
PostgreSQL:<span class="w"> </span><span class="m">13</span>+
Git:<span class="w"> </span><span class="m">2</span>.x<span class="w"> </span>or<span class="w"> </span>higher
IDE:<span class="w"> </span>IntelliJ<span class="w"> </span>IDEA,<span class="w"> </span>VS<span class="w"> </span>Code,<span class="w"> </span>or<span class="w"> </span>Eclipse<span class="w"> </span><span class="o">(</span>recommended<span class="o">)</span>
</code></pre></div>
<h3 id="local-development-setup">Local Development Setup<a class="headerlink" href="#local-development-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>&lt;repository-url&gt;
<span class="nb">cd</span><span class="w"> </span>telus-bulk-operation-extension-b2b

<span class="c1"># 2. Verify prerequisites</span>
java<span class="w"> </span>-version
mvn<span class="w"> </span>-version
docker<span class="w"> </span>--version
docker-compose<span class="w"> </span>--version

<span class="c1"># 3. Set up environment variables</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">JAVA_HOME</span><span class="o">=</span>/path/to/java11
<span class="nb">export</span><span class="w"> </span><span class="nv">MAVEN_HOME</span><span class="o">=</span>/path/to/maven
<span class="nb">export</span><span class="w"> </span><span class="nv">PATH</span><span class="o">=</span><span class="nv">$JAVA_HOME</span>/bin:<span class="nv">$MAVEN_HOME</span>/bin:<span class="nv">$PATH</span>

<span class="c1"># 4. Start local development environment</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># 5. Wait for services to be ready</span>
./scripts/wait-for-services.sh

<span class="c1"># 6. Build the project</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package

<span class="c1"># 7. Deploy NiFi flows</span>
./scripts/deploy-flows.sh<span class="w"> </span><span class="nb">local</span>
</code></pre></div>
<h3 id="docker-compose-development-environment">Docker Compose Development Environment<a class="headerlink" href="#docker-compose-development-environment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># docker-compose.yml</span>
<span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;3.8&#39;</span>

<span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="c1"># PostgreSQL Database</span>
<span class="w">  </span><span class="nt">postgres</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">postgres:13</span>
<span class="w">    </span><span class="nt">container_name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bulk-ops-postgres</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="nt">POSTGRES_DB</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bulk_operations</span>
<span class="w">      </span><span class="nt">POSTGRES_USER</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">nifi</span>
<span class="w">      </span><span class="nt">POSTGRES_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">nifi</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;5432:5432&quot;</span>
<span class="w">    </span><span class="nt">volumes</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">postgres_data:/var/lib/postgresql/data</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql</span>
<span class="w">    </span><span class="nt">healthcheck</span><span class="p">:</span>
<span class="w">      </span><span class="nt">test</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;CMD-SHELL&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;pg_isready</span><span class="nv"> </span><span class="s">-U</span><span class="nv"> </span><span class="s">nifi</span><span class="nv"> </span><span class="s">-d</span><span class="nv"> </span><span class="s">bulk_operations&quot;</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="nt">interval</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10s</span>
<span class="w">      </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5s</span>
<span class="w">      </span><span class="nt">retries</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>

<span class="w">  </span><span class="c1"># Apache NiFi</span>
<span class="w">  </span><span class="nt">nifi</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">apache/nifi:1.19.1</span>
<span class="w">    </span><span class="nt">container_name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bulk-ops-nifi</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;8080:8080&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;8443:8443&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;21014:21014&quot;</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">NIFI_WEB_HTTP_PORT=8080</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">NIFI_WEB_HTTPS_PORT=8443</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">NIFI_CLUSTER_IS_NODE=false</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">NIFI_ELECTION_MAX_WAIT=30 sec</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">NIFI_SENSITIVE_PROPS_KEY=nifi-bulk-operations</span>
<span class="w">    </span><span class="nt">volumes</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">nifi_data:/opt/nifi/nifi-current/data</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">nifi_logs:/opt/nifi/nifi-current/logs</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">nifi_conf:/opt/nifi/nifi-current/conf</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./nifi/flows:/opt/nifi/nifi-current/flows</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./nifi/lib:/opt/nifi/nifi-current/lib</span>
<span class="w">    </span><span class="nt">depends_on</span><span class="p">:</span>
<span class="w">      </span><span class="nt">postgres</span><span class="p">:</span>
<span class="w">        </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">service_healthy</span>
<span class="w">    </span><span class="nt">healthcheck</span><span class="p">:</span>
<span class="w">      </span><span class="nt">test</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;CMD&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;curl&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;-f&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;http://localhost:8080/nifi-api/system-diagnostics&quot;</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="nt">interval</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30s</span>
<span class="w">      </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10s</span>
<span class="w">      </span><span class="nt">retries</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>

<span class="w">  </span><span class="c1"># Redis Cache</span>
<span class="w">  </span><span class="nt">redis</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">redis:7-alpine</span>
<span class="w">    </span><span class="nt">container_name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bulk-ops-redis</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;6379:6379&quot;</span>
<span class="w">    </span><span class="nt">command</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">redis-server --requirepass redis-password</span>
<span class="w">    </span><span class="nt">volumes</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">redis_data:/data</span>
<span class="w">    </span><span class="nt">healthcheck</span><span class="p">:</span>
<span class="w">      </span><span class="nt">test</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;CMD&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;redis-cli&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;ping&quot;</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="nt">interval</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10s</span>
<span class="w">      </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5s</span>
<span class="w">      </span><span class="nt">retries</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>

<span class="w">  </span><span class="c1"># Mock Identity Provider</span>
<span class="w">  </span><span class="nt">mock-identity-provider</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mockserver/mockserver:5.15.0</span>
<span class="w">    </span><span class="nt">container_name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mock-identity-provider</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;8081:1080&quot;</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="nt">MOCKSERVER_PROPERTY_FILE</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/config/mockserver.properties</span>
<span class="w">    </span><span class="nt">volumes</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./test/mock-configs/identity-provider.json:/config/expectations.json</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./test/mock-configs/mockserver.properties:/config/mockserver.properties</span>

<span class="w">  </span><span class="c1"># Mock Quote Engine Service</span>
<span class="w">  </span><span class="nt">mock-qes</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mockserver/mockserver:5.15.0</span>
<span class="w">    </span><span class="nt">container_name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mock-qes</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;8082:1080&quot;</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="nt">MOCKSERVER_PROPERTY_FILE</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/config/mockserver.properties</span>
<span class="w">    </span><span class="nt">volumes</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./test/mock-configs/qes.json:/config/expectations.json</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./test/mock-configs/mockserver.properties:/config/mockserver.properties</span>

<span class="nt">volumes</span><span class="p">:</span>
<span class="w">  </span><span class="nt">postgres_data</span><span class="p">:</span>
<span class="w">  </span><span class="nt">nifi_data</span><span class="p">:</span>
<span class="w">  </span><span class="nt">nifi_logs</span><span class="p">:</span>
<span class="w">  </span><span class="nt">nifi_conf</span><span class="p">:</span>
<span class="w">  </span><span class="nt">redis_data</span><span class="p">:</span>

<span class="nt">networks</span><span class="p">:</span>
<span class="w">  </span><span class="nt">default</span><span class="p">:</span>
<span class="w">    </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bulk-operations-network</span>
</code></pre></div>
<h3 id="environment-configuration">Environment Configuration<a class="headerlink" href="#environment-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># .env.development</span>
<span class="c1"># Database Configuration</span>
<span class="nv">DB_HOST</span><span class="o">=</span>localhost
<span class="nv">DB_PORT</span><span class="o">=</span><span class="m">5432</span>
<span class="nv">DB_NAME</span><span class="o">=</span>bulk_operations
<span class="nv">DB_USERNAME</span><span class="o">=</span>nifi
<span class="nv">DB_PASSWORD</span><span class="o">=</span>nifi

<span class="c1"># NiFi Configuration</span>
<span class="nv">NIFI_HOST</span><span class="o">=</span>localhost
<span class="nv">NIFI_PORT</span><span class="o">=</span><span class="m">8080</span>
<span class="nv">NIFI_USERNAME</span><span class="o">=</span>admin
<span class="nv">NIFI_PASSWORD</span><span class="o">=</span>admin

<span class="c1"># External Services</span>
<span class="nv">IDENTITY_PROVIDER_URL</span><span class="o">=</span>http://localhost:8081
<span class="nv">QES_URL</span><span class="o">=</span>http://localhost:8082
<span class="nv">CIP_URL</span><span class="o">=</span>http://localhost:8083

<span class="c1"># OAuth2 Configuration</span>
<span class="nv">OAUTH2_CLIENT_ID</span><span class="o">=</span>bulk-operations-dev
<span class="nv">OAUTH2_CLIENT_SECRET</span><span class="o">=</span>dev-secret
<span class="nv">OAUTH2_SCOPE</span><span class="o">=</span>bulk-operations

<span class="c1"># Logging Configuration</span>
<span class="nv">LOG_LEVEL</span><span class="o">=</span>DEBUG
<span class="nv">LOG_FORMAT</span><span class="o">=</span>JSON

<span class="c1"># Performance Configuration</span>
<span class="nv">MAX_CONCURRENT_REQUESTS</span><span class="o">=</span><span class="m">10</span>
<span class="nv">REQUEST_TIMEOUT</span><span class="o">=</span><span class="m">30000</span>
<span class="nv">CACHE_TTL</span><span class="o">=</span><span class="m">300</span>
</code></pre></div>
<h2 id="nifi-flow-development">NiFi Flow Development<a class="headerlink" href="#nifi-flow-development" title="Permanent link">&para;</a></h2>
<h3 id="flow-development-workflow">Flow Development Workflow<a class="headerlink" href="#flow-development-workflow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;Development Process&quot;
        A[Design Flow] --&gt; B[Create Processors]
        B --&gt; C[Configure Properties]
        C --&gt; D[Set Relationships]
        D --&gt; E[Test Locally]
        E --&gt; F[Version Control]
        F --&gt; G[Deploy to Dev]
        G --&gt; H[Integration Testing]
        H --&gt; I[Code Review]
        I --&gt; J[Deploy to Staging]
    end

    subgraph &quot;Testing Levels&quot;
        K[Unit Testing]
        L[Flow Testing]
        M[Integration Testing]
        N[Performance Testing]
    end

    E --&gt; K
    G --&gt; L
    H --&gt; M
    J --&gt; N

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style E fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style K fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="nifi-flow-best-practices">NiFi Flow Best Practices<a class="headerlink" href="#nifi-flow-best-practices" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Flow Design Guidelines</span>
<span class="nt">flow_design</span><span class="p">:</span>
<span class="w">  </span><span class="nt">naming_conventions</span><span class="p">:</span>
<span class="w">    </span><span class="nt">processors</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;PascalCase</span><span class="nv"> </span><span class="s">with</span><span class="nv"> </span><span class="s">descriptive</span><span class="nv"> </span><span class="s">names&quot;</span>
<span class="w">    </span><span class="nt">connections</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Descriptive</span><span class="nv"> </span><span class="s">relationship</span><span class="nv"> </span><span class="s">names&quot;</span>
<span class="w">    </span><span class="nt">process_groups</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Logical</span><span class="nv"> </span><span class="s">grouping</span><span class="nv"> </span><span class="s">by</span><span class="nv"> </span><span class="s">function&quot;</span>

<span class="w">  </span><span class="nt">error_handling</span><span class="p">:</span>
<span class="w">    </span><span class="nt">failure_relationships</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Always</span><span class="nv"> </span><span class="s">connect</span><span class="nv"> </span><span class="s">failure</span><span class="nv"> </span><span class="s">relationships&quot;</span>
<span class="w">    </span><span class="nt">retry_logic</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Implement</span><span class="nv"> </span><span class="s">exponential</span><span class="nv"> </span><span class="s">backoff&quot;</span>
<span class="w">    </span><span class="nt">dead_letter_queue</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Route</span><span class="nv"> </span><span class="s">unrecoverable</span><span class="nv"> </span><span class="s">failures&quot;</span>

<span class="w">  </span><span class="nt">performance</span><span class="p">:</span>
<span class="w">    </span><span class="nt">concurrent_tasks</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Optimize</span><span class="nv"> </span><span class="s">based</span><span class="nv"> </span><span class="s">on</span><span class="nv"> </span><span class="s">resource</span><span class="nv"> </span><span class="s">availability&quot;</span>
<span class="w">    </span><span class="nt">back_pressure</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Configure</span><span class="nv"> </span><span class="s">appropriate</span><span class="nv"> </span><span class="s">thresholds&quot;</span>
<span class="w">    </span><span class="nt">flow_file_expiration</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Set</span><span class="nv"> </span><span class="s">reasonable</span><span class="nv"> </span><span class="s">expiration</span><span class="nv"> </span><span class="s">times&quot;</span>

<span class="w">  </span><span class="nt">security</span><span class="p">:</span>
<span class="w">    </span><span class="nt">sensitive_properties</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Use</span><span class="nv"> </span><span class="s">parameter</span><span class="nv"> </span><span class="s">contexts</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">secrets&quot;</span>
<span class="w">    </span><span class="nt">ssl_configuration</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Enable</span><span class="nv"> </span><span class="s">SSL</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">external</span><span class="nv"> </span><span class="s">connections&quot;</span>
<span class="w">    </span><span class="nt">authorization</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Implement</span><span class="nv"> </span><span class="s">proper</span><span class="nv"> </span><span class="s">access</span><span class="nv"> </span><span class="s">controls&quot;</span>
</code></pre></div>
<h3 id="processor-configuration-templates">Processor Configuration Templates<a class="headerlink" href="#processor-configuration-templates" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;handleHttpRequest&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi.processors.standard.HandleHttpRequest&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;listening_port&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;21014&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;allowed_paths&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/addressManagement/v1/bulkValidate/.*&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;allow_get&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;true&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;allow_post&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;true&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;maximum_threads&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;200&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;http_context_map&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;StandardHttpContextMap&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;scheduling&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;strategy&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;TIMER_DRIVEN&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;period&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;5 sec&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;concurrent_tasks&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="nt">&quot;updateAttribute&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi.processors.attributes.UpdateAttribute&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;request.id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;${uuid()}&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;${now():format(&#39;yyyy-MM-dd HH:mm:ss&#39;)}&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;source&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bulk-operations&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="nt">&quot;routeOnAttribute&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;org.apache.nifi.processors.standard.RouteOnAttribute&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;routing_strategy&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Route to Property name&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;bulk.request&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;${http.request.uri:equals(&#39;/addressManagement/v1/bulkValidate/request&#39;):and(${http.method:equals(&#39;POST&#39;)})}&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="parameter-context-management">Parameter Context Management<a class="headerlink" href="#parameter-context-management" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Parameter Context Configuration</span>
<span class="nt">parameter_contexts</span><span class="p">:</span>
<span class="w">  </span><span class="nt">bulk_address_upload</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Parameters</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">bulk</span><span class="nv"> </span><span class="s">address</span><span class="nv"> </span><span class="s">upload</span><span class="nv"> </span><span class="s">processing&quot;</span>
<span class="w">    </span><span class="nt">parameters</span><span class="p">:</span>
<span class="w">      </span><span class="nt">src</span><span class="p">:</span>
<span class="w">        </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;T&quot;</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Source</span><span class="nv"> </span><span class="s">identifier&quot;</span>
<span class="w">        </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="w">      </span><span class="nt">maxResults</span><span class="p">:</span>
<span class="w">        </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;5&quot;</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Maximum</span><span class="nv"> </span><span class="s">results</span><span class="nv"> </span><span class="s">per</span><span class="nv"> </span><span class="s">query&quot;</span>
<span class="w">        </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="w">      </span><span class="nt">oauth2_client_secret</span><span class="p">:</span>
<span class="w">        </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${OAUTH2_CLIENT_SECRET}&quot;</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;OAuth2</span><span class="nv"> </span><span class="s">client</span><span class="nv"> </span><span class="s">secret&quot;</span>
<span class="w">        </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">      </span><span class="nt">db_password</span><span class="p">:</span>
<span class="w">        </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${DB_PASSWORD}&quot;</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Database</span><span class="nv"> </span><span class="s">password&quot;</span>
<span class="w">        </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">  </span><span class="nt">environment_specific</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Environment-specific</span><span class="nv"> </span><span class="s">configuration&quot;</span>
<span class="w">    </span><span class="nt">parameters</span><span class="p">:</span>
<span class="w">      </span><span class="nt">internal_gateway</span><span class="p">:</span>
<span class="w">        </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${INTERNAL_GATEWAY_URL}&quot;</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Internal</span><span class="nv"> </span><span class="s">gateway</span><span class="nv"> </span><span class="s">URL&quot;</span>
<span class="w">        </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="w">      </span><span class="nt">qes_gateway</span><span class="p">:</span>
<span class="w">        </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${QES_GATEWAY_URL}&quot;</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;QES</span><span class="nv"> </span><span class="s">gateway</span><span class="nv"> </span><span class="s">URL&quot;</span>
<span class="w">        </span><span class="nt">sensitive</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>
</code></pre></div>
<h2 id="testing-strategies">Testing Strategies<a class="headerlink" href="#testing-strategies" title="Permanent link">&para;</a></h2>
<h3 id="unit-testing-framework">Unit Testing Framework<a class="headerlink" href="#unit-testing-framework" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Unit testing for NiFi processors using NiFi Test Framework</span>
<span class="cm"> */</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">BulkAddressProcessorTest</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">TestRunner</span><span class="w"> </span><span class="n">testRunner</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@BeforeEach</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">setUp</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">testRunner</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">TestRunners</span><span class="p">.</span><span class="na">newTestRunner</span><span class="p">(</span><span class="n">BulkAddressProcessor</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Configure test properties</span>
<span class="w">        </span><span class="n">testRunner</span><span class="p">.</span><span class="na">setProperty</span><span class="p">(</span><span class="n">BulkAddressProcessor</span><span class="p">.</span><span class="na">DB_CONNECTION_POOL</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;test-db-pool&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="n">testRunner</span><span class="p">.</span><span class="na">setProperty</span><span class="p">(</span><span class="n">BulkAddressProcessor</span><span class="p">.</span><span class="na">OAUTH2_PROVIDER</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;test-oauth2-provider&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">testValidBulkRequest</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">requestBody</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;&quot;&quot;</span>
<span class="s">            {</span>
<span class="s">              &quot;addresses&quot;: [</span>
<span class="s">                {</span>
<span class="s">                  &quot;streetNumber&quot;: &quot;123&quot;,</span>
<span class="s">                  &quot;streetName&quot;: &quot;Main Street&quot;,</span>
<span class="s">                  &quot;city&quot;: &quot;Toronto&quot;,</span>
<span class="s">                  &quot;province&quot;: &quot;ON&quot;,</span>
<span class="s">                  &quot;postalCode&quot;: &quot;M5V 3A8&quot;</span>
<span class="s">                }</span>
<span class="s">              ]</span>
<span class="s">            }</span>
<span class="s">            &quot;&quot;&quot;</span><span class="p">;</span>

<span class="w">        </span><span class="n">testRunner</span><span class="p">.</span><span class="na">enqueue</span><span class="p">(</span><span class="n">requestBody</span><span class="p">,</span><span class="w"> </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span>
<span class="w">            </span><span class="s">&quot;http.method&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;POST&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="s">&quot;http.request.uri&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;/addressManagement/v1/bulkValidate/request&quot;</span>
<span class="w">        </span><span class="p">));</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="n">testRunner</span><span class="p">.</span><span class="na">run</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">testRunner</span><span class="p">.</span><span class="na">assertTransferCount</span><span class="p">(</span><span class="n">BulkAddressProcessor</span><span class="p">.</span><span class="na">REL_SUCCESS</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span>
<span class="w">        </span><span class="n">testRunner</span><span class="p">.</span><span class="na">assertTransferCount</span><span class="p">(</span><span class="n">BulkAddressProcessor</span><span class="p">.</span><span class="na">REL_FAILURE</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>

<span class="w">        </span><span class="n">MockFlowFile</span><span class="w"> </span><span class="n">successFlowFile</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">testRunner</span><span class="p">.</span><span class="na">getFlowFilesForRelationship</span><span class="p">(</span>
<span class="w">            </span><span class="n">BulkAddressProcessor</span><span class="p">.</span><span class="na">REL_SUCCESS</span><span class="p">).</span><span class="na">get</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span>

<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">successFlowFile</span><span class="p">.</span><span class="na">getAttribute</span><span class="p">(</span><span class="s">&quot;request.id&quot;</span><span class="p">)).</span><span class="na">isNotNull</span><span class="p">();</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">successFlowFile</span><span class="p">.</span><span class="na">getAttribute</span><span class="p">(</span><span class="s">&quot;status&quot;</span><span class="p">)).</span><span class="na">isEqualTo</span><span class="p">(</span><span class="s">&quot;ACCEPTED&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">testInvalidRequest</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">invalidRequestBody</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;invalid json&quot;</span><span class="p">;</span>

<span class="w">        </span><span class="n">testRunner</span><span class="p">.</span><span class="na">enqueue</span><span class="p">(</span><span class="n">invalidRequestBody</span><span class="p">,</span><span class="w"> </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span>
<span class="w">            </span><span class="s">&quot;http.method&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;POST&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="s">&quot;http.request.uri&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;/addressManagement/v1/bulkValidate/request&quot;</span>
<span class="w">        </span><span class="p">));</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="n">testRunner</span><span class="p">.</span><span class="na">run</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">testRunner</span><span class="p">.</span><span class="na">assertTransferCount</span><span class="p">(</span><span class="n">BulkAddressProcessor</span><span class="p">.</span><span class="na">REL_SUCCESS</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>
<span class="w">        </span><span class="n">testRunner</span><span class="p">.</span><span class="na">assertTransferCount</span><span class="p">(</span><span class="n">BulkAddressProcessor</span><span class="p">.</span><span class="na">REL_FAILURE</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span>

<span class="w">        </span><span class="n">MockFlowFile</span><span class="w"> </span><span class="n">failureFlowFile</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">testRunner</span><span class="p">.</span><span class="na">getFlowFilesForRelationship</span><span class="p">(</span>
<span class="w">            </span><span class="n">BulkAddressProcessor</span><span class="p">.</span><span class="na">REL_FAILURE</span><span class="p">).</span><span class="na">get</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span>

<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">failureFlowFile</span><span class="p">.</span><span class="na">getAttribute</span><span class="p">(</span><span class="s">&quot;error.message&quot;</span><span class="p">)).</span><span class="na">contains</span><span class="p">(</span><span class="s">&quot;Invalid JSON&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="integration-testing">Integration Testing<a class="headerlink" href="#integration-testing" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Integration testing with TestContainers</span>
<span class="cm"> */</span>
<span class="nd">@Testcontainers</span>
<span class="kd">class</span> <span class="nc">BulkOperationIntegrationTest</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Container</span>
<span class="w">    </span><span class="kd">static</span><span class="w"> </span><span class="n">PostgreSQLContainer</span><span class="o">&lt;?&gt;</span><span class="w"> </span><span class="n">postgres</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">PostgreSQLContainer</span><span class="o">&lt;&gt;</span><span class="p">(</span><span class="s">&quot;postgres:13&quot;</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">withDatabaseName</span><span class="p">(</span><span class="s">&quot;bulk_operations&quot;</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">withUsername</span><span class="p">(</span><span class="s">&quot;nifi&quot;</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">withPassword</span><span class="p">(</span><span class="s">&quot;nifi&quot;</span><span class="p">);</span>

<span class="w">    </span><span class="nd">@Container</span>
<span class="w">    </span><span class="kd">static</span><span class="w"> </span><span class="n">GenericContainer</span><span class="o">&lt;?&gt;</span><span class="w"> </span><span class="n">nifi</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">GenericContainer</span><span class="o">&lt;&gt;</span><span class="p">(</span><span class="s">&quot;apache/nifi:1.19.1&quot;</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">withExposedPorts</span><span class="p">(</span><span class="mi">8080</span><span class="p">,</span><span class="w"> </span><span class="mi">21014</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">withEnv</span><span class="p">(</span><span class="s">&quot;NIFI_WEB_HTTP_PORT&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;8080&quot;</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">dependsOn</span><span class="p">(</span><span class="n">postgres</span><span class="p">);</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">RestTemplate</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">nifiBaseUrl</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@BeforeEach</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">setUp</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">restTemplate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">RestTemplate</span><span class="p">();</span>
<span class="w">        </span><span class="n">nifiBaseUrl</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;http://localhost:&quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">nifi</span><span class="p">.</span><span class="na">getMappedPort</span><span class="p">(</span><span class="mi">21014</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Wait for NiFi to be ready</span>
<span class="w">        </span><span class="n">await</span><span class="p">().</span><span class="na">atMost</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofMinutes</span><span class="p">(</span><span class="mi">5</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">until</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">isNiFiReady</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">testBulkAddressValidationEndToEnd</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">BulkValidationRequest</span><span class="w"> </span><span class="n">request</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">BulkValidationRequest</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">addresses</span><span class="p">(</span><span class="n">List</span><span class="p">.</span><span class="na">of</span><span class="p">(</span>
<span class="w">                </span><span class="n">Address</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">                    </span><span class="p">.</span><span class="na">streetNumber</span><span class="p">(</span><span class="s">&quot;123&quot;</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">streetName</span><span class="p">(</span><span class="s">&quot;Main Street&quot;</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">city</span><span class="p">(</span><span class="s">&quot;Toronto&quot;</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">province</span><span class="p">(</span><span class="s">&quot;ON&quot;</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">postalCode</span><span class="p">(</span><span class="s">&quot;M5V 3A8&quot;</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">build</span><span class="p">()</span>
<span class="w">            </span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="n">ResponseEntity</span><span class="o">&lt;</span><span class="n">BulkValidationResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="n">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">.</span><span class="na">postForEntity</span><span class="p">(</span>
<span class="w">            </span><span class="n">nifiBaseUrl</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;/addressManagement/v1/bulkValidate/request&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="n">request</span><span class="p">,</span>
<span class="w">            </span><span class="n">BulkValidationResponse</span><span class="p">.</span><span class="na">class</span>
<span class="w">        </span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getStatusCode</span><span class="p">()).</span><span class="na">isEqualTo</span><span class="p">(</span><span class="n">HttpStatus</span><span class="p">.</span><span class="na">OK</span><span class="p">);</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">().</span><span class="na">getRequestId</span><span class="p">()).</span><span class="na">isNotNull</span><span class="p">();</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">().</span><span class="na">getStatus</span><span class="p">()).</span><span class="na">isEqualTo</span><span class="p">(</span><span class="s">&quot;ACCEPTED&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Wait for processing to complete</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">requestId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">().</span><span class="na">getRequestId</span><span class="p">();</span>
<span class="w">        </span><span class="n">await</span><span class="p">().</span><span class="na">atMost</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofMinutes</span><span class="p">(</span><span class="mi">2</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">until</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">isProcessingComplete</span><span class="p">(</span><span class="n">requestId</span><span class="p">));</span>

<span class="w">        </span><span class="c1">// Verify final status</span>
<span class="w">        </span><span class="n">ResponseEntity</span><span class="o">&lt;</span><span class="n">StatusResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="n">statusResponse</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">.</span><span class="na">getForEntity</span><span class="p">(</span>
<span class="w">            </span><span class="n">nifiBaseUrl</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;/addressManagement/v1/bulkValidate/&quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">requestId</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;/status&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="n">StatusResponse</span><span class="p">.</span><span class="na">class</span>
<span class="w">        </span><span class="p">);</span>

<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">statusResponse</span><span class="p">.</span><span class="na">getBody</span><span class="p">().</span><span class="na">getStatus</span><span class="p">()).</span><span class="na">isEqualTo</span><span class="p">(</span><span class="s">&quot;COMPLETED&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isNiFiReady</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">ResponseEntity</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">.</span><span class="na">getForEntity</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;http://localhost:&quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">nifi</span><span class="p">.</span><span class="na">getMappedPort</span><span class="p">(</span><span class="mi">8080</span><span class="p">)</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;/nifi-api/system-diagnostics&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">class</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">getStatusCode</span><span class="p">().</span><span class="na">is2xxSuccessful</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isProcessingComplete</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">requestId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">ResponseEntity</span><span class="o">&lt;</span><span class="n">StatusResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="n">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">.</span><span class="na">getForEntity</span><span class="p">(</span>
<span class="w">                </span><span class="n">nifiBaseUrl</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;/addressManagement/v1/bulkValidate/&quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">requestId</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;/status&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">StatusResponse</span><span class="p">.</span><span class="na">class</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">            </span><span class="n">String</span><span class="w"> </span><span class="n">status</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">().</span><span class="na">getStatus</span><span class="p">();</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="s">&quot;COMPLETED&quot;</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">status</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s">&quot;FAILED&quot;</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">status</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="performance-testing">Performance Testing<a class="headerlink" href="#performance-testing" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Performance testing using JMeter or Gatling</span>
<span class="cm"> */</span>
<span class="nd">@Test</span>
<span class="kt">void</span><span class="w"> </span><span class="nf">testBulkOperationPerformance</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Load test configuration</span>
<span class="w">    </span><span class="kt">int</span><span class="w"> </span><span class="n">numberOfUsers</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">50</span><span class="p">;</span>
<span class="w">    </span><span class="kt">int</span><span class="w"> </span><span class="n">requestsPerUser</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">10</span><span class="p">;</span>
<span class="w">    </span><span class="n">Duration</span><span class="w"> </span><span class="n">testDuration</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Duration</span><span class="p">.</span><span class="na">ofMinutes</span><span class="p">(</span><span class="mi">5</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Create load test scenario</span>
<span class="w">    </span><span class="n">LoadTestScenario</span><span class="w"> </span><span class="n">scenario</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LoadTestScenario</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">        </span><span class="p">.</span><span class="na">numberOfUsers</span><span class="p">(</span><span class="n">numberOfUsers</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">requestsPerUser</span><span class="p">(</span><span class="n">requestsPerUser</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">testDuration</span><span class="p">(</span><span class="n">testDuration</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">targetUrl</span><span class="p">(</span><span class="n">nifiBaseUrl</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;/addressManagement/v1/bulkValidate/request&quot;</span><span class="p">)</span>
<span class="w">        </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">    </span><span class="c1">// Execute load test</span>
<span class="w">    </span><span class="n">LoadTestResults</span><span class="w"> </span><span class="n">results</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">loadTestRunner</span><span class="p">.</span><span class="na">execute</span><span class="p">(</span><span class="n">scenario</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Assert performance criteria</span>
<span class="w">    </span><span class="n">assertThat</span><span class="p">(</span><span class="n">results</span><span class="p">.</span><span class="na">getAverageResponseTime</span><span class="p">()).</span><span class="na">isLessThan</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="mi">5</span><span class="p">));</span>
<span class="w">    </span><span class="n">assertThat</span><span class="p">(</span><span class="n">results</span><span class="p">.</span><span class="na">get95thPercentileResponseTime</span><span class="p">()).</span><span class="na">isLessThan</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="mi">10</span><span class="p">));</span>
<span class="w">    </span><span class="n">assertThat</span><span class="p">(</span><span class="n">results</span><span class="p">.</span><span class="na">getErrorRate</span><span class="p">()).</span><span class="na">isLessThan</span><span class="p">(</span><span class="mf">0.01</span><span class="p">);</span><span class="w"> </span><span class="c1">// Less than 1% error rate</span>
<span class="w">    </span><span class="n">assertThat</span><span class="p">(</span><span class="n">results</span><span class="p">.</span><span class="na">getThroughput</span><span class="p">()).</span><span class="na">isGreaterThan</span><span class="p">(</span><span class="mi">100</span><span class="p">);</span><span class="w"> </span><span class="c1">// More than 100 requests/minute</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="build-and-packaging">Build and Packaging<a class="headerlink" href="#build-and-packaging" title="Permanent link">&para;</a></h2>
<h3 id="maven-build-configuration">Maven Build Configuration<a class="headerlink" href="#maven-build-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">&lt;!-- pom.xml --&gt;</span>
<span class="nt">&lt;project</span><span class="w"> </span><span class="na">xmlns=</span><span class="s">&quot;http://maven.apache.org/POM/4.0.0&quot;</span><span class="nt">&gt;</span>
<span class="w">    </span><span class="nt">&lt;modelVersion&gt;</span>4.0.0<span class="nt">&lt;/modelVersion&gt;</span>

<span class="w">    </span><span class="nt">&lt;groupId&gt;</span>com.telus.b2b<span class="nt">&lt;/groupId&gt;</span>
<span class="w">    </span><span class="nt">&lt;artifactId&gt;</span>telus-bulk-operation-extension-b2b<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">    </span><span class="nt">&lt;version&gt;</span>2023.3.1.0-SNAPSHOT<span class="nt">&lt;/version&gt;</span>
<span class="w">    </span><span class="nt">&lt;packaging&gt;</span>pom<span class="nt">&lt;/packaging&gt;</span>

<span class="w">    </span><span class="nt">&lt;properties&gt;</span>
<span class="w">        </span><span class="nt">&lt;maven.compiler.source&gt;</span>11<span class="nt">&lt;/maven.compiler.source&gt;</span>
<span class="w">        </span><span class="nt">&lt;maven.compiler.target&gt;</span>11<span class="nt">&lt;/maven.compiler.target&gt;</span>
<span class="w">        </span><span class="nt">&lt;nifi.version&gt;</span>1.19.1<span class="nt">&lt;/nifi.version&gt;</span>
<span class="w">        </span><span class="nt">&lt;junit.version&gt;</span>5.9.2<span class="nt">&lt;/junit.version&gt;</span>
<span class="w">        </span><span class="nt">&lt;testcontainers.version&gt;</span>1.17.6<span class="nt">&lt;/testcontainers.version&gt;</span>
<span class="w">    </span><span class="nt">&lt;/properties&gt;</span>

<span class="w">    </span><span class="nt">&lt;dependencies&gt;</span>
<span class="w">        </span><span class="cm">&lt;!-- NiFi Dependencies --&gt;</span>
<span class="w">        </span><span class="nt">&lt;dependency&gt;</span>
<span class="w">            </span><span class="nt">&lt;groupId&gt;</span>org.apache.nifi<span class="nt">&lt;/groupId&gt;</span>
<span class="w">            </span><span class="nt">&lt;artifactId&gt;</span>nifi-api<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">            </span><span class="nt">&lt;version&gt;</span>${nifi.version}<span class="nt">&lt;/version&gt;</span>
<span class="w">            </span><span class="nt">&lt;scope&gt;</span>provided<span class="nt">&lt;/scope&gt;</span>
<span class="w">        </span><span class="nt">&lt;/dependency&gt;</span>

<span class="w">        </span><span class="cm">&lt;!-- Testing Dependencies --&gt;</span>
<span class="w">        </span><span class="nt">&lt;dependency&gt;</span>
<span class="w">            </span><span class="nt">&lt;groupId&gt;</span>org.junit.jupiter<span class="nt">&lt;/groupId&gt;</span>
<span class="w">            </span><span class="nt">&lt;artifactId&gt;</span>junit-jupiter<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">            </span><span class="nt">&lt;version&gt;</span>${junit.version}<span class="nt">&lt;/version&gt;</span>
<span class="w">            </span><span class="nt">&lt;scope&gt;</span>test<span class="nt">&lt;/scope&gt;</span>
<span class="w">        </span><span class="nt">&lt;/dependency&gt;</span>

<span class="w">        </span><span class="nt">&lt;dependency&gt;</span>
<span class="w">            </span><span class="nt">&lt;groupId&gt;</span>org.testcontainers<span class="nt">&lt;/groupId&gt;</span>
<span class="w">            </span><span class="nt">&lt;artifactId&gt;</span>junit-jupiter<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">            </span><span class="nt">&lt;version&gt;</span>${testcontainers.version}<span class="nt">&lt;/version&gt;</span>
<span class="w">            </span><span class="nt">&lt;scope&gt;</span>test<span class="nt">&lt;/scope&gt;</span>
<span class="w">        </span><span class="nt">&lt;/dependency&gt;</span>

<span class="w">        </span><span class="nt">&lt;dependency&gt;</span>
<span class="w">            </span><span class="nt">&lt;groupId&gt;</span>org.testcontainers<span class="nt">&lt;/groupId&gt;</span>
<span class="w">            </span><span class="nt">&lt;artifactId&gt;</span>postgresql<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">            </span><span class="nt">&lt;version&gt;</span>${testcontainers.version}<span class="nt">&lt;/version&gt;</span>
<span class="w">            </span><span class="nt">&lt;scope&gt;</span>test<span class="nt">&lt;/scope&gt;</span>
<span class="w">        </span><span class="nt">&lt;/dependency&gt;</span>
<span class="w">    </span><span class="nt">&lt;/dependencies&gt;</span>

<span class="w">    </span><span class="nt">&lt;build&gt;</span>
<span class="w">        </span><span class="nt">&lt;plugins&gt;</span>
<span class="w">            </span><span class="cm">&lt;!-- Maven Assembly Plugin for NiFi packaging --&gt;</span>
<span class="w">            </span><span class="nt">&lt;plugin&gt;</span>
<span class="w">                </span><span class="nt">&lt;groupId&gt;</span>org.apache.maven.plugins<span class="nt">&lt;/groupId&gt;</span>
<span class="w">                </span><span class="nt">&lt;artifactId&gt;</span>maven-assembly-plugin<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">                </span><span class="nt">&lt;version&gt;</span>3.4.2<span class="nt">&lt;/version&gt;</span>
<span class="w">                </span><span class="nt">&lt;configuration&gt;</span>
<span class="w">                    </span><span class="nt">&lt;descriptors&gt;</span>
<span class="w">                        </span><span class="nt">&lt;descriptor&gt;</span>descriptors/nifi-assembly-module.xml<span class="nt">&lt;/descriptor&gt;</span>
<span class="w">                    </span><span class="nt">&lt;/descriptors&gt;</span>
<span class="w">                    </span><span class="nt">&lt;finalName&gt;</span>telus-bulk-operation-extension-${project.version}<span class="nt">&lt;/finalName&gt;</span>
<span class="w">                    </span><span class="nt">&lt;appendAssemblyId&gt;</span>false<span class="nt">&lt;/appendAssemblyId&gt;</span>
<span class="w">                </span><span class="nt">&lt;/configuration&gt;</span>
<span class="w">                </span><span class="nt">&lt;executions&gt;</span>
<span class="w">                    </span><span class="nt">&lt;execution&gt;</span>
<span class="w">                        </span><span class="nt">&lt;id&gt;</span>make-assembly<span class="nt">&lt;/id&gt;</span>
<span class="w">                        </span><span class="nt">&lt;phase&gt;</span>package<span class="nt">&lt;/phase&gt;</span>
<span class="w">                        </span><span class="nt">&lt;goals&gt;</span>
<span class="w">                            </span><span class="nt">&lt;goal&gt;</span>single<span class="nt">&lt;/goal&gt;</span>
<span class="w">                        </span><span class="nt">&lt;/goals&gt;</span>
<span class="w">                    </span><span class="nt">&lt;/execution&gt;</span>
<span class="w">                </span><span class="nt">&lt;/executions&gt;</span>
<span class="w">            </span><span class="nt">&lt;/plugin&gt;</span>

<span class="w">            </span><span class="cm">&lt;!-- Docker Maven Plugin --&gt;</span>
<span class="w">            </span><span class="nt">&lt;plugin&gt;</span>
<span class="w">                </span><span class="nt">&lt;groupId&gt;</span>com.spotify<span class="nt">&lt;/groupId&gt;</span>
<span class="w">                </span><span class="nt">&lt;artifactId&gt;</span>dockerfile-maven-plugin<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">                </span><span class="nt">&lt;version&gt;</span>1.4.13<span class="nt">&lt;/version&gt;</span>
<span class="w">                </span><span class="nt">&lt;configuration&gt;</span>
<span class="w">                    </span><span class="nt">&lt;repository&gt;</span>telus/nifi-bulk-operations<span class="nt">&lt;/repository&gt;</span>
<span class="w">                    </span><span class="nt">&lt;tag&gt;</span>${project.version}<span class="nt">&lt;/tag&gt;</span>
<span class="w">                    </span><span class="nt">&lt;buildArgs&gt;</span>
<span class="w">                        </span><span class="nt">&lt;NIFI_VERSION&gt;</span>${nifi.version}<span class="nt">&lt;/NIFI_VERSION&gt;</span>
<span class="w">                    </span><span class="nt">&lt;/buildArgs&gt;</span>
<span class="w">                </span><span class="nt">&lt;/configuration&gt;</span>
<span class="w">                </span><span class="nt">&lt;executions&gt;</span>
<span class="w">                    </span><span class="nt">&lt;execution&gt;</span>
<span class="w">                        </span><span class="nt">&lt;id&gt;</span>default<span class="nt">&lt;/id&gt;</span>
<span class="w">                        </span><span class="nt">&lt;goals&gt;</span>
<span class="w">                            </span><span class="nt">&lt;goal&gt;</span>build<span class="nt">&lt;/goal&gt;</span>
<span class="w">                            </span><span class="nt">&lt;goal&gt;</span>push<span class="nt">&lt;/goal&gt;</span>
<span class="w">                        </span><span class="nt">&lt;/goals&gt;</span>
<span class="w">                    </span><span class="nt">&lt;/execution&gt;</span>
<span class="w">                </span><span class="nt">&lt;/executions&gt;</span>
<span class="w">            </span><span class="nt">&lt;/plugin&gt;</span>
<span class="w">        </span><span class="nt">&lt;/plugins&gt;</span>
<span class="w">    </span><span class="nt">&lt;/build&gt;</span>
<span class="nt">&lt;/project&gt;</span>
</code></pre></div>
<h3 id="build-scripts">Build Scripts<a class="headerlink" href="#build-scripts" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="ch">#!/bin/bash</span>
<span class="c1"># build.sh</span>

<span class="nb">set</span><span class="w"> </span>-e

<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Building TELUS Bulk Operation Extension...&quot;</span>

<span class="c1"># Clean previous builds</span>
mvn<span class="w"> </span>clean

<span class="c1"># Compile and run tests</span>
mvn<span class="w"> </span>compile<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Package the application</span>
mvn<span class="w"> </span>package

<span class="c1"># Build Docker image</span>
mvn<span class="w"> </span>dockerfile:build

<span class="c1"># Tag for different environments</span>
docker<span class="w"> </span>tag<span class="w"> </span>telus/nifi-bulk-operations:<span class="si">${</span><span class="nv">VERSION</span><span class="si">}</span><span class="w"> </span>telus/nifi-bulk-operations:latest
docker<span class="w"> </span>tag<span class="w"> </span>telus/nifi-bulk-operations:<span class="si">${</span><span class="nv">VERSION</span><span class="si">}</span><span class="w"> </span>telus/nifi-bulk-operations:<span class="si">${</span><span class="nv">VERSION</span><span class="si">}</span>-<span class="si">${</span><span class="nv">BUILD_NUMBER</span><span class="si">}</span>

<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Build completed successfully!&quot;</span>

<span class="c1"># Optional: Push to registry</span>
<span class="k">if</span><span class="w"> </span><span class="o">[</span><span class="w"> </span><span class="s2">&quot;</span><span class="nv">$PUSH_TO_REGISTRY</span><span class="s2">&quot;</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s2">&quot;true&quot;</span><span class="w"> </span><span class="o">]</span><span class="p">;</span><span class="w"> </span><span class="k">then</span>
<span class="w">    </span><span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Pushing images to registry...&quot;</span>
<span class="w">    </span>mvn<span class="w"> </span>dockerfile:push
<span class="w">    </span>docker<span class="w"> </span>push<span class="w"> </span>telus/nifi-bulk-operations:latest
<span class="w">    </span>docker<span class="w"> </span>push<span class="w"> </span>telus/nifi-bulk-operations:<span class="si">${</span><span class="nv">VERSION</span><span class="si">}</span>-<span class="si">${</span><span class="nv">BUILD_NUMBER</span><span class="si">}</span>
<span class="k">fi</span>
</code></pre></div>
<h3 id="dockerfile">Dockerfile<a class="headerlink" href="#dockerfile" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c"># Dockerfile</span>
<span class="k">ARG</span><span class="w"> </span><span class="nv">NIFI_VERSION</span><span class="o">=</span><span class="m">1</span>.19.1
<span class="k">FROM</span><span class="w"> </span><span class="s">apache/nifi:${NIFI_VERSION}</span>

<span class="c"># Set environment variables</span>
<span class="k">ENV</span><span class="w"> </span><span class="nv">NIFI_BASE_DIR</span><span class="o">=</span>/opt/nifi
<span class="k">ENV</span><span class="w"> </span><span class="nv">NIFI_HOME</span><span class="o">=</span><span class="si">${</span><span class="nv">NIFI_BASE_DIR</span><span class="si">}</span>/nifi-current

<span class="c"># Copy custom NiFi flows and configurations</span>
<span class="k">COPY</span><span class="w"> </span>nifi/versioned-flows/<span class="w"> </span><span class="si">${</span><span class="nv">NIFI_HOME</span><span class="si">}</span>/flows/
<span class="k">COPY</span><span class="w"> </span>nifi/controller-services/<span class="w"> </span><span class="si">${</span><span class="nv">NIFI_HOME</span><span class="si">}</span>/conf/controller-services/
<span class="k">COPY</span><span class="w"> </span>nifi/parameter-contexts/<span class="w"> </span><span class="si">${</span><span class="nv">NIFI_HOME</span><span class="si">}</span>/conf/parameter-contexts/

<span class="c"># Copy custom libraries</span>
<span class="k">COPY</span><span class="w"> </span>target/lib/<span class="w"> </span><span class="si">${</span><span class="nv">NIFI_HOME</span><span class="si">}</span>/lib/

<span class="c"># Copy startup scripts</span>
<span class="k">COPY</span><span class="w"> </span>scripts/start-nifi.sh<span class="w"> </span><span class="si">${</span><span class="nv">NIFI_HOME</span><span class="si">}</span>/bin/
<span class="k">RUN</span><span class="w"> </span>chmod<span class="w"> </span>+x<span class="w"> </span><span class="si">${</span><span class="nv">NIFI_HOME</span><span class="si">}</span>/bin/start-nifi.sh

<span class="c"># Expose ports</span>
<span class="k">EXPOSE</span><span class="w"> </span><span class="s">8080 8443 21014</span>

<span class="c"># Health check</span>
<span class="k">HEALTHCHECK</span><span class="w"> </span>--interval<span class="o">=</span>30s<span class="w"> </span>--timeout<span class="o">=</span>10s<span class="w"> </span>--start-period<span class="o">=</span>120s<span class="w"> </span>--retries<span class="o">=</span><span class="m">3</span><span class="w"> </span><span class="se">\</span>
<span class="w">    </span>CMD<span class="w"> </span>curl<span class="w"> </span>-f<span class="w"> </span>http://localhost:8080/nifi-api/system-diagnostics<span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="nb">exit</span><span class="w"> </span><span class="m">1</span>

<span class="c"># Start NiFi</span>
<span class="k">CMD</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;./bin/start-nifi.sh&quot;</span><span class="p">]</span>
</code></pre></div>
<h2 id="debugging-and-troubleshooting">Debugging and Troubleshooting<a class="headerlink" href="#debugging-and-troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="common-issues-and-solutions">Common Issues and Solutions<a class="headerlink" href="#common-issues-and-solutions" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nt">common_issues</span><span class="p">:</span>
<span class="w">  </span><span class="nt">nifi_startup_issues</span><span class="p">:</span>
<span class="w">    </span><span class="nt">symptoms</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;NiFi</span><span class="nv"> </span><span class="s">fails</span><span class="nv"> </span><span class="s">to</span><span class="nv"> </span><span class="s">start&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Port</span><span class="nv"> </span><span class="s">binding</span><span class="nv"> </span><span class="s">errors&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Memory</span><span class="nv"> </span><span class="s">issues&quot;</span>
<span class="w">    </span><span class="nt">solutions</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Check</span><span class="nv"> </span><span class="s">port</span><span class="nv"> </span><span class="s">availability&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Increase</span><span class="nv"> </span><span class="s">memory</span><span class="nv"> </span><span class="s">allocation&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Verify</span><span class="nv"> </span><span class="s">Java</span><span class="nv"> </span><span class="s">version</span><span class="nv"> </span><span class="s">compatibility&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Check</span><span class="nv"> </span><span class="s">file</span><span class="nv"> </span><span class="s">permissions&quot;</span>

<span class="w">  </span><span class="nt">flow_execution_issues</span><span class="p">:</span>
<span class="w">    </span><span class="nt">symptoms</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Processors</span><span class="nv"> </span><span class="s">stuck</span><span class="nv"> </span><span class="s">in</span><span class="nv"> </span><span class="s">running</span><span class="nv"> </span><span class="s">state&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Flow</span><span class="nv"> </span><span class="s">files</span><span class="nv"> </span><span class="s">accumulating</span><span class="nv"> </span><span class="s">in</span><span class="nv"> </span><span class="s">queues&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;High</span><span class="nv"> </span><span class="s">memory</span><span class="nv"> </span><span class="s">usage&quot;</span>
<span class="w">    </span><span class="nt">solutions</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Check</span><span class="nv"> </span><span class="s">processor</span><span class="nv"> </span><span class="s">configuration&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Verify</span><span class="nv"> </span><span class="s">external</span><span class="nv"> </span><span class="s">service</span><span class="nv"> </span><span class="s">connectivity&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Adjust</span><span class="nv"> </span><span class="s">concurrent</span><span class="nv"> </span><span class="s">task</span><span class="nv"> </span><span class="s">settings&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Monitor</span><span class="nv"> </span><span class="s">back</span><span class="nv"> </span><span class="s">pressure</span><span class="nv"> </span><span class="s">settings&quot;</span>

<span class="w">  </span><span class="nt">database_connection_issues</span><span class="p">:</span>
<span class="w">    </span><span class="nt">symptoms</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Database</span><span class="nv"> </span><span class="s">connection</span><span class="nv"> </span><span class="s">failures&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Connection</span><span class="nv"> </span><span class="s">pool</span><span class="nv"> </span><span class="s">exhaustion&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;SQL</span><span class="nv"> </span><span class="s">execution</span><span class="nv"> </span><span class="s">errors&quot;</span>
<span class="w">    </span><span class="nt">solutions</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Verify</span><span class="nv"> </span><span class="s">database</span><span class="nv"> </span><span class="s">connectivity&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Check</span><span class="nv"> </span><span class="s">connection</span><span class="nv"> </span><span class="s">pool</span><span class="nv"> </span><span class="s">configuration&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Validate</span><span class="nv"> </span><span class="s">SQL</span><span class="nv"> </span><span class="s">queries&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Monitor</span><span class="nv"> </span><span class="s">connection</span><span class="nv"> </span><span class="s">usage&quot;</span>
</code></pre></div>
<h3 id="debugging-tools">Debugging Tools<a class="headerlink" href="#debugging-tools" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># NiFi Debugging Commands</span>
<span class="c1"># Check NiFi status</span>
curl<span class="w"> </span>-X<span class="w"> </span>GET<span class="w"> </span><span class="s2">&quot;http://localhost:8080/nifi-api/system-diagnostics&quot;</span>

<span class="c1"># Monitor flow file queues</span>
curl<span class="w"> </span>-X<span class="w"> </span>GET<span class="w"> </span><span class="s2">&quot;http://localhost:8080/nifi-api/flow/process-groups/root/status&quot;</span>

<span class="c1"># Check processor status</span>
curl<span class="w"> </span>-X<span class="w"> </span>GET<span class="w"> </span><span class="s2">&quot;http://localhost:8080/nifi-api/flow/processors/{processor-id}/status&quot;</span>

<span class="c1"># View NiFi logs</span>
docker<span class="w"> </span>logs<span class="w"> </span>-f<span class="w"> </span>bulk-ops-nifi

<span class="c1"># Database debugging</span>
psql<span class="w"> </span>-h<span class="w"> </span>localhost<span class="w"> </span>-U<span class="w"> </span>nifi<span class="w"> </span>-d<span class="w"> </span>bulk_operations<span class="w"> </span>-c<span class="w"> </span><span class="s2">&quot;SELECT * FROM bulk_request_status;&quot;</span>

<span class="c1"># Check Redis cache</span>
redis-cli<span class="w"> </span>-h<span class="w"> </span>localhost<span class="w"> </span>-p<span class="w"> </span><span class="m">6379</span><span class="w"> </span>-a<span class="w"> </span>redis-password
&gt;<span class="w"> </span>KEYS<span class="w"> </span>*
&gt;<span class="w"> </span>GET<span class="w"> </span>bulk:request:12345
</code></pre></div>
<h2 id="best-practices">Best Practices<a class="headerlink" href="#best-practices" title="Permanent link">&para;</a></h2>
<h3 id="development-best-practices">Development Best Practices<a class="headerlink" href="#development-best-practices" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nt">development_practices</span><span class="p">:</span>
<span class="w">  </span><span class="nt">code_organization</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Use</span><span class="nv"> </span><span class="s">meaningful</span><span class="nv"> </span><span class="s">processor</span><span class="nv"> </span><span class="s">names&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Group</span><span class="nv"> </span><span class="s">related</span><span class="nv"> </span><span class="s">processors</span><span class="nv"> </span><span class="s">in</span><span class="nv"> </span><span class="s">process</span><span class="nv"> </span><span class="s">groups&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Document</span><span class="nv"> </span><span class="s">complex</span><span class="nv"> </span><span class="s">flows</span><span class="nv"> </span><span class="s">with</span><span class="nv"> </span><span class="s">comments&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Version</span><span class="nv"> </span><span class="s">control</span><span class="nv"> </span><span class="s">all</span><span class="nv"> </span><span class="s">flow</span><span class="nv"> </span><span class="s">definitions&quot;</span>

<span class="w">  </span><span class="nt">configuration_management</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Use</span><span class="nv"> </span><span class="s">parameter</span><span class="nv"> </span><span class="s">contexts</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">environment-specific</span><span class="nv"> </span><span class="s">values&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Store</span><span class="nv"> </span><span class="s">sensitive</span><span class="nv"> </span><span class="s">data</span><span class="nv"> </span><span class="s">in</span><span class="nv"> </span><span class="s">secure</span><span class="nv"> </span><span class="s">parameter</span><span class="nv"> </span><span class="s">contexts&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Document</span><span class="nv"> </span><span class="s">all</span><span class="nv"> </span><span class="s">configuration</span><span class="nv"> </span><span class="s">parameters&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Use</span><span class="nv"> </span><span class="s">consistent</span><span class="nv"> </span><span class="s">naming</span><span class="nv"> </span><span class="s">conventions&quot;</span>

<span class="w">  </span><span class="nt">error_handling</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Always</span><span class="nv"> </span><span class="s">handle</span><span class="nv"> </span><span class="s">failure</span><span class="nv"> </span><span class="s">relationships&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Implement</span><span class="nv"> </span><span class="s">retry</span><span class="nv"> </span><span class="s">logic</span><span class="nv"> </span><span class="s">with</span><span class="nv"> </span><span class="s">exponential</span><span class="nv"> </span><span class="s">backoff&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Log</span><span class="nv"> </span><span class="s">errors</span><span class="nv"> </span><span class="s">with</span><span class="nv"> </span><span class="s">sufficient</span><span class="nv"> </span><span class="s">context&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Route</span><span class="nv"> </span><span class="s">unrecoverable</span><span class="nv"> </span><span class="s">errors</span><span class="nv"> </span><span class="s">to</span><span class="nv"> </span><span class="s">dead</span><span class="nv"> </span><span class="s">letter</span><span class="nv"> </span><span class="s">queues&quot;</span>

<span class="w">  </span><span class="nt">performance_optimization</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Optimize</span><span class="nv"> </span><span class="s">concurrent</span><span class="nv"> </span><span class="s">task</span><span class="nv"> </span><span class="s">settings&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Configure</span><span class="nv"> </span><span class="s">appropriate</span><span class="nv"> </span><span class="s">back</span><span class="nv"> </span><span class="s">pressure</span><span class="nv"> </span><span class="s">thresholds&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Use</span><span class="nv"> </span><span class="s">connection</span><span class="nv"> </span><span class="s">pooling</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">external</span><span class="nv"> </span><span class="s">services&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Monitor</span><span class="nv"> </span><span class="s">and</span><span class="nv"> </span><span class="s">tune</span><span class="nv"> </span><span class="s">JVM</span><span class="nv"> </span><span class="s">settings&quot;</span>

<span class="w">  </span><span class="nt">testing</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Write</span><span class="nv"> </span><span class="s">unit</span><span class="nv"> </span><span class="s">tests</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">custom</span><span class="nv"> </span><span class="s">processors&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Test</span><span class="nv"> </span><span class="s">flows</span><span class="nv"> </span><span class="s">with</span><span class="nv"> </span><span class="s">realistic</span><span class="nv"> </span><span class="s">data</span><span class="nv"> </span><span class="s">volumes&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Validate</span><span class="nv"> </span><span class="s">error</span><span class="nv"> </span><span class="s">handling</span><span class="nv"> </span><span class="s">scenarios&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Perform</span><span class="nv"> </span><span class="s">load</span><span class="nv"> </span><span class="s">testing</span><span class="nv"> </span><span class="s">before</span><span class="nv"> </span><span class="s">production</span><span class="nv"> </span><span class="s">deployment&quot;</span>
</code></pre></div>
<h3 id="security-best-practices">Security Best Practices<a class="headerlink" href="#security-best-practices" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nt">security_practices</span><span class="p">:</span>
<span class="w">  </span><span class="nt">authentication</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Use</span><span class="nv"> </span><span class="s">OAuth2</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">API</span><span class="nv"> </span><span class="s">authentication&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Implement</span><span class="nv"> </span><span class="s">token</span><span class="nv"> </span><span class="s">validation</span><span class="nv"> </span><span class="s">and</span><span class="nv"> </span><span class="s">refresh&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Store</span><span class="nv"> </span><span class="s">credentials</span><span class="nv"> </span><span class="s">securely&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Use</span><span class="nv"> </span><span class="s">service</span><span class="nv"> </span><span class="s">accounts</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">system-to-system</span><span class="nv"> </span><span class="s">communication&quot;</span>

<span class="w">  </span><span class="nt">authorization</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Implement</span><span class="nv"> </span><span class="s">role-based</span><span class="nv"> </span><span class="s">access</span><span class="nv"> </span><span class="s">control&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Follow</span><span class="nv"> </span><span class="s">principle</span><span class="nv"> </span><span class="s">of</span><span class="nv"> </span><span class="s">least</span><span class="nv"> </span><span class="s">privilege&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Audit</span><span class="nv"> </span><span class="s">access</span><span class="nv"> </span><span class="s">and</span><span class="nv"> </span><span class="s">permissions</span><span class="nv"> </span><span class="s">regularly&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Use</span><span class="nv"> </span><span class="s">network</span><span class="nv"> </span><span class="s">policies</span><span class="nv"> </span><span class="s">to</span><span class="nv"> </span><span class="s">restrict</span><span class="nv"> </span><span class="s">traffic&quot;</span>

<span class="w">  </span><span class="nt">data_protection</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Encrypt</span><span class="nv"> </span><span class="s">data</span><span class="nv"> </span><span class="s">in</span><span class="nv"> </span><span class="s">transit</span><span class="nv"> </span><span class="s">and</span><span class="nv"> </span><span class="s">at</span><span class="nv"> </span><span class="s">rest&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Mask</span><span class="nv"> </span><span class="s">sensitive</span><span class="nv"> </span><span class="s">data</span><span class="nv"> </span><span class="s">in</span><span class="nv"> </span><span class="s">logs&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Implement</span><span class="nv"> </span><span class="s">data</span><span class="nv"> </span><span class="s">retention</span><span class="nv"> </span><span class="s">policies&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Use</span><span class="nv"> </span><span class="s">secure</span><span class="nv"> </span><span class="s">communication</span><span class="nv"> </span><span class="s">protocols&quot;</span>
</code></pre></div>
<hr />
<p>This completes the comprehensive Bulk Operation Extension documentation. The documentation now includes:</p>
<ol>
<li><strong>Overview</strong> - Architecture, technology stack, and project structure</li>
<li><strong>NiFi Flow Architecture</strong> - Detailed flow design and processor configuration</li>
<li><strong>API Integration</strong> - REST API specifications and external system integration</li>
<li><strong>Deployment Configuration</strong> - Kubernetes deployment and Helm charts</li>
<li><strong>Development Guide</strong> - Setup, development workflow, testing, and best practices</li>
</ol>
<p>The documentation follows the same detailed style as the other TELUS B2B components with comprehensive technical details, Mermaid diagrams, code examples, and practical guidance for developers and operations teams.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>