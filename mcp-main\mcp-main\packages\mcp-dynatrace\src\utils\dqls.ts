export const endpointsForService = (serviceId: string, limit: number = 10) => `fetch spans 
    | filter dt.entity.service == "${serviceId}" and isNotNull(endpoint.name)
    | summarize requestCounts = count(), by: { endpointName = endpoint.name }
    | sort requestCounts desc
    | limit ${limit}`;

export const traceIdsForEndpoint = (serviceId: string, endpointName: string, limit: number = 10) => `fetch spans
    | filter dt.entity.service == "${serviceId}"
    | filter trace.id in [
        fetch spans
        | filter dt.entity.service == "${serviceId}"
        | filter contains(endpoint.name, "${endpointName}")
        | fields trace.id
    ]
    | summarize {spanCounts = count()}, by: { traceId = trace.id }
    | sort spanCounts desc
    | limit ${limit}`;

export const spansForTrace = (traceId: string) => `fetch spans 
    | filter trace.id == toUid("${traceId}")
    | sort start_time asc`

export const networkAnalysisForTrace = (traceId: string, timeWindow: string = "-12h") => `fetch spans, from:${timeWindow}
    | filter trace.id == toUid("${traceId}")
    | fields span.id, trace.id, dt.entity.host, span.status_code, span.parent_id
    | lookup [fetch dt.entity.host | fields id, ipAddress, entity.name], sourceField:dt.entity.host, lookupField:id
    | fields trace.id, span.id, span.status_code, span.parent_id, lookup.entity.name, lookup.ipAddress`
