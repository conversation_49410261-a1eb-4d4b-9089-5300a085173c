import axios from 'axios';
import { AmsConfig, Environment, TokenCache, TokenResponse } from './types.js';

/**
 * OAuth authentication handler for TELUS AMS API
 */
export class AuthHandler {
  private config: AmsConfig;
  private tokenCache: TokenCache | null = null;
  private tokenEndpoint: string;
  
  /**
   * Create a new AuthHandler instance
   * @param config AMS API configuration
   */
  constructor(config: AmsConfig) {
    this.config = config;
    
    // Set token endpoint based on environment
    this.tokenEndpoint = config.environment === Environment.PROD
      ? 'https://apigw-pr.telus.com/token'
      : 'https://apigw-st.telus.com/st/token';
    
    console.error('[Auth] Initialized with environment:', config.environment);
  }
  
  /**
   * Get a valid access token, refreshing if necessary
   * @returns Promise resolving to access token
   */
  async getAccessToken(): Promise<string> {
    // Check if we have a cached token that's still valid
    if (this.tokenCache && this.tokenCache.expiresAt > Date.now()) {
      console.error('[Auth] Using cached token');
      return this.tokenCache.token;
    }
    
    console.error('[Auth] Requesting new access token');
    
    try {
      // Prepare token request
      const params = new URLSearchParams();
      params.append('grant_type', 'client_credentials');
      params.append('client_id', this.config.clientId);
      
      // Use the appropriate client secret based on the environment
      const clientSecret = this.config.environment === Environment.PROD
        ? this.config.clientSecretProd
        : this.config.clientSecretNonProd;
      
      params.append('client_secret', clientSecret);
      params.append('scope', this.config.scope);
      
      // Make token request
      const response = await axios.post<TokenResponse>(
        this.tokenEndpoint,
        params.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );
      
      // Cache the token with expiration time (subtract 60 seconds for safety margin)
      const expiresAt = Date.now() + (response.data.expires_in - 60) * 1000;
      this.tokenCache = {
        token: response.data.access_token,
        expiresAt,
      };
      
      console.error('[Auth] Token acquired successfully');
      return this.tokenCache.token;
    } catch (error) {
      console.error('[Auth] Error acquiring token:', error);
      if (axios.isAxiosError(error)) {
        throw new Error(`OAuth authentication failed: ${error.response?.data?.error || error.message}`);
      }
      throw new Error(`OAuth authentication failed: ${error}`);
    }
  }
  
  /**
   * Clear the token cache
   */
  clearTokenCache(): void {
    this.tokenCache = null;
    console.error('[Auth] Token cache cleared');
  }
}
