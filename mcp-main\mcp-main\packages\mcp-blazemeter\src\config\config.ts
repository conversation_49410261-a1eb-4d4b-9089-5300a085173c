import dotenv from "dotenv";
import { dirname, resolve } from "path";
import { fileURLToPath } from "url";

const __dirname = dirname(fileURLToPath(import.meta.url));
dotenv.config({ path: resolve(__dirname, "../../.env") });

interface Config {
  blazeMeterApiKey: string;
  blazeMeterApiSecret: string;
  blazeMeterApiUrl: string;
}

const config: Config = {
  blazeMeterApiKey: process.env.BLAZEMETER_API_KEY || "",
  blazeMeterApiSecret: process.env.BLAZEMETER_API_SECRET || "",
  blazeMeterApiUrl:
    process.env.BLAZEMETER_API_URL || "https://a.blazemeter.com/api/v4",
};

if (!config.blazeMeterApiKey || !config.blazeMeterApiSecret) {
  throw new Error(
    "BLAZEMETER_API_KEY and BLAZEMETER_API_SECRET environment variables are required",
  );
}

export default config;
