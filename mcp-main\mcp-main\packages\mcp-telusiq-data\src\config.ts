declare const process: {
  env: {
    [key: string]: string | undefined;
  };
};

export interface DatabaseConfig {
  podName: string;
  namespace: string;
  dbName: string;
  dbUser: string;
  dbPassword: string;
  dbPort: number;
  targetPort: number;
  googleCredentials: string;
  instanceConnectionName: string;
}

export function getConfig(): DatabaseConfig {
  const requiredEnvVars = [
    'POD_NAME',
    'NAMESPACE',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD',
    'DB_PORT',
    'TARGET_PORT',
    'GOOGLE_APPLICATION_CREDENTIALS',
    'INSTANCE_CONNECTION_NAME'
  ];

  const missingVars = requiredEnvVars.filter(varName => typeof process.env[varName] === 'undefined');
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  return {
    podName: process.env.POD_NAME!,
    namespace: process.env.NAMESPACE!,
    dbName: process.env.DB_NAME!,
    dbUser: process.env.DB_USER!,
    dbPassword: process.env.DB_PASSWORD!,
    dbPort: parseInt(process.env.DB_PORT!, 10),
    targetPort: parseInt(process.env.TARGET_PORT!, 10),
    googleCredentials: process.env.GOOGLE_APPLICATION_CREDENTIALS!,
    instanceConnectionName: process.env.INSTANCE_CONNECTION_NAME!
  };
}
