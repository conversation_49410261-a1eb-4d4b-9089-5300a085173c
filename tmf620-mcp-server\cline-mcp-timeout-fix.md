# TMF620 MCP Server Cline Integration Fix

## Problem

The TMF620 MCP server was experiencing a timeout error when run through Cline MCP:

```
MCP error -32001: Request timed out
```

However, the server worked fine when manually started with `node start-server.js`.

## Root Cause Analysis

After investigating the code and configuration, we identified the following issues:

1. **API URL Mismatch**: There was a discrepancy between the API URLs in the `.env` file and the Cline MCP settings:
   - `.env` file: URLs ended with `/productOffering`
   - Cline MCP settings: URLs did not include `/productOffering`

2. This mismatch caused issues with the `getApiUrl` method in `tmf620Api.js`, which has special handling to remove `/productOffering` from the end of URLs to prevent duplication. When the URL in Cline MCP settings didn't have this suffix, the method didn't need to remove it, but the API calls were still constructed with `/productOffering` appended, potentially causing confusion in the API client.

## Solution

We created a script (`update-cline-settings.js`) to synchronize the Cline MCP settings with the `.env` file, ensuring that:

1. The API URLs in the Cline MCP settings match those in the `.env` file, including the `/productOffering` suffix
2. All other environment variables are consistent between the two configurations
3. The timeout setting is set to 120 seconds, as recommended in the previous timeout fix

## Implementation Details

The script makes the following changes to the Cline MCP settings:

1. Updated `DEV_API_URL` from:
   ```
   https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2
   ```
   to:
   ```
   https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2/productOffering
   ```

2. Updated `TEST_API_URL` from:
   ```
   https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2
   ```
   to:
   ```
   https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2/productOffering
   ```

3. Verified that the timeout setting is set to 120 seconds

## Verification

After making these changes:

1. The Cline MCP settings now match the `.env` file
2. The API URLs now include the `/productOffering` suffix, which is properly handled by the `getApiUrl` method in `tmf620Api.js`
3. The timeout setting is set to 120 seconds, giving the API operations enough time to complete

## Next Steps

1. Restart Cline to apply the updated MCP settings
2. Test the TMF620 MCP server tools to verify they work without timeout errors
3. Consider adding validation to the server initialization to check for URL consistency between the `.env` file and the environment variables

## Lessons Learned

1. Configuration consistency is critical for MCP servers, especially when they can be started in multiple ways (manually vs. through Cline MCP)
2. Special URL handling in the code (like removing `/productOffering`) should be clearly documented to prevent configuration mismatches
3. Regular testing of both manual and Cline MCP server startup can help identify configuration discrepancies early
