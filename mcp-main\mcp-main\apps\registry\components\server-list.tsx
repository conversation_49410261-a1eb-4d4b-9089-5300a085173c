import { MCPServer } from "@/types/servers";
import { ServerCard } from "./server-card";
import { useState } from "react";
import { useSearchParams } from "next/navigation";

// Create a separate component for server list
export function ServerList({
  initialServers,
}: {
  initialServers: MCPServer[];
}) {
  const searchParams = useSearchParams();
  const searchQuery = searchParams.get("q") || "";

  const [servers, setServers] = useState<MCPServer[]>(initialServers);

  const filteredServers = servers.filter((server) =>
    server.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {filteredServers.map((server) => (
        <ServerCard key={server.name} server={server} />
      ))}
    </div>
  );
}
