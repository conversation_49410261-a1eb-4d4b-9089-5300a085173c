# @telus/mcp-jira-server

## 0.5.0

### Minor Changes

- aaee6bb: Comments management.

## 0.4.0

### Minor Changes

- 2dba70d: Update of the Jira Cloud MCP to handle creation and updating

## 0.3.0

### Minor Changes

- 244279d: add proxy support capability. fix Jira Cloud fields usage

## 0.2.0

### Minor Changes

- 43696f6: Fix Status in jira_search_issues. Remove console.log. Add new fields in jira_search_issues

## 0.1.1

### Patch Changes

- 710ce6e: chore: update authors in package.json files and add CODEOWNERS file

## 0.1.0

### Minor Changes

- 9a4b87f: Added assignee field to various methods for assigning issues to users. Added resolutiondate field to track the resolution date of issues.

## 0.0.7

### Patch Changes

- 917e00b: Updated README with documentation for cloud-hosted JIRA instances and API token authentication

## 0.0.6

### Patch Changes

- 59db7fd: new guidance for MCP install

## 0.0.5

### Patch Changes

- 62ac2a7: Align Package Versions + Bump Octokit for Security

## 0.0.4

### Patch Changes

- 2758279: testing changesets
