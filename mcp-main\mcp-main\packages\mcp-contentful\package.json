{"name": "@telus/mcp-contentful", "version": "0.1.0", "description": "Contentful", "keywords": ["telus", "mcp", "contentful"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-contentful"}, "license": "MIT", "author": "<PERSON> (@kyletsang)", "type": "module", "main": "dist/index.js", "bin": {"mcp-contentful": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "dev": "concurrently --names tsc,server \"tsc -w\" \"node --watch --env-file=.env dist/index.js\"", "prepare": "pnpm run build", "start": "node --env-file=.env dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "contentful": "^11.4.4"}, "devDependencies": {"@types/node": "^22.13.10", "concurrently": "^9.1.2", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}