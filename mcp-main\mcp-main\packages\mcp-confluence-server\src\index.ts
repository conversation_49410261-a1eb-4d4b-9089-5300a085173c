#!/usr/bin/env node

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  ErrorCode,
  McpError,
} from "@modelcontextprotocol/sdk/types.js";
import axios, { AxiosInstance } from "axios";
import tunnel from "tunnel";
import https from "https";

// Configuration handling
interface ConfluenceConfig {
  url: string;
  username?: string;
  apiToken?: string;
  sslVerify: boolean;
  spacesFilter?: string;
}

// Centralized logging function with security measures
const logger = {
  debug: (message: string, data?: any) => {
    if (process.env.DEBUG === 'true') {
      // Safely log objects by redacting sensitive fields
      if (data) {
        const safeData = redactSensitiveInfo(data);
        console.error('[DEBUG]', message, safeData);
      } else {
        console.error('[DEBUG]', message);
      }
    }
  },
  info: (message: string, data?: any) => {
    if (data) {
      const safeData = redactSensitiveInfo(data);
      console.error('[INFO]', message, safeData);
    } else {
      console.error('[INFO]', message);
    }
  },
  warn: (message: string, data?: any) => {
    if (data) {
      const safeData = redactSensitiveInfo(data);
      console.error('[WARN]', message, safeData);
    } else {
      console.error('[WARN]', message);
    }
  },
  error: (message: string, data?: any) => {
    if (data) {
      const safeData = redactSensitiveInfo(data);
      console.error('[ERROR]', message, safeData);
    } else {
      console.error('[ERROR]', message);
    }
  },
  api: (method: string, url: string) => console.error(`[API] ${method} ${url}`)
};

// Helper function to redact sensitive information from logs
function redactSensitiveInfo(obj: any): any {
  if (!obj) return obj;
  
  // Create a deep copy to avoid modifying the original object
  const result = JSON.parse(JSON.stringify(obj));
  
  // List of sensitive field names to redact (case-insensitive)
  const sensitiveFields = [
    'password', 'token', 'apiToken', 'auth', 'authorization', 'key', 'secret', 
    'credential', 'credentials', 'apiKey'
  ];
  
  // Recursively redact sensitive fields
  function redact(object: any) {
    if (!object || typeof object !== 'object') return;
    
    Object.keys(object).forEach(key => {
      const lowerKey = key.toLowerCase();
      
      // Check if this is a sensitive field
      if (sensitiveFields.some(field => lowerKey.includes(field))) {
        if (typeof object[key] === 'string') {
          object[key] = '[REDACTED]';
        } else if (typeof object[key] === 'number') {
          object[key] = -1; // Replace with dummy value
        } else if (typeof object[key] === 'boolean') {
          // Keep boolean values as they usually indicate presence, not the value itself
        } else if (object[key] !== null) {
          object[key] = '[REDACTED]';
        }
      } else if (typeof object[key] === 'object' && object[key] !== null) {
        // Recursively process nested objects
        redact(object[key]);
      }
    });
  }
  
  redact(result);
  return result;
}

function isCloudInstance(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.host.endsWith('.atlassian.net');
  } catch (error) {
    logger.error('Invalid URL provided to isCloudInstance:', error);
    return false;
  }
}

function loadConfig(): ConfluenceConfig {
  const url = process.env.CONFLUENCE_BASE_URL;
  if (!url) {
    throw new Error("Missing required CONFLUENCE_BASE_URL environment variable");
  }

  // For cloud instances, the API endpoint is different from the web UI URL
  let baseUrl = url;
  if (isCloudInstance(url)) {
    // Convert web UI URL to API URL
    baseUrl = url.replace('/wiki', '');
    if (!baseUrl.endsWith('.atlassian.net')) {
      baseUrl = baseUrl.split('.atlassian.net')[0] + '.atlassian.net';
    }
  } else {
    // For server instances, remove trailing /wiki if present
    baseUrl = url.endsWith('/wiki') ? url.slice(0, -5) : url;
  }

  // Log URL transformation
  logger.debug('URL transformation:', {
    originalUrl: url,
    transformedUrl: baseUrl
  });

  const isCloud = isCloudInstance(baseUrl);
  const username = process.env.CONFLUENCE_USERNAME;
  // Try both CONFLUENCE_TOKEN and CONFLUENCE_PASSWORD
  const apiToken = process.env.CONFLUENCE_TOKEN || process.env.CONFLUENCE_PASSWORD;
  const sslVerify = process.env.CONFLUENCE_SSL_VERIFY?.toLowerCase() !== 'false';

  if (isCloud) {
    if (!username || !apiToken) {
      throw new Error("Cloud authentication requires CONFLUENCE_USERNAME and CONFLUENCE_TOKEN/CONFLUENCE_PASSWORD");
    }
  }

  // Log non-sensitive configuration details only
  logger.debug('Confluence Config initialized', {
    baseUrl,
    isCloud,
    sslVerify,
    hasCredentials: !!(username && apiToken)
  });

  return {
    url: baseUrl,
    username,
    apiToken,
    sslVerify
  };
}

const config = loadConfig();

// Log only non-sensitive configuration details
logger.debug('Confluence API configuration initialized', {
  baseUrl: config.url,
  hasCredentials: !!(config.username && config.apiToken)
});

// Create Axios instance with proper configuration
interface AxiosConfigWithProxy {
  baseURL: string;
  headers: Record<string, string>;
  timeout: number;
  httpsAgent?: any;
}

const axiosConfig: AxiosConfigWithProxy = {
  baseURL: config.url,
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  },
  timeout: 15000
};

// Configure authentication
if (isCloudInstance(config.url)) {
  // For cloud instances, use Basic Auth with base64 encoded credentials
  const auth = Buffer.from(`${config.username}:${config.apiToken}`).toString('base64');
  axiosConfig.headers.Authorization = `Basic ${auth}`;
} else {
  // For server instances, use Bearer token
  axiosConfig.headers.Authorization = `Bearer ${config.apiToken}`;
}

// Log Axios configuration
logger.debug('Axios Config:', {
  baseURL: axiosConfig.baseURL,
  hasAuth: !!axiosConfig.headers.Authorization,
  authType: isCloudInstance(config.url) ? 'Basic' : 'Bearer'
});

// Configure SSL verification and proxy if needed
if (process.env.PROXY_HOST && process.env.PROXY_PORT) {
  axiosConfig.httpsAgent = tunnel.httpsOverHttp({
    proxy: {
      host: process.env.PROXY_HOST,
      port: parseInt(process.env.PROXY_PORT, 10)
    }
  });
}

if (!config.sslVerify) {
  throw new Error("Disabling SSL verification is not allowed. Please use trusted certificates or mock servers for testing.");
}

const confluenceApi = axios.create(axiosConfig);

// Add request logging
confluenceApi.interceptors.request.use(request => {
  logger.api(request.method?.toUpperCase() || 'UNKNOWN', request.url || 'unknown');
  return request;
});

class ConfluenceServer {
  private server: Server;

  constructor() {
    this.server = new Server(
      {
        name: "confluence-server",
        version: "0.1.0",
      },
      {
        capabilities: {
          tools: {},
        },
      },
    );

    this.setupToolHandlers();

    this.server.onerror = (error) => logger.error("MCP Error:", error);
    process.on("SIGINT", async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: "confluence_search",
          description: "Search Confluence content using CQL",
          inputSchema: {
            type: "object",
            properties: {
              cql: {
                type: "string",
                description: "Confluence Query Language (CQL) search query",
              },
              limit: {
                type: "number",
                description: "Maximum number of results to return",
                default: 10,
              },
              expand: {
                type: "string",
                description: "Additional fields to expand in the response",
              },
            },
            required: ["cql"],
          },
        },
        {
          name: "confluence_get_page",
          description: "Get a Confluence page by ID",
          inputSchema: {
            type: "object",
            properties: {
              pageId: {
                type: "string",
                description: "Confluence page ID",
              },
              expand: {
                type: "string",
                description: "Additional fields to expand in the response",
              },
            },
            required: ["pageId"],
          },
        },
        {
          name: "confluence_create_page",
          description: "Create a new Confluence page",
          inputSchema: {
            type: "object",
            properties: {
              spaceKey: {
                type: "string",
                description: "Space key where the page will be created",
              },
              title: {
                type: "string",
                description: "Page title",
              },
              content: {
                type: "string",
                description: "Page content in Confluence storage format",
              },
              parentId: {
                type: "string",
                description: "Parent page ID (optional)",
              },
              labels: {
                type: "array",
                items: { type: "string" },
                description: "Labels to add to the page",
              },
            },
            required: ["spaceKey", "title", "content"],
          },
        },
        {
          name: "confluence_update_page",
          description: "Update an existing Confluence page",
          inputSchema: {
            type: "object",
            properties: {
              pageId: {
                type: "string",
                description: "Page ID to update",
              },
              title: {
                type: "string",
                description: "New page title",
              },
              content: {
                type: "string",
                description: "New page content in Confluence storage format",
              },
              version: {
                type: "number",
                description: "Page version number",
              },
              labels: {
                type: "array",
                items: { type: "string" },
                description: "Labels to set on the page",
              },
            },
            required: ["pageId", "title", "content", "version"],
          },
        },
        {
          name: "confluence_delete_page",
          description: "Delete a Confluence page",
          inputSchema: {
            type: "object",
            properties: {
              pageId: {
                type: "string",
                description: "Page ID to delete",
              },
            },
            required: ["pageId"],
          },
        },
        {
          name: "confluence_get_space",
          description: "Get details about a Confluence space",
          inputSchema: {
            type: "object",
            properties: {
              spaceKey: {
                type: "string",
                description: "Space key",
              },
              expand: {
                type: "string",
                description: "Additional fields to expand in the response",
              },
            },
            required: ["spaceKey"],
          },
        },
        {
          name: "confluence_get_space_content",
          description: "Get content in a Confluence space",
          inputSchema: {
            type: "object",
            properties: {
              spaceKey: {
                type: "string",
                description: "Space key",
              },
              type: {
                type: "string",
                description: "Content type (page, blogpost, or comment)",
                enum: ["page", "blogpost", "comment"],
              },
              limit: {
                type: "number",
                description: "Maximum number of results to return",
              },
              expand: {
                type: "string",
                description: "Additional fields to expand in the response",
              },
            },
            required: ["spaceKey"],
          },
        },
        {
          name: "confluence_get_comments",
          description: "Get comments on a Confluence page",
          inputSchema: {
            type: "object",
            properties: {
              pageId: {
                type: "string",
                description: "Page ID",
              },
              limit: {
                type: "number",
                description: "Maximum number of comments to return",
              },
              expand: {
                type: "string",
                description: "Additional fields to expand in the response",
              },
            },
            required: ["pageId"],
          },
        },
        {
          name: "confluence_add_comment",
          description: "Add a comment to a Confluence page",
          inputSchema: {
            type: "object",
            properties: {
              pageId: {
                type: "string",
                description: "Page ID",
              },
              content: {
                type: "string",
                description: "Comment content in Confluence storage format",
              },
            },
            required: ["pageId", "content"],
          },
        },
        {
          name: "confluence_get_attachments",
          description: "Get attachments for a Confluence page",
          inputSchema: {
            type: "object",
            properties: {
              pageId: {
                type: "string",
                description: "Page ID",
              },
              limit: {
                type: "number",
                description: "Maximum number of attachments to return",
                default: 25,
              },
              expand: {
                type: "string",
                description: "Additional fields to expand in the response",
              },
            },
            required: ["pageId"],
          },
        },
        {
          name: "confluence_get_labels",
          description: "Get labels for a Confluence content item",
          inputSchema: {
            type: "object",
            properties: {
              contentId: {
                type: "string",
                description: "Content ID (page, blog post, etc.)",
              },
              prefix: {
                type: "string",
                description: "Filter labels by prefix",
              },
              limit: {
                type: "number",
                description: "Maximum number of labels to return",
                default: 25,
              },
            },
            required: ["contentId"],
          },
        },
        {
          name: "confluence_add_label",
          description: "Add a label to a Confluence content item",
          inputSchema: {
            type: "object",
            properties: {
              contentId: {
                type: "string",
                description: "Content ID (page, blog post, etc.)",
              },
              label: {
                type: "string",
                description: "Label to add",
              },
              prefix: {
                type: "string",
                description: "Label prefix (default: 'global')",
                default: "global",
              },
            },
            required: ["contentId", "label"],
          },
        },
        {
          name: "confluence_remove_label",
          description: "Remove a label from a Confluence content item",
          inputSchema: {
            type: "object",
            properties: {
              contentId: {
                type: "string",
                description: "Content ID (page, blog post, etc.)",
              },
              label: {
                type: "string",
                description: "Label to remove",
              },
              prefix: {
                type: "string",
                description: "Label prefix (default: 'global')",
                default: "global",
              },
            },
            required: ["contentId", "label"],
          },
        },
        {
          name: "confluence_get_content_children",
          description: "Get children of a Confluence content item",
          inputSchema: {
            type: "object",
            properties: {
              contentId: {
                type: "string",
                description: "Content ID (page, blog post, etc.)",
              },
              type: {
                type: "string",
                description: "Child type (page, attachment, comment)",
                enum: ["page", "attachment", "comment"],
              },
              limit: {
                type: "number",
                description: "Maximum number of children to return",
                default: 25,
              },
              expand: {
                type: "string",
                description: "Additional fields to expand in the response",
              },
            },
            required: ["contentId"],
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      try {
        switch (request.params.name) {
          case "confluence_search": {
            const { cql, limit = 10, expand, spacesFilter } = request.params.arguments as {
              cql: string;
              limit?: number;
              expand?: string;
              spacesFilter?: string;
            };

            try {
              let finalCql = cql;
              const filterToUse = spacesFilter || config.spacesFilter;

              if (filterToUse) {
                const spaces = filterToUse.split(',').map((s: string) => s.trim());
                const spaceQuery = spaces.map((space: string) => `space = "${space}"`).join(' OR ');
                finalCql = cql && spaceQuery ? `(${cql}) AND (${spaceQuery})` : spaceQuery;
                logger.debug(`Applied spaces filter to query: ${finalCql}`);
              }

              const response = await confluenceApi.get("/wiki/rest/api/search", {
                params: {
                  cql: finalCql,
                  limit,
                  expand,
                  type: "page"
                },
              });

              // Process the response similar to the Python implementation
              const results = response.data.results.map((result: any) => {
                return {
                  id: result.content.id,
                  type: result.content.type,
                  status: result.content.status,
                  title: result.content.title,
                  excerpt: result.excerpt,
                  url: `${config.url}${result.content._links.webui}`,
                  // Add more fields as needed
                };
              });

              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify({ results, cql: finalCql }, null, 2),
                  },
                ],
              };
            } catch (error) {
              logger.error('Error during Confluence search:', error);
              if (axios.isAxiosError(error) && error.response?.status === 401) {
                throw new Error("Authentication failed for Confluence API. Token may be expired or invalid.");
              }
              throw error;
            }
          }

          case "confluence_get_page": {
            const { pageId, expand = "body.storage,version,space,children.attachment" } = request.params.arguments as {
              pageId: string;
              expand?: string;
            };

            try {
              const response = await confluenceApi.get(`/wiki/rest/api/content/${pageId}`, {
                params: { expand }
              });

              const page = response.data;
              const spaceKey = page.space?.key || "";
              const content = page.body.storage.value;

              // Here you would process the HTML content if needed
              // For now, we'll just use it as is
              const processedContent = content;

              const pageModel = {
                id: page.id,
                type: page.type,
                status: page.status,
                title: page.title,
                spaceKey: spaceKey,
                version: page.version.number,
                content: processedContent,
                url: `${config.url}${page._links.webui}`,
              };

              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify(pageModel, null, 2),
                  },
                ],
              };
            } catch (error) {
              logger.error('Error fetching page:', error);
              if (axios.isAxiosError(error)) {
                if (error.response?.status === 401 || error.response?.status === 403) {
                  throw new Error("Authentication failed for Confluence API. Token may be expired or invalid.");
                }
                return {
                  content: [
                    {
                      type: "text",
                      text: `Error fetching page: ${error.message}\nStatus: ${error.response?.status}\nURL: ${error.config?.url}`,
                    },
                  ],
                  isError: true,
                };
              } else {
                return {
                  content: [
                    {
                      type: "text",
                      text: `Error fetching page: ${(error as Error).message}`,
                    },
                  ],
                  isError: true,
                };
              }
            }
          }

          case "confluence_create_page": {
            const { spaceKey, title, content, parentId, labels } = request.params.arguments as {
              spaceKey: string;
              title: string;
              content: string;
              parentId?: string;
              labels?: string[];
            };

            const pageData: any = {
              type: "page",
              title,
              space: { key: spaceKey },
              body: {
                storage: {
                  value: content,
                  representation: "storage",
                },
              },
            };

            if (parentId) {
              pageData.ancestors = [{ id: parentId }];
            }

            const response = await confluenceApi.post("/wiki/rest/api/content", pageData);

            // Add labels if provided
            if (labels && labels.length > 0) {
              await confluenceApi.post(`/wiki/rest/api/content/${response.data.id}/label`, labels.map(label => ({ prefix: "global", name: label })));
            }

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(response.data, null, 2),
                },
              ],
            };
          }

          case "confluence_update_page": {
            const { pageId, title, content, version, labels } = request.params.arguments as {
              pageId: string;
              title: string;
              content: string;
              version: number;
              labels?: string[];
            };

            const pageData = {
              version: { number: version },
              title,
              type: "page",
              body: {
                storage: {
                  value: content,
                  representation: "storage",
                },
              },
            };

            const response = await confluenceApi.put(`/wiki/rest/api/content/${pageId}`, pageData);

            // Update labels if provided
            if (labels) {
              // First, delete existing labels
              await confluenceApi.delete(`/wiki/rest/api/content/${pageId}/label`);
              
              // Then add new labels
              if (labels.length > 0) {
                await confluenceApi.post(
                  `/wiki/rest/api/content/${pageId}/label`,
                  labels.map(label => ({ prefix: "global", name: label }))
                );
              }
            }

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(response.data, null, 2),
                },
              ],
            };
          }

          case "confluence_delete_page": {
            const { pageId } = request.params.arguments as {
              pageId: string;
            };

            await confluenceApi.delete(`/wiki/rest/api/content/${pageId}`);

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify({ message: "Page deleted successfully" }, null, 2),
                },
              ],
            };
          }

          case "confluence_get_space": {
            const { spaceKey, expand } = request.params.arguments as {
              spaceKey: string;
              expand?: string;
            };

            const response = await confluenceApi.get(`/wiki/rest/api/space/${spaceKey}`, {
              params: { expand },
            });

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(response.data, null, 2),
                },
              ],
            };
          }

          case "confluence_get_space_content": {
            const { spaceKey, type = "page", limit = 25, expand } = request.params.arguments as {
              spaceKey: string;
              type?: "page" | "blogpost" | "comment";
              limit?: number;
              expand?: string;
            };

            const response = await confluenceApi.get(`/wiki/rest/api/space/${spaceKey}/content`, {
              params: { limit, expand },
            });

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(response.data, null, 2),
                },
              ],
            };
          }

          case "confluence_get_comments": {
            const { pageId, limit = 25, expand } = request.params.arguments as {
              pageId: string;
              limit?: number;
              expand?: string;
            };

            const response = await confluenceApi.get(`/wiki/rest/api/content/${pageId}/child/comment`, {
              params: { limit, expand },
            });

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(response.data, null, 2),
                },
              ],
            };
          }

          case "confluence_add_comment": {
            const { pageId, content } = request.params.arguments as {
              pageId: string;
              content: string;
            };

            const commentData = {
              type: "comment",
              container: { id: pageId, type: "page" },
              body: {
                storage: {
                  value: content,
                  representation: "storage",
                },
              },
            };

            const response = await confluenceApi.post(`/wiki/rest/api/content/${pageId}/child/comment`, commentData);

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(response.data, null, 2),
                },
              ],
            };
          }

          case "confluence_get_attachments": {
            const { pageId, limit = 25, expand } = request.params.arguments as {
              pageId: string;
              limit?: number;
              expand?: string;
            };

            const response = await confluenceApi.get(`/wiki/rest/api/content/${pageId}/child/attachment`, {
              params: { limit, expand },
            });

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(response.data, null, 2),
                },
              ],
            };
          }

          case "confluence_get_labels": {
            const { contentId, prefix, limit = 25 } = request.params.arguments as {
              contentId: string;
              prefix?: string;
              limit?: number;
            };

            const params: Record<string, any> = { limit };
            if (prefix) {
              params.prefix = prefix;
            }

            const response = await confluenceApi.get(`/wiki/rest/api/content/${contentId}/label`, {
              params,
            });

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(response.data, null, 2),
                },
              ],
            };
          }

          case "confluence_add_label": {
            const { contentId, label, prefix = "global" } = request.params.arguments as {
              contentId: string;
              label: string;
              prefix?: string;
            };

            const labelData = {
              prefix,
              name: label,
            };

            const response = await confluenceApi.post(`/wiki/rest/api/content/${contentId}/label`, [labelData]);

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(response.data, null, 2),
                },
              ],
            };
          }

          case "confluence_remove_label": {
            const { contentId, label, prefix = "global" } = request.params.arguments as {
              contentId: string;
              label: string;
              prefix?: string;
            };

            await confluenceApi.delete(`/wiki/rest/api/content/${contentId}/label`, {
              params: { name: label, prefix },
            });

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify({ message: `Label '${label}' removed successfully` }, null, 2),
                },
              ],
            };
          }

          case "confluence_get_content_children": {
            const { contentId, type = "page", limit = 25, expand } = request.params.arguments as {
              contentId: string;
              type?: "page" | "attachment" | "comment";
              limit?: number;
              expand?: string;
            };

            const response = await confluenceApi.get(`/wiki/rest/api/content/${contentId}/child/${type}`, {
              params: { limit, expand },
            });

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(response.data, null, 2),
                },
              ],
            };
          }

          default:
            throw new McpError(
              ErrorCode.MethodNotFound,
              `Unknown tool: ${request.params.name}`,
            );
        }
      } catch (error) {
        if (axios.isAxiosError(error)) {
          let errorMessage = error.message;

          if (error.response?.data) {
            if (Array.isArray(error.response.data.errorMessages)) {
              errorMessage = error.response.data.errorMessages.join(", ");
            } else if (typeof error.response.data.message === "string") {
              errorMessage = error.response.data.message;
            } else if (typeof error.response.data === "string") {
              errorMessage = error.response.data;
            }
          }

          return {
            content: [
              {
                type: "text",
                text: `Confluence API Error: ${errorMessage}\nStatus: ${error.response?.status || "unknown"}\nURL: ${error.config?.url || "unknown"}`,
              },
            ],
            isError: true,
          };
        }
        throw error;
      }
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    logger.info("Confluence MCP server running on stdio");
  }
}

const server = new ConfluenceServer();
server.run().catch(err => logger.error('Server error:', err));
