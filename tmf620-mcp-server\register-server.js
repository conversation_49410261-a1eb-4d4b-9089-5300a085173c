#!/usr/bin/env node
const fs = require('fs');
const path = require('path');
const os = require('os');

// Define paths
const mcpConfigDir = path.join(os.homedir(), '.mcp');
const mcpConfigFile = path.join(mcpConfigDir, 'settings.json');
const serverDir = __dirname;
const serverName = 'tmf620-mcp-server';

// Ensure the .mcp directory exists
if (!fs.existsSync(mcpConfigDir)) {
  console.log(`Creating MCP config directory at ${mcpConfigDir}`);
  fs.mkdirSync(mcpConfigDir, { recursive: true });
}

// Load existing config or create a new one
let config = { mcpServers: {} };
if (fs.existsSync(mcpConfigFile)) {
  try {
    const configData = fs.readFileSync(mcpConfigFile, 'utf8');
    config = JSON.parse(configData);
    if (!config.mcpServers) {
      config.mcpServers = {};
    }
  } catch (error) {
    console.error(`Error reading MCP config file: ${error.message}`);
    console.log('Creating a new config file');
  }
}

// Get the absolute path to the start-server.js file
const startServerPath = path.join(serverDir, 'start-server.js');

// Create the server configuration
const serverConfig = {
  command: 'node',
  args: [startServerPath],
  env: {
    // Include any environment variables needed by the server
    // These will be merged with the existing environment variables
    DEV_API_URL: 'https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2/productOffering',
    TEST_API_URL: 'https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2/productOffering',
    TEST_QUOTE_ID: 'a2db6e1a-27ea-419e-a7a5-10575717f8b7',
    PROD_API_URL: 'https://prod-api-url.example.com',
    OAUTH_TOKEN_URL: 'https://apigw-st.telus.com/st/token',
    OAUTH_SCOPE: '4823'
  },
  disabled: false,
  autoApprove: []
};

// Add or update the server in the config
config.mcpServers[serverName] = serverConfig;

// Write the updated config back to the file
try {
  fs.writeFileSync(mcpConfigFile, JSON.stringify(config, null, 2), 'utf8');
  console.log(`Successfully registered ${serverName} in MCP config`);
  console.log(`Server will be started with: node ${startServerPath}`);
  console.log(`Config file: ${mcpConfigFile}`);
  
  // Display the registered server configuration
  console.log('\nRegistered server configuration:');
  console.log(JSON.stringify(config.mcpServers[serverName], null, 2));
  
  console.log('\nTo use this server with MCP, you need to:');
  console.log('1. Restart any running MCP instances');
  console.log('2. Use the following tools in your MCP client:');
  console.log('   - get_product_offering');
  console.log('   - get_product_offering_by_external_id');
  console.log('   - list_product_offerings');
  console.log('   - bulk_retrieve_product_offerings');
  console.log('   - get_catalog');
  console.log('   - list_catalogs');
  
  console.log('\nNOTE: You will need to set OAUTH_CLIENT_ID and OAUTH_CLIENT_SECRET in your environment');
  console.log('or add them to the .env file in the server directory.');
} catch (error) {
  console.error(`Error writing MCP config file: ${error.message}`);
  process.exit(1);
}
