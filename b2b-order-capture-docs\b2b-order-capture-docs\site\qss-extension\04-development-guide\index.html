
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/qss-extension/04-development-guide/">
      
      
        <link rel="prev" href="../03-storage-management/">
      
      
        <link rel="next" href="../../bulk-operation-extension/00-overview/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Development Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#qss-extension-development-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Development Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" checked>
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#ide-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      IDE Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="IDE Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#intellij-idea-setup" class="md-nav__link">
    <span class="md-ellipsis">
      IntelliJ IDEA Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#vs-code-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      VS Code Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-environment-variables" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Variables
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#plugin-development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Development Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Plugin Development Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-process" class="md-nav__link">
    <span class="md-ellipsis">
      Development Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-development-steps" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Development Steps
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#git-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Git Workflow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-strategies" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategies
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Strategies">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing Framework
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Testing
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#deployment-process" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Process
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Deployment Process">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#build-and-package" class="md-nav__link">
    <span class="md-ellipsis">
      Build and Package
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#helm-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Helm Deployment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#production-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Production Deployment
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#debugging-and-troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging and Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Debugging and Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues-and-solutions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues and Solutions
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#troubleshooting-checklist" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting Checklist
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#code-quality-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Code Quality Guidelines
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Guidelines
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#ide-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      IDE Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="IDE Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#intellij-idea-setup" class="md-nav__link">
    <span class="md-ellipsis">
      IntelliJ IDEA Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#vs-code-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      VS Code Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-environment-variables" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Variables
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#plugin-development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Development Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Plugin Development Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-process" class="md-nav__link">
    <span class="md-ellipsis">
      Development Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-development-steps" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Development Steps
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#git-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Git Workflow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-strategies" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategies
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Strategies">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing Framework
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Testing
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#deployment-process" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Process
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Deployment Process">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#build-and-package" class="md-nav__link">
    <span class="md-ellipsis">
      Build and Package
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#helm-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Helm Deployment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#production-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Production Deployment
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#debugging-and-troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging and Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Debugging and Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues-and-solutions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues and Solutions
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#troubleshooting-checklist" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting Checklist
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#code-quality-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Code Quality Guidelines
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Guidelines
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="qss-extension-development-guide">QSS Extension Development Guide<a class="headerlink" href="#qss-extension-development-guide" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#development-environment-setup">Development Environment Setup</a></li>
<li><a href="#plugin-development-workflow">Plugin Development Workflow</a></li>
<li><a href="#testing-strategies">Testing Strategies</a></li>
<li><a href="#deployment-process">Deployment Process</a></li>
<li><a href="#debugging-and-troubleshooting">Debugging and Troubleshooting</a></li>
<li><a href="#best-practices">Best Practices</a></li>
</ul>
<h2 id="development-environment-setup">Development Environment Setup<a class="headerlink" href="#development-environment-setup" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Required Software Versions</span>
Java:<span class="w"> </span>OpenJDK<span class="w"> </span><span class="m">17</span><span class="w"> </span>or<span class="w"> </span>higher
Maven:<span class="w"> </span><span class="m">3</span>.8.1<span class="w"> </span>or<span class="w"> </span>higher
Docker:<span class="w"> </span><span class="m">20</span>.x<span class="w"> </span>or<span class="w"> </span>higher
Kubernetes:<span class="w"> </span><span class="m">1</span>.25+<span class="w"> </span><span class="o">(</span>minikube,<span class="w"> </span>kind,<span class="w"> </span>or<span class="w"> </span>k3s<span class="w"> </span><span class="k">for</span><span class="w"> </span><span class="nb">local</span><span class="w"> </span>development<span class="o">)</span>
Helm:<span class="w"> </span><span class="m">3</span>.x<span class="w"> </span>or<span class="w"> </span>higher
Git:<span class="w"> </span><span class="m">2</span>.x<span class="w"> </span>or<span class="w"> </span>higher
IDE:<span class="w"> </span>IntelliJ<span class="w"> </span>IDEA,<span class="w"> </span>VS<span class="w"> </span>Code,<span class="w"> </span>or<span class="w"> </span>Eclipse<span class="w"> </span><span class="o">(</span>recommended<span class="o">)</span>

<span class="c1"># NetCracker Platform Components</span>
NetCracker<span class="w"> </span>QSS:<span class="w"> </span><span class="m">2023</span>.3.2.1<span class="w"> </span>or<span class="w"> </span>higher
SmartPlug<span class="w"> </span>Framework:<span class="w"> </span><span class="m">0</span>.14.11.2<span class="w"> </span>or<span class="w"> </span>higher
</code></pre></div>
<h3 id="local-development-setup">Local Development Setup<a class="headerlink" href="#local-development-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Clone the QSS extension repository</span>
git<span class="w"> </span>clone<span class="w"> </span>&lt;qss-extension-repository-url&gt;
<span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-qss-extension-b2b

<span class="c1"># 2. Verify prerequisites</span>
java<span class="w"> </span>-version
mvn<span class="w"> </span>-version
docker<span class="w"> </span>--version
kubectl<span class="w"> </span>version<span class="w"> </span>--client
helm<span class="w"> </span>version

<span class="c1"># 3. Set up development environment variables</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">JAVA_HOME</span><span class="o">=</span>/path/to/java17
<span class="nb">export</span><span class="w"> </span><span class="nv">MAVEN_HOME</span><span class="o">=</span>/path/to/maven
<span class="nb">export</span><span class="w"> </span><span class="nv">PATH</span><span class="o">=</span><span class="nv">$JAVA_HOME</span>/bin:<span class="nv">$MAVEN_HOME</span>/bin:<span class="nv">$PATH</span>

<span class="c1"># 4. Configure Maven settings for NetCracker repositories</span>
cp<span class="w"> </span>.m2/settings-template.xml<span class="w"> </span>~/.m2/settings.xml
<span class="c1"># Edit settings.xml with your NetCracker credentials</span>

<span class="c1"># 5. Build the plugin</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile

<span class="c1"># 6. Run tests</span>
mvn<span class="w"> </span><span class="nb">test</span>

<span class="c1"># 7. Package the plugin</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package
</code></pre></div>
<h3 id="ide-configuration">IDE Configuration<a class="headerlink" href="#ide-configuration" title="Permanent link">&para;</a></h3>
<h4 id="intellij-idea-setup">IntelliJ IDEA Setup<a class="headerlink" href="#intellij-idea-setup" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="cm">&lt;!-- .idea/compiler.xml --&gt;</span>
<span class="nt">&lt;component</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;CompilerConfiguration&quot;</span><span class="nt">&gt;</span>
<span class="w">  </span><span class="nt">&lt;annotationProcessing&gt;</span>
<span class="w">    </span><span class="nt">&lt;profile</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Maven default annotation processors profile&quot;</span><span class="w"> </span><span class="na">enabled=</span><span class="s">&quot;true&quot;</span><span class="nt">&gt;</span>
<span class="w">      </span><span class="nt">&lt;sourceOutputDir</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;target/generated-sources/annotations&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">      </span><span class="nt">&lt;sourceTestOutputDir</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;target/generated-test-sources/test-annotations&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">      </span><span class="nt">&lt;outputRelativeToContentRoot</span><span class="w"> </span><span class="na">value=</span><span class="s">&quot;true&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">      </span><span class="nt">&lt;module</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;b2b-qss-plugin&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">    </span><span class="nt">&lt;/profile&gt;</span>
<span class="w">  </span><span class="nt">&lt;/annotationProcessing&gt;</span>
<span class="w">  </span><span class="nt">&lt;bytecodeTargetLevel&gt;</span>
<span class="w">    </span><span class="nt">&lt;module</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;b2b-qss-plugin&quot;</span><span class="w"> </span><span class="na">target=</span><span class="s">&quot;17&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">  </span><span class="nt">&lt;/bytecodeTargetLevel&gt;</span>
<span class="nt">&lt;/component&gt;</span>
</code></pre></div>
<h4 id="vs-code-configuration">VS Code Configuration<a class="headerlink" href="#vs-code-configuration" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// .vscode/settings.json</span>
<span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;java.configuration.updateBuildConfiguration&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;automatic&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;java.compile.nullAnalysis.mode&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;automatic&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;java.format.settings.url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;https://raw.githubusercontent.com/google/styleguide/gh-pages/eclipse-java-google-style.xml&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;java.saveActions.organizeImports&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;maven.executable.path&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/path/to/maven/bin/mvn&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;files.associations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;*.yaml&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;yaml&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;*.yml&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;yaml&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;yaml.schemas&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;https://json.schemastore.org/helm-chart.json&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;deployments/charts/**/Chart.yaml&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;https://json.schemastore.org/helmfile.json&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;deployments/charts/**/values*.yaml&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="development-environment-variables">Development Environment Variables<a class="headerlink" href="#development-environment-variables" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># .env.development</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">QSS_ENVIRONMENT</span><span class="o">=</span><span class="s2">&quot;development&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">QSS_LOG_LEVEL</span><span class="o">=</span><span class="s2">&quot;DEBUG&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">QSS_PLUGIN_HOT_RELOAD</span><span class="o">=</span><span class="s2">&quot;true&quot;</span>

<span class="c1"># NetCracker QSS Configuration</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">QSS_SERVICE_URL</span><span class="o">=</span><span class="s2">&quot;http://localhost:8080&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">QSS_PLUGIN_REGISTRY_URL</span><span class="o">=</span><span class="s2">&quot;http://localhost:8080/plugins&quot;</span>

<span class="c1"># Database Configuration (for local testing)</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">QSS_DB_URL</span><span class="o">=</span><span class="s2">&quot;jdbc:h2:mem:testdb&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">QSS_DB_USERNAME</span><span class="o">=</span><span class="s2">&quot;sa&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">QSS_DB_PASSWORD</span><span class="o">=</span><span class="s2">&quot;&quot;</span>

<span class="c1"># Plugin Configuration</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">TELUS_QSS_SNAPSHOT_GENERATION_ENABLED</span><span class="o">=</span><span class="s2">&quot;true&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">TELUS_QSS_AUDIT_LOGGING_ENABLED</span><span class="o">=</span><span class="s2">&quot;true&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">TELUS_QSS_PERFORMANCE_MONITORING_ENABLED</span><span class="o">=</span><span class="s2">&quot;true&quot;</span>

<span class="c1"># Business Rules Configuration</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">TELUS_QSS_HIGH_VALUE_THRESHOLD</span><span class="o">=</span><span class="s2">&quot;50000.0&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">TELUS_QSS_ENTERPRISE_SEGMENTS</span><span class="o">=</span><span class="s2">&quot;ENTERPRISE,GOVERNMENT,WHOLESALE&quot;</span>

<span class="c1"># Performance Settings (relaxed for development)</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">TELUS_QSS_CACHE_ENABLED</span><span class="o">=</span><span class="s2">&quot;false&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">TELUS_QSS_TIMEOUT</span><span class="o">=</span><span class="s2">&quot;60000&quot;</span>
</code></pre></div>
<h2 id="plugin-development-workflow">Plugin Development Workflow<a class="headerlink" href="#plugin-development-workflow" title="Permanent link">&para;</a></h2>
<h3 id="development-process">Development Process<a class="headerlink" href="#development-process" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;Development Workflow&quot;
        A[Requirements Analysis] --&gt; B[Design Plugin Logic]
        B --&gt; C[Implement Extension Point]
        C --&gt; D[Write Unit Tests]
        D --&gt; E[Integration Testing]
        E --&gt; F[Code Review]
        F --&gt; G[Package Plugin]
        G --&gt; H[Deploy to Dev Environment]
        H --&gt; I[End-to-End Testing]
        I --&gt; J[Production Deployment]
    end

    subgraph &quot;Testing Levels&quot;
        K[Unit Tests]
        L[Integration Tests]
        M[Plugin Tests]
        N[Performance Tests]
    end

    subgraph &quot;Quality Gates&quot;
        O[Code Coverage]
        P[Static Analysis]
        Q[Security Scan]
        R[Performance Benchmark]
    end

    D --&gt; K
    E --&gt; L
    E --&gt; M
    I --&gt; N

    F --&gt; O
    F --&gt; P
    F --&gt; Q
    I --&gt; R

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style C fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style D fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="plugin-development-steps">Plugin Development Steps<a class="headerlink" href="#plugin-development-steps" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Step-by-step plugin development guide</span>
<span class="cm"> */</span>

<span class="c1">// Step 1: Create Extension Point Implementation</span>
<span class="nd">@Slf4j</span>
<span class="nd">@Implementation</span><span class="p">(</span><span class="n">forInterface</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GenerateQuoteSnapshotOnChangeEP</span><span class="p">.</span><span class="na">class</span><span class="p">,</span><span class="w"> </span><span class="n">minAllowedVersion</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;2021.1&quot;</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">MyCustomQSSPlugin</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">GenerateQuoteSnapshotOnChangeEP</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isGenerationEnabled</span><span class="p">(</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implement your business logic here</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">evaluateBusinessRules</span><span class="p">(</span><span class="n">input</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">evaluateBusinessRules</span><span class="p">(</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Your custom logic</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="c1">// Step 2: Add Configuration Support</span>
<span class="nd">@ConfigurationProperties</span><span class="p">(</span><span class="n">prefix</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;telus.qss.custom&quot;</span><span class="p">)</span>
<span class="nd">@Data</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">CustomPluginConfiguration</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">enabled</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">double</span><span class="w"> </span><span class="n">threshold</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">100000.0</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">supportedSegments</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">List</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;ENTERPRISE&quot;</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">// Step 3: Add Metrics and Monitoring</span>
<span class="nd">@Component</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">CustomPluginMetrics</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Counter</span><span class="w"> </span><span class="n">decisionsCounter</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Timer</span><span class="w"> </span><span class="n">decisionTimer</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">CustomPluginMetrics</span><span class="p">(</span><span class="n">MeterRegistry</span><span class="w"> </span><span class="n">meterRegistry</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">decisionsCounter</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Counter</span><span class="p">.</span><span class="na">builder</span><span class="p">(</span><span class="s">&quot;custom.qss.decisions&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">description</span><span class="p">(</span><span class="s">&quot;Number of snapshot decisions made&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">);</span>

<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">decisionTimer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Timer</span><span class="p">.</span><span class="na">builder</span><span class="p">(</span><span class="s">&quot;custom.qss.decision.duration&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">description</span><span class="p">(</span><span class="s">&quot;Time taken to make decisions&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="c1">// Step 4: Add Health Checks</span>
<span class="nd">@Component</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">CustomPluginHealthIndicator</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">HealthIndicator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Health</span><span class="w"> </span><span class="nf">health</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Check plugin health</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isPluginHealthy</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">Health</span><span class="p">.</span><span class="na">up</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">withDetail</span><span class="p">(</span><span class="s">&quot;status&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Plugin is running normally&quot;</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">Health</span><span class="p">.</span><span class="na">down</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">withDetail</span><span class="p">(</span><span class="s">&quot;status&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Plugin has issues&quot;</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isPluginHealthy</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implement health check logic</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="git-workflow">Git Workflow<a class="headerlink" href="#git-workflow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Create feature branch</span>
git<span class="w"> </span>checkout<span class="w"> </span>-b<span class="w"> </span>feature/enhanced-snapshot-logic

<span class="c1"># 2. Make changes to plugin code</span>
<span class="c1"># Edit GenerateQuoteSnapshotOnChangePlugin.java</span>

<span class="c1"># 3. Add comprehensive tests</span>
<span class="c1"># Create/update test files</span>

<span class="c1"># 4. Validate changes</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile
mvn<span class="w"> </span><span class="nb">test</span>
mvn<span class="w"> </span>verify

<span class="c1"># 5. Check code quality</span>
mvn<span class="w"> </span>pmd:check
mvn<span class="w"> </span>jacoco:report

<span class="c1"># 6. Commit changes with descriptive message</span>
git<span class="w"> </span>add<span class="w"> </span>.
git<span class="w"> </span>commit<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;feat: enhance snapshot generation logic</span>

<span class="s2">- Add support for customer segment-based rules</span>
<span class="s2">- Implement high-value quote detection</span>
<span class="s2">- Add comprehensive unit tests</span>
<span class="s2">- Update documentation</span>

<span class="s2">Closes #123&quot;</span>

<span class="c1"># 7. Push and create pull request</span>
git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>feature/enhanced-snapshot-logic
<span class="c1"># Create PR through GitLab interface</span>

<span class="c1"># 8. After review and approval, merge</span>
git<span class="w"> </span>checkout<span class="w"> </span>master
git<span class="w"> </span>pull<span class="w"> </span>origin<span class="w"> </span>master
git<span class="w"> </span>merge<span class="w"> </span>feature/enhanced-snapshot-logic
git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>master
</code></pre></div>
<h2 id="testing-strategies">Testing Strategies<a class="headerlink" href="#testing-strategies" title="Permanent link">&para;</a></h2>
<h3 id="unit-testing-framework">Unit Testing Framework<a class="headerlink" href="#unit-testing-framework" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Comprehensive unit testing for QSS plugin</span>
<span class="cm"> */</span>
<span class="nd">@ExtendWith</span><span class="p">(</span><span class="n">MockitoExtension</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="kd">class</span> <span class="nc">GenerateQuoteSnapshotOnChangePluginTest</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@InjectMocks</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">GenerateQuoteSnapshotOnChangePlugin</span><span class="w"> </span><span class="n">plugin</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Mock</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">QuoteChangeContext</span><span class="w"> </span><span class="n">mockContext</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="nd">@DisplayName</span><span class="p">(</span><span class="s">&quot;Should generate snapshot for prime quote&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldGenerateSnapshotForPrimeQuote</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createQuoteWithPrimeAttribute</span><span class="p">(</span><span class="kc">true</span><span class="p">);</span>
<span class="w">        </span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createInput</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="kt">boolean</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">plugin</span><span class="p">.</span><span class="na">isGenerationEnabled</span><span class="p">(</span><span class="n">input</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">).</span><span class="na">isTrue</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="nd">@DisplayName</span><span class="p">(</span><span class="s">&quot;Should generate snapshot for description change&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldGenerateSnapshotForDescriptionChange</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createQuoteWithDescription</span><span class="p">(</span><span class="s">&quot;Updated description&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createInput</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="kt">boolean</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">plugin</span><span class="p">.</span><span class="na">isGenerationEnabled</span><span class="p">(</span><span class="n">input</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">).</span><span class="na">isTrue</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="nd">@DisplayName</span><span class="p">(</span><span class="s">&quot;Should not generate snapshot for regular quote&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldNotGenerateSnapshotForRegularQuote</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createRegularQuote</span><span class="p">();</span>
<span class="w">        </span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createInput</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="kt">boolean</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">plugin</span><span class="p">.</span><span class="na">isGenerationEnabled</span><span class="p">(</span><span class="n">input</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">).</span><span class="na">isFalse</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@ParameterizedTest</span>
<span class="w">    </span><span class="nd">@ValueSource</span><span class="p">(</span><span class="n">strings</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="s">&quot;true&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;TRUE&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;yes&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;YES&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;1&quot;</span><span class="p">})</span>
<span class="w">    </span><span class="nd">@DisplayName</span><span class="p">(</span><span class="s">&quot;Should handle various prime attribute formats&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldHandleVariousPrimeAttributeFormats</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">primeValue</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createQuoteWithPrimeAttribute</span><span class="p">(</span><span class="n">primeValue</span><span class="p">);</span>
<span class="w">        </span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createInput</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="kt">boolean</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">plugin</span><span class="p">.</span><span class="na">isGenerationEnabled</span><span class="p">(</span><span class="n">input</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">).</span><span class="na">isTrue</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="nd">@DisplayName</span><span class="p">(</span><span class="s">&quot;Should handle null quote gracefully&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldHandleNullQuoteGracefully</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createInputWithNullQuote</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="kt">boolean</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">plugin</span><span class="p">.</span><span class="na">isGenerationEnabled</span><span class="p">(</span><span class="n">input</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">).</span><span class="na">isFalse</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Helper methods</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Quote</span><span class="w"> </span><span class="nf">createQuoteWithPrimeAttribute</span><span class="p">(</span><span class="n">Object</span><span class="w"> </span><span class="n">primeValue</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Quote</span><span class="p">();</span>
<span class="w">        </span><span class="n">quote</span><span class="p">.</span><span class="na">setId</span><span class="p">(</span><span class="s">&quot;test-quote-123&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="n">quote</span><span class="p">.</span><span class="na">setAttributes</span><span class="p">(</span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;isPrime&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">primeValue</span><span class="p">));</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Quote</span><span class="w"> </span><span class="nf">createQuoteWithDescription</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">description</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Quote</span><span class="p">();</span>
<span class="w">        </span><span class="n">quote</span><span class="p">.</span><span class="na">setId</span><span class="p">(</span><span class="s">&quot;test-quote-456&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="n">quote</span><span class="p">.</span><span class="na">setDescription</span><span class="p">(</span><span class="n">description</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Quote</span><span class="w"> </span><span class="nf">createRegularQuote</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Quote</span><span class="p">();</span>
<span class="w">        </span><span class="n">quote</span><span class="p">.</span><span class="na">setId</span><span class="p">(</span><span class="s">&quot;test-quote-789&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="nf">createInput</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="n">delta</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mock</span><span class="p">(</span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>
<span class="w">        </span><span class="n">when</span><span class="p">(</span><span class="n">delta</span><span class="p">.</span><span class="na">getQuoteCmd</span><span class="p">()).</span><span class="na">thenReturn</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">        </span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mock</span><span class="p">(</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>
<span class="w">        </span><span class="n">when</span><span class="p">(</span><span class="n">input</span><span class="p">.</span><span class="na">getDeltaDto</span><span class="p">()).</span><span class="na">thenReturn</span><span class="p">(</span><span class="n">delta</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">input</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="integration-testing">Integration Testing<a class="headerlink" href="#integration-testing" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Integration testing with Spring Boot Test</span>
<span class="cm"> */</span>
<span class="nd">@SpringBootTest</span>
<span class="nd">@TestPropertySource</span><span class="p">(</span><span class="n">properties</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s">&quot;telus.qss.snapshot-generation.enabled=true&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s">&quot;telus.qss.rules.high-value-threshold=50000.0&quot;</span>
<span class="p">})</span>
<span class="kd">class</span> <span class="nc">QSSPluginIntegrationTest</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">GenerateQuoteSnapshotOnChangePlugin</span><span class="w"> </span><span class="n">plugin</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">TestRestTemplate</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="nd">@DisplayName</span><span class="p">(</span><span class="s">&quot;Plugin should be properly configured and functional&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">pluginShouldBeProperlyConfigured</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Test plugin configuration and functionality</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">plugin</span><span class="p">).</span><span class="na">isNotNull</span><span class="p">();</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">plugin</span><span class="p">.</span><span class="na">getPriority</span><span class="p">()).</span><span class="na">isEqualTo</span><span class="p">(</span><span class="mi">200</span><span class="p">);</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">plugin</span><span class="p">.</span><span class="na">isApplicableToTenant</span><span class="p">(</span><span class="s">&quot;telus-b2b&quot;</span><span class="p">)).</span><span class="na">isTrue</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="nd">@DisplayName</span><span class="p">(</span><span class="s">&quot;Should integrate with QSS service correctly&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldIntegrateWithQSSServiceCorrectly</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Test integration with actual QSS service</span>
<span class="w">        </span><span class="c1">// This would require a test QSS instance</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="performance-testing">Performance Testing<a class="headerlink" href="#performance-testing" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Performance testing for plugin operations</span>
<span class="cm"> */</span>
<span class="nd">@ExtendWith</span><span class="p">(</span><span class="n">MockitoExtension</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="kd">class</span> <span class="nc">QSSPluginPerformanceTest</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@InjectMocks</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">GenerateQuoteSnapshotOnChangePlugin</span><span class="w"> </span><span class="n">plugin</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="nd">@DisplayName</span><span class="p">(</span><span class="s">&quot;Plugin should handle high volume of decisions efficiently&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldHandleHighVolumeEfficiently</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">List</span><span class="o">&lt;</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="o">&gt;</span><span class="w"> </span><span class="n">inputs</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createLargeInputSet</span><span class="p">(</span><span class="mi">10000</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="kt">long</span><span class="w"> </span><span class="n">startTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">System</span><span class="p">.</span><span class="na">currentTimeMillis</span><span class="p">();</span>

<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">inputs</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">plugin</span><span class="p">.</span><span class="na">isGenerationEnabled</span><span class="p">(</span><span class="n">input</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="kt">long</span><span class="w"> </span><span class="n">endTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">System</span><span class="p">.</span><span class="na">currentTimeMillis</span><span class="p">();</span>
<span class="w">        </span><span class="kt">long</span><span class="w"> </span><span class="n">duration</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">endTime</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="n">startTime</span><span class="p">;</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">duration</span><span class="p">).</span><span class="na">isLessThan</span><span class="p">(</span><span class="mi">5000</span><span class="p">);</span><span class="w"> </span><span class="c1">// Should complete within 5 seconds</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="nd">@DisplayName</span><span class="p">(</span><span class="s">&quot;Plugin should have consistent performance&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldHaveConsistentPerformance</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Test performance consistency across multiple runs</span>
<span class="w">        </span><span class="n">List</span><span class="o">&lt;</span><span class="n">Long</span><span class="o">&gt;</span><span class="w"> </span><span class="n">durations</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ArrayList</span><span class="o">&lt;&gt;</span><span class="p">();</span>

<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kt">int</span><span class="w"> </span><span class="n">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span><span class="w"> </span><span class="n">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">100</span><span class="p">;</span><span class="w"> </span><span class="n">i</span><span class="o">++</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="kt">long</span><span class="w"> </span><span class="n">startTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">System</span><span class="p">.</span><span class="na">nanoTime</span><span class="p">();</span>
<span class="w">            </span><span class="n">plugin</span><span class="p">.</span><span class="na">isGenerationEnabled</span><span class="p">(</span><span class="n">createTestInput</span><span class="p">());</span>
<span class="w">            </span><span class="kt">long</span><span class="w"> </span><span class="n">endTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">System</span><span class="p">.</span><span class="na">nanoTime</span><span class="p">();</span>
<span class="w">            </span><span class="n">durations</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">endTime</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="n">startTime</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Calculate statistics</span>
<span class="w">        </span><span class="kt">double</span><span class="w"> </span><span class="n">average</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">durations</span><span class="p">.</span><span class="na">stream</span><span class="p">().</span><span class="na">mapToLong</span><span class="p">(</span><span class="n">Long</span><span class="p">::</span><span class="n">longValue</span><span class="p">).</span><span class="na">average</span><span class="p">().</span><span class="na">orElse</span><span class="p">(</span><span class="mf">0.0</span><span class="p">);</span>
<span class="w">        </span><span class="kt">long</span><span class="w"> </span><span class="n">max</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">durations</span><span class="p">.</span><span class="na">stream</span><span class="p">().</span><span class="na">mapToLong</span><span class="p">(</span><span class="n">Long</span><span class="p">::</span><span class="n">longValue</span><span class="p">).</span><span class="na">max</span><span class="p">().</span><span class="na">orElse</span><span class="p">(</span><span class="mi">0</span><span class="n">L</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Assert performance criteria</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">average</span><span class="p">).</span><span class="na">isLessThan</span><span class="p">(</span><span class="mi">1_000_000</span><span class="p">);</span><span class="w"> </span><span class="c1">// Less than 1ms average</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">max</span><span class="p">).</span><span class="na">isLessThan</span><span class="p">(</span><span class="mi">10_000_000</span><span class="p">);</span><span class="w"> </span><span class="c1">// Less than 10ms maximum</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="deployment-process">Deployment Process<a class="headerlink" href="#deployment-process" title="Permanent link">&para;</a></h2>
<h3 id="build-and-package">Build and Package<a class="headerlink" href="#build-and-package" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Build configuration</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile

<span class="c1"># Run all tests</span>
mvn<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Generate test reports</span>
mvn<span class="w"> </span>jacoco:report
mvn<span class="w"> </span>surefire-report:report

<span class="c1"># Static analysis</span>
mvn<span class="w"> </span>pmd:check
mvn<span class="w"> </span>spotbugs:check

<span class="c1"># Package plugin</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package

<span class="c1"># Verify package</span>
mvn<span class="w"> </span>verify

<span class="c1"># Install to local repository</span>
mvn<span class="w"> </span>install
</code></pre></div>
<h3 id="helm-deployment">Helm Deployment<a class="headerlink" href="#helm-deployment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># values-development.yaml</span>
<span class="nt">SERVICE_NAME</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;b2b-qss-plugin&quot;</span>
<span class="nt">DEPLOYMENT_VERSION</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;v1-dev&quot;</span>
<span class="nt">DEFAULT_PLUGIN_TENANTS</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;telus-dev&quot;</span>

<span class="c1"># Development-specific settings</span>
<span class="nt">resources</span><span class="p">:</span>
<span class="w">  </span><span class="nt">requests</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;100m&quot;</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;256Mi&quot;</span>
<span class="w">  </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;500m&quot;</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;512Mi&quot;</span>

<span class="c1"># Enable debug logging</span>
<span class="nt">logging</span><span class="p">:</span>
<span class="w">  </span><span class="nt">level</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DEBUG</span>

<span class="c1"># Plugin configuration</span>
<span class="nt">plugin</span><span class="p">:</span>
<span class="w">  </span><span class="nt">hotReload</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">  </span><span class="nt">metrics</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">  </span><span class="nt">healthCheck</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</code></pre></div>
<div class="highlight"><pre><span></span><code><span class="c1"># Deploy to development environment</span>
helm<span class="w"> </span>upgrade<span class="w"> </span>--install<span class="w"> </span>b2b-qss-plugin-dev<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>deployments/charts/b2b-qss-plugin<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--values<span class="w"> </span>deployments/charts/b2b-qss-plugin/values-development.yaml<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--namespace<span class="w"> </span>qss-extensions-dev<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--create-namespace

<span class="c1"># Verify deployment</span>
kubectl<span class="w"> </span>get<span class="w"> </span>pods<span class="w"> </span>-n<span class="w"> </span>qss-extensions-dev<span class="w"> </span>-l<span class="w"> </span><span class="nv">app</span><span class="o">=</span>b2b-qss-plugin
kubectl<span class="w"> </span>get<span class="w"> </span>configmaps<span class="w"> </span>-n<span class="w"> </span>qss-extensions-dev<span class="w"> </span>-l<span class="w"> </span>config-type<span class="o">=</span>smartplug

<span class="c1"># Check plugin status</span>
kubectl<span class="w"> </span>logs<span class="w"> </span>-n<span class="w"> </span>qss-extensions-dev<span class="w"> </span>deployment/b2b-qss-plugin-dev
</code></pre></div>
<h3 id="production-deployment">Production Deployment<a class="headerlink" href="#production-deployment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Production deployment with additional safety checks</span>
helm<span class="w"> </span>upgrade<span class="w"> </span>--install<span class="w"> </span>b2b-qss-plugin-prod<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>deployments/charts/b2b-qss-plugin<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--values<span class="w"> </span>deployments/charts/b2b-qss-plugin/values-production.yaml<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--namespace<span class="w"> </span>qss-extensions-prod<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--create-namespace<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--wait<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--timeout<span class="o">=</span>300s

<span class="c1"># Verify production deployment</span>
kubectl<span class="w"> </span>get<span class="w"> </span>pods<span class="w"> </span>-n<span class="w"> </span>qss-extensions-prod
kubectl<span class="w"> </span>get<span class="w"> </span>events<span class="w"> </span>-n<span class="w"> </span>qss-extensions-prod<span class="w"> </span>--sort-by<span class="o">=</span><span class="s1">&#39;.lastTimestamp&#39;</span>

<span class="c1"># Run smoke tests</span>
kubectl<span class="w"> </span>run<span class="w"> </span>smoke-test<span class="w"> </span>--image<span class="o">=</span>curlimages/curl<span class="w"> </span>--rm<span class="w"> </span>-it<span class="w"> </span>--restart<span class="o">=</span>Never<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--<span class="w"> </span>curl<span class="w"> </span>-f<span class="w"> </span>http://b2b-qss-plugin-prod.qss-extensions-prod.svc.cluster.local:8080/health
</code></pre></div>
<h2 id="debugging-and-troubleshooting">Debugging and Troubleshooting<a class="headerlink" href="#debugging-and-troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="common-issues-and-solutions">Common Issues and Solutions<a class="headerlink" href="#common-issues-and-solutions" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Debugging utilities for QSS plugin</span>
<span class="cm"> */</span>
<span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QSSPluginDebugger</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Debug plugin execution with detailed logging</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">debugPluginExecution</span><span class="p">(</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;=== QSS Plugin Debug Information ===&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Debug input</span>
<span class="w">        </span><span class="n">debugInput</span><span class="p">(</span><span class="n">input</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Debug quote data</span>
<span class="w">        </span><span class="n">debugQuoteData</span><span class="p">(</span><span class="n">input</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Debug business rules</span>
<span class="w">        </span><span class="n">debugBusinessRules</span><span class="p">(</span><span class="n">input</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;=== End Debug Information ===&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">debugInput</span><span class="p">(</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Input validation:&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;  - Input is null: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">input</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">);</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">input</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;  - Delta DTO is null: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="na">getDeltaDto</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">);</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">input</span><span class="p">.</span><span class="na">getDeltaDto</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;  - Quote CMD is null: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="na">getDeltaDto</span><span class="p">().</span><span class="na">getQuoteCmd</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">debugQuoteData</span><span class="p">(</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">Quote</span><span class="p">)</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="na">getDeltaDto</span><span class="p">().</span><span class="na">getQuoteCmd</span><span class="p">();</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Quote data: null&quot;</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Quote data:&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;  - ID: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;  - Description: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getDescription</span><span class="p">());</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;  - Attributes: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">());</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;  - isPrime attribute: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;isPrime&quot;</span><span class="p">));</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;  - dealValue attribute: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;dealValue&quot;</span><span class="p">));</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;  - customerSegment attribute: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;customerSegment&quot;</span><span class="p">));</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">debugBusinessRules</span><span class="p">(</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">Quote</span><span class="p">)</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="na">getDeltaDto</span><span class="p">().</span><span class="na">getQuoteCmd</span><span class="p">();</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="k">return</span><span class="p">;</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Business rules evaluation:&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;  - Is prime quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">isPrimeQuote</span><span class="p">(</span><span class="n">quote</span><span class="p">));</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;  - Has description: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">hasDescription</span><span class="p">(</span><span class="n">quote</span><span class="p">));</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;  - Is high value: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">isHighValue</span><span class="p">(</span><span class="n">quote</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Helper methods for debugging</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isPrimeQuote</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>
<span class="w">               </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">containsKey</span><span class="p">(</span><span class="s">&quot;isPrime&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">hasDescription</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getDescription</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>
<span class="w">               </span><span class="o">!</span><span class="n">quote</span><span class="p">.</span><span class="na">getDescription</span><span class="p">().</span><span class="na">trim</span><span class="p">().</span><span class="na">isEmpty</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isHighValue</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Implementation for high value check</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="troubleshooting-checklist">Troubleshooting Checklist<a class="headerlink" href="#troubleshooting-checklist" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Check plugin deployment status</span>
kubectl<span class="w"> </span>get<span class="w"> </span>pods<span class="w"> </span>-n<span class="w"> </span>qss-extensions<span class="w"> </span>-l<span class="w"> </span><span class="nv">app</span><span class="o">=</span>b2b-qss-plugin
kubectl<span class="w"> </span>describe<span class="w"> </span>pod<span class="w"> </span>&lt;pod-name&gt;<span class="w"> </span>-n<span class="w"> </span>qss-extensions

<span class="c1"># 2. Check plugin logs</span>
kubectl<span class="w"> </span>logs<span class="w"> </span>-n<span class="w"> </span>qss-extensions<span class="w"> </span>deployment/b2b-qss-plugin<span class="w"> </span>--tail<span class="o">=</span><span class="m">100</span>
kubectl<span class="w"> </span>logs<span class="w"> </span>-n<span class="w"> </span>qss-extensions<span class="w"> </span>deployment/b2b-qss-plugin<span class="w"> </span>--previous

<span class="c1"># 3. Check plugin configuration</span>
kubectl<span class="w"> </span>get<span class="w"> </span>configmap<span class="w"> </span>-n<span class="w"> </span>qss-extensions<span class="w"> </span>-l<span class="w"> </span>config-type<span class="o">=</span>smartplug<span class="w"> </span>-o<span class="w"> </span>yaml

<span class="c1"># 4. Check plugin registration</span>
curl<span class="w"> </span>-X<span class="w"> </span>GET<span class="w"> </span>http://qss-service:8080/plugins/telus-b2b-qss-plugin/status

<span class="c1"># 5. Test plugin functionality</span>
curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span>http://qss-service:8080/plugins/telus-b2b-qss-plugin/test<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/json&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s1">&#39;{&quot;quoteId&quot;: &quot;test-123&quot;, &quot;action&quot;: &quot;snapshot-decision&quot;}&#39;</span>

<span class="c1"># 6. Check metrics</span>
curl<span class="w"> </span>-X<span class="w"> </span>GET<span class="w"> </span>http://qss-service:8080/metrics<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>qss

<span class="c1"># 7. Check health status</span>
curl<span class="w"> </span>-X<span class="w"> </span>GET<span class="w"> </span>http://qss-service:8080/health
</code></pre></div>
<h2 id="best-practices">Best Practices<a class="headerlink" href="#best-practices" title="Permanent link">&para;</a></h2>
<h3 id="code-quality-guidelines">Code Quality Guidelines<a class="headerlink" href="#code-quality-guidelines" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Best practices for QSS plugin development</span>
<span class="cm"> */</span>

<span class="c1">// 1. Always validate input parameters</span>
<span class="nd">@Override</span>
<span class="kd">public</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isGenerationEnabled</span><span class="p">(</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">input</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">warn</span><span class="p">(</span><span class="s">&quot;Input is null, skipping snapshot generation&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">input</span><span class="p">.</span><span class="na">getDeltaDto</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">warn</span><span class="p">(</span><span class="s">&quot;Delta DTO is null, skipping snapshot generation&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">Quote</span><span class="p">)</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="na">getDeltaDto</span><span class="p">().</span><span class="na">getQuoteCmd</span><span class="p">();</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">warn</span><span class="p">(</span><span class="s">&quot;Quote is null, skipping snapshot generation&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">evaluateBusinessRules</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">// 2. Use proper exception handling</span>
<span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">evaluateBusinessRules</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">applyBusinessLogic</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error evaluating business rules for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="c1">// Fail safe - return true to ensure audit trail</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="c1">// 3. Implement comprehensive logging</span>
<span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isPrimeQuote</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Checking if quote {} is prime&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Quote {} has no attributes&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="n">Object</span><span class="w"> </span><span class="n">isPrimeValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;isPrime&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="kt">boolean</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">evaluatePrimeValue</span><span class="p">(</span><span class="n">isPrimeValue</span><span class="p">);</span>

<span class="w">    </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Quote {} prime status: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">(),</span><span class="w"> </span><span class="n">result</span><span class="p">);</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">result</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// 4. Use configuration for business rules</span>
<span class="nd">@Value</span><span class="p">(</span><span class="s">&quot;${telus.qss.rules.high-value-threshold:100000.0}&quot;</span><span class="p">)</span>
<span class="kd">private</span><span class="w"> </span><span class="kt">double</span><span class="w"> </span><span class="n">highValueThreshold</span><span class="p">;</span>

<span class="c1">// 5. Implement proper metrics</span>
<span class="nd">@Autowired</span>
<span class="kd">private</span><span class="w"> </span><span class="n">MeterRegistry</span><span class="w"> </span><span class="n">meterRegistry</span><span class="p">;</span>

<span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">recordDecision</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">decision</span><span class="p">,</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">reason</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="n">meterRegistry</span><span class="p">.</span><span class="na">counter</span><span class="p">(</span><span class="s">&quot;qss.plugin.decisions&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">        </span><span class="s">&quot;decision&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">decision</span><span class="p">,</span><span class="w"> </span>
<span class="w">        </span><span class="s">&quot;reason&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">reason</span><span class="p">).</span><span class="na">increment</span><span class="p">();</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="performance-guidelines">Performance Guidelines<a class="headerlink" href="#performance-guidelines" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Performance optimization guidelines</span>
<span class="cm"> */</span>

<span class="c1">// 1. Cache expensive operations</span>
<span class="nd">@Cacheable</span><span class="p">(</span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;quote-evaluations&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">key</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;#quote.id&quot;</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">evaluateQuote</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">performExpensiveEvaluation</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">// 2. Use lazy evaluation</span>
<span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">shouldGenerateSnapshot</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Check lightweight conditions first</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Check prime status (lightweight)</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isPrimeQuote</span><span class="p">(</span><span class="n">quote</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Only perform expensive checks if needed</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">performExpensiveChecks</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">// 3. Minimize object creation</span>
<span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">IS_PRIME</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;isPrime&quot;</span><span class="p">;</span>
<span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">ENTERPRISE_SEGMENTS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span>
<span class="w">    </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;ENTERPRISE&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;GOVERNMENT&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;WHOLESALE&quot;</span><span class="p">);</span>

<span class="c1">// 4. Use efficient data structures</span>
<span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Predicate</span><span class="o">&lt;</span><span class="n">Quote</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">rulePredicates</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span>
<span class="w">    </span><span class="s">&quot;PRIME&quot;</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">isPrimeQuote</span><span class="p">,</span>
<span class="w">    </span><span class="s">&quot;HIGH_VALUE&quot;</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">isHighValueQuote</span><span class="p">,</span>
<span class="w">    </span><span class="s">&quot;ENTERPRISE&quot;</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">isEnterpriseQuote</span>
<span class="p">);</span>
</code></pre></div>
<hr />
<p>This completes the comprehensive QSS Extension Application documentation. The documentation now includes:</p>
<ol>
<li><strong>Overview</strong> - Architecture, technology stack, and project structure</li>
<li><strong>Plugin Architecture</strong> - Extension points, SmartPlug integration, and lifecycle management</li>
<li><strong>Snapshot Generation</strong> - Business rules engine, prime quote detection, and change analysis</li>
<li><strong>Storage Management</strong> - Storage optimization, lifecycle management, and retention policies</li>
<li><strong>Development Guide</strong> - Setup, workflow, testing, deployment, and best practices</li>
</ol>
<p>The documentation follows the same detailed style as the other TELUS B2B components with comprehensive technical details, Mermaid diagrams, code examples, and practical guidance for developers and operations teams.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>