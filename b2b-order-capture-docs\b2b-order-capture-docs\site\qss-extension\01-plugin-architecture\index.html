
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/qss-extension/01-plugin-architecture/">
      
      
        <link rel="prev" href="../00-overview/">
      
      
        <link rel="next" href="../02-snapshot-generation/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Plugin Architecture - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#qss-plugin-architecture" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Plugin Architecture
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" checked>
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#qss-extension-framework" class="md-nav__link">
    <span class="md-ellipsis">
      QSS Extension Framework
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-execution-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Execution Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#extension-point-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Point Framework
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Extension Point Framework">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#generatequotesnapshotonchangeep-interface" class="md-nav__link">
    <span class="md-ellipsis">
      GenerateQuoteSnapshotOnChangeEP Interface
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-point-input-model" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Point Input Model
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#plugin-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Implementation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Plugin Implementation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#main-plugin-class" class="md-nav__link">
    <span class="md-ellipsis">
      Main Plugin Class
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#enhanced-plugin-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Enhanced Plugin Implementation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#smartplug-integration" class="md-nav__link">
    <span class="md-ellipsis">
      SmartPlug Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="SmartPlug Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-descriptor" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Descriptor
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-registration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Registration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-properties" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Properties
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-specific-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment-Specific Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#lifecycle-management" class="md-nav__link">
    <span class="md-ellipsis">
      Lifecycle Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Lifecycle Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-lifecycle-events" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Lifecycle Events
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#hot-deployment-support" class="md-nav__link">
    <span class="md-ellipsis">
      Hot Deployment Support
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#qss-extension-framework" class="md-nav__link">
    <span class="md-ellipsis">
      QSS Extension Framework
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-execution-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Execution Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#extension-point-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Point Framework
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Extension Point Framework">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#generatequotesnapshotonchangeep-interface" class="md-nav__link">
    <span class="md-ellipsis">
      GenerateQuoteSnapshotOnChangeEP Interface
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-point-input-model" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Point Input Model
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#plugin-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Implementation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Plugin Implementation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#main-plugin-class" class="md-nav__link">
    <span class="md-ellipsis">
      Main Plugin Class
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#enhanced-plugin-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Enhanced Plugin Implementation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#smartplug-integration" class="md-nav__link">
    <span class="md-ellipsis">
      SmartPlug Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="SmartPlug Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-descriptor" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Descriptor
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-registration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Registration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-properties" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Properties
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-specific-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment-Specific Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#lifecycle-management" class="md-nav__link">
    <span class="md-ellipsis">
      Lifecycle Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Lifecycle Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-lifecycle-events" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Lifecycle Events
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#hot-deployment-support" class="md-nav__link">
    <span class="md-ellipsis">
      Hot Deployment Support
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="qss-plugin-architecture">QSS Plugin Architecture<a class="headerlink" href="#qss-plugin-architecture" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#architecture-overview">Architecture Overview</a></li>
<li><a href="#extension-point-framework">Extension Point Framework</a></li>
<li><a href="#plugin-implementation">Plugin Implementation</a></li>
<li><a href="#smartplug-integration">SmartPlug Integration</a></li>
<li><a href="#configuration-management">Configuration Management</a></li>
<li><a href="#lifecycle-management">Lifecycle Management</a></li>
</ul>
<h2 id="architecture-overview">Architecture Overview<a class="headerlink" href="#architecture-overview" title="Permanent link">&para;</a></h2>
<h3 id="qss-extension-framework">QSS Extension Framework<a class="headerlink" href="#qss-extension-framework" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;NetCracker QSS Platform&quot;
        A[Quote Storage Service]
        B[Extension Registry]
        C[Plugin Manager]
        D[Configuration Service]
    end

    subgraph &quot;SmartPlug Framework&quot;
        E[Plugin Loader]
        F[Dependency Injection]
        G[Lifecycle Manager]
        H[Hot Deployment]
    end

    subgraph &quot;TELUS QSS Extension&quot;
        I[GenerateQuoteSnapshotOnChangePlugin]
        J[Business Logic]
        K[Configuration]
        L[Monitoring]
    end

    subgraph &quot;Extension Points&quot;
        M[GenerateQuoteSnapshotOnChangeEP]
        N[Quote Storage Events]
        O[Snapshot Generation]
        P[Audit Logging]
    end

    A --&gt; B
    B --&gt; C
    C --&gt; D

    C --&gt; E
    E --&gt; F
    F --&gt; G
    G --&gt; H

    H --&gt; I
    I --&gt; J
    I --&gt; K
    I --&gt; L

    I --&gt; M
    J --&gt; N
    J --&gt; O
    L --&gt; P

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style I fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style M fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="plugin-execution-flow">Plugin Execution Flow<a class="headerlink" href="#plugin-execution-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant QSS as Quote Storage Service
    participant Registry as Extension Registry
    participant Plugin as QSS Extension Plugin
    participant Logic as Business Logic
    participant Storage as Quote Storage

    QSS-&gt;&gt;Registry: Quote Change Event
    Registry-&gt;&gt;Plugin: Invoke Extension Point
    Plugin-&gt;&gt;Logic: Evaluate Snapshot Generation

    Logic-&gt;&gt;Logic: Check isPrime Attribute
    Logic-&gt;&gt;Logic: Check Description Changes
    Logic-&gt;&gt;Logic: Apply Business Rules

    alt Snapshot Required
        Logic--&gt;&gt;Plugin: Generate Snapshot = true
        Plugin--&gt;&gt;Registry: Return true
        Registry--&gt;&gt;QSS: Snapshot Generation Enabled
        QSS-&gt;&gt;Storage: Create Quote Snapshot
        Storage--&gt;&gt;QSS: Snapshot Created
    else No Snapshot Required
        Logic--&gt;&gt;Plugin: Generate Snapshot = false
        Plugin--&gt;&gt;Registry: Return false
        Registry--&gt;&gt;QSS: Snapshot Generation Disabled
    end
</code></pre></div>
<h2 id="extension-point-framework">Extension Point Framework<a class="headerlink" href="#extension-point-framework" title="Permanent link">&para;</a></h2>
<h3 id="generatequotesnapshotonchangeep-interface">GenerateQuoteSnapshotOnChangeEP Interface<a class="headerlink" href="#generatequotesnapshotonchangeep-interface" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Extension point for controlling quote snapshot generation</span>
<span class="cm"> * on quote changes in the Quote Storage Service</span>
<span class="cm"> */</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">interface</span> <span class="nc">GenerateQuoteSnapshotOnChangeEP</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Determines whether a quote snapshot should be generated</span>
<span class="cm">     * based on the quote change context</span>
<span class="cm">     * </span>
<span class="cm">     * @param input Quote change input containing delta information</span>
<span class="cm">     * @return true if snapshot generation is enabled, false otherwise</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isGenerationEnabled</span><span class="p">(</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="p">);</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Get the priority of this extension implementation</span>
<span class="cm">     * Higher values indicate higher priority</span>
<span class="cm">     * </span>
<span class="cm">     * @return Priority value (default: 100)</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="k">default</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="nf">getPriority</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="mi">100</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Check if this extension applies to the given tenant</span>
<span class="cm">     * </span>
<span class="cm">     * @param tenantId Tenant identifier</span>
<span class="cm">     * @return true if applicable to the tenant</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="k">default</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isApplicableToTenant</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">tenantId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="extension-point-input-model">Extension Point Input Model<a class="headerlink" href="#extension-point-input-model" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Input model for quote snapshot generation extension point</span>
<span class="cm"> */</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Quote delta containing the changes made to the quote</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="n">deltaDto</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Context information about the quote change operation</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">QuoteChangeContext</span><span class="w"> </span><span class="n">context</span><span class="p">;</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Tenant information for multi-tenant support</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">TenantContext</span><span class="w"> </span><span class="n">tenantContext</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">GenerateQuoteSnapshotOnChangeEPInput</span><span class="p">(</span><span class="n">QuoteDelta</span><span class="w"> </span><span class="n">deltaDto</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                               </span><span class="n">QuoteChangeContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span>
<span class="w">                                               </span><span class="n">TenantContext</span><span class="w"> </span><span class="n">tenantContext</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">deltaDto</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">deltaDto</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">context</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">tenantContext</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">tenantContext</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Getters and utility methods</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="nf">getDeltaDto</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="n">deltaDto</span><span class="p">;</span><span class="w"> </span><span class="p">}</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteChangeContext</span><span class="w"> </span><span class="nf">getContext</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="n">context</span><span class="p">;</span><span class="w"> </span><span class="p">}</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">TenantContext</span><span class="w"> </span><span class="nf">getTenantContext</span><span class="p">()</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="n">tenantContext</span><span class="p">;</span><span class="w"> </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Get the quote command from the delta</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Quote</span><span class="w"> </span><span class="nf">getQuoteCmd</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="p">(</span><span class="n">Quote</span><span class="p">)</span><span class="w"> </span><span class="n">deltaDto</span><span class="p">.</span><span class="na">getQuoteCmd</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Check if specific attribute was changed</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isAttributeChanged</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">attributeName</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getQuoteCmd</span><span class="p">();</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>
<span class="w">               </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>
<span class="w">               </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">containsKey</span><span class="p">(</span><span class="n">attributeName</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Check if quote description was changed</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isDescriptionChanged</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getQuoteCmd</span><span class="p">();</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">Objects</span><span class="p">.</span><span class="na">nonNull</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getDescription</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="plugin-implementation">Plugin Implementation<a class="headerlink" href="#plugin-implementation" title="Permanent link">&para;</a></h2>
<h3 id="main-plugin-class">Main Plugin Class<a class="headerlink" href="#main-plugin-class" title="Permanent link">&para;</a></h3>
<p><augment_code_snippet path="nc-cloud-bss-oc-ui-qss-extension-b2b/b2b-qss-plugin/src/main/java/com/netcracker/solutions/telus/qss/extension/plugin/GenerateQuoteSnapshotOnChangePlugin.java" mode="EXCERPT">
<div class="highlight"><pre><span></span><code><span class="kn">package</span><span class="w"> </span><span class="nn">com.netcracker.solutions.telus.qss.extension.plugin</span><span class="p">;</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">com.netcracker.cloud.crm.quote.storage.extension.api.GenerateQuoteSnapshotOnChangeEP</span><span class="p">;</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">com.netcracker.cloud.crm.quote.storage.extension.api.model.GenerateQuoteSnapshotOnChangeEPInput</span><span class="p">;</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">com.netcracker.cloud.crm.quote.storage.qes.model.write.quote.Quote</span><span class="p">;</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">com.netcracker.smartplug.annotations.Implementation</span><span class="p">;</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">lombok.extern.slf4j.Slf4j</span><span class="p">;</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">java.util.Objects</span><span class="p">;</span>

<span class="cm">/**</span>
<span class="cm"> * Need to force snapshot generation if quote.attributes.IsPrime is changed.</span>
<span class="cm"> */</span>
<span class="nd">@Slf4j</span>
<span class="nd">@Implementation</span><span class="p">(</span><span class="n">forInterface</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GenerateQuoteSnapshotOnChangeEP</span><span class="p">.</span><span class="na">class</span><span class="p">,</span><span class="w"> </span><span class="n">minAllowedVersion</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;2021.1&quot;</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">GenerateQuoteSnapshotOnChangePlugin</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">GenerateQuoteSnapshotOnChangeEP</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">IS_PRIME</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;isPrime&quot;</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isGenerationEnabled</span><span class="p">(</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">generateQuoteSnapshotOnChangeEPInput</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">Quote</span><span class="p">)</span><span class="w"> </span><span class="n">generateQuoteSnapshotOnChangeEPInput</span><span class="p">.</span><span class="na">getDeltaDto</span><span class="p">().</span><span class="na">getQuoteCmd</span><span class="p">();</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span>
<span class="w">                </span><span class="p">((</span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">containsKey</span><span class="p">(</span><span class="n">IS_PRIME</span><span class="p">))</span><span class="w"> </span><span class="o">||</span>
<span class="w">                        </span><span class="p">(</span><span class="n">Objects</span><span class="p">.</span><span class="na">nonNull</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getDescription</span><span class="p">())));</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
</augment_code_snippet></p>
<h3 id="enhanced-plugin-implementation">Enhanced Plugin Implementation<a class="headerlink" href="#enhanced-plugin-implementation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Enhanced TELUS QSS Extension Plugin with comprehensive business logic</span>
<span class="cm"> */</span>
<span class="nd">@Slf4j</span>
<span class="nd">@Implementation</span><span class="p">(</span><span class="n">forInterface</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GenerateQuoteSnapshotOnChangeEP</span><span class="p">.</span><span class="na">class</span><span class="p">,</span><span class="w"> </span><span class="n">minAllowedVersion</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;2021.1&quot;</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">GenerateQuoteSnapshotOnChangePlugin</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">GenerateQuoteSnapshotOnChangeEP</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="c1">// Business rule constants</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">IS_PRIME</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;isPrime&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">QUOTE_TYPE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;quoteType&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">CUSTOMER_SEGMENT</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;customerSegment&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">DEAL_VALUE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;dealValue&quot;</span><span class="p">;</span>

<span class="w">    </span><span class="c1">// Configuration constants</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">double</span><span class="w"> </span><span class="n">HIGH_VALUE_THRESHOLD</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">100000.0</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">ENTERPRISE_SEGMENTS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;ENTERPRISE&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;GOVERNMENT&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;WHOLESALE&quot;</span><span class="p">);</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isGenerationEnabled</span><span class="p">(</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Evaluating snapshot generation for quote change&quot;</span><span class="p">);</span>

<span class="w">            </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractQuote</span><span class="p">(</span><span class="n">input</span><span class="p">);</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">warn</span><span class="p">(</span><span class="s">&quot;Quote is null, skipping snapshot generation&quot;</span><span class="p">);</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">            </span><span class="c1">// Apply business rules for snapshot generation</span>
<span class="w">            </span><span class="kt">boolean</span><span class="w"> </span><span class="n">shouldGenerate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">evaluateSnapshotRules</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">input</span><span class="p">);</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Snapshot generation decision: {} for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                    </span><span class="n">shouldGenerate</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getId</span><span class="p">());</span>

<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">shouldGenerate</span><span class="p">;</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error evaluating snapshot generation&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="c1">// Fail safe - generate snapshot on error to ensure audit trail</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Extract quote from input with null safety</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Quote</span><span class="w"> </span><span class="nf">extractQuote</span><span class="p">(</span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">input</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="na">getDeltaDto</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="p">(</span><span class="n">Quote</span><span class="p">)</span><span class="w"> </span><span class="n">input</span><span class="p">.</span><span class="na">getDeltaDto</span><span class="p">().</span><span class="na">getQuoteCmd</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Evaluate business rules for snapshot generation</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">evaluateSnapshotRules</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">GenerateQuoteSnapshotOnChangeEPInput</span><span class="w"> </span><span class="n">input</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Rule 1: Always generate snapshot for prime quotes</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isPrimeQuote</span><span class="p">(</span><span class="n">quote</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Prime quote detected, generating snapshot&quot;</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Rule 2: Generate snapshot for description changes</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">hasDescriptionChanged</span><span class="p">(</span><span class="n">quote</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Quote description changed, generating snapshot&quot;</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Rule 3: Generate snapshot for high-value quotes</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isHighValueQuote</span><span class="p">(</span><span class="n">quote</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;High-value quote detected, generating snapshot&quot;</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Rule 4: Generate snapshot for enterprise customer segments</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isEnterpriseCustomer</span><span class="p">(</span><span class="n">quote</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Enterprise customer quote, generating snapshot&quot;</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Rule 5: Generate snapshot for specific quote types</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isSpecialQuoteType</span><span class="p">(</span><span class="n">quote</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Special quote type detected, generating snapshot&quot;</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;No snapshot generation rules matched&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Check if quote is marked as prime</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isPrimeQuote</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">Object</span><span class="w"> </span><span class="n">isPrimeValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="n">IS_PRIME</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">isPrimeValue</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>
<span class="w">               </span><span class="p">(</span><span class="s">&quot;true&quot;</span><span class="p">.</span><span class="na">equalsIgnoreCase</span><span class="p">(</span><span class="n">isPrimeValue</span><span class="p">.</span><span class="na">toString</span><span class="p">())</span><span class="w"> </span><span class="o">||</span><span class="w"> </span>
<span class="w">                </span><span class="n">Boolean</span><span class="p">.</span><span class="na">TRUE</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">isPrimeValue</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Check if quote description has changed</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">hasDescriptionChanged</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">Objects</span><span class="p">.</span><span class="na">nonNull</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getDescription</span><span class="p">())</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>
<span class="w">               </span><span class="o">!</span><span class="n">quote</span><span class="p">.</span><span class="na">getDescription</span><span class="p">().</span><span class="na">trim</span><span class="p">().</span><span class="na">isEmpty</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Check if quote is high-value based on deal amount</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isHighValueQuote</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">Object</span><span class="w"> </span><span class="n">dealValueObj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="n">DEAL_VALUE</span><span class="p">);</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">dealValueObj</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="kt">double</span><span class="w"> </span><span class="n">dealValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Double</span><span class="p">.</span><span class="na">parseDouble</span><span class="p">(</span><span class="n">dealValueObj</span><span class="p">.</span><span class="na">toString</span><span class="p">());</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">dealValue</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="n">HIGH_VALUE_THRESHOLD</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">NumberFormatException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">warn</span><span class="p">(</span><span class="s">&quot;Invalid deal value format: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">dealValueObj</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Check if quote is for enterprise customer segment</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isEnterpriseCustomer</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">Object</span><span class="w"> </span><span class="n">segmentObj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="n">CUSTOMER_SEGMENT</span><span class="p">);</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">segmentObj</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">segment</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">segmentObj</span><span class="p">.</span><span class="na">toString</span><span class="p">().</span><span class="na">toUpperCase</span><span class="p">();</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ENTERPRISE_SEGMENTS</span><span class="p">.</span><span class="na">contains</span><span class="p">(</span><span class="n">segment</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Check if quote is of special type requiring snapshot</span>
<span class="cm">     */</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isSpecialQuoteType</span><span class="p">(</span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">Object</span><span class="w"> </span><span class="n">quoteTypeObj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getAttributes</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="n">QUOTE_TYPE</span><span class="p">);</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quoteTypeObj</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">quoteType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quoteTypeObj</span><span class="p">.</span><span class="na">toString</span><span class="p">().</span><span class="na">toUpperCase</span><span class="p">();</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="s">&quot;COMPLEX_SOLUTION&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;CUSTOM_PRICING&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;GOVERNMENT_CONTRACT&quot;</span><span class="p">)</span>
<span class="w">                  </span><span class="p">.</span><span class="na">contains</span><span class="p">(</span><span class="n">quoteType</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="nf">getPriority</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="mi">200</span><span class="p">;</span><span class="w"> </span><span class="c1">// Higher priority than default</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isApplicableToTenant</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">tenantId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Apply to all TELUS tenants</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">tenantId</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>
<span class="w">               </span><span class="p">(</span><span class="n">tenantId</span><span class="p">.</span><span class="na">toLowerCase</span><span class="p">().</span><span class="na">contains</span><span class="p">(</span><span class="s">&quot;telus&quot;</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s">&quot;all&quot;</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">tenantId</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="smartplug-integration">SmartPlug Integration<a class="headerlink" href="#smartplug-integration" title="Permanent link">&para;</a></h2>
<h3 id="plugin-descriptor">Plugin Descriptor<a class="headerlink" href="#plugin-descriptor" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * SmartPlug plugin descriptor for QSS extension</span>
<span class="cm"> */</span>
<span class="nd">@Configuration</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusQSSPluginDescriptor</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="nd">@Primary</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">PluginMetadata</span><span class="w"> </span><span class="nf">pluginMetadata</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">PluginMetadata</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">name</span><span class="p">(</span><span class="s">&quot;telus-b2b-qss-plugin&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">version</span><span class="p">(</span><span class="s">&quot;1.2.0&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">description</span><span class="p">(</span><span class="s">&quot;TELUS B2B Quote Storage Service Extensions&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">vendor</span><span class="p">(</span><span class="s">&quot;TELUS&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">minPlatformVersion</span><span class="p">(</span><span class="s">&quot;2021.1&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">supportedExtensionPoints</span><span class="p">(</span><span class="n">Arrays</span><span class="p">.</span><span class="na">asList</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;quote.snapshot.generation&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;quote.storage.optimization&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;quote.audit.logging&quot;</span>
<span class="w">            </span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">PluginConfiguration</span><span class="w"> </span><span class="nf">pluginConfiguration</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">PluginConfiguration</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">enableHotReload</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">enableMetrics</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">enableHealthCheck</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">configurationSource</span><span class="p">(</span><span class="s">&quot;classpath:qss-plugin.properties&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">tenantSupport</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">PluginSecurityConfiguration</span><span class="w"> </span><span class="nf">securityConfiguration</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">PluginSecurityConfiguration</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">requireAuthentication</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">allowedRoles</span><span class="p">(</span><span class="n">Arrays</span><span class="p">.</span><span class="na">asList</span><span class="p">(</span><span class="s">&quot;QSS_ADMIN&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;QSS_USER&quot;</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">auditEnabled</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="plugin-registration">Plugin Registration<a class="headerlink" href="#plugin-registration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Plugin registration and component configuration</span>
<span class="cm"> */</span>
<span class="nd">@Configuration</span>
<span class="nd">@ComponentScan</span><span class="p">(</span><span class="n">basePackages</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;com.netcracker.solutions.telus.qss.extension.plugin&quot;</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">PluginComponentConfiguration</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="nd">@ConditionalOnProperty</span><span class="p">(</span><span class="n">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;telus.qss.snapshot-generation.enabled&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">havingValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;true&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">GenerateQuoteSnapshotOnChangePlugin</span><span class="w"> </span><span class="nf">snapshotGenerationPlugin</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">GenerateQuoteSnapshotOnChangePlugin</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">PluginHealthIndicator</span><span class="w"> </span><span class="nf">pluginHealthIndicator</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">PluginHealthIndicator</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">PluginMetricsCollector</span><span class="w"> </span><span class="nf">metricsCollector</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">PluginMetricsCollector</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="configuration-management">Configuration Management<a class="headerlink" href="#configuration-management" title="Permanent link">&para;</a></h2>
<h3 id="plugin-properties">Plugin Properties<a class="headerlink" href="#plugin-properties" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># qss-plugin.properties</span>
<span class="c1"># TELUS QSS Plugin Configuration</span>

<span class="c1"># Plugin identification</span>
<span class="na">telus.qss.plugin.name</span><span class="o">=</span><span class="s">telus-b2b-qss-plugin</span>
<span class="na">telus.qss.plugin.version</span><span class="o">=</span><span class="s">1.2.0</span>
<span class="na">telus.qss.plugin.description</span><span class="o">=</span><span class="s">TELUS B2B Quote Storage Service Extensions</span>

<span class="c1"># Feature flags</span>
<span class="na">telus.qss.snapshot-generation.enabled</span><span class="o">=</span><span class="s">true</span>
<span class="na">telus.qss.audit-logging.enabled</span><span class="o">=</span><span class="s">true</span>
<span class="na">telus.qss.performance-monitoring.enabled</span><span class="o">=</span><span class="s">true</span>

<span class="c1"># Business rules configuration</span>
<span class="na">telus.qss.rules.high-value-threshold</span><span class="o">=</span><span class="s">100000.0</span>
<span class="na">telus.qss.rules.enterprise-segments</span><span class="o">=</span><span class="s">ENTERPRISE,GOVERNMENT,WHOLESALE</span>
<span class="na">telus.qss.rules.special-quote-types</span><span class="o">=</span><span class="s">COMPLEX_SOLUTION,CUSTOM_PRICING,GOVERNMENT_CONTRACT</span>

<span class="c1"># Performance settings</span>
<span class="na">telus.qss.performance.cache-enabled</span><span class="o">=</span><span class="s">true</span>
<span class="na">telus.qss.performance.cache-ttl</span><span class="o">=</span><span class="s">300</span>
<span class="na">telus.qss.performance.batch-size</span><span class="o">=</span><span class="s">100</span>
<span class="na">telus.qss.performance.timeout</span><span class="o">=</span><span class="s">30000</span>

<span class="c1"># Monitoring configuration</span>
<span class="na">telus.qss.monitoring.metrics-enabled</span><span class="o">=</span><span class="s">true</span>
<span class="na">telus.qss.monitoring.health-check-interval</span><span class="o">=</span><span class="s">60</span>
<span class="na">telus.qss.monitoring.alert-threshold</span><span class="o">=</span><span class="s">0.95</span>

<span class="c1"># Tenant configuration</span>
<span class="na">telus.qss.tenants.default</span><span class="o">=</span><span class="s">all</span>
<span class="na">telus.qss.tenants.supported</span><span class="o">=</span><span class="s">telus-b2b,telus-enterprise,telus-government</span>
</code></pre></div>
<h3 id="environment-specific-configuration">Environment-Specific Configuration<a class="headerlink" href="#environment-specific-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Development environment</span>
<span class="nt">development</span><span class="p">:</span>
<span class="w">  </span><span class="nt">telus</span><span class="p">:</span>
<span class="w">    </span><span class="nt">qss</span><span class="p">:</span>
<span class="w">      </span><span class="nt">rules</span><span class="p">:</span>
<span class="w">        </span><span class="nt">high-value-threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">50000.0</span>
<span class="w">      </span><span class="nt">performance</span><span class="p">:</span>
<span class="w">        </span><span class="nt">cache-enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>
<span class="w">        </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">60000</span>
<span class="w">      </span><span class="nt">monitoring</span><span class="p">:</span>
<span class="w">        </span><span class="nt">metrics-enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">debug-logging</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="c1"># Production environment</span>
<span class="nt">production</span><span class="p">:</span>
<span class="w">  </span><span class="nt">telus</span><span class="p">:</span>
<span class="w">    </span><span class="nt">qss</span><span class="p">:</span>
<span class="w">      </span><span class="nt">rules</span><span class="p">:</span>
<span class="w">        </span><span class="nt">high-value-threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100000.0</span>
<span class="w">      </span><span class="nt">performance</span><span class="p">:</span>
<span class="w">        </span><span class="nt">cache-enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30000</span>
<span class="w">      </span><span class="nt">monitoring</span><span class="p">:</span>
<span class="w">        </span><span class="nt">metrics-enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">debug-logging</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>
<span class="w">        </span><span class="nt">alert-threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">0.99</span>
</code></pre></div>
<h2 id="lifecycle-management">Lifecycle Management<a class="headerlink" href="#lifecycle-management" title="Permanent link">&para;</a></h2>
<h3 id="plugin-lifecycle-events">Plugin Lifecycle Events<a class="headerlink" href="#plugin-lifecycle-events" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Plugin lifecycle management</span>
<span class="cm"> */</span>
<span class="nd">@Component</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QSSPluginLifecycleManager</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">PluginLifecycleListener</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Logger</span><span class="w"> </span><span class="n">log</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LoggerFactory</span><span class="p">.</span><span class="na">getLogger</span><span class="p">(</span><span class="n">QSSPluginLifecycleManager</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">onPluginStart</span><span class="p">(</span><span class="n">PluginContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Starting TELUS QSS Plugin: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getPluginId</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Initialize plugin resources</span>
<span class="w">        </span><span class="n">initializeConfiguration</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>
<span class="w">        </span><span class="n">initializeMetrics</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>
<span class="w">        </span><span class="n">initializeHealthChecks</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;TELUS QSS Plugin started successfully&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">onPluginStop</span><span class="p">(</span><span class="n">PluginContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Stopping TELUS QSS Plugin: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getPluginId</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Cleanup plugin resources</span>
<span class="w">        </span><span class="n">cleanupMetrics</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>
<span class="w">        </span><span class="n">cleanupHealthChecks</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;TELUS QSS Plugin stopped successfully&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">onPluginReload</span><span class="p">(</span><span class="n">PluginContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Reloading TELUS QSS Plugin: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getPluginId</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Reload configuration</span>
<span class="w">        </span><span class="n">reloadConfiguration</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;TELUS QSS Plugin reloaded successfully&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">initializeConfiguration</span><span class="p">(</span><span class="n">PluginContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Load plugin configuration</span>
<span class="w">        </span><span class="n">ConfigurationManager</span><span class="p">.</span><span class="na">loadConfiguration</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">initializeMetrics</span><span class="p">(</span><span class="n">PluginContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Initialize metrics collection</span>
<span class="w">        </span><span class="n">MetricsManager</span><span class="p">.</span><span class="na">initializeMetrics</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">initializeHealthChecks</span><span class="p">(</span><span class="n">PluginContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Initialize health checks</span>
<span class="w">        </span><span class="n">HealthCheckManager</span><span class="p">.</span><span class="na">initializeHealthChecks</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="hot-deployment-support">Hot Deployment Support<a class="headerlink" href="#hot-deployment-support" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">/**</span>
<span class="cm"> * Hot deployment support for QSS plugin</span>
<span class="cm"> */</span>
<span class="nd">@Component</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QSSPluginHotDeployment</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@EventListener</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">handlePluginUpdate</span><span class="p">(</span><span class="n">PluginUpdateEvent</span><span class="w"> </span><span class="n">event</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isQSSPlugin</span><span class="p">(</span><span class="n">event</span><span class="p">.</span><span class="na">getPluginId</span><span class="p">()))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Handling hot deployment for QSS plugin: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">event</span><span class="p">.</span><span class="na">getPluginId</span><span class="p">());</span>

<span class="w">            </span><span class="c1">// Graceful shutdown of current version</span>
<span class="w">            </span><span class="n">gracefulShutdown</span><span class="p">(</span><span class="n">event</span><span class="p">.</span><span class="na">getCurrentVersion</span><span class="p">());</span>

<span class="w">            </span><span class="c1">// Deploy new version</span>
<span class="w">            </span><span class="n">deployNewVersion</span><span class="p">(</span><span class="n">event</span><span class="p">.</span><span class="na">getNewVersion</span><span class="p">());</span>

<span class="w">            </span><span class="c1">// Verify deployment</span>
<span class="w">            </span><span class="n">verifyDeployment</span><span class="p">(</span><span class="n">event</span><span class="p">.</span><span class="na">getNewVersion</span><span class="p">());</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isQSSPlugin</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">pluginId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="s">&quot;telus-b2b-qss-plugin&quot;</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">pluginId</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">gracefulShutdown</span><span class="p">(</span><span class="n">PluginVersion</span><span class="w"> </span><span class="n">currentVersion</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Complete ongoing operations</span>
<span class="w">        </span><span class="c1">// Release resources</span>
<span class="w">        </span><span class="c1">// Update status</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">deployNewVersion</span><span class="p">(</span><span class="n">PluginVersion</span><span class="w"> </span><span class="n">newVersion</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Load new plugin version</span>
<span class="w">        </span><span class="c1">// Initialize new resources</span>
<span class="w">        </span><span class="c1">// Update routing</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">verifyDeployment</span><span class="p">(</span><span class="n">PluginVersion</span><span class="w"> </span><span class="n">newVersion</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Run health checks</span>
<span class="w">        </span><span class="c1">// Verify functionality</span>
<span class="w">        </span><span class="c1">// Update monitoring</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../02-snapshot-generation/">Snapshot Generation →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>