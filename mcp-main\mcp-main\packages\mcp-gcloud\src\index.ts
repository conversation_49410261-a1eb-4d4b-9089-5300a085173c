#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  ListToolsRequestSchema,
  CallToolRequestSchema,
  ListResourcesRequestSchema,
  ListResourceTemplatesRequestSchema,
  ReadResourceRequestSchema,
  McpError,
  ErrorCode,
} from '@modelcontextprotocol/sdk/types.js';
import {
  SecretManagerServiceClient,
  protos,
} from "@google-cloud/secret-manager";
import { promisify } from "util";
import { exec } from "child_process";

interface ListSecretsArgs {
  project_id: string;
  limit?: number;
  pageToken?: string;
}

const isValidListSecretsArgs = (args: any): args is ListSecretsArgs => {
  if (
    typeof args !== "object" ||
    args === null ||
    typeof args.project_id !== "string"
  ) {
    return false;
  }

  if (args.pageToken !== undefined && typeof args.pageToken !== "string") {
    return false;
  }

  if (
    args.limit !== undefined &&
    (typeof args.limit !== "number" || args.limit <= 0)
  ) {
    return false;
  }

  return true;
};

class GCloudServer {
  private server: Server;
  private secretManager: SecretManagerServiceClient;
  private execAsync: (
    command: string,
  ) => Promise<{ stdout: string; stderr: string }>;
  private getAccessToken: () => Promise<string>;

  constructor() {
    this.execAsync = promisify(exec);
    this.getAccessToken = async () => {
      const { stdout } = await this.execAsync(
        "gcloud auth application-default print-access-token",
      );
      return stdout.trim();
    };
    this.server = new Server(
      {
        name: "mcp-gcloud",
        version: "0.0.1",
      },
      {
        capabilities: {
          resources: {},
          tools: {},
        },
      },
    );

    // Initialize the Secret Manager client with custom credentials
    const getToken = this.getAccessToken.bind(this);
    this.secretManager = new SecretManagerServiceClient({
      authClient: {
        async getRequestHeaders(): Promise<{ Authorization: string }> {
          const token = await getToken();
          return { Authorization: `Bearer ${token}` };
        },
        getAccessToken: getToken,
        getClient: async () => ({
          getRequestHeaders: async () => ({
            Authorization: `Bearer ${await getToken()}`,
          }),
        }),
      } as any,
    });
    console.error("SecretManagerServiceClient initialized successfully");

    this.setupResourceHandlers();
    this.setupToolHandlers();

    // Error handling
    this.server.onerror = (error: Error) => console.error("[MCP Error]", error);
    process.on("SIGINT", async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupResourceHandlers() {
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => ({
      resources: [],
    }));

    this.server.setRequestHandler(
      ListResourceTemplatesRequestSchema,
      async () => ({
        resourceTemplates: [],
      }),
    );

    this.server.setRequestHandler(
      ReadResourceRequestSchema,
      async (request: { params: { uri: string } }) => {
        throw new McpError(
          ErrorCode.InvalidRequest,
          `Invalid URI format: ${request.params.uri}`,
        );
      },
    );
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: "list_secrets",
          description:
            "List secrets in Google Cloud Secret Manager. Supports pagination for large result sets.",
          inputSchema: {
            type: "object",
            properties: {
              project_id: {
                type: "string",
                description: "GCP project ID",
              },
              limit: {
                type: "number",
                description:
                  "Maximum total number of secrets to return (optional)",
                minimum: 1,
              },
              pageToken: {
                type: "string",
                description:
                  "Token for retrieving the next page of results (optional)",
              },
              include_disabled: {
                type: "boolean",
                description:
                  "Include disabled secrets in the result (optional)",
              },
            },
            required: ["project_id"],
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      console.error("Received request:", JSON.stringify(request, null, 2));
      console.info("Current working directory:", process.cwd());
      if (request.params.name !== "list_secrets") {
        return {
          content: [
            {
              type: "text",
              text: `Unknown tool: ${request.params.name}`,
            },
          ],
        };
      }

      if (!isValidListSecretsArgs(request.params.arguments)) {
        return {
          content: [
            {
              type: "text",
              text: "Invalid arguments. Expected: { project_id: string }",
            },
          ],
        };
      }

      try {
        console.error("Attempting to list secrets...");
        const projectPath = `projects/${request.params.arguments.project_id}`;

        const callOpts: {
          maxResults?: number;
          autoPaginate: boolean;
        } = {
          autoPaginate: true,
        };

        if (request.params.arguments.limit) {
          callOpts.maxResults = request.params.arguments.limit;
        }

        const listSecretsOptions: {
          parent: string;
          filter?: string;
          pageSize?: number;
          pageToken?: string;
        } = {
          parent: projectPath,
          pageToken: request.params.arguments.pageToken,
        };

        const [secrets, , apiResponse] = await this.secretManager.listSecrets(
          listSecretsOptions,
          callOpts,
        );
        console.error("Successfully retrieved secrets");

        const secretList = await Promise.all(
          secrets.map(
            async (secret: protos.google.cloud.secretmanager.v1.ISecret) => {
              const name = secret.name?.split("/").pop() || "";

              return {
                name,
                createTime: secret.createTime
                  ? new Date(
                      Number(secret.createTime.seconds) * 1000,
                    ).toISOString()
                  : undefined,
              };
            },
          ),
        );

        const result = {
          secrets: secretList,
          nextPageToken: apiResponse?.nextPageToken,
        };

        console.error("Sending response:", JSON.stringify(result, null, 2));
        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(result, null, 2),
            },
          ],
          _meta: {
            nextCursor: apiResponse?.nextPageToken,
          },
        };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        const errorResponse = {
          content: [
            {
              type: "text",
              text: `Failed to list secrets: ${errorMessage}`,
            },
          ],
        };
        console.error(
          "Sending error response:",
          JSON.stringify(errorResponse, null, 2),
        );
        return errorResponse;
      }
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error("Google Cloud MCP server running on stdio");
  }
}

const server = new GCloudServer();
server.run().catch(console.error);
