// Tool definitions for the MCP server

/**
 * Returns the definitions of all tools provided by this MCP server
 */
export function getToolDefinitions(): any[] {
  return [
    {
      name: 'trigger_assessment',
      description: 'Triggers a risk engine assessment by sending a simulated webhook to the dispatcher.',
      inputSchema: {
        type: 'object',
        properties: {
          owner: { type: 'string', description: 'Repository owner' },
          repo: { type: 'string', description: 'Repository name' },
          branch: { type: 'string', description: 'Branch name (optional, defaults to main)' },
          sha: { type: 'string', description: 'Commit SHA' },
          commit_message: { type: 'string', description: 'Commit message (optional)' },
        },
        // owner defaults to 'telus'
        required: ['repo', 'sha'],
      },
    },
    // Additional tools can be added here in the future
  ];
}
