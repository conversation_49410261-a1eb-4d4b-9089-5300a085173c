import { OpenAI } from 'openai';

export interface VectorDocument {
  id: string;
  content: string;
  filename: string;
}

export interface SearchResult {
  document: VectorDocument;
  similarity: number;
}

export interface VectorStoreStats {
  totalDocuments: number;
  lastUpdated: string;
}

export interface TurbopufferClient {
  query: (params: {
    namespace: string;
    vector: number[];
    top_k: number;
    distance_metric: string;
    include_attributes: string[];
    filter?: Record<string, any>;
    rankBy?: {
      method: string;
      query: string;
    };
  }) => Promise<any>;
}

export interface ServerConfig {
  turbopuffer: TurbopufferClient;
  openai: OpenAI;
  namespace: string;
}
