#!/usr/bin/env node
import 'dotenv/config';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import axios, { AxiosError, AxiosInstance, AxiosRequestConfig } from 'axios';
import https from 'https';
import { AuthHandler } from './auth.js';
import { EpsConfig } from './types.js';
import { PaymentMethodsHandler } from './paymentMethods.js';
import { PaymentManagementHandler } from './paymentMgmt.js';

// Load configuration from environment variables
const config: EpsConfig = {
  clientId: process.env.KONG_CLIENT_ID || '',
  clientSecret: process.env.KONG_CLIENT_SECRET || '',
  epsPaymentClientId: process.env.EPS_PAYMENT_CLIENT_ID || '',
  scope: process.env.SCOPE || ''
};

// Validate configuration
if (!config.clientId || !config.clientSecret || !config.epsPaymentClientId || !config.scope) {
  console.error('[Setup] Error: Required environment variables are missing');
  process.exit(1);
}

class EpsServer {
  private server: Server;
  private auth: AuthHandler;
  private axiosInstance: AxiosInstance;
  private config: EpsConfig;
  private baseUrl: string;
  private paymentMethodsHandler: PaymentMethodsHandler;
  private paymentManagementHandler: PaymentManagementHandler;

  constructor() {
    // Get credentials and base URL from environment variables
    const clientId = process.env.KONG_CLIENT_ID;
    const clientSecret = process.env.KONG_CLIENT_SECRET;
    const baseUrl = process.env.BASE_URL;

    if (!clientId || !clientSecret || !baseUrl) {
      throw new Error('KONG_CLIENT_ID, KONG_CLIENT_SECRET, and BASE_URL environment variables are required');
    }

    this.config = {
      clientId,
      clientSecret,
      epsPaymentClientId: process.env.EPS_PAYMENT_CLIENT_ID || '',
      scope: process.env.SCOPE || ''
    };

    this.baseUrl = baseUrl;

    // Configure axios instance
    const axiosConfig: AxiosRequestConfig = {};
    axiosConfig.httpsAgent = new https.Agent({  
        rejectUnauthorized: false
      });
    
    this.axiosInstance = axios.create(axiosConfig);

    // Initialize handlers with shared axios instance
    this.auth = new AuthHandler(this.config, this.axiosInstance, this.baseUrl);

    this.server = new Server(
      {
        name: 'eps-server',
        version: '0.1.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.paymentMethodsHandler = new PaymentMethodsHandler({
      axiosInstance: this.axiosInstance,
      baseUrl: this.baseUrl
    });

    this.paymentManagementHandler = new PaymentManagementHandler(this.axiosInstance, this.baseUrl);

    this.setupToolHandlers();
    
    // Error handling
    this.server.onerror = (error: Error) => console.error('[MCP Error]', error);
    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        // Payment Method Tools
        {
          name: 'get_one_payment_method',
          description: 'Get details of a single payment method by ID',
          inputSchema: {
            type: 'object',
            properties: {
              paymentMethodId: {
                type: 'string',
                description: 'Payment Method ID',
              },
            },
            required: ['paymentMethodId'],
          },
        },
        // Payment Management Tools
        {
          name: 'get_payment_by_id',
          description: 'Get details of a single payment by ID',
          inputSchema: {
            type: 'object',
            properties: {
              paymentId: {
                type: 'string',
                description: 'Payment ID',
              },
            },
            required: ['paymentId'],
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request: any) => {
      try {
        switch (request.params.name) {
          case 'get_one_payment_method': {
            const token = await this.auth.getAccessToken();
            const headers = {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json',
              'x-payment-client-id': this.config.epsPaymentClientId,
              'env': 'sb01'
            };
            const { paymentMethodId } = request.params.arguments as { paymentMethodId: string };
            const paymentMethod = await this.paymentMethodsHandler.getOnePaymentMethod(paymentMethodId, headers);
            return {
              content: [{ type: 'text', text: JSON.stringify(paymentMethod, null, 2) }],
            };
          }
          case 'get_payment_by_id': {
            const { paymentId } = request.params.arguments as { paymentId: string };
            const payment = await this.paymentManagementHandler.getPaymentById(paymentId);
            return {
              content: [{ type: 'text', text: JSON.stringify(payment, null, 2) }],
            };
          }
          default:
            throw new McpError(
              ErrorCode.MethodNotFound,
              `Unknown tool: ${request.params.name}`
            );
        }
      } catch (error: unknown) {
        console.error('[EPS Error]', error);
        if (axios.isAxiosError(error)) {
          const axiosError = error as AxiosError<{ message?: string }>;
          const errorMessage = axiosError.response?.data?.message || axiosError.message;
          return {
            content: [
              {
                type: 'text',
                text: `API Error: ${errorMessage}`,
              },
            ],
            isError: true,
          };
        }
        throw error;
      }
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('EPS MCP server running on stdio');
  }
}

const server = new EpsServer();
server.run().catch(console.error);
