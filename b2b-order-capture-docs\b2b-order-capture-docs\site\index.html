
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/">
      
      
      
        <link rel="next" href="00-overview/">
      
      
      <link rel="icon" href="assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL(".",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#telus-b2b-order-capture-ecosystem-documentation" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Home
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="." class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#documentation-structure" class="md-nav__link">
    <span class="md-ellipsis">
      📚 Documentation Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📚 Documentation Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chapter-0-overview-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      📖 Chapter 0: Overview &amp; Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-1-frontend-guide" class="md-nav__link">
    <span class="md-ellipsis">
      🎨 Chapter 1: Frontend Guide
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-2-backend-guide" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Chapter 2: Backend Guide
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-3-extensions-guide" class="md-nav__link">
    <span class="md-ellipsis">
      🔌 Chapter 3: Extensions Guide
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-4-integration-guide" class="md-nav__link">
    <span class="md-ellipsis">
      🔗 Chapter 4: Integration Guide
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-5-development-guide" class="md-nav__link">
    <span class="md-ellipsis">
      💻 Chapter 5: Development Guide
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-6-deployment-guide" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 Chapter 6: Deployment Guide
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-7-reference" class="md-nav__link">
    <span class="md-ellipsis">
      📋 Chapter 7: Reference
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cip-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      ⚙️ CIP Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quick-navigation" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Quick Navigation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Quick Navigation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#for-new-developers" class="md-nav__link">
    <span class="md-ellipsis">
      For New Developers
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#for-devops-teams" class="md-nav__link">
    <span class="md-ellipsis">
      For DevOps Teams
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#for-integration-teams" class="md-nav__link">
    <span class="md-ellipsis">
      For Integration Teams
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-overview" class="md-nav__link">
    <span class="md-ellipsis">
      🏗️ System Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🏗️ System Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#repository-details" class="md-nav__link">
    <span class="md-ellipsis">
      📦 Repository Details
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Key Technologies
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#getting-started" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 Getting Started
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🚀 Getting Started">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#quick-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Quick Setup
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#support-contributing" class="md-nav__link">
    <span class="md-ellipsis">
      📞 Support &amp; Contributing
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#documentation-structure" class="md-nav__link">
    <span class="md-ellipsis">
      📚 Documentation Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📚 Documentation Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chapter-0-overview-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      📖 Chapter 0: Overview &amp; Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-1-frontend-guide" class="md-nav__link">
    <span class="md-ellipsis">
      🎨 Chapter 1: Frontend Guide
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-2-backend-guide" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Chapter 2: Backend Guide
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-3-extensions-guide" class="md-nav__link">
    <span class="md-ellipsis">
      🔌 Chapter 3: Extensions Guide
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-4-integration-guide" class="md-nav__link">
    <span class="md-ellipsis">
      🔗 Chapter 4: Integration Guide
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-5-development-guide" class="md-nav__link">
    <span class="md-ellipsis">
      💻 Chapter 5: Development Guide
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-6-deployment-guide" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 Chapter 6: Deployment Guide
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chapter-7-reference" class="md-nav__link">
    <span class="md-ellipsis">
      📋 Chapter 7: Reference
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cip-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      ⚙️ CIP Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quick-navigation" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Quick Navigation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Quick Navigation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#for-new-developers" class="md-nav__link">
    <span class="md-ellipsis">
      For New Developers
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#for-devops-teams" class="md-nav__link">
    <span class="md-ellipsis">
      For DevOps Teams
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#for-integration-teams" class="md-nav__link">
    <span class="md-ellipsis">
      For Integration Teams
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-overview" class="md-nav__link">
    <span class="md-ellipsis">
      🏗️ System Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🏗️ System Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#repository-details" class="md-nav__link">
    <span class="md-ellipsis">
      📦 Repository Details
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Key Technologies
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#getting-started" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 Getting Started
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🚀 Getting Started">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#quick-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Quick Setup
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#support-contributing" class="md-nav__link">
    <span class="md-ellipsis">
      📞 Support &amp; Contributing
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="telus-b2b-order-capture-ecosystem-documentation">TELUS B2B Order Capture Ecosystem - Documentation<a class="headerlink" href="#telus-b2b-order-capture-ecosystem-documentation" title="Permanent link">&para;</a></h1>
<p>Welcome to the comprehensive documentation for the TELUS B2B Order Capture ecosystem. This documentation covers four interconnected repositories that form a complete enterprise-grade B2B order capture and quote generation solution.</p>
<h2 id="documentation-structure">📚 Documentation Structure<a class="headerlink" href="#documentation-structure" title="Permanent link">&para;</a></h2>
<p>This documentation is organized into the following chapters:</p>
<h3 id="chapter-0-overview-architecture"><a href="00-overview/">📖 Chapter 0: Overview &amp; Architecture</a><a class="headerlink" href="#chapter-0-overview-architecture" title="Permanent link">&para;</a></h3>
<ul>
<li>Ecosystem overview and key features</li>
<li>High-level system architecture</li>
<li>Repository relationships and integration points</li>
<li>Technology stack overview</li>
</ul>
<h3 id="chapter-1-frontend-guide"><a href="01-frontend-guide/">🎨 Chapter 1: Frontend Guide</a><a class="headerlink" href="#chapter-1-frontend-guide" title="Permanent link">&para;</a></h3>
<ul>
<li>Angular 15 frontend application</li>
<li>Custom TELUS components and modules</li>
<li>State management with NgRx</li>
<li>UI/UX patterns and development guidelines</li>
</ul>
<h3 id="chapter-2-backend-guide"><a href="02-backend-guide/">🔧 Chapter 2: Backend Guide</a><a class="headerlink" href="#chapter-2-backend-guide" title="Permanent link">&para;</a></h3>
<ul>
<li>Spring Boot 3.1 backend services</li>
<li>REST API documentation</li>
<li>Business logic and data processing</li>
<li>Integration patterns</li>
</ul>
<h3 id="chapter-3-extensions-guide"><a href="03-extensions-guide/">🔌 Chapter 3: Extensions Guide</a><a class="headerlink" href="#chapter-3-extensions-guide" title="Permanent link">&para;</a></h3>
<ul>
<li>QES (Quote Engine Service) plugins</li>
<li>Business rule validation</li>
<li>Custom bandwidth validators</li>
<li>Extension development patterns</li>
</ul>
<h3 id="chapter-4-integration-guide"><a href="04-integration-guide/">🔗 Chapter 4: Integration Guide</a><a class="headerlink" href="#chapter-4-integration-guide" title="Permanent link">&para;</a></h3>
<ul>
<li>Inter-repository communication</li>
<li>API contracts and data flow</li>
<li>External system integrations</li>
<li>Relationship with bulk operation services</li>
</ul>
<h3 id="chapter-5-development-guide"><a href="05-development-guide/">💻 Chapter 5: Development Guide</a><a class="headerlink" href="#chapter-5-development-guide" title="Permanent link">&para;</a></h3>
<ul>
<li>Development environment setup</li>
<li>Build processes and workflows</li>
<li>Testing strategies</li>
<li>Debugging and troubleshooting</li>
</ul>
<h3 id="chapter-6-deployment-guide"><a href="06-deployment-guide/">🚀 Chapter 6: Deployment Guide</a><a class="headerlink" href="#chapter-6-deployment-guide" title="Permanent link">&para;</a></h3>
<ul>
<li>Kubernetes deployment configurations</li>
<li>Helm charts and values</li>
<li>Environment-specific settings</li>
<li>CI/CD pipeline setup</li>
</ul>
<h3 id="chapter-7-reference"><a href="07-reference/">📋 Chapter 7: Reference</a><a class="headerlink" href="#chapter-7-reference" title="Permanent link">&para;</a></h3>
<ul>
<li>Complete API reference</li>
<li>Configuration parameters</li>
<li>Component inventory</li>
<li>External resources</li>
</ul>
<h3 id="cip-configuration"><a href="cip-config/00-overview/">⚙️ CIP Configuration</a><a class="headerlink" href="#cip-configuration" title="Permanent link">&para;</a></h3>
<ul>
<li>Cloud Integration Platform configuration</li>
<li>Integration chains and service catalog</li>
<li>TMF-compliant API specifications</li>
<li>SFDC and external system integrations</li>
</ul>
<h2 id="quick-navigation">🎯 Quick Navigation<a class="headerlink" href="#quick-navigation" title="Permanent link">&para;</a></h2>
<h3 id="for-new-developers">For New Developers<a class="headerlink" href="#for-new-developers" title="Permanent link">&para;</a></h3>
<ol>
<li>Start with <a href="00-overview/">Overview &amp; Architecture</a></li>
<li>Follow <a href="05-development-guide/">Development Guide</a></li>
<li>Reference <a href="01-frontend-guide/">Frontend</a> or <a href="02-backend-guide/">Backend Guide</a></li>
</ol>
<h3 id="for-devops-teams">For DevOps Teams<a class="headerlink" href="#for-devops-teams" title="Permanent link">&para;</a></h3>
<ol>
<li>Review <a href="00-overview/">Architecture</a></li>
<li>Study <a href="06-deployment-guide/">Deployment Guide</a></li>
<li>Use <a href="07-reference/">Reference</a> for configurations</li>
</ol>
<h3 id="for-integration-teams">For Integration Teams<a class="headerlink" href="#for-integration-teams" title="Permanent link">&para;</a></h3>
<ol>
<li>Understand <a href="04-integration-guide/">Integration Guide</a></li>
<li>Review <a href="03-extensions-guide/">Extensions Guide</a></li>
<li>Reference API documentation in each guide</li>
</ol>
<h2 id="system-overview">🏗️ System Overview<a class="headerlink" href="#system-overview" title="Permanent link">&para;</a></h2>
<p>The TELUS B2B Order Capture ecosystem consists of four main repositories:</p>
<div class="highlight"><pre><span></span><code>┌─────────────────────────────────────────────────────────────────────────────────┐
│                        TELUS B2B Order Capture Ecosystem                       │
├─────────────────────────────────────────────────────────────────────────────────┤
│  Frontend               │    Backend                │    Extensions             │
│  (Angular 15)           │    (Spring Boot 3.1)      │    (QES Plugins)          │
├─────────────────────────┼───────────────────────────┼──────────────────────────┤
│  • User Interface       │    • Core APIs            │    • Business Rules      │
│  • Order Forms          │    • Business Logic       │    • Validation Logic    │
│  • Cart Management      │    • Data Processing      │    • Quote Extensions    │
│  • Service Configuration│    • Integration Layer    │    • Custom Validators   │
└─────────────────────────┴───────────────────────────┴──────────────────────────┘
</code></pre></div>
<h3 id="repository-details">📦 Repository Details<a class="headerlink" href="#repository-details" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Repository</th>
<th>Purpose</th>
<th>Technology</th>
<th>Key Features</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>nc-cloud-bss-oc-ui-frontend-b2b</strong></td>
<td>User Interface</td>
<td>Angular 15.2.9</td>
<td>Custom TELUS components, NgRx state management, responsive design</td>
</tr>
<tr>
<td><strong>nc-cloud-bss-oc-ui-backend-b2b</strong></td>
<td>Core Backend</td>
<td>Spring Boot 3.1.12</td>
<td>REST APIs, business logic, Order Capture integration</td>
</tr>
<tr>
<td><strong>nc-cloud-bss-oc-ui-be-extension-b2b</strong></td>
<td>Business Extensions</td>
<td>QES Plugins</td>
<td>Bandwidth validators, quote modifications, business rules</td>
</tr>
<tr>
<td><strong>nc-cloud-bss-cip-config-b2b</strong></td>
<td>Integration Platform</td>
<td>CIP Configuration</td>
<td>Integration chains, service catalog, TMF compliance, SFDC integration</td>
</tr>
</tbody>
</table>
<h2 id="key-technologies">🔧 Key Technologies<a class="headerlink" href="#key-technologies" title="Permanent link">&para;</a></h2>
<ul>
<li><strong>Frontend</strong>: Angular 15, TypeScript, NgRx, LESS, Webpack</li>
<li><strong>Backend</strong>: Spring Boot 3, Java 17, Maven, OpenAPI</li>
<li><strong>Extensions</strong>: QES Plugin Framework, Custom Validators</li>
<li><strong>Deployment</strong>: Docker, Kubernetes, Helm, GitLab CI</li>
<li><strong>Integration</strong>: Order Capture Product 2023.3.2.x</li>
</ul>
<h2 id="getting-started">🚀 Getting Started<a class="headerlink" href="#getting-started" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<ul>
<li>Node.js 16+ and npm</li>
<li>Java 17+</li>
<li>Maven 3.6+</li>
<li>Docker and Kubernetes (for deployment)</li>
</ul>
<h3 id="quick-setup">Quick Setup<a class="headerlink" href="#quick-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Clone all repositories</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/telus/nc-cloud-bss-oc-ui-frontend-b2b
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/telus/nc-cloud-bss-oc-ui-backend-b2b
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/telus/nc-cloud-bss-oc-ui-be-extension-b2b
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/telus/nc-cloud-bss-cip-config-b2b

<span class="c1"># Build extensions first</span>
<span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-be-extension-b2b
mvn<span class="w"> </span>clean<span class="w"> </span>install

<span class="c1"># Build backend</span>
<span class="nb">cd</span><span class="w"> </span>../nc-cloud-bss-oc-ui-backend-b2b
mvn<span class="w"> </span>clean<span class="w"> </span>install

<span class="c1"># Setup frontend</span>
<span class="nb">cd</span><span class="w"> </span>../nc-cloud-bss-oc-ui-frontend-b2b
npm<span class="w"> </span>install
npm<span class="w"> </span>run<span class="w"> </span>serve:dev
</code></pre></div>
<h2 id="support-contributing">📞 Support &amp; Contributing<a class="headerlink" href="#support-contributing" title="Permanent link">&para;</a></h2>
<ul>
<li><strong>Documentation Issues</strong>: Create issues in the respective repository</li>
<li><strong>Development Questions</strong>: Contact the TELUS Cloud BSS team</li>
<li><strong>Integration Support</strong>: Reference the Integration Guide</li>
</ul>
<hr />
<p><strong>Ready to explore?</strong> Start with the <a href="00-overview/">Overview &amp; Architecture Guide</a>!</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": ".", "features": [], "search": "assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>