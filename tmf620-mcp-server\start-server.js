#!/usr/bin/env node
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const dotenv = require('dotenv');

let serverProcess;

try {
  console.log('Starting TMF620 MCP server wrapper...');
  
  // Log the current directory and build path
  console.log('Current directory:', __dirname);
  const buildPath = path.resolve(__dirname, 'build/index.js');
  console.log('Build path:', buildPath);
  
  // Check if the build file exists
  if (!fs.existsSync(buildPath)) {
    console.error(`ERROR: Build file not found at ${buildPath}`);
    console.error('Please run "npm run build" to compile the TypeScript files');
    process.exit(1);
  }

  // Create a new environment object starting with current process.env
  const env = { ...process.env };

  // Load .env file as fallback values
  const envPath = path.resolve(__dirname, '.env');
  console.log('Attempting to load .env file from:', envPath);
  const dotenvResult = dotenv.config({ path: envPath });
  if (dotenvResult.error) {
    console.warn('Warning: Error loading .env file:', dotenvResult.error.message);
  } else {
    console.log('.env file loaded successfully');
    // Only use .env values for variables that don't already exist
    Object.entries(dotenvResult.parsed || {}).forEach(([key, value]) => {
      if (!env[key]) {
        env[key] = value;
      }
    });
  }
  
  // Validate required environment variables
  const requiredEnvVars = [
    'DEV_API_URL',
    'TEST_API_URL',
    'PROD_API_URL',
    'OAUTH_CLIENT_ID',
    'OAUTH_CLIENT_SECRET',
    'OAUTH_TOKEN_URL',
    'OAUTH_SCOPE'
  ];

  const missingEnvVars = requiredEnvVars.filter(varName => !env[varName]);
  if (missingEnvVars.length > 0) {
    console.error('ERROR: Missing required environment variables:');
    missingEnvVars.forEach(varName => console.error(`- ${varName}`));
    process.exit(1);
  }

  // Log the environment variables being used (excluding sensitive values)
  console.log('Using environment variables:');
  console.log('DEV_API_URL:', env.DEV_API_URL);
  console.log('TEST_API_URL:', env.TEST_API_URL);
  console.log('PROD_API_URL:', env.PROD_API_URL);
  console.log('OAUTH_CLIENT_ID:', env.OAUTH_CLIENT_ID);
  console.log('OAUTH_TOKEN_URL:', env.OAUTH_TOKEN_URL);
  console.log('OAUTH_SCOPE:', env.OAUTH_SCOPE);
  // Don't log OAUTH_CLIENT_SECRET for security
  
  console.log('Spawning server process...');
  serverProcess = spawn('node', ['build/index.js'], {
    env: env, // Use our merged environment variables
    stdio: ['pipe', 'pipe', 'pipe'],
    cwd: __dirname
  });
  
  // Forward stdout to console
  serverProcess.stdout.on('data', (data) => {
    console.log(`[Server] ${data}`);
  });

  // Forward stderr to console
  serverProcess.stderr.on('data', (data) => {
    console.error(`[Server Error] ${data}`);
  });

  serverProcess.on('error', (err) => {
    console.error('Failed to start server:', err);
    process.exit(1);
  });

  serverProcess.on('close', (code) => {
    console.log(`Server process exited with code ${code}`);
    process.exit(code || 0);
  });

  // Handle process signals
  process.on('SIGINT', () => {
    console.log('Received SIGINT. Shutting down server...');
    serverProcess.kill();
  });

  process.on('SIGTERM', () => {
    console.log('Received SIGTERM. Shutting down server...');
    serverProcess.kill();
  });

  console.log('Server wrapper setup complete');
} catch (error) {
  console.error('Error in start-server.js:', error);
  process.exit(1);
}
