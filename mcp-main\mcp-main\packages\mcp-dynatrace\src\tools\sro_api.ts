import { z } from 'zod';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { formatTimestamp, getEntities, getProblems, getMetrics, getEntityById, calculateAverage } from './api.js';
import { executeDqlQuery } from './platform.js';

let lastSearchResults: Array<{ entityId: string; displayName: string }> = [];

function formatInvestigationResponse(
    serviceData: any, 
    serviceProblems: any[], 
    infraProblems: any[], 
    gkeProblems: any[],
    serviceMetrics: any,
    hostMetrics: any,
    gkeMetrics: any,
    traceData: any
): string {
    let responseText = `Investigation Results for Service ID ${serviceData.entityId}:\n\n`;
    responseText += "Service Details:\n";
    responseText += `ID: ${serviceData.entityId}\n`;
    responseText += `Type: ${serviceData.type}\n`;
    responseText += `Display Name: ${serviceData.displayName}\n\n`;

    // Add workload information
    responseText += "Workload Information:\n";
    if (serviceData.fromRelationships?.isServiceOf && serviceData.fromRelationships.isServiceOf.length > 0) {
        serviceData.fromRelationships.isServiceOf.forEach((app: any) => {
            responseText += `- ${app.type}: ${app.id}\n`;
        });
    } else {
        responseText += "No workload information found\n";
    }
    responseText += "\n";

    responseText += "Related Services:\n";
    responseText += "Services called by this service:\n";
    if (serviceData.fromRelationships?.calls && serviceData.fromRelationships.calls.length > 0) {
        serviceData.fromRelationships.calls.forEach((service: any) => {
            responseText += `- ${service.type} (${service.id})\n`;
        });
    } else {
        responseText += "None\n";
    }

    responseText += "\nServices calling this service:\n";
    if (serviceData.toRelationships?.calls && serviceData.toRelationships.calls.length > 0) {
        serviceData.toRelationships.calls.forEach((service: any) => {
            responseText += `- ${service.type} (${service.id})\n`;
        });
    } else {
        responseText += "None\n";
    }

    responseText += "\nProblems:\n";
    
    responseText += "\nService Problems:\n";
    const filteredServiceProblems = serviceProblems.filter(problem => 
        problem.affectedEntities.some((entity: any) => entity.entityId.id === serviceData.entityId)
    );
    if (filteredServiceProblems.length > 0) {
        filteredServiceProblems.forEach(problem => {
            responseText += `- Problem ID: ${problem.displayId}\n`;
            responseText += `  Title: ${problem.title}\n`;
            responseText += `  Impact Level: ${problem.impactLevel}\n`;
            responseText += `  Severity Level: ${problem.severityLevel}\n`;
            responseText += `  Duration: ${formatTimestamp(problem.startTime)} - ${formatTimestamp(problem.endTime)}\n`;
            responseText += "  Affected Entities:\n";
            problem.affectedEntities
                .filter((entity: any) => entity.entityId.id === serviceData.entityId)
                .forEach((entity: any) => {
                    responseText += `    - ${entity.name} (${entity.entityId.id})\n`;
                });
            responseText += "\n";
        });
    } else {
        responseText += "No direct service problems found.\n";
    }


    responseText += "\nInfrastructure Problems:\n";
    const allInfraProblems = [...infraProblems, ...gkeProblems].filter(problem => 
        problem.impactLevel === 'INFRASTRUCTURE' || 
        (problem.affectedEntities && problem.affectedEntities.some((entity: any) => 
            entity.entityId.id.startsWith('HOST-') || 
            entity.entityId.id.startsWith('KUBERNETES_CLUSTER-')
        ))
    );
    
    if (allInfraProblems.length > 0) {
        allInfraProblems.forEach(problem => {
            responseText += `- Problem ID: ${problem.displayId}\n`;
            responseText += `  Title: ${problem.title}\n`;
            responseText += `  Impact Level: ${problem.impactLevel}\n`;
            responseText += `  Severity Level: ${problem.severityLevel}\n`;
            responseText += `  Duration: ${formatTimestamp(problem.startTime)} - ${formatTimestamp(problem.endTime)}\n`;
            if (problem.rootCauseEntity) {
                responseText += "  Root Cause:\n";
                responseText += `    - ${problem.rootCauseEntity.name} (${problem.rootCauseEntity.entityId.id})\n`;
            }
            responseText += "\n";
        });
    } else {
        responseText += "No infrastructure problems found.\n";
    }

    responseText += "\nGKE Application Problems:\n";
    if (gkeProblems.length > 0) {
        gkeProblems.forEach(problem => {
            responseText += `- Problem ID: ${problem.displayId}\n`;
            responseText += `  Title: ${problem.title}\n`;
            responseText += `  Impact Level: ${problem.impactLevel}\n`;
            responseText += `  Severity Level: ${problem.severityLevel}\n`;
            responseText += `  Duration: ${formatTimestamp(problem.startTime)} - ${formatTimestamp(problem.endTime)}\n`;
            responseText += "  Affected Entities:\n";
            problem.affectedEntities.forEach((entity: any) => {
                responseText += `    - ${entity.name} (${entity.entityId.id})\n`;
            });
            responseText += "  Impacted Entities:\n";
            problem.impactedEntities.forEach((entity: any) => {
                responseText += `    - ${entity.name} (${entity.entityId.id})\n`;
            });
            if (problem.rootCauseEntity) {
                responseText += "  Root Cause:\n";
                responseText += `    - ${problem.rootCauseEntity.name} (${problem.rootCauseEntity.entityId.id})\n`;
            }
            responseText += "\n";
        });
    } else {
        responseText += "No GKE application problems found.\n";
    }

    function getMetricUnit(metricName: string): string {
        if (metricName.includes('response.time')) return 'ms';
        if (metricName.includes('rate')) return '%';
        if (metricName.includes('requestCount')) return 'requests';
        if (metricName.includes('cpu.usage') || metricName.includes('mem.usage')) return '%';
        if (metricName.includes('disk.usedPct')) return '%';
        if (metricName.includes('traffic')) return 'MB/sec';
        if (metricName.includes('memory')) return 'MB';
        if (metricName.includes('cpu')) return 'millicores';
        return '';
    }

function formatMetricValue(value: number | null, unit: string): string {
    if (value === null) {
        return "N/A";
    }
    
    let formattedValue = value;
    // Convert bytes to MB for traffic and memory metrics
    if (unit === 'MB/sec' || unit === 'MB') {
        formattedValue = value / (1024 * 1024); // Convert bytes to MB
    }
    
    return unit ? `${formattedValue.toFixed(2)} ${unit}` : formattedValue.toFixed(2);
}

function processMetrics(metrics: any): Map<string, { latest: number | null, average: number | null }> {
    const metricMap = new Map<string, { latest: number | null, average: number | null }>();
    
    if (metrics?.result) {
        metrics.result.forEach((metric: any) => {
            const metricName = metric.metricId.split(":")[1] || metric.metricId;
            
            if (metric.data && metric.data.length > 0) {
                metric.data.forEach((dataPoint: any) => {
                    let entityName = '';
                    if (Array.isArray(dataPoint.dimensions) && dataPoint.dimensions.length > 0) {
                        entityName = dataPoint.dimensions[0];
                    } else if (typeof dataPoint.dimensions === 'object') {
                        entityName = Object.values(dataPoint.dimensions)[0] as string;
                    }
                    
                    const metricKey = entityName ? `${metricName}.${entityName}` : metricName;
                    
                    if (dataPoint.values && dataPoint.values.length > 0) {
                        const latestValue = dataPoint.values[dataPoint.values.length - 1];
                        const averageValue = calculateAverage(dataPoint.values);
                        metricMap.set(metricKey, { 
                            latest: latestValue ?? null, 
                            average: averageValue
                        });
                    } else {
                        metricMap.set(metricKey, { latest: null, average: null });
                    }
                });
            } else {
                metricMap.set(metricName, { latest: null, average: null });
            }
        });
    }
    return metricMap;
}

function formatMetricSection(title: string, metrics: Map<string, { latest: number | null, average: number | null }>): string {
    let section = `\n${title}:\n`;
    if (metrics.size > 0) {
        metrics.forEach(({ latest, average }, metricName) => {
            const unit = getMetricUnit(metricName);
            section += `${metricName}:\n`;
            section += `  Latest Value: ${formatMetricValue(latest, unit)}\n`;
            section += `  Average Value: ${formatMetricValue(average, unit)}\n`;
        });
    } else {
        section += `No ${title.toLowerCase()} available.\n`;
    }
    return section;
}

    const serviceMetricsMap = processMetrics(serviceMetrics);
    const hostMetricsMap = processMetrics(hostMetrics);
    const gkeMetricsMap = processMetrics(gkeMetrics);

    responseText += formatMetricSection("Service Metrics", serviceMetricsMap);
    responseText += formatMetricSection("Host Metrics", hostMetricsMap);
    responseText += formatMetricSection("GKE Metrics", gkeMetricsMap);

    responseText += "\nTrace Data:\n";
    responseText += JSON.stringify(traceData, null, 2);

    return responseText;
}

async function searchServicesByName(serviceName: string) {
    serviceName = serviceName.replace(/['"]+/g, '');
    const entitySelector = `type(SERVICE),entityName("${serviceName}")`;
    const fields = "+lastSeenTms";
    
    const entitiesData = await getEntities(entitySelector, undefined, undefined, fields);

    if (!entitiesData) {
        return "Failed to retrieve services data";
    }

    const services = entitiesData.entities || [];
    if (services.length === 0) {
        lastSearchResults = [];
        return `No services found matching "${serviceName}"`;
    }

    lastSearchResults = services.map((service: any) => ({
        entityId: service.entityId,
        displayName: service.displayName
    }));

    const servicesText = services.map((service: any, index: number) => 
        `${index + 1}. ID: ${service.entityId}, Name: ${service.displayName}`).join("\n");

    return `Found services:\n\n${servicesText}\n\nUse the 'select-service' tool with the number of the service you want to query.`;
}

async function investigateService(entityId: string, from?: string, to?: string) {
    // Default to last 2h minutes if no time range specified
    const defaultFrom = new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString();
    const defaultTo = new Date().toISOString();
    const From = from || defaultFrom;
    const To = to || defaultTo;

    const fields = "fromRelationships,toRelationships,properties";
    const serviceData = await getEntityById(entityId, fields, From, To);

    if (!serviceData) {
        return "Failed to retrieve service details";
    }

    const relatedServiceIds = new Set<string>();
    const runsOnInfra = new Set<string>();
    const gkeAppIds = new Set<string>();

    if (serviceData.fromRelationships?.calls) {
        serviceData.fromRelationships.calls.forEach((service: any) => {
            if (service.type === "SERVICE") {
                relatedServiceIds.add(service.id)
            }
        });
    }
    if (serviceData.toRelationships?.calls) {
        serviceData.toRelationships.calls.forEach((service: any) => {
            if (service.type === "SERVICE") {
                relatedServiceIds.add(service.id)
            }
        });
    }
    if (serviceData.fromRelationships?.runsOnHost) {
        serviceData.fromRelationships.runsOnHost.forEach((host: any) => {
            if (host.type === "HOST") {
                runsOnInfra.add(host.id)
            }
        });
    }
    if (serviceData.fromRelationships?.isServiceOf) {
        serviceData.fromRelationships.isServiceOf.forEach((app: any) => {
            if (app.type === "CLOUD_APPLICATION") {
                gkeAppIds.add(app.id);
            }
        });
    }


    const serviceProblemsData = await getProblems(undefined, `entityId(${entityId})`, From, To);
    const relatedProblemsData = await getProblems(undefined, `entityId(${[...relatedServiceIds].join(",")})`, From, To);
    const infraProblemsData = await getProblems(undefined, `entityId(${[...runsOnInfra].join(",")})`, From, To);
    const gkeProblemsData = await getProblems(undefined, `entityId(${[...gkeAppIds].join(",")})`, From, To);

    const serviceProblems = serviceProblemsData?.problems || [];
    const relatedProblems = relatedProblemsData?.problems || [];
    const infraProblems = infraProblemsData?.problems || [];
    const gkeProblems = gkeProblemsData?.problems || [];

    const serviceMetricSelectors = [
        "builtin:service.response.time",
        "builtin:service.errors.total.rate",
        "builtin:service.requestCount.total"
    ];
    const serviceMetrics = await getMetrics(
        serviceMetricSelectors.join(","),
        `entityId(${entityId})`,
        From,
        To
    );

    let hostMetrics = null;
    if (runsOnInfra.size > 0) {
        const hostMetricSelectors = [
            "builtin:host.cpu.usage",
            "builtin:host.mem.usage",
            "builtin:host.disk.usedPct",
            "builtin:host.net.nic.traffic"
        ];
        hostMetrics = await getMetrics(
            hostMetricSelectors.join(","),
            `entityId(${Array.from(runsOnInfra).join(",")})`,
            From,
            To
        );
    }

    let gkeMetrics = null;
    if (gkeAppIds.size > 0) {
        const gkeMetricSelectors = [
            "builtin:kubernetes.workload.conditions",
            "builtin:kubernetes.workload.pods_desired",
            "builtin:kubernetes.workload.limits_cpu",
            "builtin:kubernetes.workload.requests_cpu",
            "builtin:kubernetes.workload.limits_memory",
            "builtin:kubernetes.workload.requests_memory"
        ];
        gkeMetrics = await getMetrics(
            gkeMetricSelectors.join(","),
            `entityId(${Array.from(gkeAppIds).join(",")})`,
            From,
            To
        );
    }

    const fromDate = new Date(From).toISOString().split('.')[0] + "Z";
    const toDate = new Date(To).toISOString().split('.')[0] + "Z";
    
    const dqlQuery = `fetch spans, from: formatTimestamp(toTimestamp("${From}")), to: formatTimestamp(toTimestamp("${To}"))
    | fieldsAdd endpoint.url
    | filter dt.entity.service == "${entityId}"
    | sort startTime desc
    | limit 30`;

    const traceData = await executeDqlQuery(dqlQuery);


    const response = formatInvestigationResponse(
        serviceData,
        serviceProblems,
        infraProblems,
        gkeProblems,
        serviceMetrics,
        hostMetrics,
        gkeMetrics,
        traceData
    );

    const totalProblems = serviceProblems.length + relatedProblems.length + infraProblems.length + gkeProblems.length;
    const problemSummary = `Problem Summary (Last 24 hours):
- Total Problems: ${totalProblems}
- Service Problems: ${serviceProblems.length}
- Related Service Problems: ${relatedProblems.length}
- Infrastructure Problems: ${infraProblems.length}
- GKE Application Problems: ${gkeProblems.length}

`;

    return problemSummary + response;
}

export const searchServicesByNameApi = (server: McpServer) => {
    return server.tool(
        "search-services-by-name",
        "Search Dynatrace services by name",
        {
            serviceName: z.string().describe("Name of the service to search for"),
        },
        async ({ serviceName }) => {
            const result = await searchServicesByName(serviceName);
            return {
                content: [{ type: "text", text: result }],
            };
        }
    );
};

export const selectServiceApi = (server: McpServer) => {
    return server.tool(
        "select-service",
        "Select a service from the last search results",
        {
            serviceNumber: z.number().int().positive().describe("Number of the service from the search results"),
        },
        async ({ serviceNumber }) => {
            if (lastSearchResults.length === 0) {
                return {
                    content: [{ type: "text", text: "No search results available. Please use the 'search-services' tool first." }],
                };
            }

            if (serviceNumber < 1 || serviceNumber > lastSearchResults.length) {
                return {
                    content: [{ type: "text", text: `Invalid service number. Please choose a number between 1 and ${lastSearchResults.length}.` }],
                };
            }

            const selectedService = lastSearchResults[serviceNumber - 1];
            return {
                content: [{ type: "text", text: `Selected service: ID: ${selectedService.entityId}, Name: ${selectedService.displayName}` }],
            };
        }
    );
};

export const investigateServiceApi = (server: McpServer) => {
    return server.tool(
        "investigate-service",
        "run an Investigate on a service, analyze its relationships, underlying infrastructure, multidimensional metrics, all related problems and traces",
        {
            entityId: z.string().describe("Entity ID of the service to investigate"),
            from: z.string().optional().describe("Start time in ISO format (e.g., 2024-02-04T13:00:00Z)"),
            to: z.string().optional().describe("End time in ISO format (e.g., 2024-02-04T14:00:00Z)")
        },
        async ({ entityId, from, to }) => {
            const result = await investigateService(entityId, from, to);
            // Hard-coded prompt for consistent formatting
            const defaultPrompt = `
- Summarize the above investigation result with strict following sections, you need to use exact the following section names:
- **Service Details section:** Key attributes and relationships.
- **Problems** Summarize problems and their category, include Problem IDs.
- **Metrics** Include multidimensional metric insights, break down per metrics category.
- **Infrastructure:** Highlight underlying infrastructure.
- **Traces** Summarize trace data
    - list traces that has problem, each listed trace should have trace.id, k8s.pod.name, span.name, http.route and k8s.namespace.name, also the issue find in those trace data.
    - if no trace has problem, list at least one example trace,should have trace.id, k8s.pod.name, span.name, http.route and k8s.namespace.name.
- **Root Cause** Detailed summary of the potential root cause.
- **Next Steps** Give short term next steps to solve the issue found, also give long term recommendation to improve in the future.
- Use bullet points and concise descriptions.
            `;
            return {
                content: [
                    { 
                        type: "text",
                        text: `${result}\n\n${defaultPrompt}`
                    }
                ],
            };
        }
    );
};
