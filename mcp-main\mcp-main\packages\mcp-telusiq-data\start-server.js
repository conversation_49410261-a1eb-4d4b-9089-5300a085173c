import fs from 'fs';
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the MCP settings file
const settingsPath = path.join(process.env.APPDATA, 'Code', 'User', 'globalStorage', 'saoudrizwan.claude-dev', 'settings', 'cline_mcp_settings.json');
const settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));

// Get the telusiq-data server configuration
const serverConfig = settings.mcpServers['telusiq-data'];

// Function to start kubectl port-forward
function startPortForward() {
  const portForwardProcess = spawn('kubectl', [
    'port-forward',
    serverConfig.env.POD_NAME,
    `${serverConfig.env.TARGET_PORT}:${serverConfig.env.DB_PORT}`,
    '-n',
    serverConfig.env.NAMESPACE
  ]);

  portForwardProcess.stdout.on('data', (data) => {
    console.log(`kubectl port-forward: ${data}`);
  });

  portForwardProcess.stderr.on('data', (data) => {
    console.error(`kubectl port-forward error: ${data}`);
  });

  portForwardProcess.on('close', (code) => {
    console.log(`kubectl port-forward process exited with code ${code}`);
  });

  // Wait for port-forward to establish
  setTimeout(() => {
    startServer();
  }, 5000);
}

// Function to start the server
function startServer() {
  const serverProcess = spawn('node', [path.join(__dirname, 'dist', 'index.js')], {
    env: { ...process.env, ...serverConfig.env },
    stdio: ['inherit', 'inherit', 'pipe'] // Pipe stderr for detailed error logging
  });

  serverProcess.stderr.on('data', (data) => {
    console.error(`Server error: ${data}`);
  });

  serverProcess.on('error', (err) => {
    console.error('Failed to start server:', err);
  });

  serverProcess.on('exit', (code, signal) => {
    if (code !== null) {
      console.log(`Server process exited with code ${code}`);
    } else if (signal !== null) {
      console.log(`Server process was killed with signal ${signal}`);
    }
    process.exit();
  });
}

// Start the process
startPortForward();
