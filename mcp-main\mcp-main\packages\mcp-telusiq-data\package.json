{"name": "@telus/mcp-telusiq-data", "version": "1.0.0", "description": "MCP server for TelusIQ PostgreSQL queries", "keywords": ["telus", "mcp", "telusiq-data"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-telusiq-data"}, "license": "MIT", "author": "<PERSON><PERSON> (@alwin<PERSON><PERSON>)", "main": "dist/index.js", "type": "module", "bin": {"mcp-telusiq-data": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc --listFiles --listEmittedFiles || (echo 'Build failed' && exit 1)", "start": "node dist/index.js", "dev": "tsc -w", "lint": "eslint src --ext .ts", "test": "jest", "prepare": "npm run build"}, "dependencies": {"@google-cloud/cloud-sql-connector": "^1.1.0", "@modelcontextprotocol/sdk": "^1.7.0", "@types/pg": "^8.11.2", "google-auth-library": "^9.15.1", "pg": "^8.11.3"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^22.13.10", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "eslint": "^9", "jest": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5.8.2"}, "engines": {"node": ">=14.0.0"}}