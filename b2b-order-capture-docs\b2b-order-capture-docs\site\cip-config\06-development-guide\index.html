
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/cip-config/06-development-guide/">
      
      
        <link rel="prev" href="../05-deployment-guide/">
      
      
        <link rel="next" href="../07-troubleshooting/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Development Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#development-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Development Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" checked>
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#directory-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Directory Organization
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Development Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#git-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Git Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#branch-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Branch Strategy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-process" class="md-nav__link">
    <span class="md-ellipsis">
      Development Process
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-development" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chain-development" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#creating-a-new-integration-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Creating a New Integration Chain
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#groovy-script-development" class="md-nav__link">
    <span class="md-ellipsis">
      Groovy Script Development
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-development" class="md-nav__link">
    <span class="md-ellipsis">
      Service Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#creating-a-new-service-definition" class="md-nav__link">
    <span class="md-ellipsis">
      Creating a New Service Definition
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Framework
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Framework">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#test-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Test Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Best Practices
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-categories" class="md-nav__link">
    <span class="md-ellipsis">
      Test Categories
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#code-quality-and-standards" class="md-nav__link">
    <span class="md-ellipsis">
      Code Quality and Standards
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Code Quality and Standards">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#coding-standards" class="md-nav__link">
    <span class="md-ellipsis">
      Coding Standards
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Coding Standards">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#yaml-configuration-standards" class="md-nav__link">
    <span class="md-ellipsis">
      YAML Configuration Standards
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#groovy-script-standards" class="md-nav__link">
    <span class="md-ellipsis">
      Groovy Script Standards
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#code-review-checklist" class="md-nav__link">
    <span class="md-ellipsis">
      Code Review Checklist
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#debugging-and-troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging and Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Debugging and Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#local-development-debugging" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Debugging
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#debugging-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging Tools
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#common-issues-and-solutions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues and Solutions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#contribution-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Contribution Guidelines
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Contribution Guidelines">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#pull-request-process" class="md-nav__link">
    <span class="md-ellipsis">
      Pull Request Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#commit-message-format" class="md-nav__link">
    <span class="md-ellipsis">
      Commit Message Format
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Development Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      1. Configuration Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-testing" class="md-nav__link">
    <span class="md-ellipsis">
      2. Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-performance" class="md-nav__link">
    <span class="md-ellipsis">
      3. Performance
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#4-security" class="md-nav__link">
    <span class="md-ellipsis">
      4. Security
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#5-documentation" class="md-nav__link">
    <span class="md-ellipsis">
      5. Documentation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#directory-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Directory Organization
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Development Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#git-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Git Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#branch-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Branch Strategy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-process" class="md-nav__link">
    <span class="md-ellipsis">
      Development Process
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-development" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chain-development" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#creating-a-new-integration-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Creating a New Integration Chain
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#groovy-script-development" class="md-nav__link">
    <span class="md-ellipsis">
      Groovy Script Development
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-development" class="md-nav__link">
    <span class="md-ellipsis">
      Service Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#creating-a-new-service-definition" class="md-nav__link">
    <span class="md-ellipsis">
      Creating a New Service Definition
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Framework
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Framework">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#test-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Test Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Best Practices
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#test-categories" class="md-nav__link">
    <span class="md-ellipsis">
      Test Categories
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#code-quality-and-standards" class="md-nav__link">
    <span class="md-ellipsis">
      Code Quality and Standards
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Code Quality and Standards">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#coding-standards" class="md-nav__link">
    <span class="md-ellipsis">
      Coding Standards
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Coding Standards">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#yaml-configuration-standards" class="md-nav__link">
    <span class="md-ellipsis">
      YAML Configuration Standards
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#groovy-script-standards" class="md-nav__link">
    <span class="md-ellipsis">
      Groovy Script Standards
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#code-review-checklist" class="md-nav__link">
    <span class="md-ellipsis">
      Code Review Checklist
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#debugging-and-troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging and Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Debugging and Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#local-development-debugging" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Debugging
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#debugging-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging Tools
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#common-issues-and-solutions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues and Solutions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#contribution-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Contribution Guidelines
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Contribution Guidelines">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#pull-request-process" class="md-nav__link">
    <span class="md-ellipsis">
      Pull Request Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#commit-message-format" class="md-nav__link">
    <span class="md-ellipsis">
      Commit Message Format
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Development Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      1. Configuration Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-testing" class="md-nav__link">
    <span class="md-ellipsis">
      2. Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-performance" class="md-nav__link">
    <span class="md-ellipsis">
      3. Performance
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#4-security" class="md-nav__link">
    <span class="md-ellipsis">
      4. Security
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#5-documentation" class="md-nav__link">
    <span class="md-ellipsis">
      5. Documentation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="development-guide">Development Guide<a class="headerlink" href="#development-guide" title="Permanent link">&para;</a></h1>
<h2 id="overview">Overview<a class="headerlink" href="#overview" title="Permanent link">&para;</a></h2>
<p>This guide provides comprehensive information for developers working with the CIP Configuration Package, including development environment setup, coding standards, testing practices, and contribution workflows.</p>
<h2 id="development-environment-setup">Development Environment Setup<a class="headerlink" href="#development-environment-setup" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Development Tools&quot;
        Java[Java 17 JDK]
        Maven[Maven 3.8.6+]
        Git[Git 2.30+]
        IDE[IntelliJ IDEA / VS Code]
    end

    subgraph &quot;Runtime Environment&quot;
        Docker[Docker 20.10+]
        K8s[Kubernetes 1.24+]
        Helm[Helm 3.8+]
        CIP[CIP Platform Access]
    end

    subgraph &quot;Testing Tools&quot;
        Groovy[Groovy 3.0.10]
        Spock[Spock Framework]
        TestContainers[TestContainers]
        WireMock[WireMock]
    end

    Java --&gt; Maven
    Maven --&gt; Git
    Git --&gt; IDE

    Docker --&gt; K8s
    K8s --&gt; Helm
    Helm --&gt; CIP

    Groovy --&gt; Spock
    Spock --&gt; TestContainers
    TestContainers --&gt; WireMock
</code></pre></div>
<h3 id="environment-setup">Environment Setup<a class="headerlink" href="#environment-setup" title="Permanent link">&para;</a></h3>
<p><augment_code_snippet path="nc-cloud-bss-cip-config-b2b/buildConfig.yaml" mode="EXCERPT">
<div class="highlight"><pre><span></span><code><span class="c1"># Build tool versions</span>
<span class="nt">build</span><span class="p">:</span>
<span class="w">  </span><span class="nt">java</span><span class="p">:</span>
<span class="w">    </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;17&quot;</span>
<span class="w">    </span><span class="nt">distribution</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;temurin&quot;</span>
<span class="w">  </span><span class="nt">maven</span><span class="p">:</span>
<span class="w">    </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;3.8.6&quot;</span>
<span class="w">  </span><span class="nt">groovy</span><span class="p">:</span>
<span class="w">    </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;3.0.10&quot;</span>

<span class="c1"># Development environment</span>
<span class="nt">development</span><span class="p">:</span>
<span class="w">  </span><span class="nt">ide</span><span class="p">:</span>
<span class="w">    </span><span class="nt">recommended</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;IntelliJ</span><span class="nv"> </span><span class="s">IDEA&quot;</span>
<span class="w">    </span><span class="nt">plugins</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Groovy&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Spock</span><span class="nv"> </span><span class="s">Framework&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Kubernetes&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Docker&quot;</span>

<span class="w">  </span><span class="nt">tools</span><span class="p">:</span>
<span class="w">    </span><span class="nt">docker</span><span class="p">:</span>
<span class="w">      </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;20.10+&quot;</span>
<span class="w">    </span><span class="nt">kubernetes</span><span class="p">:</span>
<span class="w">      </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.24+&quot;</span>
<span class="w">    </span><span class="nt">helm</span><span class="p">:</span>
<span class="w">      </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;3.8+&quot;</span>
</code></pre></div>
</augment_code_snippet></p>
<h3 id="local-development-setup">Local Development Setup<a class="headerlink" href="#local-development-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Clone the repository</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/telus/nc-cloud-bss-cip-config-b2b.git
<span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-cip-config-b2b

<span class="c1"># Set up Java environment</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">JAVA_HOME</span><span class="o">=</span>/path/to/java17
<span class="nb">export</span><span class="w"> </span><span class="nv">PATH</span><span class="o">=</span><span class="nv">$JAVA_HOME</span>/bin:<span class="nv">$PATH</span>

<span class="c1"># Verify Java version</span>
java<span class="w"> </span>-version

<span class="c1"># Install dependencies</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install

<span class="c1"># Run tests</span>
mvn<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Start local development environment</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>-d
</code></pre></div>
<h2 id="project-structure">Project Structure<a class="headerlink" href="#project-structure" title="Permanent link">&para;</a></h2>
<h3 id="directory-organization">Directory Organization<a class="headerlink" href="#directory-organization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>nc-cloud-bss-cip-config-b2b/
├── content/                          # Configuration content
│   ├── config.json                  # Main configuration orchestrator
│   ├── chains/                      # Integration chain definitions
│   │   └── {chain-id}/
│   │       ├── chain-{id}.yaml     # Chain configuration
│   │       └── scripts/            # Groovy scripts
│   ├── services/                    # Service catalog definitions
│   │   └── {service-name}/
│   │       ├── service-{name}.yaml # Service configuration
│   │       └── schemas/            # API schemas
│   └── variables/                   # Environment variables
│       ├── common-variables.yaml   # Common variables
│       └── {env}/                  # Environment-specific variables
├── deployments/                     # Deployment configurations
│   ├── charts/                     # Helm charts
│   └── deployment-configuration.json
├── TLS-config/                      # TLS certificate configurations
│   └── cert/                       # Environment-specific certificates
├── src/                            # Source code
│   ├── main/                       # Main source code
│   └── test/                       # Test code
│       └── groovy/                 # Groovy test files
├── descriptors/                     # Maven assembly descriptors
├── pom.xml                         # Maven configuration
├── buildConfig.yaml                # Build configuration
├── docker-compose.yml              # Local development environment
└── README.md                       # Project documentation
</code></pre></div>
<h2 id="development-workflow">Development Workflow<a class="headerlink" href="#development-workflow" title="Permanent link">&para;</a></h2>
<h3 id="git-workflow">Git Workflow<a class="headerlink" href="#git-workflow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>gitgraph
    commit id: &quot;Initial&quot;
    branch develop
    checkout develop
    commit id: &quot;Feature 1&quot;
    branch feature/new-chain
    checkout feature/new-chain
    commit id: &quot;Add chain config&quot;
    commit id: &quot;Add tests&quot;
    checkout develop
    merge feature/new-chain
    commit id: &quot;Integration tests&quot;
    checkout main
    merge develop
    commit id: &quot;Release v1.1.0&quot;
    tag: &quot;v1.1.0&quot;
</code></pre></div>
<h3 id="branch-strategy">Branch Strategy<a class="headerlink" href="#branch-strategy" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>main</strong>: Production-ready code</li>
<li><strong>develop</strong>: Integration branch for features</li>
<li><strong>feature/</strong>*: Feature development branches</li>
<li><strong>hotfix/</strong>*: Critical bug fixes</li>
<li><strong>release/</strong>*: Release preparation branches</li>
</ul>
<h3 id="development-process">Development Process<a class="headerlink" href="#development-process" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Create Feature Branch</strong>: <code>git checkout -b feature/new-integration-chain</code></li>
<li><strong>Develop Feature</strong>: Implement configuration changes and tests</li>
<li><strong>Run Tests</strong>: <code>mvn test</code> to ensure all tests pass</li>
<li><strong>Code Review</strong>: Create pull request for peer review</li>
<li><strong>Integration Testing</strong>: Merge to develop branch for integration testing</li>
<li><strong>Release</strong>: Merge to main branch and tag release</li>
</ol>
<h2 id="configuration-development">Configuration Development<a class="headerlink" href="#configuration-development" title="Permanent link">&para;</a></h2>
<h3 id="chain-development">Chain Development<a class="headerlink" href="#chain-development" title="Permanent link">&para;</a></h3>
<h4 id="creating-a-new-integration-chain">Creating a New Integration Chain<a class="headerlink" href="#creating-a-new-integration-chain" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Example: New customer onboarding chain</span>
<span class="nt">chain</span><span class="p">:</span>
<span class="w">  </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;customer-onboarding-chain&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Customer</span><span class="nv"> </span><span class="s">Onboarding</span><span class="nv"> </span><span class="s">Workflow&quot;</span>
<span class="w">  </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.0.0&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Orchestrates</span><span class="nv"> </span><span class="s">customer</span><span class="nv"> </span><span class="s">onboarding</span><span class="nv"> </span><span class="s">process&quot;</span>

<span class="w">  </span><span class="nt">metadata</span><span class="p">:</span>
<span class="w">    </span><span class="nt">category</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;customer-management&quot;</span>
<span class="w">    </span><span class="nt">priority</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;high&quot;</span>
<span class="w">    </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">300000</span>
<span class="w">    </span><span class="nt">retryPolicy</span><span class="p">:</span>
<span class="w">      </span><span class="nt">maxAttempts</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">      </span><span class="nt">backoffMultiplier</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>

<span class="w">  </span><span class="nt">triggers</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">event</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;customer.created&quot;</span>
<span class="w">      </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;customer.type</span><span class="nv"> </span><span class="s">==</span><span class="nv"> </span><span class="s">&#39;B2B&#39;&quot;</span>

<span class="w">  </span><span class="nt">variables</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;customerId&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">      </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;accountType&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">      </span><span class="nt">default</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;standard&quot;</span>

<span class="w">  </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;validateCustomer&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy&quot;</span>
<span class="w">      </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;scripts/validate-customer.groovy&quot;</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;createAccount&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service&quot;</span>
<span class="w">      </span><span class="nt">service</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;crm-integration&quot;</span>
<span class="w">      </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;createAccount&quot;</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;setupBilling&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service&quot;</span>
<span class="w">      </span><span class="nt">service</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;billing-integration&quot;</span>
<span class="w">      </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;createBillingAccount&quot;</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;sendWelcomeEmail&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy&quot;</span>
<span class="w">      </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;scripts/send-welcome-email.groovy&quot;</span>
</code></pre></div>
<h4 id="groovy-script-development">Groovy Script Development<a class="headerlink" href="#groovy-script-development" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// scripts/validate-customer.groovy</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">groovy.json.JsonSlurper</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">groovy.json.JsonBuilder</span>

<span class="kt">def</span><span class="w"> </span><span class="nf">validateCustomer</span><span class="o">(</span><span class="n">customerData</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="n">errors</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">[]</span>

<span class="w">    </span><span class="c1">// Validate required fields</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="o">(!</span><span class="n">customerData</span><span class="o">.</span><span class="na">name</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="n">errors</span><span class="o">.</span><span class="na">add</span><span class="o">(</span><span class="s2">&quot;Customer name is required&quot;</span><span class="o">)</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="o">(!</span><span class="n">customerData</span><span class="o">.</span><span class="na">email</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="n">errors</span><span class="o">.</span><span class="na">add</span><span class="o">(</span><span class="s2">&quot;Customer email is required&quot;</span><span class="o">)</span>
<span class="w">    </span><span class="o">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="o">(!</span><span class="n">isValidEmail</span><span class="o">(</span><span class="n">customerData</span><span class="o">.</span><span class="na">email</span><span class="o">))</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="n">errors</span><span class="o">.</span><span class="na">add</span><span class="o">(</span><span class="s2">&quot;Invalid email format&quot;</span><span class="o">)</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="o">(!</span><span class="n">customerData</span><span class="o">.</span><span class="na">businessNumber</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="n">errors</span><span class="o">.</span><span class="na">add</span><span class="o">(</span><span class="s2">&quot;Business number is required&quot;</span><span class="o">)</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="c1">// Return validation result</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="o">[</span>
<span class="w">        </span><span class="nl">valid:</span><span class="w"> </span><span class="n">errors</span><span class="o">.</span><span class="na">isEmpty</span><span class="o">(),</span>
<span class="w">        </span><span class="nl">errors:</span><span class="w"> </span><span class="n">errors</span><span class="o">,</span>
<span class="w">        </span><span class="nl">customerData:</span><span class="w"> </span><span class="n">customerData</span>
<span class="w">    </span><span class="o">]</span>
<span class="o">}</span>

<span class="kt">def</span><span class="w"> </span><span class="nf">isValidEmail</span><span class="o">(</span><span class="n">email</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">email</span><span class="w"> </span><span class="o">==~</span><span class="w"> </span><span class="s">/^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/</span>
<span class="o">}</span>

<span class="c1">// Main execution</span>
<span class="kt">def</span><span class="w"> </span><span class="n">input</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">binding</span><span class="o">.</span><span class="na">variables</span><span class="o">.</span><span class="na">input</span>
<span class="kt">def</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validateCustomer</span><span class="o">(</span><span class="n">input</span><span class="o">.</span><span class="na">customer</span><span class="o">)</span>

<span class="c1">// Set output variables</span>
<span class="n">binding</span><span class="o">.</span><span class="na">variables</span><span class="o">.</span><span class="na">validationResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">result</span>
<span class="n">binding</span><span class="o">.</span><span class="na">variables</span><span class="o">.</span><span class="na">isValid</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">result</span><span class="o">.</span><span class="na">valid</span>

<span class="k">return</span><span class="w"> </span><span class="n">result</span>
</code></pre></div>
<h3 id="service-development">Service Development<a class="headerlink" href="#service-development" title="Permanent link">&para;</a></h3>
<h4 id="creating-a-new-service-definition">Creating a New Service Definition<a class="headerlink" href="#creating-a-new-service-definition" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Example: Customer management service</span>
<span class="nt">service</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;customer-management-tmf&quot;</span>
<span class="w">  </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.0.0&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TMF-compliant</span><span class="nv"> </span><span class="s">customer</span><span class="nv"> </span><span class="s">management</span><span class="nv"> </span><span class="s">service&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TMF&quot;</span>

<span class="w">  </span><span class="nt">specification</span><span class="p">:</span>
<span class="w">    </span><span class="nt">group</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;tmf-629&quot;</span>
<span class="w">    </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;4.0.0&quot;</span>
<span class="w">    </span><span class="nt">title</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Customer</span><span class="nv"> </span><span class="s">Management&quot;</span>

<span class="w">  </span><span class="nt">endpoints</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;customer&quot;</span>
<span class="w">      </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/tmf-api/customerManagement/v4/customer&quot;</span>
<span class="w">      </span><span class="nt">methods</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;GET&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;POST&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;PATCH&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;DELETE&quot;</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Customer</span><span class="nv"> </span><span class="s">lifecycle</span><span class="nv"> </span><span class="s">management&quot;</span>
<span class="w">      </span><span class="nt">parameters</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;id&quot;</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">          </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Customer</span><span class="nv"> </span><span class="s">identifier&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;fields&quot;</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">          </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Comma-separated</span><span class="nv"> </span><span class="s">list</span><span class="nv"> </span><span class="s">of</span><span class="nv"> </span><span class="s">fields</span><span class="nv"> </span><span class="s">to</span><span class="nv"> </span><span class="s">include&quot;</span>
<span class="w">      </span><span class="nt">responses</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">code</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">200</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Success&quot;</span>
<span class="w">          </span><span class="nt">schema</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Customer&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">code</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">400</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Bad</span><span class="nv"> </span><span class="s">Request&quot;</span>
<span class="w">          </span><span class="nt">schema</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Error&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">code</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">404</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Not</span><span class="nv"> </span><span class="s">Found&quot;</span>
<span class="w">          </span><span class="nt">schema</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Error&quot;</span>

<span class="w">  </span><span class="nt">authentication</span><span class="p">:</span>
<span class="w">    </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;oauth2&quot;</span>
<span class="w">    </span><span class="nt">scopes</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;customer:read&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;customer:write&quot;</span><span class="p p-Indicator">]</span>

<span class="w">  </span><span class="nt">behavior</span><span class="p">:</span>
<span class="w">    </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30000</span>
<span class="w">    </span><span class="nt">retries</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">    </span><span class="nt">circuitBreaker</span><span class="p">:</span>
<span class="w">      </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">failureThreshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">      </span><span class="nt">recoveryTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">60000</span>
</code></pre></div>
<h2 id="testing-framework">Testing Framework<a class="headerlink" href="#testing-framework" title="Permanent link">&para;</a></h2>
<h3 id="test-structure">Test Structure<a class="headerlink" href="#test-structure" title="Permanent link">&para;</a></h3>
<p><augment_code_snippet path="nc-cloud-bss-cip-config-b2b/src/test/groovy/Create_Or_Amend_Credit_Assessment_Request/Compose_Credit_Related_Product_Request_Tests.groovy" mode="EXCERPT">
<div class="highlight"><pre><span></span><code><span class="kn">package</span><span class="w"> </span><span class="n">Create_Or_Amend_Credit_Assessment_Request</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">spock.lang.Specification</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">spock.lang.Unroll</span>

<span class="kd">class</span><span class="w"> </span><span class="nc">Compose_Credit_Related_Product_Request_Tests</span><span class="w"> </span><span class="kd">extends</span><span class="w"> </span><span class="n">Specification</span><span class="w"> </span><span class="o">{</span>

<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="n">chainExecutor</span>
<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="n">mockSfdcService</span>
<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="n">mockQuoteService</span>

<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="nf">setup</span><span class="o">()</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="c1">// Initialize test dependencies</span>
<span class="w">        </span><span class="n">chainExecutor</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ChainExecutor</span><span class="o">()</span>
<span class="w">        </span><span class="n">mockSfdcService</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Mock</span><span class="o">(</span><span class="n">SfdcService</span><span class="o">)</span>
<span class="w">        </span><span class="n">mockQuoteService</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Mock</span><span class="o">(</span><span class="n">QuoteService</span><span class="o">)</span>

<span class="w">        </span><span class="n">chainExecutor</span><span class="o">.</span><span class="na">registerService</span><span class="o">(</span><span class="s2">&quot;sfdc-integration&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">mockSfdcService</span><span class="o">)</span>
<span class="w">        </span><span class="n">chainExecutor</span><span class="o">.</span><span class="na">registerService</span><span class="o">(</span><span class="s2">&quot;quote-service&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">mockQuoteService</span><span class="o">)</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="nf">&quot;should compose credit related product request successfully&quot;</span><span class="o">()</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="nl">given:</span><span class="w"> </span><span class="s2">&quot;valid input data&quot;</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">inputData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">[</span>
<span class="w">            </span><span class="nl">customerId:</span><span class="w"> </span><span class="s2">&quot;12345&quot;</span><span class="o">,</span>
<span class="w">            </span><span class="nl">quoteId:</span><span class="w"> </span><span class="s2">&quot;Q-67890&quot;</span><span class="o">,</span>
<span class="w">            </span><span class="nl">products:</span><span class="w"> </span><span class="o">[</span>
<span class="w">                </span><span class="o">[</span><span class="nl">id:</span><span class="w"> </span><span class="s2">&quot;product1&quot;</span><span class="o">,</span><span class="w"> </span><span class="nl">quantity:</span><span class="w"> </span><span class="mi">1</span><span class="o">,</span><span class="w"> </span><span class="nl">price:</span><span class="w"> </span><span class="mf">100.00</span><span class="o">]</span>
<span class="w">            </span><span class="o">]</span>
<span class="w">        </span><span class="o">]</span>

<span class="w">        </span><span class="nl">and:</span><span class="w"> </span><span class="s2">&quot;mock service responses&quot;</span>
<span class="w">        </span><span class="n">mockSfdcService</span><span class="o">.</span><span class="na">createCreditAssessment</span><span class="o">(</span><span class="n">_</span><span class="o">)</span><span class="w"> </span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="o">[</span>
<span class="w">            </span><span class="nl">id:</span><span class="w"> </span><span class="s2">&quot;CA-12345&quot;</span><span class="o">,</span>
<span class="w">            </span><span class="nl">status:</span><span class="w"> </span><span class="s2">&quot;PENDING&quot;</span>
<span class="w">        </span><span class="o">]</span>

<span class="w">        </span><span class="nl">when:</span><span class="w"> </span><span class="s2">&quot;chain is executed&quot;</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">chainExecutor</span><span class="o">.</span><span class="na">execute</span><span class="o">(</span><span class="s2">&quot;credit-assessment-chain&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">inputData</span><span class="o">)</span>

<span class="w">        </span><span class="nl">then:</span><span class="w"> </span><span class="s2">&quot;request is composed correctly&quot;</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">success</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">true</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">data</span><span class="o">.</span><span class="na">creditRequest</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">data</span><span class="o">.</span><span class="na">creditRequest</span><span class="o">.</span><span class="na">customerId</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="s2">&quot;12345&quot;</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">data</span><span class="o">.</span><span class="na">creditRequest</span><span class="o">.</span><span class="na">products</span><span class="o">.</span><span class="na">size</span><span class="o">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="mi">1</span>

<span class="w">        </span><span class="nl">and:</span><span class="w"> </span><span class="s2">&quot;SFDC service is called&quot;</span>
<span class="w">        </span><span class="mi">1</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">mockSfdcService</span><span class="o">.</span><span class="na">createCreditAssessment</span><span class="o">(</span><span class="n">_</span><span class="o">)</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="nd">@Unroll</span>
<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="s2">&quot;should handle invalid input: #scenario&quot;</span><span class="o">()</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="nl">given:</span><span class="w"> </span><span class="s2">&quot;invalid input data&quot;</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">inputData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">input</span>

<span class="w">        </span><span class="nl">when:</span><span class="w"> </span><span class="s2">&quot;chain is executed&quot;</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">chainExecutor</span><span class="o">.</span><span class="na">execute</span><span class="o">(</span><span class="s2">&quot;credit-assessment-chain&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">inputData</span><span class="o">)</span>

<span class="w">        </span><span class="nl">then:</span><span class="w"> </span><span class="s2">&quot;validation error is returned&quot;</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">success</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">false</span>
<span class="w">        </span><span class="n">result</span><span class="o">.</span><span class="na">errors</span><span class="o">.</span><span class="na">contains</span><span class="o">(</span><span class="n">expectedError</span><span class="o">)</span>

<span class="w">        </span><span class="nl">where:</span>
<span class="w">        </span><span class="n">scenario</span><span class="w">              </span><span class="o">|</span><span class="w"> </span><span class="n">input</span><span class="w">                           </span><span class="o">|</span><span class="w"> </span><span class="n">expectedError</span>
<span class="w">        </span><span class="s2">&quot;missing customer ID&quot;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="o">[</span><span class="nl">quoteId:</span><span class="w"> </span><span class="s2">&quot;Q-123&quot;</span><span class="o">]</span><span class="w">             </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;Customer ID is required&quot;</span>
<span class="w">        </span><span class="s2">&quot;missing quote ID&quot;</span><span class="w">    </span><span class="o">|</span><span class="w"> </span><span class="o">[</span><span class="nl">customerId:</span><span class="w"> </span><span class="s2">&quot;12345&quot;</span><span class="o">]</span><span class="w">          </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;Quote ID is required&quot;</span>
<span class="w">        </span><span class="s2">&quot;empty products&quot;</span><span class="w">      </span><span class="o">|</span><span class="w"> </span><span class="o">[</span><span class="nl">customerId:</span><span class="w"> </span><span class="s2">&quot;12345&quot;</span><span class="o">,</span><span class="w"> </span>
<span class="w">                                </span><span class="nl">quoteId:</span><span class="w"> </span><span class="s2">&quot;Q-123&quot;</span><span class="o">,</span><span class="w"> </span>
<span class="w">                                </span><span class="nl">products:</span><span class="w"> </span><span class="o">[]]</span><span class="w">                  </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;At least one product is required&quot;</span>
<span class="w">    </span><span class="o">}</span>
<span class="o">}</span>
</code></pre></div>
</augment_code_snippet></p>
<h3 id="testing-best-practices">Testing Best Practices<a class="headerlink" href="#testing-best-practices" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Testing Strategy&quot;
        Unit[Unit Tests]
        Integration[Integration Tests]
        Contract[Contract Tests]
        E2E[End-to-End Tests]
    end

    subgraph &quot;Test Tools&quot;
        Spock[Spock Framework]
        WireMock[WireMock]
        TestContainers[TestContainers]
        Pact[Pact Testing]
    end

    subgraph &quot;Test Coverage&quot;
        Logic[Business Logic]
        Config[Configuration]
        Integration[External Services]
        Performance[Performance]
    end

    Unit --&gt; Spock
    Integration --&gt; WireMock
    Contract --&gt; Pact
    E2E --&gt; TestContainers

    Spock --&gt; Logic
    WireMock --&gt; Integration
    TestContainers --&gt; Config
    Pact --&gt; Performance
</code></pre></div>
<h3 id="test-categories">Test Categories<a class="headerlink" href="#test-categories" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Unit Tests</strong>: Test individual components and business logic</li>
<li><strong>Integration Tests</strong>: Test service integrations and data flow</li>
<li><strong>Contract Tests</strong>: Verify API contracts and schemas</li>
<li><strong>End-to-End Tests</strong>: Test complete workflows and scenarios</li>
</ol>
<h2 id="code-quality-and-standards">Code Quality and Standards<a class="headerlink" href="#code-quality-and-standards" title="Permanent link">&para;</a></h2>
<h3 id="coding-standards">Coding Standards<a class="headerlink" href="#coding-standards" title="Permanent link">&para;</a></h3>
<h4 id="yaml-configuration-standards">YAML Configuration Standards<a class="headerlink" href="#yaml-configuration-standards" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Good: Clear, consistent formatting</span>
<span class="nt">chain</span><span class="p">:</span>
<span class="w">  </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;customer-onboarding-chain&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Customer</span><span class="nv"> </span><span class="s">Onboarding</span><span class="nv"> </span><span class="s">Workflow&quot;</span>
<span class="w">  </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.0.0&quot;</span>

<span class="w">  </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;validateInput&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy&quot;</span>
<span class="w">      </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;scripts/validate-input.groovy&quot;</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;processCustomer&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service&quot;</span>
<span class="w">      </span><span class="nt">service</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;customer-service&quot;</span>
<span class="w">      </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;createCustomer&quot;</span>

<span class="c1"># Bad: Inconsistent formatting, unclear naming</span>
<span class="nt">chain</span><span class="p">:</span>
<span class="w">  </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;chain1&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;some</span><span class="nv"> </span><span class="s">chain&quot;</span>
<span class="w">  </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;step1&quot;</span>
<span class="w">    </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;groovy&quot;</span>
<span class="w">    </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script1.groovy&quot;</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;step2&quot;</span>
<span class="w">    </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service&quot;</span>
<span class="w">    </span><span class="nt">service</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;svc1&quot;</span>
</code></pre></div>
<h4 id="groovy-script-standards">Groovy Script Standards<a class="headerlink" href="#groovy-script-standards" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// Good: Clear, well-documented script</span>
<span class="cm">/**</span>
<span class="cm"> * Validates customer data for onboarding process</span>
<span class="cm"> * @param customerData Customer information to validate</span>
<span class="cm"> * @return Validation result with errors if any</span>
<span class="cm"> */</span>
<span class="kt">def</span><span class="w"> </span><span class="nf">validateCustomerData</span><span class="o">(</span><span class="n">customerData</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="n">errors</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">[]</span>

<span class="w">    </span><span class="c1">// Validate required fields</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="o">(!</span><span class="n">customerData</span><span class="o">?.</span><span class="na">name</span><span class="o">?.</span><span class="na">trim</span><span class="o">())</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="n">errors</span><span class="w"> </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="s2">&quot;Customer name is required&quot;</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="o">(!</span><span class="n">customerData</span><span class="o">?.</span><span class="na">email</span><span class="o">?.</span><span class="na">trim</span><span class="o">())</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="n">errors</span><span class="w"> </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="s2">&quot;Customer email is required&quot;</span>
<span class="w">    </span><span class="o">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="o">(!</span><span class="n">isValidEmail</span><span class="o">(</span><span class="n">customerData</span><span class="o">.</span><span class="na">email</span><span class="o">))</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="n">errors</span><span class="w"> </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="s2">&quot;Invalid email format&quot;</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="o">[</span>
<span class="w">        </span><span class="nl">valid:</span><span class="w"> </span><span class="n">errors</span><span class="o">.</span><span class="na">isEmpty</span><span class="o">(),</span>
<span class="w">        </span><span class="nl">errors:</span><span class="w"> </span><span class="n">errors</span>
<span class="w">    </span><span class="o">]</span>
<span class="o">}</span>

<span class="kt">def</span><span class="w"> </span><span class="nf">isValidEmail</span><span class="o">(</span><span class="n">String</span><span class="w"> </span><span class="n">email</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">email</span><span class="w"> </span><span class="o">==~</span><span class="w"> </span><span class="s">/^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/</span>
<span class="o">}</span>

<span class="c1">// Main execution</span>
<span class="kt">def</span><span class="w"> </span><span class="n">input</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">binding</span><span class="o">.</span><span class="na">variables</span><span class="o">.</span><span class="na">input</span>
<span class="kt">def</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validateCustomerData</span><span class="o">(</span><span class="n">input</span><span class="o">.</span><span class="na">customer</span><span class="o">)</span>
<span class="n">binding</span><span class="o">.</span><span class="na">variables</span><span class="o">.</span><span class="na">validationResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">result</span>

<span class="k">return</span><span class="w"> </span><span class="n">result</span>
</code></pre></div>
<h3 id="code-review-checklist">Code Review Checklist<a class="headerlink" href="#code-review-checklist" title="Permanent link">&para;</a></h3>
<ul>
<li>[ ] Configuration follows YAML standards</li>
<li>[ ] Groovy scripts are well-documented</li>
<li>[ ] Error handling is comprehensive</li>
<li>[ ] Tests cover all scenarios</li>
<li>[ ] Security considerations addressed</li>
<li>[ ] Performance implications considered</li>
<li>[ ] Documentation is updated</li>
</ul>
<h2 id="debugging-and-troubleshooting">Debugging and Troubleshooting<a class="headerlink" href="#debugging-and-troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="local-development-debugging">Local Development Debugging<a class="headerlink" href="#local-development-debugging" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># docker-compose.yml for local development</span>
<span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;3.8&#39;</span>
<span class="nt">services</span><span class="p">:</span>
<span class="w">  </span><span class="nt">cip-platform</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus/cip-platform:latest</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;8080:8080&quot;</span>
<span class="w">    </span><span class="nt">environment</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">LOG_LEVEL=DEBUG</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ENVIRONMENT=development</span>
<span class="w">    </span><span class="nt">volumes</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./content:/app/config</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./logs:/app/logs</span>

<span class="w">  </span><span class="nt">mock-services</span><span class="p">:</span>
<span class="w">    </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">wiremock/wiremock:latest</span>
<span class="w">    </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;8081:8080&quot;</span>
<span class="w">    </span><span class="nt">volumes</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./test/wiremock:/home/<USER>/span>
</code></pre></div>
<h3 id="debugging-tools">Debugging Tools<a class="headerlink" href="#debugging-tools" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Debugging Tools&quot;
        Logs[Application Logs]
        Metrics[Performance Metrics]
        Traces[Distributed Tracing]
        Debugger[Remote Debugging]
    end

    subgraph &quot;Monitoring&quot;
        ELK[ELK Stack]
        Prometheus[Prometheus]
        Jaeger[Jaeger]
        IDE[IDE Debugger]
    end

    Logs --&gt; ELK
    Metrics --&gt; Prometheus
    Traces --&gt; Jaeger
    Debugger --&gt; IDE
</code></pre></div>
<h3 id="common-issues-and-solutions">Common Issues and Solutions<a class="headerlink" href="#common-issues-and-solutions" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Issue</th>
<th>Symptoms</th>
<th>Solution</th>
</tr>
</thead>
<tbody>
<tr>
<td>Configuration validation errors</td>
<td>Build failures, deployment errors</td>
<td>Check YAML syntax, validate against schema</td>
</tr>
<tr>
<td>Chain execution failures</td>
<td>Runtime errors, timeout exceptions</td>
<td>Review chain logic, check service dependencies</td>
</tr>
<tr>
<td>Service integration issues</td>
<td>Connection timeouts, authentication errors</td>
<td>Verify service configurations, check credentials</td>
</tr>
<tr>
<td>Performance problems</td>
<td>Slow response times, high resource usage</td>
<td>Profile code, optimize queries, add caching</td>
</tr>
</tbody>
</table>
<h2 id="contribution-guidelines">Contribution Guidelines<a class="headerlink" href="#contribution-guidelines" title="Permanent link">&para;</a></h2>
<h3 id="pull-request-process">Pull Request Process<a class="headerlink" href="#pull-request-process" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Create Feature Branch</strong>: <code>git checkout -b feature/description</code></li>
<li><strong>Implement Changes</strong>: Follow coding standards and best practices</li>
<li><strong>Add Tests</strong>: Ensure comprehensive test coverage</li>
<li><strong>Update Documentation</strong>: Update relevant documentation</li>
<li><strong>Run Tests</strong>: <code>mvn clean test</code> to verify all tests pass</li>
<li><strong>Create Pull Request</strong>: Submit PR with clear description</li>
<li><strong>Code Review</strong>: Address reviewer feedback</li>
<li><strong>Merge</strong>: Merge after approval and CI/CD success</li>
</ol>
<h3 id="commit-message-format">Commit Message Format<a class="headerlink" href="#commit-message-format" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>type(scope): description

[optional body]

[optional footer]
</code></pre></div>
<p><strong>Types:</strong>
- <code>feat</code>: New feature
- <code>fix</code>: Bug fix
- <code>docs</code>: Documentation changes
- <code>style</code>: Code style changes
- <code>refactor</code>: Code refactoring
- <code>test</code>: Test additions or modifications
- <code>chore</code>: Build process or auxiliary tool changes</p>
<p><strong>Examples:</strong>
<div class="highlight"><pre><span></span><code>feat(chains): add customer onboarding chain

Add new integration chain for B2B customer onboarding process
including validation, account creation, and welcome email.

Closes #123
</code></pre></div></p>
<h2 id="development-best-practices">Development Best Practices<a class="headerlink" href="#development-best-practices" title="Permanent link">&para;</a></h2>
<h3 id="1-configuration-management">1. Configuration Management<a class="headerlink" href="#1-configuration-management" title="Permanent link">&para;</a></h3>
<ul>
<li>Use meaningful names for chains and services</li>
<li>Implement proper error handling</li>
<li>Document complex business logic</li>
<li>Version configurations appropriately</li>
</ul>
<h3 id="2-testing">2. Testing<a class="headerlink" href="#2-testing" title="Permanent link">&para;</a></h3>
<ul>
<li>Write tests before implementation (TDD)</li>
<li>Maintain high test coverage (&gt;80%)</li>
<li>Use meaningful test descriptions</li>
<li>Mock external dependencies</li>
</ul>
<h3 id="3-performance">3. Performance<a class="headerlink" href="#3-performance" title="Permanent link">&para;</a></h3>
<ul>
<li>Optimize chain execution paths</li>
<li>Implement appropriate timeouts</li>
<li>Use caching where beneficial</li>
<li>Monitor resource usage</li>
</ul>
<h3 id="4-security">4. Security<a class="headerlink" href="#4-security" title="Permanent link">&para;</a></h3>
<ul>
<li>Validate all input data</li>
<li>Use secure credential storage</li>
<li>Implement proper authorization</li>
<li>Regular security reviews</li>
</ul>
<h3 id="5-documentation">5. Documentation<a class="headerlink" href="#5-documentation" title="Permanent link">&para;</a></h3>
<ul>
<li>Keep documentation up-to-date</li>
<li>Document API changes</li>
<li>Provide usage examples</li>
<li>Maintain troubleshooting guides</li>
</ul>
<p>This development guide provides a comprehensive foundation for contributing to the CIP Configuration Package while maintaining high quality, security, and performance standards.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>