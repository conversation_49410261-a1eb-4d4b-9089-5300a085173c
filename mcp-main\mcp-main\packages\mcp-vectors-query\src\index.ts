import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { ListToolsRequestSchema, CallToolRequestSchema } from '@modelcontextprotocol/sdk/types.js';
import { OpenAI } from 'openai';
import axios from 'axios';
import dotenv from 'dotenv';
import { ServerConfig, TurbopufferClient, VectorDocument, SearchResult } from './types.js';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __dirname = dirname(fileURLToPath(import.meta.url));
dotenv.config({ path: join(__dirname, '../.env') });

const VECTORS_KEY = process.env.VECTORS_KEY;
const VECTORS_USER_ID = process.env.VECTORS_USER_ID;
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
const NAMESPACE_BASE = process.env.TURBOPUFFER_NAMESPACE;
const NAMESPACE = `${VECTORS_USER_ID}-${NAMESPACE_BASE}`;
const OPENAI_MODEL_ID = process.env.OPENAI_MODEL_ID;
const TURBOPUFFER_REGION = process.env.TURBOPUFFER_REGION;
const OPENAI_API_BASE = process.env.OPENAI_API_BASE;
const VECTORS_API_BASE = process.env.VECTORS_API_BASE;

if (!VECTORS_KEY || !VECTORS_USER_ID || !OPENAI_API_KEY || !NAMESPACE_BASE || !OPENAI_MODEL_ID || !TURBOPUFFER_REGION || !OPENAI_API_BASE || !VECTORS_API_BASE) {
  console.error("Please set all required environment variables in your .env file");
  process.exit(1);
}

const openai = new OpenAI({
  apiKey: OPENAI_API_KEY,
  baseURL: OPENAI_API_BASE
});

const turbopuffer: TurbopufferClient = {
  query: async (params) => {
    const response = await axios.post(
      `${VECTORS_API_BASE}/${TURBOPUFFER_REGION}/v1/namespaces/${params.namespace}/query`,
      params,
      { headers: { 'Authorization': `Bearer ${VECTORS_KEY}` } }
    );
    console.error('Turbopuffer API Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  }
};

const config: ServerConfig = {
  turbopuffer,
  openai,
  namespace: NAMESPACE
};

class VectorsMCPServer {
  private server: Server;

  constructor(private config: ServerConfig) {
    this.server = new Server(
      {
        name: 'vectors-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          resources: {},
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    
    this.server.onerror = (error) => console.error('[MCP Error]', error);
    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'search_vectors',
          description: 'Search through vectors with a text query',
          inputSchema: {
            type: 'object',
            properties: {
              query: { type: 'string' },
              top_k: { type: 'number', default: 5 }
            },
            required: ['query']
          },
        }
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      try {
        if (request.params.name === 'search_vectors') {
          const { query, top_k } = request.params.arguments as { query: string; top_k?: number };
          const results = await this.searchVectors(query, top_k);
          return {
            content: [{
              type: 'text',
              text: JSON.stringify(results, null, 2)
            }]
          };
        } else {
          throw new Error(`Unknown tool: ${request.params.name}`);
        }
      } catch (error) {
        console.error('Error in CallToolRequestSchema handler:', error);
        return {
          content: [{
            type: 'text',
            text: (error as Error).message
          }],
          isError: true
        };
      }
    });
  }

  private async searchVectors(query: string, top_k: number = 5): Promise<SearchResult[]> {
    const embedding = await this.generateEmbedding(query);
    const results = await this.config.turbopuffer.query({
      namespace: this.config.namespace,
      vector: embedding,
      top_k,
      distance_metric: 'cosine_distance',
      include_attributes: ['filename', 'content']
    });

    console.error('searchVectors results:', JSON.stringify(results, null, 2));
    
    // Check if results is an array (direct response) or has a matches property
    const matches = Array.isArray(results) ? results : results.matches || [];
    
    if (matches.length > 0) {
      console.error('First match:', JSON.stringify(matches[0], null, 2));
      // Check if distance is in 'distance' or 'dist' property
      const distanceProperty = matches[0].distance !== undefined ? 'distance' : 'dist';
      console.error(`Distance property of first match:`, matches[0][distanceProperty]);
    }

    return matches.map((match: any) => ({
      document: {
        id: match.id,
        filename: match.attributes.filename,
        content: match.attributes.content
      },
      // Use either distance or dist property, whichever exists
      similarity: 1 - (match.distance !== undefined ? match.distance : match.dist)
    }));
  }

  private async generateEmbedding(text: string): Promise<number[]> {
    const response = await this.config.openai.embeddings.create({
      model: OPENAI_MODEL_ID as string,
      input: text
    });
    return response.data[0].embedding;
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Vectors MCP server running on stdio');
  }
}

const server = new VectorsMCPServer(config);
server.run().catch(console.error);
