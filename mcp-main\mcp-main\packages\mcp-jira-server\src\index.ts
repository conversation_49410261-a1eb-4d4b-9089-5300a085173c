#!/usr/bin/env node

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  <PERSON>rror<PERSON>ode,
  McpError,
} from "@modelcontextprotocol/sdk/types.js";
import axios, { AxiosInstance } from "axios";
import tunnel from "tunnel";

const JIRA_USERNAME = process.env.JIRA_USERNAME;
const JIRA_PASSWORD = process.env.JIRA_PASSWORD || process.env.JIRA_TOKEN;
const JIRA_BASE_URL = process.env.JIRA_BASE_URL;
const SPRINT_BOARD_ID = process.env.SPRINT_BOARD_ID;

// Proxy configuration
const PROXY_HOST = process.env.PROXY_HOST;
const PROXY_PORT = process.env.PROXY_PORT;

if (!JIRA_USERNAME || !JIRA_PASSWORD || !JIRA_BASE_URL) {
  throw new Error("JIRA environment variables are not set");
}

// Create Axios instance with proxy support if configured
let jiraApi: AxiosInstance;

if (PROXY_HOST && PROXY_PORT) {
  console.error(`[Setup] Using proxy: ${PROXY_HOST}:${PROXY_PORT}`);

  // Create tunnel agent for HTTPS
  const tunnelAgent = tunnel.httpsOverHttp({
    proxy: {
      host: PROXY_HOST,
      port: parseInt(PROXY_PORT, 10),
    },
  });

  // Create Axios instance with tunnel agent
  jiraApi = axios.create({
    baseURL: JIRA_BASE_URL,
    auth: {
      username: JIRA_USERNAME,
      password: JIRA_PASSWORD,
    },
    httpsAgent: tunnelAgent,
    timeout: 15000, // 15 seconds timeout
  });

  console.error("[Setup] HTTPS tunnel agent configured for Jira API");
} else {
  // Standard configuration without proxy
  jiraApi = axios.create({
    baseURL: JIRA_BASE_URL,
    auth: {
      username: JIRA_USERNAME,
      password: JIRA_PASSWORD,
    },
  });

  console.error("[Setup] Standard connection configured for Jira API");
}

interface JiraField {
  id: string;
  name: string;
  custom: boolean;
  type?: string;
  searchable: boolean;
}

interface StatusMapping {
  id: string;
  name: string;
  description?: string;
  categoryName?: string;
  categoryKey?: string;
}

class JiraServer {
  private server: Server;
  private userCache: Map<string, string>;
  private fieldMapping: Map<string, JiraField>;
  private statusMapping: Map<string, StatusMapping>;

  constructor() {
    this.server = new Server(
      {
        name: "jira-server",
        version: "0.1.0",
      },
      {
        capabilities: {
          tools: {},
        },
      },
    );

    this.userCache = new Map();
    this.fieldMapping = new Map();
    this.statusMapping = new Map();
    this.initializeFieldMapping();
    this.initializeStatusMapping();
    this.setupToolHandlers();

    this.server.onerror = (error) => console.error("[MCP Error]", error);
    process.on("SIGINT", async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private async initializeStatusMapping(): Promise<void> {
    try {
      const response = await jiraApi.get("/rest/api/2/status");
      response.data.forEach((status: any) => {
        this.statusMapping.set(status.name.toLowerCase(), {
          id: status.id,
          name: status.name,
          description: status.description,
          categoryName: status.statusCategory?.name,
          categoryKey: status.statusCategory?.key,
        });
      });
    } catch (error) {
      // Silent fail on initialization
    }
  }

  private getStatusId(statusName: string): string | undefined {
    const status = this.statusMapping.get(statusName.toLowerCase());
    return status?.id;
  }

  private async initializeFieldMapping(): Promise<void> {
    try {
      const response = await jiraApi.get("/rest/api/2/field");
      response.data.forEach((field: any) => {
        // Normalize field names to handle case-insensitive matching
        const normalizedName = field.name.toLowerCase();
        this.fieldMapping.set(normalizedName, {
          id: field.id,
          name: field.name,
          custom: field.custom,
          type: field.schema?.type,
          searchable: field.searchable,
        });
      });
    } catch (error) {
      // Silent fail on initialization
    }
  }

  private getFieldId(fieldName: string): string | undefined {
    const field = this.fieldMapping.get(fieldName.toLowerCase());
    return field?.id;
  }

  private getFieldName(fieldId: string): string | undefined {
    for (const [_, field] of this.fieldMapping.entries()) {
      if (field.id === fieldId) {
        return field.name;
      }
    }
    // If not found, return the original ID
    return fieldId;
  }

  private async getUserDisplayName(accountId: string): Promise<string> {
    if (this.userCache.has(accountId)) {
      return this.userCache.get(accountId)!;
    }

    try {
      const response = await jiraApi.get(
        `/rest/api/2/user?accountId=${accountId}`,
      );
      const displayName = response.data.displayName;
      this.userCache.set(accountId, displayName);
      return displayName;
    } catch (error) {
      // On error, return the account ID as fallback
      return accountId;
    }
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: "jira_search_issues",
          description: "Get JIRA issues using JQL query",
          inputSchema: {
            type: "object",
            properties: {
              jql: {
                type: "string",
                description: "JQL query string",
              },
              maxResults: {
                type: "number",
                description: "Maximum number of issues to return",
              },
              expand: {
                type: "string",
                description:
                  "Comma-separated list of additional fields to include (e.g., 'components,fixVersions,dueDate')",
              },
            },
            required: ["jql"],
          },
        },
        {
          name: "jira_get_epic_timeline",
          description: "Get a timeline report of all EPICs in a project",
          inputSchema: {
            type: "object",
            properties: {
              projectKey: {
                type: "string",
                description: "Project key",
              },
              labels: {
                type: "string",
                description: "Comma-separated list of labels to filter by",
              },
            },
            required: ["projectKey"],
          },
        },
        {
          name: "jira_create_issue",
          description: "Create a new JIRA issue",
          inputSchema: {
            type: "object",
            properties: {
              projectKey: {
                type: "string",
                description: "Project key",
              },
              issueType: {
                type: "string",
                description:
                  "Issue type (Enabler, Story, Spike, EPIC, Initiative)",
              },
              summary: {
                type: "string",
                description: "Issue summary",
              },
              description: {
                type: "string",
                description: "Issue description",
              },
              epicLink: {
                type: "string",
                description: "EPIC key to link the issue to",
              },
              parentIssueKey: {
                type: "string",
                description: "Parent issue key (required for sub-tasks)",
              },
              subTasks: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    summary: { type: "string" },
                    description: { type: "string" },
                  },
                },
                description: "Sub-tasks to create under this issue",
              },
              technicalInformation: {
                type: "string",
                description: "Technical information custom field",
              },
              acceptanceCriteria: {
                type: "string",
                description: "Acceptance criteria custom field",
              },
              labels: {
                type: "array",
                items: { type: "string" },
                description: "Labels to add to the issue",
              },
              storyPoints: {
                type: "number",
                description: "Story points estimate for the issue",
              },
              priority: {
                type: "string",
                description: "Priority of the issue (e.g., 'Highest', 'High', 'Medium', 'Low', 'Lowest')",
              },
            },
            required: ["projectKey", "issueType", "summary"],
          },
        },
        {
          name: "jira_update_issue",
          description: "Update an existing JIRA issue",
          inputSchema: {
            type: "object",
            properties: {
              issueKey: {
                type: "string",
                description: "Key of the issue to update",
              },
              fields: {
                type: "object",
                description: "Fields to update",
                properties: {
                  summary: {
                    type: "string",
                    description: "Updated summary",
                  },
                  description: {
                    type: "string",
                    description: "Updated description",
                  },
                  technicalInformation: {
                    type: "string",
                    description: "Updated technical information",
                  },
                  acceptanceCriteria: {
                    type: "string",
                    description: "Updated acceptance criteria",
                  },
                  labels: {
                    type: "array",
                    items: { type: "string" },
                    description: "Updated labels",
                  },
                  status: {
                    type: "string",
                    description:
                      "New status name (e.g., 'In Progress', 'Done')",
                  },
                  assignee: {
                    type: "string",
                    description:
                      "Account ID or username of the user to assign the issue to",
                  },
                  storyPoints: {
                    type: "number",
                    description: "Updated story points estimate",
                  },
                  priority: {
                    type: "string",
                    description: "Updated priority (e.g., 'Highest', 'High', 'Medium', 'Low', 'Lowest', 'Critical')",
                  },
                },
              },
            },
            required: ["issueKey", "fields"],
          },
        },
        {
          name: "jira_get_project_issues",
          description: "Get all issues for a specific Jira project",
          inputSchema: {
            type: "object",
            properties: {
              projectKey: {
                type: "string",
                description: "Project key",
              },
              limit: {
                type: "number",
                description: "Results limit (1-50, default: 10)",
              },
            },
            required: ["projectKey"],
          },
        },
        {
          name: "jira_get_issue",
          description: "Get details of a specific Jira issue",
          inputSchema: {
            type: "object",
            properties: {
              issueKey: {
                type: "string",
                description: "Jira issue key (e.g., 'PROJ-123')",
              },
              expand: {
                type: "string",
                description:
                  "Comma-separated list of additional fields to include",
              },
            },
            required: ["issueKey"],
          },
        },
        {
          name: "jira_add_comment",
          description: "Add a comment to a JIRA issue",
          inputSchema: {
            type: "object",
            properties: {
              issueKey: {
                type: "string",
                description: "Jira issue key (e.g., 'PROJ-123')",
              },
              comment: {
                type: "string",
                description: "Comment text (supports JIRA markup)",
              },
            },
            required: ["issueKey", "comment"],
          },
        },
        {
          name: "jira_get_fields",
          description:
            "Get all field names and IDs from JIRA including custom fields",
          inputSchema: {
            type: "object",
            properties: {},
            required: [],
          },
        },
        {
          name: "jira_update_comment",
          description: "Update an existing comment on a JIRA issue",
          inputSchema: {
            type: "object",
            properties: {
              issueKey: {
                type: "string",
                description: "Jira issue key (e.g., 'PROJ-123')",
              },
              commentId: {
                type: "string",
                description: "ID of the comment to update",
              },
              body: {
                type: "string",
                description: "New content for the comment (supports JIRA markup)",
              },
            },
            required: ["issueKey", "commentId", "body"],
          },
        },
        {
          name: "jira_get_worklogs",
          description: "Get worklogs for a specific JIRA issue",
          inputSchema: {
            type: "object",
            properties: {
              issueKey: {
                type: "string",
                description: "JIRA issue key (e.g., 'PROJ-123')",
              },
              startAt: {
                type: "number",
                description:
                  "The index of the first item to return in the page of results (page offset)",
              },
              maxResults: {
                type: "number",
                description: "The maximum number of items to return per page",
              },
              startedAfter: {
                type: "string",
                description:
                  "The start of the period to return worklogs for (ISO 8601 format)",
              },
            },
            required: ["issueKey"],
          },
        },
        {
          name: "jira_delete_comment",
          description: "Delete an existing comment from a JIRA issue",
          inputSchema: {
            type: "object",
            properties: {
              issueKey: {
                type: "string",
                description: "Jira issue key (e.g., 'PROJ-123')",
              },
              commentId: {
                type: "string",
                description: "ID of the comment to delete",
              },
            },
            required: ["issueKey", "commentId"],
          },
        },
        {
          name: "jira_add_worklog",
          description: "Add a worklog to a specific JIRA issue",
          inputSchema: {
            type: "object",
            properties: {
              issueKey: {
                type: "string",
                description: "JIRA issue key (e.g., 'PROJ-123')",
              },
              timeSpent: {
                type: "string",
                description:
                  "The time spent working on the issue (e.g., '3h 30m')",
              },
              comment: {
                type: "string",
                description: "A comment about the work performed",
              },
              started: {
                type: "string",
                description:
                  "The datetime on which the worklog was started (ISO 8601 format)",
              },
            },
            required: ["issueKey", "timeSpent"],
          },
        },
        {
          name: "jira_update_worklog",
          description: "Update an existing worklog in a JIRA issue",
          inputSchema: {
            type: "object",
            properties: {
              issueKey: {
                type: "string",
                description: "JIRA issue key (e.g., 'PROJ-123')",
              },
              worklogId: {
                type: "string",
                description: "The ID of the worklog to update",
              },
              timeSpent: {
                type: "string",
                description:
                  "The new time spent working on the issue (e.g., '3h 30m')",
              },
              comment: {
                type: "string",
                description: "A new comment about the work performed",
              },
              started: {
                type: "string",
                description:
                  "The new datetime on which the worklog was started (ISO 8601 format)",
              },
            },
            required: ["issueKey", "worklogId"],
          },
        },
        {
          name: "jira_delete_worklog",
          description: "Delete a worklog from a JIRA issue",
          inputSchema: {
            type: "object",
            properties: {
              issueKey: {
                type: "string",
                description: "JIRA issue key (e.g., 'PROJ-123')",
              },
              worklogId: {
                type: "string",
                description: "The ID of the worklog to delete",
              },
              adjustEstimate: {
                type: "string",
                enum: ["auto", "leave", "new", "manual"],
                description:
                  "Determines how to update the issue's remaining time estimate",
              },
              newEstimate: {
                type: "string",
                description:
                  "The new estimate for the issue (when adjustEstimate is 'new')",
              },
              reduceBy: {
                type: "string",
                description:
                  "The amount to reduce the estimate by (when adjustEstimate is 'manual')",
              },
            },
            required: ["issueKey", "worklogId"],
          },
        },
        {
          name: "jira_get_board_sprints",
          description: "Get all sprints from a specific board",
          inputSchema: {
            type: "object",
            properties: {
              boardId: {
                type: "string",
                description: "Board ID (rapidView ID)",
              },
              state: {
                type: "string",
                description: "Filter sprints by state (active,future,closed)",
                enum: ["active", "future", "closed"],
              },
            },
            required: ["boardId"],
          },
        },
        {
          name: "jira_get_sprint_issues",
          description:
            "Get all issues from the current active sprint using the configured SPRINT_BOARD_ID.",
          inputSchema: {
            type: "object",
            properties: {},
            required: [],
          },
        },
        {
          name: "jira_get_user_worklogs",
          description:
            "Get and analyze worklogs for a specific user within a date range",
          inputSchema: {
            type: "object",
            properties: {
              username: {
                type: "string",
                description: "Username or display name of the user",
              },
              startDate: {
                type: "string",
                description: "Start date in YYYY-MM-DD format",
              },
              endDate: {
                type: "string",
                description: "End date in YYYY-MM-DD format",
              },
            },
            required: ["username", "startDate", "endDate"],
          },
        },
        {
          name: "jira_get_user_info",
          description: "Get detailed information about a JIRA user",
          inputSchema: {
            type: "object",
            properties: {
              username: {
                type: "string",
                description: "Username or display name of the JIRA user",
              },
              accountId: {
                type: "string",
                description: "Account ID of the JIRA user",
              },
            },
            anyOf: [{ required: ["username"] }, { required: ["accountId"] }],
          },
        },
        {
          name: "jira_get_worklogs_bulk",
          description: "Get worklogs for multiple issues",
          inputSchema: {
            type: "object",
            properties: {
              issueKeys: {
                type: "array",
                items: { type: "string" },
                description:
                  "Array of issue keys to get worklogs for (max 1000)",
              },
              startedAfter: {
                type: "string",
                description:
                  "The start of the period to return worklogs for (ISO 8601 format)",
              },
            },
            required: ["issueKeys"],
          },
        },
        {
          name: "jira_search_users",
          description: "Search for users in JIRA",
          inputSchema: {
            type: "object",
            properties: {
              query: {
                type: "string",
                description:
                  "A query string used to search username, name or e-mail address",
              },
              startAt: {
                type: "number",
                description: "The index of the first user to return (0-based)",
              },
              maxResults: {
                type: "number",
                description:
                  "The maximum number of users to return (default: 50)",
              },
              includeInactive: {
                type: "boolean",
                description:
                  "Whether to include inactive users in the search results (default: false)",
              },
            },
            required: ["query"],
          },
        },
        {
          name: "jira_get_comments",
          description: "Get comments for a specific JIRA issue",
          inputSchema: {
            type: "object",
            properties: {
              issueKey: {
                type: "string",
                description: "Jira issue key (e.g., 'PROJ-123')",
              },
              maxResults: {
                type: "number",
                description: "Maximum number of comments to return",
              },
              startAt: {
                type: "number",
                description: "Index of the first comment to return",
              },
            },
            required: ["issueKey"],
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      try {
        switch (request.params.name) {
          case "jira_get_epic_timeline": {
            const { projectKey, labels } = request.params.arguments || {};

            let jql = `project = ${projectKey} AND issuetype = Epic AND status NOT IN ("Done", "Cancelled")`;
            if (labels) {
              const labelList = (labels as string)
                .split(",")
                .map((label: string) => label.trim());
              jql += ` AND labels IN (${labelList.join(",")})`;
            }

            // Get IDs for the fields we want to retrieve
            const fieldIds = {
              summary: this.getFieldId("Summary") || "summary",
              epicName: this.getFieldId("Epic Name") || "customfield_10014",
              startDate: this.getFieldId("Start Date") || "customfield_11112",
              endDate: this.getFieldId("End Date") || "customfield_11113",
              labels: this.getFieldId("Labels") || "labels",
              fixVersions: this.getFieldId("Fix Version/s") || "fixVersions",
              status: this.getFieldId("Status") || "status",
              priority: this.getFieldId("Priority") || "priority",
              created: this.getFieldId("Created") || "created",
            };

            const response = await jiraApi.get("/rest/api/2/search", {
              params: {
                jql: jql,
                maxResults: 1000,
                fields: Object.values(fieldIds),
                expand: "changelog",
              },
            });

            const epics = await Promise.all(
              response.data.issues.map(async (epic: any) => {
                let startDate =
                  epic.fields[fieldIds.startDate] ||
                  epic.fields[fieldIds.created];
                let endDate = epic.fields[fieldIds.endDate] || null;

                startDate = startDate && startDate.trim() ? startDate : null;
                endDate = endDate && endDate.trim() ? endDate : null;

                // Extract and enrich status transitions with user display names
                const statusTransitions = await Promise.all(
                  epic.changelog.histories
                    .filter((history: any) =>
                      history.items.some(
                        (item: any) => item.field === "status",
                      ),
                    )
                    .map(async (history: any) => {
                      const statusChange = history.items.find(
                        (item: any) => item.field === "status",
                      );
                      const displayName = await this.getUserDisplayName(
                        history.author.accountId,
                      );
                      return {
                        sourceStatus: statusChange.fromString,
                        destinationStatus: statusChange.toString,
                        date: history.created,
                        userId: history.author.name,
                        userName: displayName,
                      };
                    }),
                );

                return {
                  key: epic.key,
                  name:
                    epic.fields[fieldIds.epicName] ||
                    epic.fields[fieldIds.summary],
                  summary: epic.fields[fieldIds.summary],
                  status:
                    epic.fields[fieldIds.status]?.name ||
                    epic.fields.status?.name,
                  startDate,
                  endDate,
                  labels: epic.fields[fieldIds.labels] || epic.fields.labels,
                  fixVersions:
                    epic.fields[fieldIds.fixVersions]?.map(
                      (v: any) => v.name,
                    ) ||
                    epic.fields.fixVersions?.map((v: any) => v.name) ||
                    [],
                  priority:
                    epic.fields[fieldIds.priority]?.name ||
                    epic.fields.priority?.name,
                  statusTransitions,
                  // Include field IDs for reference
                  _fieldIds: fieldIds,
                };
              }),
            );

            epics.sort((a: any, b: any) => {
              if (!a.startDate) return 1;
              if (!b.startDate) return -1;
              return (
                new Date(a.startDate).getTime() -
                new Date(b.startDate).getTime()
              );
            });

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(epics, null, 2),
                },
              ],
            };
          }

          case "jira_search_issues": {
            const jql = String(request.params.arguments?.jql);
            const maxResults =
              Number(request.params.arguments?.maxResults) || 100;
            const expand = request.params.arguments?.expand as
              | string
              | undefined;

            // Base fields that are always included
            const baseFieldIds = {
              summary: this.getFieldId("Summary") || "summary",
              description: this.getFieldId("Description") || "description", // Use standard field ID, custom field ID is handled in the transformation
              status: this.getFieldId("Status") || "status",
              issuetype: this.getFieldId("Issue Type") || "issuetype",
              priority: this.getFieldId("Priority") || "priority",
              created: this.getFieldId("Created") || "created",
              updated: this.getFieldId("Updated") || "updated",
              assignee: this.getFieldId("Assignee") || "assignee",
              reporter: this.getFieldId("Reporter") || "reporter",
              technical:
                this.getFieldId("Technical documentation") ||
                "customfield_17065" ||
                "customfield_12850",
              acceptance:
                this.getFieldId("Acceptance Criteria") || "customfield_11001",
              labels: this.getFieldId("Labels") || "labels",
              timeTracking: this.getFieldId("Time Tracking") || "timetracking",
              resolutiondate:
                this.getFieldId("Resolution Date") || "resolutiondate",
              sprint:
                this.getFieldId("Sprint") ||
                "customfield_10003" ||
                "customfield_10000",
              epicLink: this.getFieldId("Epic Link") || "customfield_10004",
              storyPoints:
                this.getFieldId("Story Points") || "customfield_17372" || "customfield_10016",
              resolution: this.getFieldId("Resolution") || "resolution",
              components: this.getFieldId("Component/s") || "components",
              fixVersions: this.getFieldId("Fix Version/s") || "fixVersions",
            };

            // Additional fields that can be expanded
            const expandableFieldIds: { [key: string]: string } = {
              components: this.getFieldId("Component/s") || "components",
              fixVersions: this.getFieldId("Fix Version/s") || "fixVersions",
              dueDate: this.getFieldId("Due Date") || "duedate",
              environment: this.getFieldId("Environment") || "environment",
              attachments: this.getFieldId("Attachment") || "attachment",
              issueLinks: this.getFieldId("Linked Issues") || "issuelinks",
              subtasks: this.getFieldId("Sub-Tasks") || "subtasks",
              // Add more expandable fields as needed
            };

            // Add requested expanded fields
            const requestedFields = expand
              ? expand.split(",").map((f) => f.trim().toLowerCase())
              : [];

            const allFieldIds = {
              ...baseFieldIds,
              ...Object.fromEntries(
                requestedFields
                  .filter((field) => expandableFieldIds[field])
                  .map((field) => [field, expandableFieldIds[field]]),
              ),
            };

            const response = await jiraApi.get("/rest/api/2/search", {
              params: {
                jql: jql,
                maxResults: maxResults,
                fields: Object.values(allFieldIds)
                  .concat([
                    "status",
                    "description",
                    "assignee",
                    "reporter",
                    "priority",
                    "issuetype",
                    "resolution",
                    "labels",
                    "updated",
                    "customfield_10004",
                    "customfield_11001",
                  ])
                  .join(","),
                expand: "renderedFields",
              },
            });

            const issues = response.data.issues.map((issue: any) => {
              const result: any = {
                key: issue.key,
                summary: issue.fields[allFieldIds.summary],
                description:
                  issue.renderedFields?.[allFieldIds.description] ||
                  issue.fields[allFieldIds.description] ||
                  issue.fields.description, // Also check standard description field
                status:
                  issue.fields[allFieldIds.status]?.name ||
                  issue.fields.status?.name ||
                  "Unknown",
                issueType:
                  issue.fields[allFieldIds.issuetype]?.name ||
                  issue.fields.issuetype?.name,
                priority:
                  issue.fields[allFieldIds.priority]?.name ||
                  issue.fields.priority?.name,
                created: issue.fields[allFieldIds.created],
                updated:
                  issue.fields.updated ||
                  issue.fields[allFieldIds.updated] ||
                  issue.updated,
                assignee:
                  issue.fields[allFieldIds.assignee]?.displayName ||
                  issue.fields.assignee?.displayName,
                reporter:
                  issue.fields[allFieldIds.reporter]?.displayName ||
                  issue.fields.reporter?.displayName,
                technical:
                  issue.renderedFields?.[allFieldIds.technical] ||
                  issue.fields[allFieldIds.technical],
                acceptance:
                  issue.renderedFields?.[allFieldIds.acceptance] ||
                  issue.fields.customfield_11001 ||
                  issue.fields[allFieldIds.acceptance],
                labels: issue.fields[allFieldIds.labels] || issue.fields.labels,
                epicLink:
                  issue.fields[allFieldIds.epicLink] ||
                  issue.fields.customfield_10004 ||
                  issue.fields.epicLink,
                timeTracking: issue.fields[allFieldIds.timeTracking],
                resolutiondate: issue.fields[allFieldIds.resolutiondate],
                sprint: (() => {
                  const sprintField = issue.fields[allFieldIds.sprint];
                  // If it's an array (Jira Datacenter format)
                  if (Array.isArray(sprintField) && sprintField.length > 0) {
                    // Check if array contains objects with name property (Jira Cloud format)
                    if (
                      typeof sprintField[0] === "object" &&
                      sprintField[0] !== null &&
                      "name" in sprintField[0]
                    ) {
                      return sprintField[0].name;
                    }

                    // Otherwise try the string format with name pattern (Jira Datacenter format)
                    const sprintStr = String(sprintField[0]);
                    const match = sprintStr.match(/name=(.*?)(?:,|$)/);
                    if (match) {
                      return match[1];
                    }
                    // If no match found, return the raw string
                    return sprintStr;
                  }
                  // If it's a single object (another possible Jira Cloud format)
                  else if (
                    sprintField &&
                    typeof sprintField === "object" &&
                    !Array.isArray(sprintField)
                  ) {
                    if ("name" in sprintField) {
                      return sprintField.name;
                    }
                  }
                  return null;
                })(),
                storyPoints: issue.fields[allFieldIds.storyPoints],
                resolution:
                  issue.fields[allFieldIds.resolution]?.name ||
                  issue.fields.resolution?.name,
                components:
                  issue.fields[allFieldIds.components]?.map(
                    (c: any) => c.name,
                  ) || [],
                fixVersions:
                  issue.fields[allFieldIds.fixVersions]?.map(
                    (v: any) => v.name,
                  ) || [],
              };

              // Add expanded fields if requested
              requestedFields.forEach((field) => {
                if (expandableFieldIds[field]) {
                  const fieldId = expandableFieldIds[field];
                  switch (field) {
                    case "components":
                      result[field] = issue.fields[fieldId] || [];
                      break;
                    case "fixVersions":
                      result.fixVersions = (issue.fields[fieldId] || []).map(
                        (v: any) => v.name,
                      );
                      break;
                    default:
                      result[field] = issue.fields[fieldId];
                  }
                }
              });

              // Remove _fieldIds and _availableExpansions from the result
              delete result._fieldIds;
              delete result._availableExpansions;

              return result;
            });

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(issues, null, 2),
                },
              ],
            };
          }

          case "jira_create_issue": {
            const {
              projectKey,
              issueType,
              summary,
              description,
              epicLink,
              subTasks,
              technicalInformation,
              acceptanceCriteria,
              labels,
              parentIssueKey,
              storyPoints,
              priority,
            } = request.params.arguments as {
              projectKey: string;
              issueType: string;
              summary: string;
              description?: string;
              epicLink?: string;
              parentIssueKey?: string;
              subTasks?: Array<{ summary: string; description: string }>;
              technicalInformation?: string;
              acceptanceCriteria?: string;
              labels?: string[];
              storyPoints?: number;
              priority?: string;
              originalEstimate?: string;
              remainingEstimate?: string;
            };

            const { originalEstimate, remainingEstimate } = request.params
              .arguments as {
              originalEstimate?: string;
              remainingEstimate?: string;
            };

            // First, get project metadata to understand available issue types
            const metadataResponse = await jiraApi.get(
              `/rest/api/2/issue/createmeta?projectKeys=${projectKey}&expand=projects.issuetypes.fields`,
            );
            const project = metadataResponse.data.projects[0];

            if (!project) {
              throw new McpError(
                ErrorCode.InvalidParams,
                `Project ${projectKey} not found`,
              );
            }

            const matchingIssueType = project.issuetypes.find(
              (type: any) =>
                type.name.toLowerCase() === issueType.toLowerCase(),
            );

            if (!matchingIssueType) {
              throw new McpError(
                ErrorCode.InvalidParams,
                `Invalid issue type: ${issueType}. Available types: ${project.issuetypes.map((t: any) => t.name).join(", ")}`,
              );
            }

            // Get field information
            const fields = matchingIssueType.fields;

            // Get IDs for all relevant fields with fallbacks
            const fieldIds = {
              summary: this.getFieldId("Summary") || "summary",
              description: this.getFieldId("Description") || "description",
              issuetype: this.getFieldId("Issue Type") || "issuetype",
              technicalInformation:
                this.getFieldId("Technical documentation") ||
                "customfield_12850" ||
                "customfield_17065",
              acceptanceCriteria:
                this.getFieldId("Acceptance Criteria") ||
                "customfield_10011" ||
                "customfield_11001",
              epicName:
                this.getFieldId("Epic Name") ||
                "customfield_10014" ||
                "customfield_10006",
              epicLink:
                this.getFieldId("Epic Link") ||
                "customfield_10004" ||
                "customfield_10000",
              labels: this.getFieldId("Labels") || "labels",
              timetracking: this.getFieldId("Time Tracking") || "timetracking",
              storyPoints:
                this.getFieldId("Story Points") || "customfield_17372" || "customfield_10016",
              priority: this.getFieldId("Priority") || "priority",
            };

            const issueData: any = {
              fields: {
                project: { key: projectKey },
                summary: summary,
                issuetype: { id: matchingIssueType.id },
                description: description,
                labels: labels,
              },
            };

            // Add custom fields with robust approach
            if (technicalInformation) {
              // Try primary field ID
              if (fields[fieldIds.technicalInformation]) {
                issueData.fields[fieldIds.technicalInformation] =
                  technicalInformation;
              } else {
                // Try fallback field IDs
                const fallbackFields = [
                  "customfield_12850",
                  "customfield_17065",
                ];
                for (const field of fallbackFields) {
                  if (fields[field]) {
                    issueData.fields[field] = technicalInformation;
                    break;
                  }
                }

                // Last resort: search by field name
                if (!issueData.fields[fieldIds.technicalInformation]) {
                  const techField = Object.entries(fields).find(
                    ([_, value]: [string, any]) =>
                      value.name?.toLowerCase().includes("technical") ||
                      value.name?.toLowerCase().includes("documentation"),
                  );
                  if (techField) {
                    issueData.fields[techField[0]] = technicalInformation;
                  }
                }
              }
            }

            if (acceptanceCriteria) {
              // Try primary field ID
              if (fields[fieldIds.acceptanceCriteria]) {
                issueData.fields[fieldIds.acceptanceCriteria] =
                  acceptanceCriteria;
              } else {
                // Try fallback field IDs
                const fallbackFields = [
                  "customfield_10011",
                  "customfield_11001",
                ];
                for (const field of fallbackFields) {
                  if (fields[field]) {
                    issueData.fields[field] = acceptanceCriteria;
                    break;
                  }
                }

                // Last resort: search by field name
                if (!issueData.fields[fieldIds.acceptanceCriteria]) {
                  const acceptanceField = Object.entries(fields).find(
                    ([_, value]: [string, any]) =>
                      value.name?.toLowerCase().includes("acceptance") ||
                      value.name?.toLowerCase().includes("criteria"),
                  );
                  if (acceptanceField) {
                    issueData.fields[acceptanceField[0]] = acceptanceCriteria;
                  }
                }
              }
            }

            // Handle epic-specific fields with robust approach
            if (issueType.toLowerCase() === "epic") {
              // Try primary field ID
              if (fields[fieldIds.epicName]) {
                issueData.fields[fieldIds.epicName] = summary;
              } else {
                // Try to find by name
                const epicNameField = Object.entries(fields).find(
                  ([_, value]: [string, any]) =>
                    value.name === "Epic Name" ||
                    value.name?.toLowerCase().includes("epic name"),
                );
                if (epicNameField) {
                  issueData.fields[epicNameField[0]] = summary;
                } else {
                  // Try fallback field IDs
                  const fallbackFields = [
                    "customfield_10014",
                    "customfield_10006",
                  ];
                  for (const field of fallbackFields) {
                    if (fields[field]) {
                      issueData.fields[field] = summary;
                      break;
                    }
                  }
                }
              }
            } else if (epicLink) {
              // Try primary field ID
              if (fields[fieldIds.epicLink]) {
                issueData.fields[fieldIds.epicLink] = epicLink;
              } else {
                // Try to find by name
                const epicLinkField = Object.entries(fields).find(
                  ([_, value]: [string, any]) =>
                    value.name?.toLowerCase().includes("epic link"),
                );
                if (epicLinkField) {
                  issueData.fields[epicLinkField[0]] = epicLink;
                } else {
                  // Try fallback field IDs
                  const fallbackFields = [
                    "customfield_10004",
                    "customfield_10000",
                  ];
                  for (const field of fallbackFields) {
                    if (fields[field]) {
                      issueData.fields[field] = epicLink;
                      break;
                    }
                  }
                }
              }
            }

            // Handle parent for sub-tasks
            if (parentIssueKey) {
              issueData.fields.parent = { key: parentIssueKey };
            }

            // Check if this is a sub-task type but no parent is specified
            const isSubTaskType =
              matchingIssueType.name.toLowerCase().includes("sub-task") ||
              matchingIssueType.name.toLowerCase().includes("sous-tâche") ||
              matchingIssueType.name.toLowerCase().includes("subtask");

            if (isSubTaskType && !parentIssueKey) {
              throw new McpError(
                ErrorCode.InvalidParams,
                `Parent issue key is required for sub-task creation. Please provide 'parentIssueKey' parameter.`,
              );
            }

            // Add Story Points with robust approach
            if (storyPoints !== undefined) {
              // Try primary field ID
              if (fields[fieldIds.storyPoints]) {
                issueData.fields[fieldIds.storyPoints] = storyPoints;
              } else {
                // Try fallback field IDs
                const fallbackFields = [
                  "customfield_17372",
                  "customfield_10016",
                ];
                for (const field of fallbackFields) {
                  if (fields[field]) {
                    issueData.fields[field] = storyPoints;
                    break;
                  }
                }

                // Last resort: search by field name
                if (!issueData.fields[fieldIds.storyPoints]) {
                  const storyPointsField = Object.entries(fields).find(
                    ([_, value]: [string, any]) =>
                      value.name?.toLowerCase().includes("story point") ||
                      value.name?.toLowerCase().includes("storypoint") ||
                      value.name?.toLowerCase().includes("story points")
                  );
                  if (storyPointsField) {
                    issueData.fields[storyPointsField[0]] = storyPoints;
                  }
                }
              }
            }

            // Add Priority with robust approach
            if (priority) {
              // Try primary field ID
              if (fields[fieldIds.priority]) {
                issueData.fields[fieldIds.priority] = { name: priority };
              } else {
                // Try to find by name
                const priorityField = Object.entries(fields).find(
                  ([_, value]: [string, any]) =>
                    value.name?.toLowerCase() === "priority" ||
                    value.name?.toLowerCase().includes("priority")
                );
                if (priorityField) {
                  issueData.fields[priorityField[0]] = { name: priority };
                } else {
                  // Try standard field ID
                  if (fields["priority"]) {
                    issueData.fields["priority"] = { name: priority };
                  }
                }
              }
            }

            // Add Original Estimate and Remaining Estimate
            if (originalEstimate || remainingEstimate) {
              issueData.fields.timetracking = {};
              if (originalEstimate) {
                issueData.fields.timetracking.originalEstimate =
                  originalEstimate;
              }
              if (remainingEstimate) {
                issueData.fields.timetracking.remainingEstimate =
                  remainingEstimate;
              }
            }

            let createdIssue: {
              key: string;
              self: string;
              subTasks?: Array<{ key: string; self: string }>;
            };

            try {
              const response = await jiraApi.post(
                "/rest/api/2/issue",
                issueData,
              );
              createdIssue = {
                key: response.data.key,
                self: response.data.self,
              };
            } catch (error) {
              if (axios.isAxiosError(error) && error.response) {
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${JSON.stringify(error.response.data)}`,
                );
              }
              throw error;
            }

            // Create sub-tasks if provided
            if (Array.isArray(subTasks) && subTasks.length > 0) {
              const subTaskPromises = subTasks.map(
                async (subTask: {
                  summary: string;
                  description: string;
                  originalEstimate?: string;
                  remainingEstimate?: string;
                }) => {
                  // First, get project metadata to find available sub-task issue types
                  const subTaskMetadataResponse = await jiraApi.get(
                    `/rest/api/2/issue/createmeta?projectKeys=${projectKey}&expand=projects.issuetypes.fields`,
                  );
                  const subTaskProject =
                    subTaskMetadataResponse.data.projects[0];

                  if (!subTaskProject) {
                    throw new McpError(
                      ErrorCode.InvalidParams,
                      `Project ${projectKey} not found when creating sub-task`,
                    );
                  }

                  // Find a sub-task issue type
                  const subTaskIssueType = subTaskProject.issuetypes.find(
                    (type: any) =>
                      type.name.toLowerCase().includes("sub-task") ||
                      type.name.toLowerCase().includes("sous-tâche") ||
                      type.name.toLowerCase().includes("subtask"),
                  );

                  if (!subTaskIssueType) {
                    throw new McpError(
                      ErrorCode.InvalidParams,
                      `No sub-task issue type found in project ${projectKey}. Available types: ${subTaskProject.issuetypes.map((t: any) => t.name).join(", ")}`,
                    );
                  }

                  const subTaskData: any = {
                    fields: {
                      project: { key: projectKey },
                      parent: { key: createdIssue.key },
                      summary: subTask.summary,
                      description: subTask.description,
                      issuetype: { id: subTaskIssueType.id },
                    },
                  };
                  if (subTask.originalEstimate || subTask.remainingEstimate) {
                    subTaskData.fields.timetracking = {};
                    if (subTask.originalEstimate) {
                      subTaskData.fields.timetracking.originalEstimate =
                        subTask.originalEstimate;
                    }
                    if (subTask.remainingEstimate) {
                      subTaskData.fields.timetracking.remainingEstimate =
                        subTask.remainingEstimate;
                    }
                  }
                  const subTaskResponse = await jiraApi.post(
                    "/rest/api/2/issue",
                    subTaskData,
                  );
                  return {
                    key: subTaskResponse.data.key,
                    self: subTaskResponse.data.self,
                  };
                },
              );

              createdIssue.subTasks = await Promise.all(subTaskPromises);
            }

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(createdIssue, null, 2),
                },
              ],
            };
          }

          case "jira_update_issue": {
            const { issueKey, fields } = request.params.arguments as {
              issueKey: string;
              fields: {
                summary?: string;
                description?: string;
                technicalInformation?: string;
                acceptanceCriteria?: string;
                labels?: string[];
                status?: string;
                assignee?: string;
                storyPoints?: number;
                priority?: string;
                timetracking?: {
                  originalEstimate: string;
                  remainingEstimate: string;
                };
              };
            };

            try {
              // First, get issue metadata to understand available fields
              const metadataResponse = await jiraApi.get(
                `/rest/api/2/issue/${issueKey}/editmeta`,
              );
              const availableFields = metadataResponse.data.fields || {};

              // Handle status update separately using transitions
              if (fields.status) {
                // First, get available transitions
                const transitionsResponse = await jiraApi.get(
                  `/rest/api/2/issue/${issueKey}/transitions`,
                );
                const transitions = transitionsResponse.data.transitions;

                // Find the transition that matches our target status
                const targetTransition = transitions.find(
                  (t: any) =>
                    t.to.name.toLowerCase() === fields.status?.toLowerCase(),
                );

                if (!targetTransition) {
                  throw new McpError(
                    ErrorCode.InvalidParams,
                    `Cannot transition to status '${fields.status}'. Available transitions: ${transitions.map((t: any) => t.to.name).join(", ")}`,
                  );
                }

                // Execute the transition
                await jiraApi.post(
                  `/rest/api/2/issue/${issueKey}/transitions`,
                  {
                    transition: { id: targetTransition.id },
                  },
                );
              }

              // Define field IDs with multiple fallbacks
              const fieldIds = {
                summary: this.getFieldId("Summary") || "summary",
                description: this.getFieldId("Description") || "description",
                technicalInformation:
                  this.getFieldId("Technical documentation") ||
                  "customfield_17065" ||
                  "customfield_12850",
                acceptanceCriteria:
                  this.getFieldId("Acceptance Criteria") ||
                  "customfield_11001" ||
                  "customfield_10011",
                labels: this.getFieldId("Labels") || "labels",
                assignee: this.getFieldId("Assignee") || "assignee",
                timetracking:
                  this.getFieldId("Time Tracking") || "timetracking",
                storyPoints:
                  this.getFieldId("Story Points") || "customfield_17372" || "customfield_10016",
              };

              // Helper function to find the correct field ID
              const findFieldId = (baseFieldId: string, fieldName: string) => {
                // Try the primary field ID first
                if (availableFields[baseFieldId]) {
                  return baseFieldId;
                }

                // Search for field by name
                const matchingField = Object.entries(availableFields).find(
                  ([_, field]: [string, any]) =>
                    field.name?.toLowerCase() === fieldName.toLowerCase() ||
                    field.name?.toLowerCase().includes(fieldName.toLowerCase()),
                );

                if (matchingField) {
                  return matchingField[0];
                }

                // Try fallbacks for custom fields
                if (fieldName.toLowerCase().includes("technical")) {
                  const fallbacks = ["customfield_17065", "customfield_12850"];
                  for (const fallback of fallbacks) {
                    if (availableFields[fallback]) {
                      return fallback;
                    }
                  }
                } else if (fieldName.toLowerCase().includes("acceptance")) {
                  const fallbacks = ["customfield_11001", "customfield_10011"];
                  for (const fallback of fallbacks) {
                    if (availableFields[fallback]) {
                      return fallback;
                    }
                  }
                }

                // Return the original ID if no match found
                return baseFieldId;
              };

              const issueData: any = { fields: {} };

              // Apply field updates with robust field ID handling
              if (fields.summary) {
                const fieldId = findFieldId(fieldIds.summary, "Summary");
                issueData.fields[fieldId] = fields.summary;
              }

              if (fields.description) {
                const fieldId = findFieldId(
                  fieldIds.description,
                  "Description",
                );
                issueData.fields[fieldId] = fields.description;
              }

              if (fields.technicalInformation) {
                const fieldId = findFieldId(
                  fieldIds.technicalInformation,
                  "Technical documentation",
                );
                issueData.fields[fieldId] = fields.technicalInformation;
              }

              if (fields.acceptanceCriteria) {
                const fieldId = findFieldId(
                  fieldIds.acceptanceCriteria,
                  "Acceptance Criteria",
                );
                issueData.fields[fieldId] = fields.acceptanceCriteria;
              }

              if (fields.labels) {
                const fieldId = findFieldId(fieldIds.labels, "Labels");
                issueData.fields[fieldId] = fields.labels;
              }

              // Handle assignee with support for both Cloud and Datacenter
              if (fields.assignee) {
                const fieldId = findFieldId(fieldIds.assignee, "Assignee");

                // Check if it looks like an accountId (for Cloud) or username (for Datacenter)
                if (
                  fields.assignee.includes("@") ||
                  fields.assignee.length > 20
                ) {
                  // Likely an accountId format
                  issueData.fields[fieldId] = { accountId: fields.assignee };
                } else {
                  // Likely a username format
                  issueData.fields[fieldId] = { name: fields.assignee };
                }
              }

              // Handle time tracking
              if (fields.timetracking) {
                const fieldId = findFieldId(
                  fieldIds.timetracking,
                  "Time Tracking",
                );
                const { originalEstimate, remainingEstimate } =
                  fields.timetracking;

                issueData.fields[fieldId] = {
                  originalEstimate,
                  remainingEstimate,
                };
              }

              // Handle story points
              if (fields.storyPoints !== undefined) {
                const fieldId = findFieldId(fieldIds.storyPoints, "Story Points");
                issueData.fields[fieldId] = fields.storyPoints;
              }

              // Handle priority field
              if (fields.priority) {
                const fieldId = findFieldId("priority", "Priority");
                console.error(`[Debug] Using field ID ${fieldId} for priority update to ${fields.priority}`);
                
                // Verify if the priority is an object or a string
                if (typeof fields.priority === 'object') {
                  issueData.fields[fieldId] = fields.priority;
                } else {
                  // If it's a string, create an object with the name
                  issueData.fields[fieldId] = { name: fields.priority };
                }
              }

              // Only make the fields update API call if we have fields to update
              if (Object.keys(issueData.fields).length > 0) {
                await jiraApi.put(`/rest/api/2/issue/${issueKey}`, issueData);
              }

              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify(
                      {
                        message: "Issue updated successfully",
                        issueKey,
                        updates: {
                          status: fields.status,
                          fields: Object.keys(issueData.fields),
                        },
                      },
                      null,
                      2,
                    ),
                  },
                ],
              };
            } catch (error) {
              if (axios.isAxiosError(error) && error.response) {
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${JSON.stringify(error.response.data)}`,
                );
              }
              throw error;
            }
          }

          case "jira_get_project_issues": {
            const { projectKey, limit = 100 } = request.params.arguments as {
              projectKey: string;
              limit?: number;
            };
            const maxResults = Math.min(Math.max(1, limit), 100);

            // Get IDs for the fields we want to retrieve
            const fieldIds = {
              summary: this.getFieldId("Summary") || "summary",
              description: this.getFieldId("Description") || "description",
              status: this.getFieldId("Status") || "status",
              issuetype: this.getFieldId("Issue Type") || "issuetype",
              priority: this.getFieldId("Priority") || "priority",
              created: this.getFieldId("Created") || "created",
              updated: this.getFieldId("Updated") || "updated",
              assignee: this.getFieldId("Assignee") || "assignee",
              reporter: this.getFieldId("Reporter") || "reporter",
              labels: this.getFieldId("Labels") || "labels",
              technical:
                this.getFieldId("Technical documentation") ||
                "customfield_17065" ||
                "customfield_12850",
              acceptance:
                this.getFieldId("Acceptance Criteria") || "customfield_11001",
              timeTracking: this.getFieldId("Time Tracking") || "timetracking",
              resolutiondate:
                this.getFieldId("Resolution Date") || "resolutiondate",
              sprint:
                this.getFieldId("Sprint") ||
                "customfield_10003" ||
                "customfield_10000",
              epicLink: this.getFieldId("Epic Link") || "customfield_10004",
              storyPoints:
                this.getFieldId("Story Points") || "customfield_17372" || "customfield_10016",
              resolution: this.getFieldId("Resolution") || "resolution",
              components: this.getFieldId("Component/s") || "components",
              fixVersions: this.getFieldId("Fix Version/s") || "fixVersions",
            };

            const response = await jiraApi.get("/rest/api/2/search", {
              params: {
                jql: `project = ${projectKey} ORDER BY created DESC`,
                maxResults: maxResults,
                fields: Object.values(fieldIds)
                  .concat([
                    "status",
                    "description",
                    "assignee",
                    "reporter",
                    "priority",
                    "issuetype",
                    "resolution",
                    "labels",
                    "updated",
                    "customfield_10004",
                    "customfield_11001",
                  ])
                  .join(","),
                expand: "renderedFields",
              },
            });

            const issues = response.data.issues.map((issue: any) => ({
              key: issue.key,
              summary: issue.fields[fieldIds.summary],
              description:
                issue.renderedFields?.[fieldIds.description] ||
                issue.fields[fieldIds.description] ||
                issue.fields.description,
              status:
                issue.fields[fieldIds.status]?.name ||
                issue.fields.status?.name ||
                "Unknown",
              issueType:
                issue.fields[fieldIds.issuetype]?.name ||
                issue.fields.issuetype?.name,
              priority:
                issue.fields[fieldIds.priority]?.name ||
                issue.fields.priority?.name,
              created: issue.fields[fieldIds.created],
              updated:
                issue.fields.updated ||
                issue.fields[fieldIds.updated] ||
                issue.updated,
              assignee:
                issue.fields[fieldIds.assignee]?.displayName ||
                issue.fields.assignee?.displayName,
              reporter:
                issue.fields[fieldIds.reporter]?.displayName ||
                issue.fields.reporter?.displayName,
              labels: issue.fields[fieldIds.labels] || issue.fields.labels,
              technical:
                issue.renderedFields?.[fieldIds.technical] ||
                issue.fields[fieldIds.technical],
              acceptance:
                issue.fields[fieldIds.acceptance] ||
                issue.fields.customfield_11001 ||
                issue.renderedFields?.[fieldIds.acceptance],
              epicLink:
                issue.fields[fieldIds.epicLink] ||
                issue.fields.customfield_10004 ||
                issue.fields.epicLink,
              timeTracking: issue.fields[fieldIds.timeTracking],
              resolutiondate: issue.fields[fieldIds.resolutiondate],
              sprint: (() => {
                const sprintField = issue.fields[fieldIds.sprint];
                // If it's an array (Jira Datacenter format)
                if (Array.isArray(sprintField) && sprintField.length > 0) {
                  // Check if array contains objects with name property (Jira Cloud format)
                  if (
                    typeof sprintField[0] === "object" &&
                    sprintField[0] !== null &&
                    "name" in sprintField[0]
                  ) {
                    return sprintField[0].name;
                  }

                  // Otherwise try the string format with name pattern (Jira Datacenter format)
                  const sprintStr = String(sprintField[0]);
                  const match = sprintStr.match(/name=(.*?)(?:,|$)/);
                  if (match) {
                    return match[1];
                  }
                  // If no match found, return the raw string
                  return sprintStr;
                }
                // If it's a single object (another possible Jira Cloud format)
                else if (
                  sprintField &&
                  typeof sprintField === "object" &&
                  !Array.isArray(sprintField)
                ) {
                  if ("name" in sprintField) {
                    return sprintField.name;
                  }
                }
                return null;
              })(),
              storyPoints: issue.fields[fieldIds.storyPoints],
              resolution:
                issue.fields[fieldIds.resolution]?.name ||
                issue.fields.resolution?.name,
              components:
                issue.fields[fieldIds.components]?.map((c: any) => c.name) ||
                [],
              fixVersions:
                issue.fields[fieldIds.fixVersions]?.map((v: any) => v.name) ||
                [],
              // Include field IDs for reference
              _fieldIds: fieldIds,
            }));

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(issues, null, 2),
                },
              ],
            };
          }

          case "jira_get_issue": {
            const { issueKey, expand } = request.params.arguments as {
              issueKey: string;
              expand?: string;
            };

            // Base fields that are always included
            const baseFieldIds = {
              summary: this.getFieldId("Summary") || "summary",
              description:
                this.getFieldId("Description") || "customfield_16099", // Updated to use the correct custom field ID
              status: this.getFieldId("Status") || "status",
              issuetype: this.getFieldId("Issue Type") || "issuetype",
              priority: this.getFieldId("Priority") || "priority",
              created: this.getFieldId("Created") || "created",
              updated: this.getFieldId("Updated") || "updated",
              assignee: this.getFieldId("Assignee") || "assignee",
              reporter: this.getFieldId("Reporter") || "reporter",
              technical:
                this.getFieldId("Technical documentation") ||
                "customfield_12850",
              acceptance:
                this.getFieldId("Acceptance Criteria") || "customfield_10011",
              labels: this.getFieldId("Labels") || "labels",
              timeTracking: this.getFieldId("Time Tracking") || "timetracking",
              resolutiondate:
                this.getFieldId("Resolution Date") || "resolutiondate",
              storyPoints:
                this.getFieldId("Story Points") || "customfield_17372" || "customfield_10016",
            };

            // Additional fields that can be expanded
            const expandableFieldIds: { [key: string]: string } = {
              components: this.getFieldId("Component/s") || "components",
              fixVersions: this.getFieldId("Fix Version/s") || "fixVersions",
              dueDate: this.getFieldId("Due Date") || "duedate",
              environment: this.getFieldId("Environment") || "environment",
              attachments: this.getFieldId("Attachment") || "attachment",
              issueLinks: this.getFieldId("Linked Issues") || "issuelinks",
              subtasks: this.getFieldId("Sub-Tasks") || "subtasks",
              storyPoints: this.getFieldId("Story Points") || "customfield_17372" || "customfield_10016",
              // Add more expandable fields as needed
            };

            // Add requested expanded fields
            const requestedFields = expand
              ? expand.split(",").map((f) => f.trim().toLowerCase())
              : [];

            const allFieldIds = {
              ...baseFieldIds,
              ...Object.fromEntries(
                requestedFields
                  .filter((field) => expandableFieldIds[field])
                  .map((field) => [field, expandableFieldIds[field]]),
              ),
            };

            const response = await jiraApi.get(
              `/rest/api/2/issue/${issueKey}`,
              {
                params: {
                  fields: [
                    ...Object.values(allFieldIds),
                    "status",
                    "priority",
                    "assignee",
                    "reporter",
                    "description",
                    "issuetype",
                    "resolution",
                    "labels",
                    "updated",
                    "customfield_13504",
                  ].join(","),
                  expand: "renderedFields",
                },
              },
            );

            // Transform the response
            const issue = response.data;
            const result: any = {
              key: issue.key,
              summary: issue.fields[allFieldIds.summary],
              description:
                issue.renderedFields?.[allFieldIds.description] ||
                issue.fields[allFieldIds.description] ||
                issue.fields.description, // Also check standard description field
              status: issue.fields[allFieldIds.status]?.name,
              issueType: issue.fields[allFieldIds.issuetype]?.name,
              priority:
                issue.fields[allFieldIds.priority]?.name ||
                issue.fields.priority?.name,
              created: issue.fields[allFieldIds.created],
              updated:
                issue.fields[allFieldIds.updated] || issue.fields.updated,
              assignee:
                issue.fields[allFieldIds.assignee]?.displayName ||
                issue.fields.assignee?.displayName,
              reporter:
                issue.fields[allFieldIds.reporter]?.displayName ||
                issue.fields.reporter?.displayName,
              resolutiondate: issue.fields[allFieldIds.resolutiondate],
              labels: issue.fields[allFieldIds.labels] || issue.fields.labels,
              storyPoints: issue.fields[allFieldIds.storyPoints],
            };

            // Add expanded fields if requested
            requestedFields.forEach((field) => {
              if (expandableFieldIds[field]) {
                const fieldId = expandableFieldIds[field];
                switch (field) {
                  case "technical":
                    result.technicalInformation =
                      issue.renderedFields?.[fieldId] || issue.fields[fieldId];
                    break;
                  case "acceptance":
                    result.acceptanceCriteria =
                      issue.renderedFields?.[fieldId] || issue.fields[fieldId];
                    break;
                  case "labels":
                  case "components":
                    result[field] = issue.fields[fieldId] || [];
                    break;
                  case "fixVersions":
                    result.fixVersions = (issue.fields[fieldId] || []).map(
                      (v: any) => v.name,
                    );
                    break;
                  case "timeTracking":
                    result.timeTracking = issue.fields[fieldId];
                    break;
                  default:
                    result[field] = issue.fields[fieldId];
                }
              }
            });

            // Include field IDs and available expansions for reference
            result._fieldIds = allFieldIds;
            result._availableExpansions = Object.keys(expandableFieldIds);

            // Get status from either the custom field or standard status field
            const statusFromCustomField = issue.fields.customfield_13504?.value;
            const statusFromStandard = issue.fields.status?.name;

            // Set the status and status details
            result.status =
              statusFromCustomField || statusFromStandard || "Unknown";
            result.statusDetails = {
              name: statusFromCustomField || statusFromStandard,
              id: issue.fields.status?.id,
              description: issue.fields.status?.description,
              categoryName: issue.fields.status?.statusCategory?.name,
              categoryKey: issue.fields.status?.statusCategory?.key,
              customFieldValue: statusFromCustomField,
            };

            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(result, null, 2),
                },
              ],
            };
          }

          case "jira_add_comment": {
            if (
              !request.params.arguments ||
              typeof request.params.arguments.issueKey !== "string" ||
              typeof request.params.arguments.comment !== "string"
            ) {
              throw new McpError(
                ErrorCode.InvalidParams,
                "Invalid parameters. Required: issueKey (string), comment (string)",
              );
            }

            const { issueKey, comment } = request.params.arguments;

            // JIRA has a maximum comment length of 32767 characters
            if (comment.length > 32767) {
              throw new McpError(
                ErrorCode.InvalidParams,
                "Comment exceeds maximum length of 32767 characters",
              );
            }

            try {
              const response = await jiraApi.post(
                `/rest/api/2/issue/${issueKey}/comment`,
                { body: comment },
              );
              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify(
                      {
                        message: "Comment added successfully",
                        commentId: response.data.id,
                        issueKey: issueKey,
                        commentUrl: `${JIRA_BASE_URL}/browse/${issueKey}?focusedCommentId=${response.data.id}`,
                        created: response.data.created,
                        author: response.data.author.displayName,
                      },
                      null,
                      2,
                    ),
                  },
                ],
              };
            } catch (error) {
              if (axios.isAxiosError(error) && error.response) {
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${error.response.status} - ${error.response.statusText}. Details: ${JSON.stringify(error.response.data)}`,
                );
              }
              throw new McpError(
                ErrorCode.InternalError,
                `Unexpected error: ${error}`,
              );
            }
          }

          case "jira_get_comments": {
            if (
              !request.params.arguments ||
              typeof request.params.arguments.issueKey !== "string"
            ) {
              throw new McpError(
                ErrorCode.InvalidParams,
                "Invalid parameters. Required: issueKey (string)",
              );
            }

            const { issueKey } = request.params.arguments;
            const maxResults = request.params.arguments.maxResults || 100;
            const startAt = request.params.arguments.startAt || 0;

            try {
              const response = await jiraApi.get(
                `/rest/api/2/issue/${issueKey}/comment`,
                {
                  params: {
                    maxResults,
                    startAt,
                  },
                }
              );

              // Format comments with author name, body, created date, etc.
              const comments = response.data.comments.map((comment: any) => ({
                id: comment.id,
                author: comment.author.displayName,
                body: comment.body,
                created: comment.created,
                updated: comment.updated,
                url: `${JIRA_BASE_URL}/browse/${issueKey}?focusedCommentId=${comment.id}`,
              }));

              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify(
                      {
                        total: response.data.total,
                        comments,
                        issueKey,
                      },
                      null,
                      2
                    ),
                  },
                ],
              };
            } catch (error) {
              if (axios.isAxiosError(error) && error.response) {
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${error.response.status} - ${error.response.statusText}. Details: ${JSON.stringify(error.response.data)}`,
                );
              }
              throw new McpError(
                ErrorCode.InternalError,
                `Unexpected error: ${error}`,
              );
            }
          }

          case "jira_get_fields": {
            return {
              content: [
                {
                  type: "text",
                  text: JSON.stringify(
                    Array.from(this.fieldMapping.values()).sort((a, b) => {
                      if (a.custom && !b.custom) return 1;
                      if (!a.custom && b.custom) return -1;
                      return a.name.localeCompare(b.name);
                    }),
                    null,
                    2,
                  ),
                },
              ],
            };
          }

          case "jira_get_board_sprints": {
            let boardId = request.params.arguments?.boardId;

            if (!boardId) {
              if (SPRINT_BOARD_ID) {
                boardId = SPRINT_BOARD_ID;
              } else {
                throw new McpError(
                  ErrorCode.InvalidParams,
                  "Board ID not provided and SPRINT_BOARD_ID environment variable not set",
                );
              }
            }

            try {
              const response = await jiraApi.get(
                `/rest/agile/1.0/board/${boardId}/sprint`,
                {
                  params: { state: "active,future" },
                },
              );

              const sprints = response.data.values.map((sprint: any) => ({
                id: sprint.id,
                name: sprint.name,
                state: sprint.state,
                startDate: sprint.startDate,
                endDate: sprint.endDate,
                completeDate: sprint.completeDate,
                goal: sprint.goal,
              }));

              // Find the current (active) sprint
              const currentSprint = sprints.find(
                (sprint: { state: string }) => sprint.state === "active",
              );

              if (!currentSprint) {
                throw new McpError(
                  ErrorCode.InternalError,
                  "No active sprint found",
                );
              }

              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify(
                      { currentSprint, allSprints: sprints },
                      null,
                      2,
                    ),
                  },
                ],
              };
            } catch (error) {
              if (axios.isAxiosError(error) && error.response) {
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${error.response.status} - ${error.response.statusText}. Details: ${JSON.stringify(error.response.data)}`,
                );
              }
              throw new McpError(
                ErrorCode.InternalError,
                `Unexpected error: ${error}`,
              );
            }
          }

          case "jira_get_worklogs": {
            const { issueKey, startAt, maxResults, startedAfter } = request
              .params.arguments as {
              issueKey: string;
              startAt?: number;
              maxResults?: number;
              startedAfter?: string;
            };

            try {
              const response = await jiraApi.get(
                `/rest/api/2/issue/${issueKey}/worklog`,
                {
                  params: {
                    startAt,
                    maxResults,
                    startedAfter,
                  },
                },
              );

              const worklogs = await Promise.all(
                response.data.worklogs.map(async (worklog: any) => {
                  const authorDisplayName = await this.getUserDisplayName(
                    worklog.author.accountId,
                  );
                  return {
                    id: worklog.id,
                    timeSpent: worklog.timeSpent,
                    timeSpentSeconds: worklog.timeSpentSeconds,
                    author: authorDisplayName,
                    created: worklog.created,
                    updated: worklog.updated,
                    comment: worklog.comment,
                    started: worklog.started,
                  };
                }),
              );

              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify(
                      {
                        startAt: response.data.startAt,
                        maxResults: response.data.maxResults,
                        total: response.data.total,
                        worklogs: worklogs,
                      },
                      null,
                      2,
                    ),
                  },
                ],
              };
            } catch (error) {
              if (axios.isAxiosError(error) && error.response) {
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${error.response.status} - ${error.response.statusText}. Details: ${JSON.stringify(error.response.data)}`,
                );
              }
              throw new McpError(
                ErrorCode.InternalError,
                `Unexpected error: ${error}`,
              );
            }
          }

          case "jira_get_sprint_issues": {
            if (!SPRINT_BOARD_ID) {
              throw new McpError(
                ErrorCode.InternalError,
                "SPRINT_BOARD_ID environment variable not set",
              );
            }

            try {
              const sprintsResponse = await jiraApi.get(
                `/rest/agile/1.0/board/${SPRINT_BOARD_ID}/sprint`,
                {
                  params: { state: "active" },
                },
              );

              const currentSprint = sprintsResponse.data.values[0];
              if (!currentSprint) {
                throw new McpError(
                  ErrorCode.InternalError,
                  "No active sprint found",
                );
              }

              // Get IDs for the fields we want to retrieve
              const fieldIds = {
                summary: this.getFieldId("Summary") || "summary",
                status: this.getFieldId("Status") || "status",
                assignee: this.getFieldId("Assignee") || "assignee",
                priority: this.getFieldId("Priority") || "priority",
                issuetype: this.getFieldId("Issue Type") || "issuetype",
                storyPoints:
                  this.getFieldId("Story Points") || "customfield_17372" || "customfield_10014",
                created: this.getFieldId("Created") || "created",
                updated: this.getFieldId("Updated") || "updated",
                labels: this.getFieldId("Labels") || "labels",
                epicLink: this.getFieldId("Epic Link") || "customfield_10004",
              };

              const response = await jiraApi.get(
                `/rest/agile/1.0/board/${SPRINT_BOARD_ID}/sprint/${currentSprint.id}/issue`,
                {
                  params: {
                    fields: Object.values(fieldIds).join(","),
                  },
                },
              );

              const issues = response.data.issues.map((issue: any) => ({
                key: issue.key,
                summary: issue.fields[fieldIds.summary],
                status:
                  issue.fields[fieldIds.status]?.name ||
                  issue.fields.status?.name ||
                  "Unknown",
                assignee:
                  issue.fields[fieldIds.assignee]?.displayName ||
                  issue.fields.assignee?.displayName,
                priority:
                  issue.fields[fieldIds.priority]?.name ||
                  issue.fields.priority?.name,
                issueType:
                  issue.fields[fieldIds.issuetype]?.name ||
                  issue.fields.issuetype?.name,
                storyPoints: issue.fields[fieldIds.storyPoints],
                created: issue.fields[fieldIds.created],
                updated: issue.fields[fieldIds.updated] || issue.fields.updated,
                labels: issue.fields[fieldIds.labels] || issue.fields.labels,
                epicLink:
                  issue.fields[fieldIds.epicLink] ||
                  issue.fields.customfield_10004,
                // Include field IDs for reference
                _fieldIds: fieldIds,
              }));

              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify(
                      {
                        total: response.data.total,
                        issues: issues,
                      },
                      null,
                      2,
                    ),
                  },
                ],
              };
            } catch (error) {
              if (axios.isAxiosError(error) && error.response) {
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${error.response.status} - ${error.response.statusText}. Details: ${JSON.stringify(error.response.data)}`,
                );
              }
              throw new McpError(
                ErrorCode.InternalError,
                `Unexpected error: ${error}`,
              );
            }
          }

          case "jira_add_worklog": {
            const { issueKey, timeSpent, comment, started } = request.params
              .arguments as {
              issueKey: string;
              timeSpent: string;
              comment?: string;
              started?: string;
            };

            try {
              const worklogData: any = {
                timeSpent: timeSpent,
              };

              if (comment) {
                worklogData.comment = comment;
              }

              if (started) {
                worklogData.started = started;
              }

              const response = await jiraApi.post(
                `/rest/api/2/issue/${issueKey}/worklog`,
                worklogData,
              );

              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify(
                      {
                        message: "Worklog added successfully",
                        worklogId: response.data.id,
                        issueKey: issueKey,
                        timeSpent: response.data.timeSpent,
                        timeSpentSeconds: response.data.timeSpentSeconds,
                        author: response.data.author.displayName,
                        created: response.data.created,
                        started: response.data.started,
                      },
                      null,
                      2,
                    ),
                  },
                ],
              };
            } catch (error) {
              if (axios.isAxiosError(error) && error.response) {
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${error.response.status} - ${error.response.statusText}. Details: ${JSON.stringify(error.response.data)}`,
                );
              }
              throw new McpError(
                ErrorCode.InternalError,
                `Unexpected error: ${error}`,
              );
            }
          }

          case "jira_get_user_info": {
            const { username, accountId } = request.params.arguments as {
              username?: string;
              accountId?: string;
            };

            if (!username && !accountId) {
              throw new McpError(
                ErrorCode.InvalidParams,
                "Either username or accountId is required",
              );
            }

            try {
              // Try with accountId first if available (works with Jira Cloud)
              if (accountId) {
                try {
                  const response = await jiraApi.get("/rest/api/2/user", {
                    params: { accountId },
                  });

                  const userInfo = {
                    self: response.data.self,
                    key: response.data.key,
                    name: response.data.name,
                    accountId: response.data.accountId,
                    emailAddress: response.data.emailAddress,
                    displayName: response.data.displayName,
                    active: response.data.active,
                    timeZone: response.data.timeZone,
                    locale: response.data.locale,
                    avatarUrls: response.data.avatarUrls,
                    groups: response.data.groups,
                    applicationRoles: response.data.applicationRoles,
                  };

                  return {
                    content: [
                      {
                        type: "text",
                        text: JSON.stringify(userInfo, null, 2),
                      },
                    ],
                  };
                } catch (accountIdError) {
                  // If accountId fails and we have a username, try with username
                  if (username) {
                    console.error(
                      "[JIRA] AccountId lookup failed, trying with username",
                    );
                  } else {
                    // If we don't have a username to fall back to, rethrow the error
                    throw accountIdError;
                  }
                }
              }

              // Try with username (works with Jira Datacenter)
              if (username) {
                try {
                  const response = await jiraApi.get("/rest/api/2/user", {
                    params: { username },
                  });

                  const userInfo = {
                    self: response.data.self,
                    key: response.data.key,
                    name: response.data.name,
                    accountId: response.data.accountId,
                    emailAddress: response.data.emailAddress,
                    displayName: response.data.displayName,
                    active: response.data.active,
                    timeZone: response.data.timeZone,
                    locale: response.data.locale,
                    avatarUrls: response.data.avatarUrls,
                    groups: response.data.groups,
                    applicationRoles: response.data.applicationRoles,
                  };

                  return {
                    content: [
                      {
                        type: "text",
                        text: JSON.stringify(userInfo, null, 2),
                      },
                    ],
                  };
                } catch (usernameError) {
                  // If both accountId and username failed, throw the username error
                  throw usernameError;
                }
              }

              // This should never happen due to the initial check, but just in case
              throw new McpError(
                ErrorCode.InvalidParams,
                "Either username or accountId is required",
              );
            } catch (error) {
              if (axios.isAxiosError(error)) {
                if (error.response) {
                  throw new McpError(
                    ErrorCode.InternalError,
                    `JIRA API Error: ${error.message}. Status: ${error.response.status}. Details: ${JSON.stringify(error.response.data)}`,
                  );
                }
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${error.message}`,
                );
              }
              throw error;
            }
          }

          case "jira_update_worklog": {
            const { issueKey, worklogId, timeSpent, comment, started } = request
              .params.arguments as {
              issueKey: string;
              worklogId: string;
              timeSpent: string;
              comment?: string;
              started?: string;
            };

            try {
              const worklogData: any = {
                timeSpent: timeSpent,
              };

              if (comment !== undefined) {
                worklogData.comment = comment;
              }

              if (started !== undefined) {
                worklogData.started = started;
              }

              const response = await jiraApi.put(
                `/rest/api/2/issue/${issueKey}/worklog/${worklogId}`,
                worklogData,
              );

              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify(
                      {
                        message: "Worklog updated successfully",
                        worklogId: response.data.id,
                        issueKey: issueKey,
                        timeSpent: response.data.timeSpent,
                        timeSpentSeconds: response.data.timeSpentSeconds,
                        author: response.data.author.displayName,
                        updated: response.data.updated,
                        started: response.data.started,
                      },
                      null,
                      2,
                    ),
                  },
                ],
              };
            } catch (error) {
              if (axios.isAxiosError(error)) {
                if (error.response) {
                  throw new McpError(
                    ErrorCode.InternalError,
                    `JIRA API Error: ${error.message}. Status: ${error.response.status}. Details: ${JSON.stringify(error.response.data)}`,
                  );
                }
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${error.message}`,
                );
              }
              throw error;
            }
          }

          case "jira_delete_worklog": {
            const {
              issueKey,
              worklogId,
              adjustEstimate,
              newEstimate,
              reduceBy,
            } = request.params.arguments as {
              issueKey: string;
              worklogId: string;
              adjustEstimate?: "auto" | "leave" | "new" | "manual";
              newEstimate?: string;
              reduceBy?: string;
            };

            try {
              // Build query parameters based on adjustEstimate option
              const queryParams: Record<string, string> = {};
              if (adjustEstimate) {
                queryParams.adjustEstimate = adjustEstimate;
                if (adjustEstimate === "new" && newEstimate) {
                  queryParams.newEstimate = newEstimate;
                } else if (adjustEstimate === "manual" && reduceBy) {
                  queryParams.reduceBy = reduceBy;
                }
              }

              await jiraApi.delete(
                `/rest/api/2/issue/${issueKey}/worklog/${worklogId}`,
                { params: queryParams },
              );

              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify(
                      {
                        message: "Worklog deleted successfully",
                        worklogId,
                        issueKey,
                        adjustEstimate,
                        ...(newEstimate && { newEstimate }),
                        ...(reduceBy && { reduceBy }),
                      },
                      null,
                      2,
                    ),
                  },
                ],
              };
            } catch (error) {
              if (axios.isAxiosError(error)) {
                if (error.response) {
                  throw new McpError(
                    ErrorCode.InternalError,
                    `JIRA API Error: ${error.message}. Status: ${error.response.status}. Details: ${JSON.stringify(error.response.data)}`,
                  );
                }
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${error.message}`,
                );
              }
              throw error;
            }
          }

          case "jira_get_worklogs_bulk": {
            const { issueKeys, startedAfter } = request.params.arguments as {
              issueKeys: string[];
              startedAfter?: string;
            };

            if (!Array.isArray(issueKeys) || issueKeys.length === 0) {
              throw new McpError(
                ErrorCode.InvalidParams,
                "issueKeys must be a non-empty array of strings",
              );
            }

            if (issueKeys.length > 1000) {
              throw new McpError(
                ErrorCode.InvalidParams,
                "Maximum of 1000 issue keys allowed",
              );
            }

            try {
              // First, get all worklog IDs for the given issues
              const worklogIds: string[] = [];
              for (const issueKey of issueKeys) {
                const response = await jiraApi.get(
                  `/rest/api/2/issue/${issueKey}/worklog`,
                );

                const issueWorklogs = response.data.worklogs || [];
                for (const worklog of issueWorklogs) {
                  // Only include worklogs after the startedAfter date if specified
                  if (
                    !startedAfter ||
                    new Date(worklog.started) >= new Date(startedAfter)
                  ) {
                    worklogIds.push(worklog.id);
                  }
                }
              }

              if (worklogIds.length === 0) {
                return {
                  content: [
                    {
                      type: "text",
                      text: JSON.stringify(
                        {
                          message:
                            "No worklogs found for the specified criteria",
                          issueKeys,
                          startedAfter,
                        },
                        null,
                        2,
                      ),
                    },
                  ],
                };
              }

              // Then get the worklog details using the worklog IDs
              const response = await jiraApi.post("/rest/api/2/worklog/list", {
                ids: worklogIds,
              });

              const worklogs = await Promise.all(
                response.data.map(async (worklog: any) => {
                  const authorDisplayName = await this.getUserDisplayName(
                    worklog.author.accountId,
                  );
                  return {
                    issueKey: worklog.issueKey,
                    id: worklog.id,
                    timeSpent: worklog.timeSpent,
                    timeSpentSeconds: worklog.timeSpentSeconds,
                    author: authorDisplayName,
                    created: worklog.created,
                    updated: worklog.updated,
                    comment: worklog.comment,
                    started: worklog.started,
                  };
                }),
              );

              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify(
                      {
                        total: worklogs.length,
                        worklogs: worklogs,
                      },
                      null,
                      2,
                    ),
                  },
                ],
              };
            } catch (error) {
              if (axios.isAxiosError(error)) {
                if (error.response) {
                  throw new McpError(
                    ErrorCode.InternalError,
                    `JIRA API Error: ${error.message}. Status: ${error.response.status}. Details: ${JSON.stringify(error.response.data)}`,
                  );
                }
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${error.message}`,
                );
              }
              throw error;
            }
          }

          case "jira_search_users": {
            const {
              query,
              startAt = 0,
              maxResults = 100,
              includeInactive = false,
            } = request.params.arguments as {
              query: string;
              startAt?: number;
              maxResults?: number;
              includeInactive?: boolean;
            };

            if (!query) {
              throw new McpError(
                ErrorCode.InvalidParams,
                "Query parameter is required",
              );
            }

            try {
              // Essayer d'abord avec le paramètre 'query' (compatible Jira Cloud)
              const response = await jiraApi.get("/rest/api/2/user/search", {
                params: {
                  query: query,
                  startAt,
                  maxResults,
                  includeActive: true,
                  includeInactive,
                },
              });

              const users = response.data.map((user: any) => ({
                accountId: user.accountId,
                displayName: user.displayName,
                emailAddress: user.emailAddress,
                active: user.active,
                timeZone: user.timeZone,
                locale: user.locale,
                avatarUrls: user.avatarUrls,
              }));

              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify(
                      {
                        total: users.length,
                        startAt,
                        maxResults,
                        users,
                      },
                      null,
                      2,
                    ),
                  },
                ],
              };
            } catch (firstError) {
              // Si la première tentative échoue, essayer avec 'username' (compatible Jira Datacenter)
              try {
                const response = await jiraApi.get("/rest/api/2/user/search", {
                  params: {
                    username: query,
                    startAt,
                    maxResults,
                    includeActive: true,
                    includeInactive,
                  },
                });

                const users = response.data.map((user: any) => ({
                  accountId: user.accountId,
                  displayName: user.displayName,
                  emailAddress: user.emailAddress,
                  active: user.active,
                  timeZone: user.timeZone,
                  locale: user.locale,
                  avatarUrls: user.avatarUrls,
                }));

                return {
                  content: [
                    {
                      type: "text",
                      text: JSON.stringify(
                        {
                          total: users.length,
                          startAt,
                          maxResults,
                          users,
                        },
                        null,
                        2,
                      ),
                    },
                  ],
                };
              } catch (secondError) {
                // Si les deux méthodes échouent, renvoyer une erreur détaillée
                // Utiliser l'erreur la plus informative
                const error =
                  axios.isAxiosError(firstError) && firstError.response
                    ? firstError
                    : secondError;

                if (axios.isAxiosError(error) && error.response) {
                  throw new McpError(
                    ErrorCode.InternalError,
                    `JIRA API Error: ${error.message}. Status: ${error.response.status}. Details: ${JSON.stringify(error.response.data)}`,
                  );
                }
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${error instanceof Error ? error.message : "Unknown error"}`,
                );
              }
            }
          }

          case "jira_get_user_worklogs": {
            try {
              const { username, startDate, endDate } = request.params
                .arguments as {
                username: string;
                startDate: string;
                endDate: string;
              };

              // Search for issues with worklogs in the date range
              const jql = `worklogAuthor = "${username}" AND worklogDate >= "${startDate}" AND worklogDate <= "${endDate}"`;
              const issuesResponse = await jiraApi.get("/rest/api/2/search", {
                params: {
                  jql,
                  maxResults: 1000,
                  fields: ["summary"],
                },
              });

              const issues = issuesResponse.data.issues;

              const worklogsByDay: { [key: string]: number } = {};
              const worklogsByWeek: { [key: string]: number } = {};
              let totalSeconds = 0;
              const worklogDetails: any[] = [];

              for (const issue of issues) {
                // Get detailed worklogs for each issue
                const worklogsResponse = await jiraApi.get(
                  `/rest/api/2/issue/${issue.key}/worklog`,
                );

                const worklogs = worklogsResponse.data.worklogs;

                if (!worklogs) {
                  continue;
                }

                for (const worklog of worklogs) {
                  const startedDate = new Date(worklog.started);
                  const dateStr = startedDate.toISOString().split("T")[0];
                  const weekStr = `${dateStr.slice(0, 4)}-W${String(
                    this.getWeekNumber(startedDate),
                  ).padStart(2, "0")}`;

                  // Only count if within date range
                  if (
                    dateStr >= startDate &&
                    dateStr <= endDate &&
                    worklog.author.name === username
                  ) {
                    // Add to daily totals
                    worklogsByDay[dateStr] =
                      (worklogsByDay[dateStr] || 0) + worklog.timeSpentSeconds;

                    // Add to weekly totals
                    worklogsByWeek[weekStr] =
                      (worklogsByWeek[weekStr] || 0) + worklog.timeSpentSeconds;

                    totalSeconds += worklog.timeSpentSeconds;

                    // Add worklog details
                    worklogDetails.push({
                      issueKey: issue.key,
                      issueSummary: issue.fields.summary,
                      date: dateStr,
                      timeSpent: worklog.timeSpent,
                      timeSpentSeconds: worklog.timeSpentSeconds,
                      comment: worklog.comment,
                    });
                  }
                }
              }

              // Convert seconds to hours for all totals
              const formatHours = (seconds: number) =>
                Math.round(seconds / 36) / 100;

              const dailyTotals = Object.entries(worklogsByDay)
                .sort(([a], [b]) => a.localeCompare(b))
                .map(([date, seconds]) => ({
                  date,
                  hours: formatHours(seconds),
                }));

              const weeklyTotals = Object.entries(worklogsByWeek)
                .sort(([a], [b]) => a.localeCompare(b))
                .map(([week, seconds]) => ({
                  week,
                  hours: formatHours(seconds),
                }));

              return {
                content: [
                  {
                    type: "text",
                    text: JSON.stringify(
                      {
                        totalHours: formatHours(totalSeconds),
                        byWeek: weeklyTotals,
                        byDay: dailyTotals,
                        worklogDetails: worklogDetails,
                      },
                      null,
                      2,
                    ),
                  },
                ],
              };
            } catch (error) {
              if (axios.isAxiosError(error)) {
                if (error.response) {
                  throw new McpError(
                    ErrorCode.InternalError,
                    `JIRA API Error: ${error.message}. Status: ${error.response.status}. Details: ${JSON.stringify(error.response.data)}`,
                  );
                }
                throw new McpError(
                  ErrorCode.InternalError,
                  `JIRA API Error: ${error.message}`,
                );
              }
              throw new McpError(
                ErrorCode.InternalError,
                `Unexpected error: ${error}`,
              );
            }
          }

          case "jira_update_comment": {
            if (
              !request.params.arguments ||
              typeof request.params.arguments.issueKey !== "string" ||
              typeof request.params.arguments.commentId !== "string" ||
              typeof request.params.arguments.body !== "string"
            ) {
              throw new McpError(
                ErrorCode.InvalidParams,
                "Invalid parameters. Required: issueKey (string), commentId (string), body (string)",
              );
            }

            const { issueKey, commentId, body } = request.params.arguments;

            try {
              // Vérifier d'abord que le commentaire existe
              const getCommentResponse = await jiraApi.get(
                `/rest/api/2/issue/${issueKey}/comment/${commentId}`
              );
              
              // Si la réponse est 200, le commentaire existe
              if (getCommentResponse.status === 200) {
                // Mettre à jour le commentaire
                const updateResponse = await jiraApi.put(
                  `/rest/api/2/issue/${issueKey}/comment/${commentId}`,
                  { body: body }
                );
                
                return {
                  content: [
                    {
                      type: "text",
                      text: JSON.stringify(
                        {
                          message: "Comment updated successfully",
                          commentId: updateResponse.data.id,
                          issueKey: issueKey,
                          commentUrl: `${JIRA_BASE_URL}/browse/${issueKey}?focusedCommentId=${updateResponse.data.id}`,
                          updated: updateResponse.data.updated,
                          author: updateResponse.data.author.displayName,
                        },
                        null,
                        2
                      ),
                    },
                  ],
                };
              } else {
                throw new McpError(
                  ErrorCode.InvalidParams,
                  `Comment with ID ${commentId} not found in issue ${issueKey}`
                );
              }
            } catch (error) {
              if (axios.isAxiosError(error)) {
                if (error.response?.status === 404) {
                  throw new McpError(
                    ErrorCode.InvalidParams,
                    `Comment with ID ${commentId} not found in issue ${issueKey}`,
                  );
                } else if (error.response) {
                  throw new McpError(
                    ErrorCode.InternalError,
                    `JIRA API Error: ${error.response.status} - ${error.response.statusText}. Details: ${JSON.stringify(error.response.data)}`,
                  );
                }
              }
              throw new McpError(
                ErrorCode.InternalError,
                `Unexpected error: ${error}`,
              );
            }
          }

          case "jira_delete_comment": {
            if (
              !request.params.arguments ||
              typeof request.params.arguments.issueKey !== "string" ||
              typeof request.params.arguments.commentId !== "string"
            ) {
              throw new McpError(
                ErrorCode.InvalidParams,
                "Invalid parameters. Required: issueKey (string), commentId (string)",
              );
            }

            const { issueKey, commentId } = request.params.arguments;

            try {
              // Vérifier d'abord que le commentaire existe
              const getCommentResponse = await jiraApi.get(
                `/rest/api/2/issue/${issueKey}/comment/${commentId}`
              );
              
              // Si la réponse est 200, le commentaire existe
              if (getCommentResponse.status === 200) {
                // Supprimer le commentaire
                await jiraApi.delete(
                  `/rest/api/2/issue/${issueKey}/comment/${commentId}`
                );
                
                return {
                  content: [
                    {
                      type: "text",
                      text: JSON.stringify(
                        {
                          message: "Comment deleted successfully",
                          commentId: commentId,
                          issueKey: issueKey,
                        },
                        null,
                        2
                      ),
                    },
                  ],
                };
              } else {
                throw new McpError(
                  ErrorCode.InvalidParams,
                  `Comment with ID ${commentId} not found in issue ${issueKey}`
                );
              }
            } catch (error) {
              if (axios.isAxiosError(error)) {
                if (error.response?.status === 404) {
                  throw new McpError(
                    ErrorCode.InvalidParams,
                    `Comment with ID ${commentId} not found in issue ${issueKey}`,
                  );
                } else if (error.response) {
                  throw new McpError(
                    ErrorCode.InternalError,
                    `JIRA API Error: ${error.response.status} - ${error.response.statusText}. Details: ${JSON.stringify(error.response.data)}`,
                  );
                }
              }
              throw new McpError(
                ErrorCode.InternalError,
                `Unexpected error: ${error}`,
              );
            }
          }

          default:
            throw new McpError(
              ErrorCode.MethodNotFound,
              `Unknown tool: ${request.params.name}`,
            );
        }
      } catch (error) {
        if (axios.isAxiosError(error)) {
          let errorMessage = error.message;

          if (error.response?.data) {
            if (Array.isArray(error.response.data.errorMessages)) {
              errorMessage = error.response.data.errorMessages.join(", ");
            } else if (typeof error.response.data.errors === "object") {
              errorMessage = Object.entries(error.response.data.errors)
                .map(([key, value]) => `${key}: ${value}`)
                .join(", ");
            } else if (typeof error.response.data.message === "string") {
              errorMessage = error.response.data.message;
            } else if (typeof error.response.data === "string") {
              errorMessage = error.response.data;
            }
          }

          return {
            content: [
              {
                type: "text",
                text: `JIRA API Error: ${errorMessage}\nStatus: ${error.response?.status || "unknown"}\nURL: ${error.config?.url || "unknown"}\nRequest Data: ${JSON.stringify(error.config?.data)}`,
              },
            ],
            isError: true,
          };
        }
        throw error;
      }
    });
  }

  private getWeekNumber(date: Date): number {
    const d = new Date(
      Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()),
    );
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil(((d.getTime() - yearStart.getTime()) / 86400000 + 1) / 7);
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error("JIRA MCP server running on stdio");
  }
}

const server = new JiraServer();
server.run().catch(console.error);
