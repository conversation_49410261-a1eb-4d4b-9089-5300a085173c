#!/usr/bin/env node
import { Octokit } from "@octokit/rest";
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  ErrorCode,
  McpError,
} from "@modelcontextprotocol/sdk/types.js";
import AdmZ<PERSON> from "adm-zip";

const GITHUB_TOKEN = process.env.GITHUB_TOKEN;
if (!GITHUB_TOKEN || !GITHUB_TOKEN.startsWith("ghp_")) {
  throw new Error(
    "Invalid GITHUB_TOKEN. Please provide a valid personal access token starting with 'ghp_'",
  );
}

interface GitHubError extends Error {
  status?: number;
  response?: {
    data?: {
      message?: string;
    };
  };
}

class GitHubActionsServer {
  private async verifyTokenPermissions() {
    try {
      const { data } = await this.octokit.users.getAuthenticated();
      console.error("GitHub Actions MCP server authenticated as:", data.login);
    } catch (error) {
      const githubError = error as GitHubError;
      if (githubError.status === 401) {
        throw new Error(
          "Authentication failed. Please check your personal access token permissions.",
        );
      }
      throw error;
    }
  }

  private server: Server;
  private octokit: Octokit;

  constructor() {
    this.server = new Server(
      {
        name: "github-actions-server",
        version: "1.0.0",
      },
      {
        capabilities: {
          tools: {},
        },
      },
    );
    this.octokit = new Octokit({
      auth: GITHUB_TOKEN,
      baseUrl: "https://api.github.com",
    });
    this.setupToolHandlers();
    // Verify token permissions on startup
    this.verifyTokenPermissions().catch((error) => {
      console.error("Token verification failed:", error.message);
      process.exit(1);
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: "list_workflows",
          description: "List workflows for a repository",
          inputSchema: {
            type: "object",
            properties: {
              owner: { type: "string" },
              repo: { type: "string" },
            },
            required: ["owner", "repo"],
          },
        },
        {
          name: "get_workflow_runs",
          description: "Get workflow runs for a specific workflow",
          inputSchema: {
            type: "object",
            properties: {
              owner: { type: "string" },
              repo: { type: "string" },
              workflow_id: { type: "string" },
            },
            required: ["owner", "repo", "workflow_id"],
          },
        },
        {
          name: "trigger_workflow",
          description: "Trigger a workflow",
          inputSchema: {
            type: "object",
            properties: {
              owner: { type: "string" },
              repo: { type: "string" },
              workflow_id: { type: "string" },
              ref: { type: "string" },
            },
            required: ["owner", "repo", "workflow_id", "ref"],
          },
        },
        {
          name: "get_run_logs",
          description: "Get logs for a specific workflow run",
          inputSchema: {
            type: "object",
            properties: {
              owner: { type: "string" },
              repo: { type: "string" },
              run_id: { type: "number" },
            },
            required: ["owner", "repo", "run_id"],
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      switch (request.params.name) {
        case "list_workflows":
          return this.listWorkflows(
            request.params.arguments as { owner: string; repo: string },
          );
        case "get_workflow_runs":
          return this.getWorkflowRuns(
            request.params.arguments as {
              owner: string;
              repo: string;
              workflow_id: string;
            },
          );
        case "trigger_workflow":
          return this.triggerWorkflow(
            request.params.arguments as {
              owner: string;
              repo: string;
              workflow_id: string;
              ref: string;
            },
          );
        case "get_run_logs":
          return this.getRunLogs(
            request.params.arguments as {
              owner: string;
              repo: string;
              run_id: number;
            },
          );
        default:
          throw new McpError(
            ErrorCode.MethodNotFound,
            `Unknown tool: ${request.params.name}`,
          );
      }
    });
  }

  private async listWorkflows(params: { owner: string; repo: string }) {
    try {
      const { data } = await this.octokit.actions.listRepoWorkflows({
        ...params,
        headers: {
          accept: "application/vnd.github.v3+json",
          authorization: `token ${GITHUB_TOKEN}`,
        },
      });
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(data, null, 2),
          },
        ],
      };
    } catch (error: any) {
      return {
        content: [
          {
            type: "text",
            text: `Failed to list workflows: ${error.message}`,
          },
        ],
        isError: true,
      };
    }
  }

  private async getWorkflowRuns(params: {
    owner: string;
    repo: string;
    workflow_id: string;
  }) {
    try {
      const { data } = await this.octokit.actions.listWorkflowRuns({
        ...params,
        headers: {
          accept: "application/vnd.github.v3+json",
          authorization: `token ${GITHUB_TOKEN}`,
        },
      });
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(data, null, 2),
          },
        ],
      };
    } catch (error: any) {
      return {
        content: [
          {
            type: "text",
            text: `Failed to get workflow runs: ${error.message}`,
          },
        ],
        isError: true,
      };
    }
  }

  private async triggerWorkflow(params: {
    owner: string;
    repo: string;
    workflow_id: string;
    ref: string;
  }) {
    try {
      const { data } = await this.octokit.actions.createWorkflowDispatch({
        ...params,
        headers: {
          accept: "application/vnd.github.v3+json",
          authorization: `token ${GITHUB_TOKEN}`,
        },
      });
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(data, null, 2),
          },
        ],
      };
    } catch (error: any) {
      return {
        content: [
          {
            type: "text",
            text: `Failed to trigger workflow: ${error.message}`,
          },
        ],
        isError: true,
      };
    }
  }

  private async getRunLogs(params: {
    owner: string;
    repo: string;
    run_id: number;
  }) {
    try {
      // First get the download URL
      const response = await this.octokit.request(
        "GET /repos/{owner}/{repo}/actions/runs/{run_id}/logs",
        {
          owner: params.owner,
          repo: params.repo,
          run_id: params.run_id,
          headers: {
            accept: "application/vnd.github.v3.raw",
            authorization: `token ${GITHUB_TOKEN}`,
          },
        },
      );

      // Now download the logs with axios
      const axios = (await import("axios")).default;
      const { data } = await axios.get(response.url, {
        responseType: "arraybuffer",
        headers: {
          Authorization: `token ${GITHUB_TOKEN}`,
        },
      });

      // Use AdmZip to extract the contents
      const zip = new AdmZip(data);
      const zipEntries = zip.getEntries();

      // Combine all log entries
      let logText = "";
      for (const entry of zipEntries) {
        if (!entry.isDirectory) {
          logText += entry.getData().toString("utf8") + "\n";
        }
      }

      return {
        content: [
          {
            type: "resource",
            resource: {
              uri: `github-actions://logs/${params.owner}/${params.repo}/${params.run_id}`,
              name: "Workflow Run Logs",
              mimeType: "text/plain",
              text: logText,
            },
          },
        ],
      };
    } catch (error: any) {
      return {
        content: [
          {
            type: "resource",
            resource: {
              uri: `github-actions://error`,
              name: "Error",
              mimeType: "text/plain",
              text: `Failed to get run logs: ${error.message}`,
            },
          },
        ],
        isError: true,
      };
    }
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error("GitHub Actions MCP server running on stdio");
  }
}

const server = new GitHubActionsServer();
server.run().catch(console.error);
