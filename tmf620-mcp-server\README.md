# TMF620 MCP Server

This is a Model Context Protocol (MCP) server for interacting with the TMF620 Product Catalog Management API. It provides tools for retrieving product offerings, catalogs, and related information from the TELUS B2B Product Catalog.

## Features

- OAuth 2.0 authentication with client credentials flow
- Rate limiting to prevent API throttling
- Automatic token refresh
- Support for multiple environments (dev, test, prod)
- Comprehensive error handling and logging
- MCP integration for use with Claude and other MCP-compatible assistants

## Available Tools

The server provides the following MCP tools:

- `get_product_offering`: Fetch a specific product offering by ID
- `get_product_offering_by_external_id`: Fetch a specific product offering by external ID
- `list_product_offerings`: List or search for product offerings with filtering
- `bulk_retrieve_product_offerings`: Retrieve multiple product offerings by their IDs
- `get_catalog`: Fetch a specific catalog by ID
- `list_catalogs`: List or search for catalogs with filtering

## Setup

### Prerequisites

- Node.js v16 or higher
- OAuth client credentials for the TMF620 API

### Installation

1. Clone this repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file with the following variables:
   ```
   DEV_API_URL=https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2/productOffering
   TEST_API_URL=https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2/productOffering
   TEST_QUOTE_ID=a2db6e1a-27ea-419e-a7a5-10575717f8b7
   PROD_API_URL=https://prod-api-url.example.com
   
   # Proxy Configuration
   # Disable proxy to allow direct connections to the API
   HTTP_PROXY=
   HTTPS_PROXY=
   NO_PROXY=*
   
   OAUTH_CLIENT_ID=your-client-id
   OAUTH_CLIENT_SECRET=your-client-secret
   OAUTH_TOKEN_URL=https://apigw-st.telus.com/st/token
   OAUTH_SCOPE=4823
   ```

   **Note**: The TMF620 API requires the 'env' header with value 'itn05' in all requests. This is automatically added by the server code in the `makeRequest` method of the `TMF620Api` class.
   ```
4. Build the TypeScript code:
   ```
   npm run build
   ```
5. Register the server with MCP:
   ```
   node register-server.js
   ```

### Running the Server

The server can be started manually:

```
node start-server.js
```

Or it will be started automatically by the MCP framework when needed.

## Troubleshooting

### API Connection Issues

If you encounter API connection issues, check the following:

1. Verify that your OAuth credentials are correct in the `.env` file
2. Ensure the API URLs are correct and accessible from your network
3. Check if you need to use a proxy to access the API
4. Verify that the API is available and not experiencing downtime

### Common Error Messages

- `getaddrinfo ENOTFOUND`: The API URL is incorrect or not accessible
- `401 Unauthorized`: The OAuth credentials are invalid
- `403 Forbidden`: The OAuth token doesn't have sufficient permissions
- `429 Too Many Requests`: The API rate limit has been exceeded
- `400 Environment not supported`: The 'env' header is missing or has an incorrect value (should be 'itn05')

## Recent Changes

### Proxy Configuration Update

The server was updated to explicitly disable proxy settings to resolve connection issues with the TMF620 API. This change was made to ensure the server can connect directly to the API without going through a proxy server.

The key changes were:

1. Added proxy configuration settings to the `.env` file:
   ```
   HTTP_PROXY=
   HTTPS_PROXY=
   NO_PROXY=*
   ```
2. Created a `fix-proxy.js` script that can be used to update the `.env` file with the proxy settings
3. Created a `test-api-connection.js` script that explicitly disables the proxy for API requests

### API URL Update

The server was updated to use the public API URL (`apigw-st.telus.com`) instead of the private API URL (`apigw-private-nane-np-001.tsl.telus.com`). This change was made to ensure the server can connect to the API from outside the TELUS network.

The key changes were:

1. Updated the `.env` file with the public API URLs
2. Modified the `getApiUrl` method in `tmf620Api.ts` to read the API URLs directly from environment variables each time it's called
3. Added additional logging to show which API URL is being used

### Environment Header Update

The server was updated to include the required 'env' header with value 'itn05' in all API requests. This header is required by the TMF620 API and without it, the API returns a 400 error with the message "Environment not supported".

The key changes were:

1. Added the 'env': 'itn05' header to the makeRequest method in tmf620Api.ts
2. Updated the test-api-connection.js script to include this header
3. Added documentation about this requirement in the README.md and analysis documents

### Cline MCP Integration

The server has been integrated with Cline MCP to provide a seamless experience for users of the Cline assistant. The following scripts have been created to facilitate this integration:

1. `update-cline-mcp-settings.js`: Updates the Cline MCP settings file to include the TMF620 server configuration, copying OAuth credentials from the AMS server configuration.
   ```
   node update-cline-mcp-settings.js
   ```

2. `test-api-connection.js`: Tests the API connection using the updated settings, verifying that OAuth authentication is working and that the API is accessible.
   ```
   node test-api-connection.js
   ```

### Analysis Documents

Two analysis documents have been created to document the API connection issues and their solutions:

1. `api-connection-analysis.md`: Provides a detailed analysis of the API connection issues in the TMF620 MCP server and the solutions implemented to resolve them.

2. `mcp-server-comparison-analysis.md`: Provides a comprehensive analysis of the differences between the MCP-AMS server and the TMF620 MCP server, with a focus on identifying the root causes of API connection issues in the TMF620 server.

### Troubleshooting Proxy Issues

If you encounter connection issues with the API, you may need to check your proxy settings:

1. Run the `test-api-connection.js` script to verify the API connection:
   ```
   node test-api-connection.js
   ```
2. If the test fails with a proxy-related error, run the `fix-proxy.js` script to update your `.env` file:
   ```
   node fix-proxy.js
   ```
3. Restart the server after updating the proxy settings:
   ```
   node start-server.js
   ```

For more details, see the `api-connection-analysis.md` file.

### Cline MCP Setup

To use the TMF620 server with Cline MCP:

1. Run the `update-cline-mcp-settings.js` script to register the server in the Cline MCP settings:
   ```
   node update-cline-mcp-settings.js
   ```
2. Restart Cline to apply the changes
3. Use the following tools in your Cline session:
   - `use_mcp_tool` with `server_name: tmf620`
   - Available tools: `get_product_offering`, `get_product_offering_by_external_id`, `list_product_offerings`, `bulk_retrieve_product_offerings`, `get_catalog`, `list_catalogs`

## License

This project is licensed under the MIT License - see the LICENSE file for details.
