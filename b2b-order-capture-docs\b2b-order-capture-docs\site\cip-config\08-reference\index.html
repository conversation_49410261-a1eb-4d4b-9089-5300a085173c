
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/cip-config/08-reference/">
      
      
        <link rel="prev" href="../07-troubleshooting/">
      
      
        <link rel="next" href="../09-chain-catalog/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Reference - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#reference-documentation" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Reference
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" checked>
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-schema-reference" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Schema Reference
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Schema Reference">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#main-configuration-schema" class="md-nav__link">
    <span class="md-ellipsis">
      Main Configuration Schema
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chain-configuration-schema" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Configuration Schema
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-configuration-schema" class="md-nav__link">
    <span class="md-ellipsis">
      Service Configuration Schema
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#api-reference" class="md-nav__link">
    <span class="md-ellipsis">
      API Reference
    </span>
  </a>
  
    <nav class="md-nav" aria-label="API Reference">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#cip-platform-apis" class="md-nav__link">
    <span class="md-ellipsis">
      CIP Platform APIs
    </span>
  </a>
  
    <nav class="md-nav" aria-label="CIP Platform APIs">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#configuration-management-api" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Management API
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chain-management-api" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Management API
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-management-api" class="md-nav__link">
    <span class="md-ellipsis">
      Service Management API
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#tmf-api-specifications" class="md-nav__link">
    <span class="md-ellipsis">
      TMF API Specifications
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TMF API Specifications">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#tmf-620-product-catalog-management" class="md-nav__link">
    <span class="md-ellipsis">
      TMF 620 - Product Catalog Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#tmf-648-quote-management" class="md-nav__link">
    <span class="md-ellipsis">
      TMF 648 - Quote Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#environment-configuration-reference" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration Reference
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Environment Configuration Reference">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#environment-variables" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Variables
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#resource-limits" class="md-nav__link">
    <span class="md-ellipsis">
      Resource Limits
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-codes-reference" class="md-nav__link">
    <span class="md-ellipsis">
      Error Codes Reference
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Codes Reference">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#configuration-errors" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Errors
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#runtime-errors" class="md-nav__link">
    <span class="md-ellipsis">
      Runtime Errors
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-errors" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Errors
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-benchmarks" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Benchmarks
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Benchmarks">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chain-execution-performance" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Execution Performance
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-call-performance" class="md-nav__link">
    <span class="md-ellipsis">
      Service Call Performance
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Security Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#oauth2-scopes" class="md-nav__link">
    <span class="md-ellipsis">
      OAuth2 Scopes
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#tls-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      TLS Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-schema-reference" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Schema Reference
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Schema Reference">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#main-configuration-schema" class="md-nav__link">
    <span class="md-ellipsis">
      Main Configuration Schema
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chain-configuration-schema" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Configuration Schema
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-configuration-schema" class="md-nav__link">
    <span class="md-ellipsis">
      Service Configuration Schema
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#api-reference" class="md-nav__link">
    <span class="md-ellipsis">
      API Reference
    </span>
  </a>
  
    <nav class="md-nav" aria-label="API Reference">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#cip-platform-apis" class="md-nav__link">
    <span class="md-ellipsis">
      CIP Platform APIs
    </span>
  </a>
  
    <nav class="md-nav" aria-label="CIP Platform APIs">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#configuration-management-api" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Management API
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chain-management-api" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Management API
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-management-api" class="md-nav__link">
    <span class="md-ellipsis">
      Service Management API
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#tmf-api-specifications" class="md-nav__link">
    <span class="md-ellipsis">
      TMF API Specifications
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TMF API Specifications">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#tmf-620-product-catalog-management" class="md-nav__link">
    <span class="md-ellipsis">
      TMF 620 - Product Catalog Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#tmf-648-quote-management" class="md-nav__link">
    <span class="md-ellipsis">
      TMF 648 - Quote Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#environment-configuration-reference" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration Reference
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Environment Configuration Reference">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#environment-variables" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Variables
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#resource-limits" class="md-nav__link">
    <span class="md-ellipsis">
      Resource Limits
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-codes-reference" class="md-nav__link">
    <span class="md-ellipsis">
      Error Codes Reference
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Codes Reference">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#configuration-errors" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Errors
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#runtime-errors" class="md-nav__link">
    <span class="md-ellipsis">
      Runtime Errors
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-errors" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Errors
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-benchmarks" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Benchmarks
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Benchmarks">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chain-execution-performance" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Execution Performance
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-call-performance" class="md-nav__link">
    <span class="md-ellipsis">
      Service Call Performance
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Security Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#oauth2-scopes" class="md-nav__link">
    <span class="md-ellipsis">
      OAuth2 Scopes
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#tls-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      TLS Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="reference-documentation">Reference Documentation<a class="headerlink" href="#reference-documentation" title="Permanent link">&para;</a></h1>
<h2 id="overview">Overview<a class="headerlink" href="#overview" title="Permanent link">&para;</a></h2>
<p>This reference documentation provides comprehensive technical specifications, API contracts, configuration schemas, and integration details for the CIP Configuration Package.</p>
<h2 id="configuration-schema-reference">Configuration Schema Reference<a class="headerlink" href="#configuration-schema-reference" title="Permanent link">&para;</a></h2>
<h3 id="main-configuration-schema">Main Configuration Schema<a class="headerlink" href="#main-configuration-schema" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># config.json schema</span>
<span class="p p-Indicator">{</span>
<span class="w">  </span><span class="s">&quot;$schema&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;http://json-schema.org/draft-07/schema#&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;required&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;configMapVariables&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;serviceImports&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;chainImports&quot;</span><span class="p p-Indicator">],</span>
<span class="w">  </span><span class="s">&quot;properties&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">    </span><span class="s">&quot;configMapVariables&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">      </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;array&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;items&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">        </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span><span class="p p-Indicator">,</span>
<span class="w">        </span><span class="s">&quot;required&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;path&quot;</span><span class="p p-Indicator">],</span>
<span class="w">        </span><span class="s">&quot;properties&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">          </span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">            </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span><span class="p p-Indicator">,</span>
<span class="w">            </span><span class="s">&quot;description&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;ConfigMap</span><span class="nv"> </span><span class="s">name&quot;</span>
<span class="w">          </span><span class="p p-Indicator">},</span>
<span class="w">          </span><span class="s">&quot;path&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">            </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span><span class="p p-Indicator">,</span>
<span class="w">            </span><span class="s">&quot;description&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;Path</span><span class="nv"> </span><span class="s">to</span><span class="nv"> </span><span class="s">variable</span><span class="nv"> </span><span class="s">file&quot;</span>
<span class="w">          </span><span class="p p-Indicator">}</span>
<span class="w">        </span><span class="p p-Indicator">}</span>
<span class="w">      </span><span class="p p-Indicator">}</span>
<span class="w">    </span><span class="p p-Indicator">},</span>
<span class="w">    </span><span class="s">&quot;secretVariables&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">      </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;array&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;items&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">        </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span><span class="p p-Indicator">,</span>
<span class="w">        </span><span class="s">&quot;required&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;path&quot;</span><span class="p p-Indicator">],</span>
<span class="w">        </span><span class="s">&quot;properties&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">          </span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">            </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span><span class="p p-Indicator">,</span>
<span class="w">            </span><span class="s">&quot;description&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;Secret</span><span class="nv"> </span><span class="s">name&quot;</span>
<span class="w">          </span><span class="p p-Indicator">},</span>
<span class="w">          </span><span class="s">&quot;path&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">            </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span><span class="p p-Indicator">,</span>
<span class="w">            </span><span class="s">&quot;description&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;Path</span><span class="nv"> </span><span class="s">to</span><span class="nv"> </span><span class="s">secret</span><span class="nv"> </span><span class="s">file&quot;</span>
<span class="w">          </span><span class="p p-Indicator">}</span>
<span class="w">        </span><span class="p p-Indicator">}</span>
<span class="w">      </span><span class="p p-Indicator">}</span>
<span class="w">    </span><span class="p p-Indicator">},</span>
<span class="w">    </span><span class="s">&quot;tlsConfig&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">      </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;properties&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">        </span><span class="s">&quot;enabled&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">          </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;boolean&quot;</span><span class="p p-Indicator">,</span>
<span class="w">          </span><span class="s">&quot;description&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;Enable</span><span class="nv"> </span><span class="s">TLS</span><span class="nv"> </span><span class="s">configuration&quot;</span>
<span class="w">        </span><span class="p p-Indicator">},</span>
<span class="w">        </span><span class="s">&quot;environments&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">          </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;array&quot;</span><span class="p p-Indicator">,</span>
<span class="w">          </span><span class="s">&quot;items&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">            </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span><span class="p p-Indicator">,</span>
<span class="w">            </span><span class="s">&quot;enum&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;qa&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;staging&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;production&quot;</span><span class="p p-Indicator">]</span>
<span class="w">          </span><span class="p p-Indicator">}</span>
<span class="w">        </span><span class="p p-Indicator">}</span>
<span class="w">      </span><span class="p p-Indicator">}</span>
<span class="w">    </span><span class="p p-Indicator">}</span>
<span class="w">  </span><span class="p p-Indicator">}</span>
<span class="p p-Indicator">}</span>
</code></pre></div>
<h3 id="chain-configuration-schema">Chain Configuration Schema<a class="headerlink" href="#chain-configuration-schema" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Chain schema definition</span>
<span class="nt">chain</span><span class="p">:</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">  </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;id&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;version&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;steps&quot;</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">id</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">      </span><span class="nt">pattern</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$&quot;</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Unique</span><span class="nv"> </span><span class="s">chain</span><span class="nv"> </span><span class="s">identifier</span><span class="nv"> </span><span class="s">(UUID)&quot;</span>

<span class="w">    </span><span class="nt">name</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">      </span><span class="nt">minLength</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">      </span><span class="nt">maxLength</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Human-readable</span><span class="nv"> </span><span class="s">chain</span><span class="nv"> </span><span class="s">name&quot;</span>

<span class="w">    </span><span class="nt">version</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">      </span><span class="nt">pattern</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;^\\d+\\.\\d+\\.\\d+$&quot;</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Semantic</span><span class="nv"> </span><span class="s">version</span><span class="nv"> </span><span class="s">(e.g.,</span><span class="nv"> </span><span class="s">1.0.0)&quot;</span>

<span class="w">    </span><span class="nt">description</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">      </span><span class="nt">maxLength</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">500</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Chain</span><span class="nv"> </span><span class="s">description&quot;</span>

<span class="w">    </span><span class="nt">metadata</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">      </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">        </span><span class="nt">category</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">          </span><span class="nt">enum</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;credit-assessment&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;quote-management&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;provisioning&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;billing&quot;</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">priority</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">          </span><span class="nt">enum</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;low&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;medium&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;high&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;critical&quot;</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">timeout</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;integer&quot;</span>
<span class="w">          </span><span class="nt">minimum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
<span class="w">          </span><span class="nt">maximum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">600000</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Chain</span><span class="nv"> </span><span class="s">timeout</span><span class="nv"> </span><span class="s">in</span><span class="nv"> </span><span class="s">milliseconds&quot;</span>
<span class="w">        </span><span class="nt">retryPolicy</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">          </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">            </span><span class="nt">maxAttempts</span><span class="p">:</span>
<span class="w">              </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;integer&quot;</span>
<span class="w">              </span><span class="nt">minimum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">              </span><span class="nt">maximum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">            </span><span class="nt">backoffMultiplier</span><span class="p">:</span>
<span class="w">              </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;number&quot;</span>
<span class="w">              </span><span class="nt">minimum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1.0</span>
<span class="w">              </span><span class="nt">maximum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5.0</span>

<span class="w">    </span><span class="nt">triggers</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;array&quot;</span>
<span class="w">      </span><span class="nt">items</span><span class="p">:</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">        </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;event&quot;</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">          </span><span class="nt">event</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">            </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Event</span><span class="nv"> </span><span class="s">type</span><span class="nv"> </span><span class="s">that</span><span class="nv"> </span><span class="s">triggers</span><span class="nv"> </span><span class="s">the</span><span class="nv"> </span><span class="s">chain&quot;</span>
<span class="w">          </span><span class="nt">condition</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">            </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Groovy</span><span class="nv"> </span><span class="s">expression</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">trigger</span><span class="nv"> </span><span class="s">condition&quot;</span>
<span class="w">          </span><span class="nt">filter</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">            </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Additional</span><span class="nv"> </span><span class="s">filtering</span><span class="nv"> </span><span class="s">logic&quot;</span>

<span class="w">    </span><span class="nt">variables</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;array&quot;</span>
<span class="w">      </span><span class="nt">items</span><span class="p">:</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">        </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">          </span><span class="nt">name</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">            </span><span class="nt">pattern</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;^[a-zA-Z][a-zA-Z0-9_]*$&quot;</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">            </span><span class="nt">enum</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;string&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;number&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;boolean&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;object&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;array&quot;</span><span class="p p-Indicator">]</span>
<span class="w">          </span><span class="nt">required</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;boolean&quot;</span>
<span class="w">            </span><span class="nt">default</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>
<span class="w">          </span><span class="nt">default</span><span class="p">:</span>
<span class="w">            </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Default</span><span class="nv"> </span><span class="s">value</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">the</span><span class="nv"> </span><span class="s">variable&quot;</span>

<span class="w">    </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;array&quot;</span>
<span class="w">      </span><span class="nt">minItems</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">      </span><span class="nt">items</span><span class="p">:</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">        </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">          </span><span class="nt">name</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">            </span><span class="nt">pattern</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;^[a-zA-Z][a-zA-Z0-9_]*$&quot;</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">            </span><span class="nt">enum</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;groovy&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;service&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;condition&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;loop&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;parallel&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;reuse&quot;</span><span class="p p-Indicator">]</span>
</code></pre></div>
<h3 id="service-configuration-schema">Service Configuration Schema<a class="headerlink" href="#service-configuration-schema" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Service schema definition</span>
<span class="nt">service</span><span class="p">:</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">  </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;version&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;endpoints&quot;</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">name</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">      </span><span class="nt">pattern</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;^[a-z][a-z0-9-]*[a-z0-9]$&quot;</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Service</span><span class="nv"> </span><span class="s">name</span><span class="nv"> </span><span class="s">(kebab-case)&quot;</span>

<span class="w">    </span><span class="nt">version</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">      </span><span class="nt">pattern</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;^\\d+\\.\\d+\\.\\d+$&quot;</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Service</span><span class="nv"> </span><span class="s">version&quot;</span>

<span class="w">    </span><span class="nt">type</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">      </span><span class="nt">enum</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;TMF&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;REST&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;SOAP&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;GraphQL&quot;</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Service</span><span class="nv"> </span><span class="s">type&quot;</span>

<span class="w">    </span><span class="nt">specification</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">      </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">        </span><span class="nt">group</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">          </span><span class="nt">pattern</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;^tmf-\\d{3}$&quot;</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TMF</span><span class="nv"> </span><span class="s">specification</span><span class="nv"> </span><span class="s">group&quot;</span>
<span class="w">        </span><span class="nt">version</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TMF</span><span class="nv"> </span><span class="s">specification</span><span class="nv"> </span><span class="s">version&quot;</span>
<span class="w">        </span><span class="nt">title</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Specification</span><span class="nv"> </span><span class="s">title&quot;</span>

<span class="w">    </span><span class="nt">endpoints</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;array&quot;</span>
<span class="w">      </span><span class="nt">minItems</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">      </span><span class="nt">items</span><span class="p">:</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">        </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;path&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;methods&quot;</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">          </span><span class="nt">name</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">            </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Endpoint</span><span class="nv"> </span><span class="s">name&quot;</span>
<span class="w">          </span><span class="nt">path</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">            </span><span class="nt">pattern</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;^/.*&quot;</span>
<span class="w">            </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;API</span><span class="nv"> </span><span class="s">path&quot;</span>
<span class="w">          </span><span class="nt">methods</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;array&quot;</span>
<span class="w">            </span><span class="nt">items</span><span class="p">:</span>
<span class="w">              </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">              </span><span class="nt">enum</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;GET&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;POST&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;PUT&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;PATCH&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;DELETE&quot;</span><span class="p p-Indicator">]</span>
<span class="w">          </span><span class="nt">parameters</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;array&quot;</span>
<span class="w">            </span><span class="nt">items</span><span class="p">:</span>
<span class="w">              </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">              </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">]</span>
<span class="w">              </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">                </span><span class="nt">name</span><span class="p">:</span>
<span class="w">                  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">                </span><span class="nt">type</span><span class="p">:</span>
<span class="w">                  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">                  </span><span class="nt">enum</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;string&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;number&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;boolean&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;array&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;object&quot;</span><span class="p p-Indicator">]</span>
<span class="w">                </span><span class="nt">required</span><span class="p">:</span>
<span class="w">                  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;boolean&quot;</span>
<span class="w">                  </span><span class="nt">default</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>
<span class="w">          </span><span class="nt">responses</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;array&quot;</span>
<span class="w">            </span><span class="nt">items</span><span class="p">:</span>
<span class="w">              </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">              </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;code&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;description&quot;</span><span class="p p-Indicator">]</span>
<span class="w">              </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">                </span><span class="nt">code</span><span class="p">:</span>
<span class="w">                  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;integer&quot;</span>
<span class="w">                  </span><span class="nt">minimum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span>
<span class="w">                  </span><span class="nt">maximum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">599</span>
<span class="w">                </span><span class="nt">description</span><span class="p">:</span>
<span class="w">                  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">                </span><span class="nt">schema</span><span class="p">:</span>
<span class="w">                  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>

<span class="w">    </span><span class="nt">authentication</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">      </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">          </span><span class="nt">enum</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;oauth2&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;basic&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;apikey&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;none&quot;</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="nt">scopes</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;array&quot;</span>
<span class="w">          </span><span class="nt">items</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">        </span><span class="nt">tokenEndpoint</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;string&quot;</span>
<span class="w">          </span><span class="nt">format</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;uri&quot;</span>

<span class="w">    </span><span class="nt">behavior</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">      </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">        </span><span class="nt">timeout</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;integer&quot;</span>
<span class="w">          </span><span class="nt">minimum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
<span class="w">          </span><span class="nt">maximum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">300000</span>
<span class="w">        </span><span class="nt">retries</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;integer&quot;</span>
<span class="w">          </span><span class="nt">minimum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">0</span>
<span class="w">          </span><span class="nt">maximum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">        </span><span class="nt">circuitBreaker</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;object&quot;</span>
<span class="w">          </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">            </span><span class="nt">enabled</span><span class="p">:</span>
<span class="w">              </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;boolean&quot;</span>
<span class="w">            </span><span class="nt">failureThreshold</span><span class="p">:</span>
<span class="w">              </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;integer&quot;</span>
<span class="w">              </span><span class="nt">minimum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">            </span><span class="nt">recoveryTimeout</span><span class="p">:</span>
<span class="w">              </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;integer&quot;</span>
<span class="w">              </span><span class="nt">minimum</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
</code></pre></div>
<h2 id="api-reference">API Reference<a class="headerlink" href="#api-reference" title="Permanent link">&para;</a></h2>
<h3 id="cip-platform-apis">CIP Platform APIs<a class="headerlink" href="#cip-platform-apis" title="Permanent link">&para;</a></h3>
<h4 id="configuration-management-api">Configuration Management API<a class="headerlink" href="#configuration-management-api" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Configuration deployment endpoint</span>
<span class="l l-Scalar l-Scalar-Plain">POST /api/v1/configuration/deploy</span>
<span class="l l-Scalar l-Scalar-Plain">Content-Type</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">application/zip</span>
<span class="nt">Authorization</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Bearer {token}</span>

<span class="c1"># Request body: Configuration package ZIP file</span>

<span class="c1"># Response</span>
<span class="p p-Indicator">{</span>
<span class="w">  </span><span class="s">&quot;deploymentId&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;deploy-12345&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;status&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;IN_PROGRESS&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;timestamp&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;2024-01-15T10:30:00Z&quot;</span>
<span class="p p-Indicator">}</span>

<span class="c1"># Check deployment status</span>
<span class="l l-Scalar l-Scalar-Plain">GET /api/v1/configuration/deploy/{deploymentId}/status</span>
<span class="nt">Authorization</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Bearer {token}</span>

<span class="c1"># Response</span>
<span class="p p-Indicator">{</span>
<span class="w">  </span><span class="s">&quot;deploymentId&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;deploy-12345&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;status&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;COMPLETED&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;progress&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="nv">100</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;details&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">    </span><span class="s">&quot;servicesImported&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="nv">15</span><span class="p p-Indicator">,</span>
<span class="w">    </span><span class="s">&quot;chainsImported&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="nv">8</span><span class="p p-Indicator">,</span>
<span class="w">    </span><span class="s">&quot;variablesUpdated&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="nv">25</span>
<span class="w">  </span><span class="p p-Indicator">},</span>
<span class="w">  </span><span class="s">&quot;timestamp&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;2024-01-15T10:35:00Z&quot;</span>
<span class="p p-Indicator">}</span>
</code></pre></div>
<h4 id="chain-management-api">Chain Management API<a class="headerlink" href="#chain-management-api" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># List chains</span>
<span class="l l-Scalar l-Scalar-Plain">GET /api/v1/chains</span>
<span class="l l-Scalar l-Scalar-Plain">Authorization</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Bearer {token}</span>
<span class="nt">Parameters</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">category</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string (optional)</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string (optional)</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">limit: integer (default</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">50)</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">offset: integer (default</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">0)</span>

<span class="c1"># Response</span>
<span class="p p-Indicator">{</span>
<span class="w">  </span><span class="s">&quot;chains&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">[</span>
<span class="w">    </span><span class="p p-Indicator">{</span>
<span class="w">      </span><span class="s">&quot;id&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;901ec8bd-32c0-4e48-9996-0b2100c2b79d&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">Or</span><span class="nv"> </span><span class="s">Amend</span><span class="nv"> </span><span class="s">Credit</span><span class="nv"> </span><span class="s">Assessment</span><span class="nv"> </span><span class="s">Request&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;version&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;1.0.0&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;category&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;credit-assessment&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;status&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;ACTIVE&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;lastExecuted&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;2024-01-15T09:45:00Z&quot;</span>
<span class="w">    </span><span class="p p-Indicator">}</span>
<span class="w">  </span><span class="p p-Indicator">],</span>
<span class="w">  </span><span class="s">&quot;total&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="nv">8</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;limit&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="nv">50</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;offset&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="nv">0</span>
<span class="p p-Indicator">}</span>

<span class="c1"># Execute chain</span>
<span class="l l-Scalar l-Scalar-Plain">POST /api/v1/chains/{chainId}/execute</span>
<span class="nt">Authorization</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Bearer {token}</span>
<span class="nt">Content-Type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">application/json</span>

<span class="c1"># Request body</span>
<span class="p p-Indicator">{</span>
<span class="w">  </span><span class="s">&quot;input&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">    </span><span class="s">&quot;customerId&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;12345&quot;</span><span class="p p-Indicator">,</span>
<span class="w">    </span><span class="s">&quot;quoteId&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;Q-67890&quot;</span>
<span class="w">  </span><span class="p p-Indicator">},</span>
<span class="w">  </span><span class="s">&quot;context&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">    </span><span class="s">&quot;userId&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;user123&quot;</span><span class="p p-Indicator">,</span>
<span class="w">    </span><span class="s">&quot;sessionId&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;session456&quot;</span>
<span class="w">  </span><span class="p p-Indicator">}</span>
<span class="p p-Indicator">}</span>

<span class="c1"># Response</span>
<span class="p p-Indicator">{</span>
<span class="w">  </span><span class="s">&quot;executionId&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;exec-78901&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;status&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;RUNNING&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;startTime&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;2024-01-15T10:00:00Z&quot;</span>
<span class="p p-Indicator">}</span>

<span class="c1"># Get execution status</span>
<span class="l l-Scalar l-Scalar-Plain">GET /api/v1/chains/{chainId}/executions/{executionId}</span>
<span class="nt">Authorization</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Bearer {token}</span>

<span class="c1"># Response</span>
<span class="p p-Indicator">{</span>
<span class="w">  </span><span class="s">&quot;executionId&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;exec-78901&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;chainId&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;901ec8bd-32c0-4e48-9996-0b2100c2b79d&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;status&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;COMPLETED&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;startTime&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;2024-01-15T10:00:00Z&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;endTime&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;2024-01-15T10:02:30Z&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;duration&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="nv">150000</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;result&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">    </span><span class="s">&quot;success&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="nv">true</span><span class="p p-Indicator">,</span>
<span class="w">    </span><span class="s">&quot;data&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">      </span><span class="s">&quot;creditAssessmentId&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;CA-12345&quot;</span>
<span class="w">    </span><span class="p p-Indicator">}</span>
<span class="w">  </span><span class="p p-Indicator">}</span>
<span class="p p-Indicator">}</span>
</code></pre></div>
<h4 id="service-management-api">Service Management API<a class="headerlink" href="#service-management-api" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># List services</span>
<span class="l l-Scalar l-Scalar-Plain">GET /api/v1/services</span>
<span class="l l-Scalar l-Scalar-Plain">Authorization</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Bearer {token}</span>
<span class="nt">Parameters</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string (optional)</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">group</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string (optional)</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string (optional)</span>

<span class="c1"># Response</span>
<span class="p p-Indicator">{</span>
<span class="w">  </span><span class="s">&quot;services&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">[</span>
<span class="w">    </span><span class="p p-Indicator">{</span>
<span class="w">      </span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;catalog-integration-tmf&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;version&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;1.0.0&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;TMF&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;group&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;tmf-620&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;status&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;ACTIVE&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;endpoints&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="nv">4</span>
<span class="w">    </span><span class="p p-Indicator">}</span>
<span class="w">  </span><span class="p p-Indicator">]</span>
<span class="p p-Indicator">}</span>

<span class="c1"># Get service details</span>
<span class="l l-Scalar l-Scalar-Plain">GET /api/v1/services/{serviceName}</span>
<span class="nt">Authorization</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Bearer {token}</span>

<span class="c1"># Response</span>
<span class="p p-Indicator">{</span>
<span class="w">  </span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;catalog-integration-tmf&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;version&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;1.0.0&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;type&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;TMF&quot;</span><span class="p p-Indicator">,</span>
<span class="w">  </span><span class="s">&quot;specification&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">{</span>
<span class="w">    </span><span class="s">&quot;group&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;tmf-620&quot;</span><span class="p p-Indicator">,</span>
<span class="w">    </span><span class="s">&quot;version&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;4.0.0&quot;</span><span class="p p-Indicator">,</span>
<span class="w">    </span><span class="s">&quot;title&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;Product</span><span class="nv"> </span><span class="s">Catalog</span><span class="nv"> </span><span class="s">Management&quot;</span>
<span class="w">  </span><span class="p p-Indicator">},</span>
<span class="w">  </span><span class="s">&quot;endpoints&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">[</span>
<span class="w">    </span><span class="p p-Indicator">{</span>
<span class="w">      </span><span class="s">&quot;name&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;productCatalog&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;path&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;/tmf-api/productCatalogManagement/v4&quot;</span><span class="p p-Indicator">,</span>
<span class="w">      </span><span class="s">&quot;methods&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;GET&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;POST&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;PATCH&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;DELETE&quot;</span><span class="p p-Indicator">]</span>
<span class="w">    </span><span class="p p-Indicator">}</span>
<span class="w">  </span><span class="p p-Indicator">],</span>
<span class="w">  </span><span class="s">&quot;status&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;ACTIVE&quot;</span>
<span class="p p-Indicator">}</span>
</code></pre></div>
<h2 id="tmf-api-specifications">TMF API Specifications<a class="headerlink" href="#tmf-api-specifications" title="Permanent link">&para;</a></h2>
<h3 id="tmf-620-product-catalog-management">TMF 620 - Product Catalog Management<a class="headerlink" href="#tmf-620-product-catalog-management" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Product Offering Resource</span>
<span class="nt">ProductOffering</span><span class="p">:</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">object</span>
<span class="w">  </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">id</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">name</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">version</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">id</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Unique identifier of the product offering</span>
<span class="w">    </span><span class="nt">href</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Hyperlink reference</span>
<span class="w">    </span><span class="nt">name</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Name of the product offering</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Description of the product offering</span>
<span class="w">    </span><span class="nt">version</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Product offering version</span>
<span class="w">    </span><span class="nt">validFor</span><span class="p">:</span>
<span class="w">      </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/TimePeriod&#39;</span>
<span class="w">    </span><span class="nt">lifecycleStatus</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">      </span><span class="nt">enum</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">In Study</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">In Design</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">In Test</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">Active</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">Launched</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">Retired</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">Obsolete</span><span class="p p-Indicator">]</span>
<span class="w">    </span><span class="nt">category</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">array</span>
<span class="w">      </span><span class="nt">items</span><span class="p">:</span>
<span class="w">        </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/CategoryRef&#39;</span>
<span class="w">    </span><span class="nt">productSpecification</span><span class="p">:</span>
<span class="w">      </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/ProductSpecificationRef&#39;</span>
<span class="w">    </span><span class="nt">productOfferingPrice</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">array</span>
<span class="w">      </span><span class="nt">items</span><span class="p">:</span>
<span class="w">        </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/ProductOfferingPriceRef&#39;</span>

<span class="c1"># API Endpoints</span>
<span class="nt">paths</span><span class="p">:</span>
<span class="w">  </span><span class="nt">/productOffering</span><span class="p">:</span>
<span class="w">    </span><span class="nt">get</span><span class="p">:</span>
<span class="w">      </span><span class="nt">summary</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">List or find ProductOffering objects</span>
<span class="w">      </span><span class="nt">parameters</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">fields</span>
<span class="w">          </span><span class="nt">in</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">query</span>
<span class="w">          </span><span class="nt">schema</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">offset</span>
<span class="w">          </span><span class="nt">in</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">query</span>
<span class="w">          </span><span class="nt">schema</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">integer</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">limit</span>
<span class="w">          </span><span class="nt">in</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">query</span>
<span class="w">          </span><span class="nt">schema</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">integer</span>
<span class="w">      </span><span class="nt">responses</span><span class="p">:</span>
<span class="w">        </span><span class="s">&#39;200&#39;</span><span class="p p-Indicator">:</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Success</span>
<span class="w">          </span><span class="nt">content</span><span class="p">:</span>
<span class="w">            </span><span class="nt">application/json</span><span class="p">:</span>
<span class="w">              </span><span class="nt">schema</span><span class="p">:</span>
<span class="w">                </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">array</span>
<span class="w">                </span><span class="nt">items</span><span class="p">:</span>
<span class="w">                  </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/ProductOffering&#39;</span>

<span class="w">    </span><span class="nt">post</span><span class="p">:</span>
<span class="w">      </span><span class="nt">summary</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Creates a ProductOffering</span>
<span class="w">      </span><span class="nt">requestBody</span><span class="p">:</span>
<span class="w">        </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">content</span><span class="p">:</span>
<span class="w">          </span><span class="nt">application/json</span><span class="p">:</span>
<span class="w">            </span><span class="nt">schema</span><span class="p">:</span>
<span class="w">              </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/ProductOfferingCreate&#39;</span>
<span class="w">      </span><span class="nt">responses</span><span class="p">:</span>
<span class="w">        </span><span class="s">&#39;201&#39;</span><span class="p p-Indicator">:</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Created</span>
<span class="w">          </span><span class="nt">content</span><span class="p">:</span>
<span class="w">            </span><span class="nt">application/json</span><span class="p">:</span>
<span class="w">              </span><span class="nt">schema</span><span class="p">:</span>
<span class="w">                </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/ProductOffering&#39;</span>
</code></pre></div>
<h3 id="tmf-648-quote-management">TMF 648 - Quote Management<a class="headerlink" href="#tmf-648-quote-management" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Quote Resource</span>
<span class="nt">Quote</span><span class="p">:</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">object</span>
<span class="w">  </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">id</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">state</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">id</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Unique identifier of the quote</span>
<span class="w">    </span><span class="nt">href</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Hyperlink reference</span>
<span class="w">    </span><span class="nt">state</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">      </span><span class="nt">enum</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">Draft</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">Submitted</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">Approved</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">Rejected</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">Cancelled</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">Expired</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">State of the quote</span>
<span class="w">    </span><span class="nt">quoteDate</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">      </span><span class="nt">format</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">date-time</span>
<span class="w">      </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Date when the quote was created</span>
<span class="w">    </span><span class="nt">validFor</span><span class="p">:</span>
<span class="w">      </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/TimePeriod&#39;</span>
<span class="w">    </span><span class="nt">quoteTotalPrice</span><span class="p">:</span>
<span class="w">      </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/Price&#39;</span>
<span class="w">    </span><span class="nt">quoteItem</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">array</span>
<span class="w">      </span><span class="nt">items</span><span class="p">:</span>
<span class="w">        </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/QuoteItem&#39;</span>
<span class="w">    </span><span class="nt">relatedParty</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">array</span>
<span class="w">      </span><span class="nt">items</span><span class="p">:</span>
<span class="w">        </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/RelatedParty&#39;</span>

<span class="c1"># Quote State Transitions</span>
<span class="nt">QuoteStateTransitions</span><span class="p">:</span>
<span class="w">  </span><span class="nt">Draft</span><span class="p">:</span>
<span class="w">    </span><span class="nt">allowedTransitions</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">Submitted</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">Cancelled</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">Submitted</span><span class="p">:</span>
<span class="w">    </span><span class="nt">allowedTransitions</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">Approved</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">Rejected</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">Cancelled</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">Approved</span><span class="p">:</span>
<span class="w">    </span><span class="nt">allowedTransitions</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">Expired</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">Converted</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">Rejected</span><span class="p">:</span>
<span class="w">    </span><span class="nt">allowedTransitions</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[]</span>
<span class="w">  </span><span class="nt">Cancelled</span><span class="p">:</span>
<span class="w">    </span><span class="nt">allowedTransitions</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[]</span>
<span class="w">  </span><span class="nt">Expired</span><span class="p">:</span>
<span class="w">    </span><span class="nt">allowedTransitions</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[]</span>
</code></pre></div>
<h2 id="environment-configuration-reference">Environment Configuration Reference<a class="headerlink" href="#environment-configuration-reference" title="Permanent link">&para;</a></h2>
<h3 id="environment-variables">Environment Variables<a class="headerlink" href="#environment-variables" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Variable</th>
<th>Description</th>
<th>Default</th>
<th>Required</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>ENVIRONMENT</code></td>
<td>Deployment environment</td>
<td><code>development</code></td>
<td>Yes</td>
</tr>
<tr>
<td><code>LOG_LEVEL</code></td>
<td>Logging level</td>
<td><code>INFO</code></td>
<td>No</td>
</tr>
<tr>
<td><code>CIP_PLATFORM_URL</code></td>
<td>CIP platform base URL</td>
<td>-</td>
<td>Yes</td>
</tr>
<tr>
<td><code>SFDC_ENDPOINT</code></td>
<td>Salesforce API endpoint</td>
<td>-</td>
<td>Yes</td>
</tr>
<tr>
<td><code>SFDC_CLIENT_ID</code></td>
<td>Salesforce OAuth client ID</td>
<td>-</td>
<td>Yes</td>
</tr>
<tr>
<td><code>SFDC_CLIENT_SECRET</code></td>
<td>Salesforce OAuth client secret</td>
<td>-</td>
<td>Yes</td>
</tr>
<tr>
<td><code>TMF_BASE_URL</code></td>
<td>TMF API base URL</td>
<td>-</td>
<td>Yes</td>
</tr>
<tr>
<td><code>OAUTH_TOKEN_ENDPOINT</code></td>
<td>OAuth token endpoint</td>
<td>-</td>
<td>Yes</td>
</tr>
<tr>
<td><code>OAUTH_CLIENT_ID</code></td>
<td>OAuth client ID</td>
<td>-</td>
<td>Yes</td>
</tr>
<tr>
<td><code>OAUTH_CLIENT_SECRET</code></td>
<td>OAuth client secret</td>
<td>-</td>
<td>Yes</td>
</tr>
<tr>
<td><code>DATABASE_URL</code></td>
<td>Database connection URL</td>
<td>-</td>
<td>No</td>
</tr>
<tr>
<td><code>REDIS_URL</code></td>
<td>Redis connection URL</td>
<td>-</td>
<td>No</td>
</tr>
<tr>
<td><code>RABBITMQ_URL</code></td>
<td>RabbitMQ connection URL</td>
<td>-</td>
<td>No</td>
</tr>
</tbody>
</table>
<h3 id="resource-limits">Resource Limits<a class="headerlink" href="#resource-limits" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Environment</th>
<th>CPU Request</th>
<th>CPU Limit</th>
<th>Memory Request</th>
<th>Memory Limit</th>
<th>Replicas</th>
</tr>
</thead>
<tbody>
<tr>
<td>Development</td>
<td>50m</td>
<td>200m</td>
<td>128Mi</td>
<td>256Mi</td>
<td>1</td>
</tr>
<tr>
<td>QA</td>
<td>100m</td>
<td>500m</td>
<td>256Mi</td>
<td>512Mi</td>
<td>2</td>
</tr>
<tr>
<td>Staging</td>
<td>200m</td>
<td>1000m</td>
<td>512Mi</td>
<td>1Gi</td>
<td>3</td>
</tr>
<tr>
<td>Production</td>
<td>500m</td>
<td>2000m</td>
<td>1Gi</td>
<td>2Gi</td>
<td>5</td>
</tr>
</tbody>
</table>
<h2 id="error-codes-reference">Error Codes Reference<a class="headerlink" href="#error-codes-reference" title="Permanent link">&para;</a></h2>
<h3 id="configuration-errors">Configuration Errors<a class="headerlink" href="#configuration-errors" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Code</th>
<th>Message</th>
<th>Description</th>
<th>Resolution</th>
</tr>
</thead>
<tbody>
<tr>
<td>CFG001</td>
<td>Invalid YAML syntax</td>
<td>YAML file contains syntax errors</td>
<td>Validate YAML syntax</td>
</tr>
<tr>
<td>CFG002</td>
<td>Missing required field</td>
<td>Required configuration field not provided</td>
<td>Add missing field</td>
</tr>
<tr>
<td>CFG003</td>
<td>Invalid reference</td>
<td>Referenced chain/service doesn't exist</td>
<td>Verify reference exists</td>
</tr>
<tr>
<td>CFG004</td>
<td>Circular dependency</td>
<td>Chains reference each other circularly</td>
<td>Remove circular references</td>
</tr>
<tr>
<td>CFG005</td>
<td>Schema validation failed</td>
<td>Configuration doesn't match schema</td>
<td>Fix schema violations</td>
</tr>
</tbody>
</table>
<h3 id="runtime-errors">Runtime Errors<a class="headerlink" href="#runtime-errors" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Code</th>
<th>Message</th>
<th>Description</th>
<th>Resolution</th>
</tr>
</thead>
<tbody>
<tr>
<td>RUN001</td>
<td>Chain execution timeout</td>
<td>Chain execution exceeded timeout</td>
<td>Increase timeout or optimize chain</td>
</tr>
<tr>
<td>RUN002</td>
<td>Service call failed</td>
<td>External service call failed</td>
<td>Check service availability</td>
</tr>
<tr>
<td>RUN003</td>
<td>Script execution error</td>
<td>Groovy script execution failed</td>
<td>Debug script logic</td>
</tr>
<tr>
<td>RUN004</td>
<td>Variable resolution failed</td>
<td>Variable could not be resolved</td>
<td>Check variable definition</td>
</tr>
<tr>
<td>RUN005</td>
<td>Authentication failed</td>
<td>Service authentication failed</td>
<td>Verify credentials</td>
</tr>
</tbody>
</table>
<h3 id="integration-errors">Integration Errors<a class="headerlink" href="#integration-errors" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Code</th>
<th>Message</th>
<th>Description</th>
<th>Resolution</th>
</tr>
</thead>
<tbody>
<tr>
<td>INT001</td>
<td>SFDC API error</td>
<td>Salesforce API call failed</td>
<td>Check SFDC configuration</td>
</tr>
<tr>
<td>INT002</td>
<td>TMF validation error</td>
<td>TMF schema validation failed</td>
<td>Verify TMF compliance</td>
</tr>
<tr>
<td>INT003</td>
<td>Rate limit exceeded</td>
<td>API rate limit exceeded</td>
<td>Implement rate limiting</td>
</tr>
<tr>
<td>INT004</td>
<td>Connection timeout</td>
<td>Connection to external service timed out</td>
<td>Check network connectivity</td>
</tr>
<tr>
<td>INT005</td>
<td>Invalid response format</td>
<td>Unexpected response format</td>
<td>Verify API contract</td>
</tr>
</tbody>
</table>
<h2 id="performance-benchmarks">Performance Benchmarks<a class="headerlink" href="#performance-benchmarks" title="Permanent link">&para;</a></h2>
<h3 id="chain-execution-performance">Chain Execution Performance<a class="headerlink" href="#chain-execution-performance" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Chain Type</th>
<th>Average Duration</th>
<th>95th Percentile</th>
<th>Throughput (req/min)</th>
</tr>
</thead>
<tbody>
<tr>
<td>Credit Assessment</td>
<td>2.5s</td>
<td>5.0s</td>
<td>120</td>
</tr>
<tr>
<td>Quote Management</td>
<td>1.8s</td>
<td>3.5s</td>
<td>180</td>
</tr>
<tr>
<td>Service Provisioning</td>
<td>8.2s</td>
<td>15.0s</td>
<td>45</td>
</tr>
<tr>
<td>Billing Integration</td>
<td>3.1s</td>
<td>6.0s</td>
<td>95</td>
</tr>
</tbody>
</table>
<h3 id="service-call-performance">Service Call Performance<a class="headerlink" href="#service-call-performance" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Service</th>
<th>Average Response Time</th>
<th>95th Percentile</th>
<th>Error Rate</th>
</tr>
</thead>
<tbody>
<tr>
<td>SFDC Integration</td>
<td>850ms</td>
<td>1.5s</td>
<td>0.2%</td>
</tr>
<tr>
<td>TMF Product Catalog</td>
<td>320ms</td>
<td>600ms</td>
<td>0.1%</td>
</tr>
<tr>
<td>TMF Quote Management</td>
<td>450ms</td>
<td>800ms</td>
<td>0.3%</td>
</tr>
<tr>
<td>Billing Service</td>
<td>1.2s</td>
<td>2.1s</td>
<td>0.5%</td>
</tr>
</tbody>
</table>
<h2 id="security-configuration">Security Configuration<a class="headerlink" href="#security-configuration" title="Permanent link">&para;</a></h2>
<h3 id="oauth2-scopes">OAuth2 Scopes<a class="headerlink" href="#oauth2-scopes" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Scope</th>
<th>Description</th>
<th>Required For</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>cip:config:read</code></td>
<td>Read configuration data</td>
<td>Configuration queries</td>
</tr>
<tr>
<td><code>cip:config:write</code></td>
<td>Modify configuration data</td>
<td>Configuration updates</td>
</tr>
<tr>
<td><code>cip:chains:execute</code></td>
<td>Execute integration chains</td>
<td>Chain execution</td>
</tr>
<tr>
<td><code>cip:services:call</code></td>
<td>Call registered services</td>
<td>Service invocation</td>
</tr>
<tr>
<td><code>cip:monitoring:read</code></td>
<td>Read monitoring data</td>
<td>Health checks, metrics</td>
</tr>
</tbody>
</table>
<h3 id="tls-configuration">TLS Configuration<a class="headerlink" href="#tls-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># TLS configuration example</span>
<span class="nt">tls</span><span class="p">:</span>
<span class="w">  </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.3&quot;</span>
<span class="w">  </span><span class="nt">cipherSuites</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;TLS_AES_256_GCM_SHA384&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;TLS_CHACHA20_POLY1305_SHA256&quot;</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;TLS_AES_128_GCM_SHA256&quot;</span>
<span class="w">  </span><span class="nt">certificateValidation</span><span class="p">:</span>
<span class="w">    </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">verifyHostname</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">verifyChain</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">  </span><span class="nt">mutualTLS</span><span class="p">:</span>
<span class="w">    </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span><span class="w">  </span><span class="c1"># Production only</span>
<span class="w">    </span><span class="nt">clientCertificateRequired</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</code></pre></div>
<p>This reference documentation provides comprehensive technical specifications and configuration details for the CIP Configuration Package, serving as the authoritative source for developers, operators, and integrators working with the system.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>