/**
 * Types for TELUS AMS MCP Server
 */

/**
 * Environment options for AMS API
 */
export enum Environment {
  PROD = 'PROD',
  NON_PROD = 'NON_PROD',
}

/**
 * Configuration for AMS API
 */
export interface AmsConfig {
  clientId: string;
  clientSecretProd: string;
  clientSecretNonProd: string;
  environment: Environment;
  scope: string;
}

/**
 * OAuth token response
 */
export interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

/**
 * Token cache with expiration
 */
export interface TokenCache {
  token: string;
  expiresAt: number;
}

/**
 * Smart search address request parameters
 */
export interface SmartSearchAddressParams {
  address: string;
  environment?: Environment;
}

/**
 * AMS API response structure
 */
export interface AmsApiResponse {
  action: string;
  response: string;
  responseData?: {
    numhits: number;
    hit: AmsAddressHit[];
  };
  error?: {
    code: string;
    message: string;
  };
}

/**
 * AMS Address Hit structure (from API)
 */
export interface AmsAddressHit {
  id: string;
  score: string;
  address: string;
  source: string;
  lastUpdated: string;
  payload: {
    id: string;
    [key: string]: any;
  };
}

/**
 * AMS Address structure (normalized for client use)
 */
export interface AmsAddress {
  id: string;
  formattedAddress: string;
  score: number;
  source: string;
  lastUpdated: string;
  telus_id?: string;
  streetNumber?: string;
  streetName?: string;
  streetType?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  country?: string;
  [key: string]: any; // For additional fields that might be returned
}
