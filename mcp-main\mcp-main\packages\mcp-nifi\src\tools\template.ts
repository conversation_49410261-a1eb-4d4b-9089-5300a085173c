import { NiFiClient } from '../nifi-client.js';
import { z } from 'zod';

type ToolRequest = {
  method: string;
  params?: {
    arguments?: Record<string, unknown>;
  };
};

type ToolResponse = {
  content: Array<{ type: string; text: string }>;
  isError?: boolean;
};

export const tools = {
  export_template: {
    description: 'Export a template by ID',
    inputSchema: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'The template id'
        }
      },
      required: ['id']
    }
  }
};

export const exportTemplate = (client: NiFiClient) => async (request: ToolRequest): Promise<ToolResponse> => {
  const { id } = (request.params?.arguments || {}) as { id: string };
  try {
    const response = await client.get(`/templates/${id}/download`);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2)
        }
      ]
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      content: [
        {
          type: 'text',
          text: `Error exporting template: ${errorMessage}`
        }
      ],
      isError: true
    };
  }
};
