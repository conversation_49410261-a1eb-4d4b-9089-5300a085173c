
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/cpq-config/03-service-catalog/">
      
      
        <link rel="prev" href="../02-integration-chains/">
      
      
        <link rel="next" href="../04-security-configuration/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Service Catalog - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#service-catalog" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Service Catalog
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" checked>
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#service-catalog-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Service Catalog Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Catalog Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#service-registry-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Service Registry Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-discovery-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Service Discovery Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#tmf-api-services" class="md-nav__link">
    <span class="md-ellipsis">
      TMF API Services
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TMF API Services">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#tmf-quote-management-service" class="md-nav__link">
    <span class="md-ellipsis">
      TMF Quote Management Service
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#tmf-service-catalog-integration" class="md-nav__link">
    <span class="md-ellipsis">
      TMF Service Catalog Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#tmf-service-activation" class="md-nav__link">
    <span class="md-ellipsis">
      TMF Service Activation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#sfdc-crm-integration" class="md-nav__link">
    <span class="md-ellipsis">
      SFDC CRM Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="SFDC CRM Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#sfdc-service-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      SFDC Service Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#sfdc-authentication-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      SFDC Authentication Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#billing-and-support-services" class="md-nav__link">
    <span class="md-ellipsis">
      Billing and Support Services
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Billing and Support Services">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#service-support-fee-api" class="md-nav__link">
    <span class="md-ellipsis">
      Service Support Fee API
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#billing-integration-service" class="md-nav__link">
    <span class="md-ellipsis">
      Billing Integration Service
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#service-configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      Service Configuration Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Configuration Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#environment-specific-service-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment-Specific Service Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-health-monitoring" class="md-nav__link">
    <span class="md-ellipsis">
      Service Health Monitoring
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#api-specification-management" class="md-nav__link">
    <span class="md-ellipsis">
      API Specification Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="API Specification Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#openapi-specification-structure" class="md-nav__link">
    <span class="md-ellipsis">
      OpenAPI Specification Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#api-version-management" class="md-nav__link">
    <span class="md-ellipsis">
      API Version Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#service-catalog-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Service Catalog Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Catalog Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#service-registry-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Service Registry Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-discovery-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Service Discovery Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#tmf-api-services" class="md-nav__link">
    <span class="md-ellipsis">
      TMF API Services
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TMF API Services">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#tmf-quote-management-service" class="md-nav__link">
    <span class="md-ellipsis">
      TMF Quote Management Service
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#tmf-service-catalog-integration" class="md-nav__link">
    <span class="md-ellipsis">
      TMF Service Catalog Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#tmf-service-activation" class="md-nav__link">
    <span class="md-ellipsis">
      TMF Service Activation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#sfdc-crm-integration" class="md-nav__link">
    <span class="md-ellipsis">
      SFDC CRM Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="SFDC CRM Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#sfdc-service-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      SFDC Service Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#sfdc-authentication-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      SFDC Authentication Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#billing-and-support-services" class="md-nav__link">
    <span class="md-ellipsis">
      Billing and Support Services
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Billing and Support Services">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#service-support-fee-api" class="md-nav__link">
    <span class="md-ellipsis">
      Service Support Fee API
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#billing-integration-service" class="md-nav__link">
    <span class="md-ellipsis">
      Billing Integration Service
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#service-configuration-management" class="md-nav__link">
    <span class="md-ellipsis">
      Service Configuration Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Configuration Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#environment-specific-service-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment-Specific Service Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-health-monitoring" class="md-nav__link">
    <span class="md-ellipsis">
      Service Health Monitoring
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#api-specification-management" class="md-nav__link">
    <span class="md-ellipsis">
      API Specification Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="API Specification Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#openapi-specification-structure" class="md-nav__link">
    <span class="md-ellipsis">
      OpenAPI Specification Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#api-version-management" class="md-nav__link">
    <span class="md-ellipsis">
      API Version Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="service-catalog">Service Catalog<a class="headerlink" href="#service-catalog" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#service-catalog-overview">Service Catalog Overview</a></li>
<li><a href="#tmf-api-services">TMF API Services</a></li>
<li><a href="#sfdc-crm-integration">SFDC CRM Integration</a></li>
<li><a href="#billing-and-support-services">Billing and Support Services</a></li>
<li><a href="#service-configuration-management">Service Configuration Management</a></li>
<li><a href="#api-specification-management">API Specification Management</a></li>
</ul>
<h2 id="service-catalog-overview">Service Catalog Overview<a class="headerlink" href="#service-catalog-overview" title="Permanent link">&para;</a></h2>
<h3 id="service-registry-architecture">Service Registry Architecture<a class="headerlink" href="#service-registry-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Service Catalog Structure&quot;
        A[Service Registry]
        B[API Specifications]
        C[Environment Configurations]
        D[Service Metadata]
    end

    subgraph &quot;Service Categories&quot;
        E[TMF Standard APIs]
        F[SFDC CRM Services]
        G[Billing Services]
        H[Network Services]
        I[Support Services]
    end

    subgraph &quot;Integration Types&quot;
        J[REST/HTTP APIs]
        K[SOAP Web Services]
        L[GraphQL APIs]
        M[Message Queues]
    end

    subgraph &quot;Environment Management&quot;
        N[Development]
        O[QA/Testing]
        P[Staging]
        Q[Production]
    end

    A --&gt; B
    A --&gt; C
    A --&gt; D

    A --&gt; E
    A --&gt; F
    A --&gt; G
    A --&gt; H
    A --&gt; I

    B --&gt; J
    B --&gt; K
    B --&gt; L
    B --&gt; M

    C --&gt; N
    C --&gt; O
    C --&gt; P
    C --&gt; Q

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style E fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="service-discovery-flow">Service Discovery Flow<a class="headerlink" href="#service-discovery-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Client as Integration Client
    participant Registry as Service Registry
    participant Config as Configuration Store
    participant Service as Target Service

    Client-&gt;&gt;Registry: Request Service Endpoint
    Registry-&gt;&gt;Config: Get Environment Configuration
    Config--&gt;&gt;Registry: Return Service Details
    Registry--&gt;&gt;Client: Service Endpoint &amp; Metadata

    Client-&gt;&gt;Service: API Call with Configuration
    Service--&gt;&gt;Client: Response

    Client-&gt;&gt;Registry: Report Service Health
    Registry-&gt;&gt;Registry: Update Service Status
</code></pre></div>
<h2 id="tmf-api-services">TMF API Services<a class="headerlink" href="#tmf-api-services" title="Permanent link">&para;</a></h2>
<h3 id="tmf-quote-management-service">TMF Quote Management Service<a class="headerlink" href="#tmf-quote-management-service" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># service-quote-tmf-service.yaml</span>
<span class="nn">---</span>
<span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;quote-tmf-service&quot;</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TMF</span><span class="nv"> </span><span class="s">Quote</span><span class="nv"> </span><span class="s">Management</span><span class="nv"> </span><span class="s">Service&quot;</span>
<span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TMF</span><span class="nv"> </span><span class="s">648</span><span class="nv"> </span><span class="s">Quote</span><span class="nv"> </span><span class="s">Management</span><span class="nv"> </span><span class="s">API</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">B2B</span><span class="nv"> </span><span class="s">order</span><span class="nv"> </span><span class="s">capture&quot;</span>
<span class="nt">systemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INTEGRATION_SYSTEM&quot;</span>
<span class="nt">integrationSystemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INTERNAL&quot;</span>
<span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;HTTP&quot;</span>
<span class="nt">activeEnvironmentId</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;prod-env-001&quot;</span>

<span class="nt">environments</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;dev-env-001&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Development&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://quote-service-dev.telus.internal:8080&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;DEVELOPMENT&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;QA&quot;</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">api-version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;6.2&quot;</span>
<span class="w">    </span><span class="nt">authentication</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;m2m&quot;</span>
<span class="w">    </span><span class="nt">rate-limit</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1000/hour&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;qa-env-001&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;QA&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://quote-service-qa.telus.internal:8080&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">20000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;QA&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;TESTING&quot;</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">api-version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;6.2&quot;</span>
<span class="w">    </span><span class="nt">authentication</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;m2m&quot;</span>
<span class="w">    </span><span class="nt">rate-limit</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;5000/hour&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;prod-env-001&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Production&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://quote-service.telus.com&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">15000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;PRODUCTION&quot;</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">api-version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;6.2&quot;</span>
<span class="w">    </span><span class="nt">authentication</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;m2m&quot;</span>
<span class="w">    </span><span class="nt">rate-limit</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;10000/hour&quot;</span>
<span class="w">    </span><span class="nt">circuit-breaker</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;enabled&quot;</span>

<span class="nt">specifications</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;tmf-648-quote-management-6.2&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TMF</span><span class="nv"> </span><span class="s">648</span><span class="nv"> </span><span class="s">Quote</span><span class="nv"> </span><span class="s">Management</span><span class="nv"> </span><span class="s">API</span><span class="nv"> </span><span class="s">v6.2&quot;</span>
<span class="w">  </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;6.2&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;OpenAPI&quot;</span>
<span class="w">  </span><span class="nt">source</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;source-TMF648-Quote-Management-6.2/TMF648-Quote-Management-6.2.json&quot;</span>

<span class="nt">operations</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;createQuote&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">Quote&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/quoteManagement/v6/quote&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">a</span><span class="nv"> </span><span class="s">new</span><span class="nv"> </span><span class="s">quote&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;getQuote&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Get</span><span class="nv"> </span><span class="s">Quote&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;GET&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/quoteManagement/v6/quote/{id}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Retrieve</span><span class="nv"> </span><span class="s">quote</span><span class="nv"> </span><span class="s">by</span><span class="nv"> </span><span class="s">ID&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;updateQuote&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Update</span><span class="nv"> </span><span class="s">Quote&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;PATCH&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/quoteManagement/v6/quote/{id}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Update</span><span class="nv"> </span><span class="s">existing</span><span class="nv"> </span><span class="s">quote&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;changeQuoteState&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Change</span><span class="nv"> </span><span class="s">Quote</span><span class="nv"> </span><span class="s">State&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/quoteManagement/v6/quote/{id}/changeState&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Change</span><span class="nv"> </span><span class="s">quote</span><span class="nv"> </span><span class="s">state&quot;</span>
</code></pre></div>
<h3 id="tmf-service-catalog-integration">TMF Service Catalog Integration<a class="headerlink" href="#tmf-service-catalog-integration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># service-catalog-integration-tmf.yaml</span>
<span class="nn">---</span>
<span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;catalog-integration-tmf&quot;</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TMF</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Catalog</span><span class="nv"> </span><span class="s">Integration&quot;</span>
<span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TMF</span><span class="nv"> </span><span class="s">620</span><span class="nv"> </span><span class="s">Product</span><span class="nv"> </span><span class="s">Catalog</span><span class="nv"> </span><span class="s">Management</span><span class="nv"> </span><span class="s">API</span><span class="nv"> </span><span class="s">integration&quot;</span>
<span class="nt">systemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INTEGRATION_SYSTEM&quot;</span>
<span class="nt">integrationSystemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INTERNAL&quot;</span>
<span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;HTTP&quot;</span>

<span class="nt">environments</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;internal-env&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Internal&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://catalog-service.telus.internal:8080&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;INTERNAL&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;TMF620&quot;</span><span class="p p-Indicator">]</span>

<span class="nt">specifications</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;tmf-620-product-catalog-2.9&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TMF</span><span class="nv"> </span><span class="s">620</span><span class="nv"> </span><span class="s">Product</span><span class="nv"> </span><span class="s">Catalog</span><span class="nv"> </span><span class="s">Management</span><span class="nv"> </span><span class="s">API</span><span class="nv"> </span><span class="s">v2.9&quot;</span>
<span class="w">  </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;2.9&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;OpenAPI&quot;</span>
<span class="w">  </span><span class="nt">source</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;source-citmf-620-v2.9-v2.9/TMF620-ProductCatalog-v2.9.json&quot;</span>

<span class="nt">operations</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;listProductOfferings&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;List</span><span class="nv"> </span><span class="s">Product</span><span class="nv"> </span><span class="s">Offerings&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;GET&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/productCatalogManagement/v2/productOffering&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Retrieve</span><span class="nv"> </span><span class="s">product</span><span class="nv"> </span><span class="s">offerings</span><span class="nv"> </span><span class="s">from</span><span class="nv"> </span><span class="s">catalog&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;getProductOffering&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Get</span><span class="nv"> </span><span class="s">Product</span><span class="nv"> </span><span class="s">Offering&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;GET&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/productCatalogManagement/v2/productOffering/{id}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Retrieve</span><span class="nv"> </span><span class="s">specific</span><span class="nv"> </span><span class="s">product</span><span class="nv"> </span><span class="s">offering&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;getProductSpecification&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Get</span><span class="nv"> </span><span class="s">Product</span><span class="nv"> </span><span class="s">Specification&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;GET&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/productCatalogManagement/v2/productSpecification/{id}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Retrieve</span><span class="nv"> </span><span class="s">product</span><span class="nv"> </span><span class="s">specification</span><span class="nv"> </span><span class="s">details&quot;</span>
</code></pre></div>
<h3 id="tmf-service-activation">TMF Service Activation<a class="headerlink" href="#tmf-service-activation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># service-activation-tmf.yaml</span>
<span class="nn">---</span>
<span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-activation-tmf&quot;</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TMF</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Activation</span><span class="nv"> </span><span class="s">and</span><span class="nv"> </span><span class="s">Configuration&quot;</span>
<span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TMF</span><span class="nv"> </span><span class="s">640</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Activation</span><span class="nv"> </span><span class="s">and</span><span class="nv"> </span><span class="s">Configuration</span><span class="nv"> </span><span class="s">API&quot;</span>
<span class="nt">systemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INTEGRATION_SYSTEM&quot;</span>
<span class="nt">integrationSystemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;EXTERNAL&quot;</span>
<span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;HTTP&quot;</span>

<span class="nt">environments</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;telus-prod&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TELUS</span><span class="nv"> </span><span class="s">Production&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://apigw-private-yul-pr-001.tsl.telus.com/serviceActivation/v4&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">60000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;PRODUCTION&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;TMF640&quot;</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">authentication</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;oauth2&quot;</span>
<span class="w">    </span><span class="nt">scope</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-activation&quot;</span>

<span class="nt">specifications</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;tmf-640-service-activation-4.0&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TMF</span><span class="nv"> </span><span class="s">640</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Activation</span><span class="nv"> </span><span class="s">and</span><span class="nv"> </span><span class="s">Configuration</span><span class="nv"> </span><span class="s">API</span><span class="nv"> </span><span class="s">v4.0&quot;</span>
<span class="w">  </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;4.0&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;OpenAPI&quot;</span>
<span class="w">  </span><span class="nt">source</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;source-TMF640-ServiceActivation-4.0/TMF640-ServiceActivation-4.0.json&quot;</span>

<span class="nt">operations</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;createService&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">Service&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/service&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">and</span><span class="nv"> </span><span class="s">activate</span><span class="nv"> </span><span class="s">a</span><span class="nv"> </span><span class="s">new</span><span class="nv"> </span><span class="s">service&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;getService&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Get</span><span class="nv"> </span><span class="s">Service&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;GET&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/service/{id}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Retrieve</span><span class="nv"> </span><span class="s">service</span><span class="nv"> </span><span class="s">details</span><span class="nv"> </span><span class="s">and</span><span class="nv"> </span><span class="s">status&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;updateService&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Update</span><span class="nv"> </span><span class="s">Service&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;PATCH&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/service/{id}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Update</span><span class="nv"> </span><span class="s">service</span><span class="nv"> </span><span class="s">configuration&quot;</span>
</code></pre></div>
<h2 id="sfdc-crm-integration">SFDC CRM Integration<a class="headerlink" href="#sfdc-crm-integration" title="Permanent link">&para;</a></h2>
<h3 id="sfdc-service-configuration">SFDC Service Configuration<a class="headerlink" href="#sfdc-service-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># service-sfdc-crm.yaml</span>
<span class="nn">---</span>
<span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;310a1ed6-b83c-4dff-a288-231b8c486a13&quot;</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;SFDC</span><span class="nv"> </span><span class="s">CRM</span><span class="nv"> </span><span class="s">Integration&quot;</span>
<span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Salesforce</span><span class="nv"> </span><span class="s">CRM</span><span class="nv"> </span><span class="s">integration</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">credit</span><span class="nv"> </span><span class="s">assessment</span><span class="nv"> </span><span class="s">and</span><span class="nv"> </span><span class="s">customer</span><span class="nv"> </span><span class="s">management&quot;</span>
<span class="nt">systemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INTEGRATION_SYSTEM&quot;</span>
<span class="nt">integrationSystemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;EXTERNAL&quot;</span>
<span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;HTTP&quot;</span>

<span class="nt">environments</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;sfdc-sandbox&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;SFDC</span><span class="nv"> </span><span class="s">Sandbox&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://telus--sandbox.sandbox.my.salesforce.com&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;SANDBOX&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;DEVELOPMENT&quot;</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">api-version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;58.0&quot;</span>
<span class="w">    </span><span class="nt">authentication</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;oauth2&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;sfdc-production&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;SFDC</span><span class="nv"> </span><span class="s">Production&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://telus.my.salesforce.com&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">20000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;PRODUCTION&quot;</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">    </span><span class="nt">api-version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;58.0&quot;</span>
<span class="w">    </span><span class="nt">authentication</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;oauth2&quot;</span>
<span class="w">    </span><span class="nt">circuit-breaker</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;enabled&quot;</span>

<span class="nt">specifications</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;sfdc-crm-api-58.0&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Salesforce</span><span class="nv"> </span><span class="s">CRM</span><span class="nv"> </span><span class="s">API</span><span class="nv"> </span><span class="s">v58.0&quot;</span>
<span class="w">  </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;58.0&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;REST&quot;</span>
<span class="w">  </span><span class="nt">source</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;source-SFDC-CRM-58.0/SFDC-CRM-API.json&quot;</span>

<span class="nt">operations</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;create_car&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">Credit</span><span class="nv"> </span><span class="s">Assessment</span><span class="nv"> </span><span class="s">Request&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/services/data/v58.0/actions/custom/flow/Create_CAR_from_API&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">new</span><span class="nv"> </span><span class="s">credit</span><span class="nv"> </span><span class="s">assessment</span><span class="nv"> </span><span class="s">request</span><span class="nv"> </span><span class="s">in</span><span class="nv"> </span><span class="s">SFDC&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;amend_car&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Amend</span><span class="nv"> </span><span class="s">Credit</span><span class="nv"> </span><span class="s">Assessment</span><span class="nv"> </span><span class="s">Request&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/services/data/v58.0/actions/custom/flow/Amend_CAR_from_API&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Amend</span><span class="nv"> </span><span class="s">existing</span><span class="nv"> </span><span class="s">credit</span><span class="nv"> </span><span class="s">assessment</span><span class="nv"> </span><span class="s">request&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;get_account&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Get</span><span class="nv"> </span><span class="s">Account</span><span class="nv"> </span><span class="s">Details&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;GET&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/services/data/v58.0/sobjects/Account/{id}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Retrieve</span><span class="nv"> </span><span class="s">customer</span><span class="nv"> </span><span class="s">account</span><span class="nv"> </span><span class="s">information&quot;</span>
</code></pre></div>
<h3 id="sfdc-authentication-configuration">SFDC Authentication Configuration<a class="headerlink" href="#sfdc-authentication-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># SFDC OAuth2 configuration</span>
<span class="nt">sfdc-authentication</span><span class="p">:</span>
<span class="w">  </span><span class="nt">oauth2</span><span class="p">:</span>
<span class="w">    </span><span class="nt">grant-type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;client_credentials&quot;</span>
<span class="w">    </span><span class="nt">token-endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://telus.my.salesforce.com/services/oauth2/token&quot;</span>
<span class="w">    </span><span class="nt">client-id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${SFDC_CLIENT_ID}&quot;</span>
<span class="w">    </span><span class="nt">client-secret</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${SFDC_CLIENT_SECRET}&quot;</span>
<span class="w">    </span><span class="nt">scope</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;api&quot;</span>

<span class="w">  </span><span class="nt">connection-pooling</span><span class="p">:</span>
<span class="w">    </span><span class="nt">max-connections</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">20</span>
<span class="w">    </span><span class="nt">connection-timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5000</span>
<span class="w">    </span><span class="nt">socket-timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30000</span>

<span class="w">  </span><span class="nt">retry-policy</span><span class="p">:</span>
<span class="w">    </span><span class="nt">max-attempts</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">    </span><span class="nt">backoff-multiplier</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
<span class="w">    </span><span class="nt">initial-delay</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>

<span class="w">  </span><span class="nt">circuit-breaker</span><span class="p">:</span>
<span class="w">    </span><span class="nt">failure-threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">    </span><span class="nt">recovery-timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">60000</span>
<span class="w">    </span><span class="nt">half-open-max-calls</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
</code></pre></div>
<h2 id="billing-and-support-services">Billing and Support Services<a class="headerlink" href="#billing-and-support-services" title="Permanent link">&para;</a></h2>
<h3 id="service-support-fee-api">Service Support Fee API<a class="headerlink" href="#service-support-fee-api" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># service-support-fee-api.yaml</span>
<span class="nn">---</span>
<span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;24392ee9-0e28-431c-85c3-06a013fc4089&quot;</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Service</span><span class="nv"> </span><span class="s">&amp;</span><span class="nv"> </span><span class="s">Support</span><span class="nv"> </span><span class="s">Fee</span><span class="nv"> </span><span class="s">API&quot;</span>
<span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;REST</span><span class="nv"> </span><span class="s">API</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Support</span><span class="nv"> </span><span class="s">Fees</span><span class="nv"> </span><span class="s">transactions&quot;</span>
<span class="nt">systemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INTEGRATION_SYSTEM&quot;</span>
<span class="nt">integrationSystemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;EXTERNAL&quot;</span>
<span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;HTTP&quot;</span>

<span class="nt">environments</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;ssf-dev&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Development&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://ssf-dv.paas-app-east-np.tsl.telus.com&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;DEVELOPMENT&quot;</span><span class="p p-Indicator">]</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;ssf-prod&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Production&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://ssf-pr.paas-app-east-pr.tsl.telus.com&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">20000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;PRODUCTION&quot;</span><span class="p p-Indicator">]</span>

<span class="nt">specifications</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-support-fee-api-1.0&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Service</span><span class="nv"> </span><span class="s">&amp;</span><span class="nv"> </span><span class="s">Support</span><span class="nv"> </span><span class="s">Fee</span><span class="nv"> </span><span class="s">API</span><span class="nv"> </span><span class="s">v1.0&quot;</span>
<span class="w">  </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.0&quot;</span>
<span class="w">  </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;OpenAPI&quot;</span>
<span class="w">  </span><span class="nt">source</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;source-Service-1.0/Service</span><span class="nv"> </span><span class="s">&amp;</span><span class="nv"> </span><span class="s">Support</span><span class="nv"> </span><span class="s">Fee</span><span class="nv"> </span><span class="s">API.json&quot;</span>

<span class="nt">operations</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;applySSF&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Apply</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Support</span><span class="nv"> </span><span class="s">Fee&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/ssf/v1/apply&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Apply</span><span class="nv"> </span><span class="s">service</span><span class="nv"> </span><span class="s">support</span><span class="nv"> </span><span class="s">fee</span><span class="nv"> </span><span class="s">to</span><span class="nv"> </span><span class="s">subscriber&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;creditSSF&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Credit</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Support</span><span class="nv"> </span><span class="s">Fee&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/ssf/v1/credit&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Credit</span><span class="nv"> </span><span class="s">service</span><span class="nv"> </span><span class="s">support</span><span class="nv"> </span><span class="s">fee</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">subscriber&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;getSSFHistory&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Get</span><span class="nv"> </span><span class="s">SSF</span><span class="nv"> </span><span class="s">History&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;GET&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/ssf/v1/history/{ban}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Retrieve</span><span class="nv"> </span><span class="s">SSF</span><span class="nv"> </span><span class="s">transaction</span><span class="nv"> </span><span class="s">history</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">BAN&quot;</span>
</code></pre></div>
<h3 id="billing-integration-service">Billing Integration Service<a class="headerlink" href="#billing-integration-service" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># billing-integration-service.yaml</span>
<span class="nn">---</span>
<span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;billing-integration-service&quot;</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Billing</span><span class="nv"> </span><span class="s">Integration</span><span class="nv"> </span><span class="s">Service&quot;</span>
<span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Integration</span><span class="nv"> </span><span class="s">with</span><span class="nv"> </span><span class="s">external</span><span class="nv"> </span><span class="s">billing</span><span class="nv"> </span><span class="s">systems&quot;</span>
<span class="nt">systemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INTEGRATION_SYSTEM&quot;</span>
<span class="nt">integrationSystemType</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;EXTERNAL&quot;</span>
<span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;HTTP&quot;</span>

<span class="nt">environments</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;billing-qa&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Billing</span><span class="nv"> </span><span class="s">QA&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://billing-qa.telus.internal&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">45000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;QA&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;BILLING&quot;</span><span class="p p-Indicator">]</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;billing-prod&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Billing</span><span class="nv"> </span><span class="s">Production&quot;</span>
<span class="w">  </span><span class="nt">address</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://billing.telus.internal&quot;</span>
<span class="w">  </span><span class="nt">connectTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3000</span>
<span class="w">  </span><span class="nt">readTimeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30000</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;PRODUCTION&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;BILLING&quot;</span><span class="p p-Indicator">]</span>

<span class="nt">operations</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;createSubscription&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">Billing</span><span class="nv"> </span><span class="s">Subscription&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/billing/v2/subscription&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">new</span><span class="nv"> </span><span class="s">billing</span><span class="nv"> </span><span class="s">subscription&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;updateSubscription&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Update</span><span class="nv"> </span><span class="s">Billing</span><span class="nv"> </span><span class="s">Subscription&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;PATCH&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/billing/v2/subscription/{id}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Update</span><span class="nv"> </span><span class="s">existing</span><span class="nv"> </span><span class="s">billing</span><span class="nv"> </span><span class="s">subscription&quot;</span>

<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;getBillingAccount&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Get</span><span class="nv"> </span><span class="s">Billing</span><span class="nv"> </span><span class="s">Account&quot;</span>
<span class="w">  </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;GET&quot;</span>
<span class="w">  </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/billing/v2/account/{ban}&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Retrieve</span><span class="nv"> </span><span class="s">billing</span><span class="nv"> </span><span class="s">account</span><span class="nv"> </span><span class="s">details&quot;</span>
</code></pre></div>
<h2 id="service-configuration-management">Service Configuration Management<a class="headerlink" href="#service-configuration-management" title="Permanent link">&para;</a></h2>
<h3 id="environment-specific-service-configuration">Environment-Specific Service Configuration<a class="headerlink" href="#environment-specific-service-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Environment configuration matrix</span>
<span class="nt">service-environments</span><span class="p">:</span>
<span class="w">  </span><span class="nt">development</span><span class="p">:</span>
<span class="w">    </span><span class="nt">timeout-multiplier</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2.0</span>
<span class="w">    </span><span class="nt">retry-attempts</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">    </span><span class="nt">circuit-breaker</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;disabled&quot;</span>
<span class="w">    </span><span class="nt">logging-level</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;DEBUG&quot;</span>
<span class="w">    </span><span class="nt">mock-external-services</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">  </span><span class="nt">qa</span><span class="p">:</span>
<span class="w">    </span><span class="nt">timeout-multiplier</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1.5</span>
<span class="w">    </span><span class="nt">retry-attempts</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">    </span><span class="nt">circuit-breaker</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;enabled&quot;</span>
<span class="w">    </span><span class="nt">logging-level</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INFO&quot;</span>
<span class="w">    </span><span class="nt">mock-external-services</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="w">  </span><span class="nt">staging</span><span class="p">:</span>
<span class="w">    </span><span class="nt">timeout-multiplier</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1.2</span>
<span class="w">    </span><span class="nt">retry-attempts</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">    </span><span class="nt">circuit-breaker</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;enabled&quot;</span>
<span class="w">    </span><span class="nt">logging-level</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INFO&quot;</span>
<span class="w">    </span><span class="nt">mock-external-services</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>

<span class="w">  </span><span class="nt">production</span><span class="p">:</span>
<span class="w">    </span><span class="nt">timeout-multiplier</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1.0</span>
<span class="w">    </span><span class="nt">retry-attempts</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
<span class="w">    </span><span class="nt">circuit-breaker</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;enabled&quot;</span>
<span class="w">    </span><span class="nt">logging-level</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;WARN&quot;</span>
<span class="w">    </span><span class="nt">mock-external-services</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span>
<span class="w">    </span><span class="nt">performance-monitoring</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;enabled&quot;</span>
</code></pre></div>
<h3 id="service-health-monitoring">Service Health Monitoring<a class="headerlink" href="#service-health-monitoring" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Service health check configuration</span>
<span class="nt">health-checks</span><span class="p">:</span>
<span class="w">  </span><span class="nt">tmf-quote-service</span><span class="p">:</span>
<span class="w">    </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/health&quot;</span>
<span class="w">    </span><span class="nt">interval</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30</span>
<span class="w">    </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">    </span><span class="nt">failure-threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>

<span class="w">  </span><span class="nt">sfdc-crm</span><span class="p">:</span>
<span class="w">    </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/services/data/v58.0/limits&quot;</span>
<span class="w">    </span><span class="nt">interval</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">60</span>
<span class="w">    </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">    </span><span class="nt">failure-threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>

<span class="w">  </span><span class="nt">billing-service</span><span class="p">:</span>
<span class="w">    </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/billing/v2/health&quot;</span>
<span class="w">    </span><span class="nt">interval</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">45</span>
<span class="w">    </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8</span>
<span class="w">    </span><span class="nt">failure-threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>

<span class="w">  </span><span class="nt">service-support-fee</span><span class="p">:</span>
<span class="w">    </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/ssf/v1/health&quot;</span>
<span class="w">    </span><span class="nt">interval</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30</span>
<span class="w">    </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">    </span><span class="nt">failure-threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
</code></pre></div>
<h2 id="api-specification-management">API Specification Management<a class="headerlink" href="#api-specification-management" title="Permanent link">&para;</a></h2>
<h3 id="openapi-specification-structure">OpenAPI Specification Structure<a class="headerlink" href="#openapi-specification-structure" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;swagger&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2.0&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;info&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;version&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;1.0&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;title&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Service &amp; Support Fee API&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;REST api which provides access to Service Support Fees transactions&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;contact&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;email&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;dl Red Panda&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;x-apiname&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;serviceAndSupportFees&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;x-tmfdomain&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;resource&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;x-cmdbid&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">14505</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;x-api-engagement-num&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;657&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;x-tps&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;x-responsetime&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1000</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;host&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ssf-dv.paas-app-east-np.tsl.telus.com&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;basePath&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/ssf/v1&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;consumes&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;application/json&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;produces&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;application/json&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;schemes&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;https&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;paths&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;/apply&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;post&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;summary&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Apply Service Support Fee&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Apply service support fee to a subscriber&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;operationId&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;applySSF&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;parameters&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">          </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;body&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;in&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;body&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;required&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;schema&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;$ref&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;#/definitions/SSFRequest&quot;</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">          </span><span class="p">}</span>
<span class="w">        </span><span class="p">],</span>
<span class="w">        </span><span class="nt">&quot;responses&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;200&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;SSF applied successfully&quot;</span><span class="p">,</span>
<span class="w">            </span><span class="nt">&quot;schema&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;$ref&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;#/definitions/SSFResponse&quot;</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">          </span><span class="p">},</span>
<span class="w">          </span><span class="nt">&quot;400&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Bad request&quot;</span>
<span class="w">          </span><span class="p">},</span>
<span class="w">          </span><span class="nt">&quot;500&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Internal server error&quot;</span>
<span class="w">          </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;definitions&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;SSFRequest&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;object&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;required&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;subscriber&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;vendor&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;amount&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;subscriber&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;$ref&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;#/definitions/Subscriber&quot;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;vendor&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;$ref&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;#/definitions/Vendor&quot;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;amount&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;number&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;format&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;double&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;SSF amount to apply&quot;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;Subscriber&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;object&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;required&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;billingAcctId&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;phoneNumberTxt&quot;</span><span class="p">],</span>
<span class="w">      </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;brandIdNum&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;integer&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;format&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;int32&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Brand identifier&quot;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;billingAcctId&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Billing account number (BAN)&quot;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;esn&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;USIM-Serial number&quot;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;phoneNumberTxt&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;pattern&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;\\d{10}&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;10-digit phone number&quot;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="api-version-management">API Version Management<a class="headerlink" href="#api-version-management" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># API version management strategy</span>
<span class="nt">api-versioning</span><span class="p">:</span>
<span class="w">  </span><span class="nt">strategy</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;semantic-versioning&quot;</span>

<span class="w">  </span><span class="nt">version-lifecycle</span><span class="p">:</span>
<span class="w">    </span><span class="nt">development</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;0.x.x&quot;</span>
<span class="w">    </span><span class="nt">beta</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.0.0-beta.x&quot;</span>
<span class="w">    </span><span class="nt">stable</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;1.x.x&quot;</span>
<span class="w">    </span><span class="nt">deprecated</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;marked</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">removal&quot;</span>

<span class="w">  </span><span class="nt">compatibility-matrix</span><span class="p">:</span>
<span class="w">    </span><span class="nt">tmf-quote-service</span><span class="p">:</span>
<span class="w">      </span><span class="nt">supported-versions</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;6.1&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;6.2&quot;</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="nt">default-version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;6.2&quot;</span>
<span class="w">      </span><span class="nt">deprecation-schedule</span><span class="p">:</span>
<span class="w">        </span><span class="s">&quot;6.1&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;2024-12-31&quot;</span>

<span class="w">    </span><span class="nt">sfdc-crm</span><span class="p">:</span>
<span class="w">      </span><span class="nt">supported-versions</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;57.0&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;58.0&quot;</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="nt">default-version</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;58.0&quot;</span>
<span class="w">      </span><span class="nt">deprecation-schedule</span><span class="p">:</span>
<span class="w">        </span><span class="s">&quot;57.0&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;2024-06-30&quot;</span>

<span class="w">  </span><span class="nt">migration-strategy</span><span class="p">:</span>
<span class="w">    </span><span class="nt">parallel-versions</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
<span class="w">    </span><span class="nt">migration-period</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;6</span><span class="nv"> </span><span class="s">months&quot;</span>
<span class="w">    </span><span class="nt">backward-compatibility</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;required&quot;</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../04-security-configuration/">Security Configuration →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>