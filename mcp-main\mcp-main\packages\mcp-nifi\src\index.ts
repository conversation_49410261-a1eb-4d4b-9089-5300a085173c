import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { z } from 'zod';
import { NiFiClient } from './nifi-client.js';

// Define JSON-RPC types
type JSONRPCMessage = {
  jsonrpc: '2.0';
  id?: string | number;
};

type JSONRPCRequest = JSONRPCMessage & {
  method: string;
  params?: unknown;
};

type JSONRPCSuccessResponse = JSONRPCMessage & {
  id: string | number;
  result: {
    [x: string]: unknown;
    _meta?: { [x: string]: unknown };
  };
};

type JSONRPCErrorResponse = JSONRPCMessage & {
  id: string | number;
  error: {
    code: number;
    message: string;
    data?: unknown;
  };
};

type JSONRPCResponse = JSONRPCSuccessResponse | JSONRPCErrorResponse;

// Define the expected structure for the transport.send method
type TransportSendMessage = {
  jsonrpc: '2.0';
  id: string | number;
} & ({
  result: {
    [x: string]: unknown;
    _meta?: { [x: string]: unknown };
  };
} | {
  error: {
    code: number;
    message: string;
    data?: unknown;
  };
});

// Import tool implementations
import * as flowTools from './tools/flow.js';
import * as systemTools from './tools/system.js';
import * as versionTools from './tools/version.js';
import * as processGroupTools from './tools/process-group.js';
import * as clusterTools from './tools/cluster.js';
import * as templateTools from './tools/template.js';

const nifiClient = new NiFiClient(
  process.env.NIFI_URL!,
  process.env.NIFI_USERNAME!,
  process.env.NIFI_PASSWORD!
);

type ToolHandler = (request: { method: string; params: { arguments: Record<string, unknown> } }) => Promise<any>;

interface Tool {
  schema: unknown;
  handler: ToolHandler;
}

const tools: { [key: string]: Tool } = {
  'get_flow': {
    schema: flowTools.tools.get_flow,
    handler: flowTools.getFlow(nifiClient)
  },
  'get_component_status': {
    schema: flowTools.tools.get_component_status,
    handler: flowTools.getComponentStatus(nifiClient)
  },
  'create_connection': {
    schema: flowTools.tools.create_connection,
    handler: flowTools.createConnection(nifiClient)
  },
  'get_system_diagnostics': {
    schema: systemTools.tools.get_system_diagnostics,
    handler: systemTools.getSystemDiagnostics(nifiClient)
  },
  'get_controller_config': {
    schema: systemTools.tools.get_controller_config,
    handler: systemTools.getControllerConfig(nifiClient)
  },
  'get_jmx_metrics': {
    schema: systemTools.tools.get_jmx_metrics,
    handler: systemTools.getJmxMetrics(nifiClient)
  },
  'manage_version': {
    schema: versionTools.tools.manage_version,
    handler: versionTools.manageVersion(nifiClient)
  },
  'manage_process_group': {
    schema: processGroupTools.tools.manage_process_group,
    handler: processGroupTools.manageProcessGroup(nifiClient)
  },
  'manage_cluster': {
    schema: clusterTools.tools.manage_cluster,
    handler: clusterTools.manageCluster(nifiClient)
  },
  'export_template': {
    schema: templateTools.tools.export_template,
    handler: templateTools.exportTemplate(nifiClient)
  }
};

const toolSchema = {
  get_flow: {
    type: 'tool',
    description: flowTools.tools.get_flow.description,
    inputSchema: flowTools.tools.get_flow.inputSchema
  },
  get_component_status: {
    type: 'tool',
    description: flowTools.tools.get_component_status.description,
    inputSchema: flowTools.tools.get_component_status.inputSchema
  },
  create_connection: {
    type: 'tool',
    description: flowTools.tools.create_connection.description,
    inputSchema: flowTools.tools.create_connection.inputSchema
  },
  get_system_diagnostics: {
    type: 'tool',
    description: systemTools.tools.get_system_diagnostics.description,
    inputSchema: systemTools.tools.get_system_diagnostics.inputSchema
  },
  get_controller_config: {
    type: 'tool',
    description: systemTools.tools.get_controller_config.description,
    inputSchema: systemTools.tools.get_controller_config.inputSchema
  },
  manage_version: {
    type: 'tool',
    description: versionTools.tools.manage_version.description,
    inputSchema: versionTools.tools.manage_version.inputSchema
  },
  manage_process_group: {
    type: 'tool',
    description: processGroupTools.tools.manage_process_group.description,
    inputSchema: processGroupTools.tools.manage_process_group.inputSchema
  },
  manage_cluster: {
    type: 'tool',
    description: clusterTools.tools.manage_cluster.description,
    inputSchema: clusterTools.tools.manage_cluster.inputSchema
  },
  get_jmx_metrics: {
    type: 'tool',
    description: systemTools.tools.get_jmx_metrics.description,
    inputSchema: systemTools.tools.get_jmx_metrics.inputSchema
  },
  export_template: {
    type: 'tool',
    description: templateTools.tools.export_template.description,
    inputSchema: templateTools.tools.export_template.inputSchema
  }
};

const server = new Server(
  {
    name: 'nifi',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: toolSchema
    }
  }
);

server.setRequestHandler(
  z.object({
    method: z.literal('tools/call'),
    jsonrpc: z.literal('2.0'),
    id: z.union([z.string(), z.number()]),
    params: z.object({
      name: z.string(),
      arguments: z.record(z.unknown()).optional()
    })
  }),
  async (request) => {
    try {
      const toolName = request.params.name;
      const tool = tools[toolName];
      if (!tool) {
        throw new Error(`Unknown tool: ${toolName}`);
      }

      const timeoutPromise = new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error('Request timed out')), 60000) // 60 second timeout
      );
      
      const resultPromise = tool.handler({
        method: toolName,
        params: {
          arguments: request.params.arguments || {}
        }
      });
      
      const result = await Promise.race([resultPromise, timeoutPromise]);
      
      return result;
    } catch (error) {
      console.error('Error handling request:', error);
      if (error instanceof Error) {
        console.error('Error stack:', error.stack);
      }
      throw error;
    }
  }
);

const transport = new StdioServerTransport();

// Set up message handling before connecting
transport.onmessage = (message: JSONRPCMessage) => {
  if ('method' in message && message.method === 'initialize' && message.id !== undefined) {
    const response: TransportSendMessage = {
      jsonrpc: '2.0',
      id: message.id,
      result: {
        capabilities: {
          tools: toolSchema
        }
      }
    };
    transport.send(response);
  }
};

server.connect(transport);


// Handle process termination and uncaught errors
process.on('SIGINT', () => process.exit(0));
process.on('uncaughtException', (error) => console.error('Uncaught exception:', error));
