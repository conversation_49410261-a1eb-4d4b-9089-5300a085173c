import json
import os
from collections import defaultdict

def get_json_file_path():
    """Get the JSON file path from user input if not found at default location"""
    default_path = r"C:\Users\<USER>\Documents\Cline\telus-bulk-operation-extension-b2b\bulk_address_upload.json"
    
    print(f"\nLooking for bulk_address_upload.json at: {default_path}")
    if os.path.exists(default_path):
        print("File found at default location.")
        return default_path
        
    print("\nERROR: Could not find bulk_address_upload.json at the default location.")
    print("Current working directory:", os.getcwd())
    print("Files in current directory:", os.listdir())
    
    # List contents of the expected parent directory
    expected_parent_dir = r"C:\Users\<USER>\Documents\Cline\telus-bulk-operation-extension-b2b"
    if os.path.exists(expected_parent_dir):
        print(f"\nContents of {expected_parent_dir}:")
        for item in os.listdir(expected_parent_dir):
            print(f"  {item}")
    else:
        print(f"\nThe directory {expected_parent_dir} does not exist.")
    
    print("\nPlease enter the full path to the bulk_address_upload.json file:")
    user_path = input("> ").strip().strip('"').strip("'")
    
    if not os.path.exists(user_path):
        raise FileNotFoundError(f"Could not find file at: {user_path}")
    
    if os.path.isdir(user_path):
        raise IsADirectoryError(f"The provided path is a directory, not a file: {user_path}")
    
    return user_path

def load_json_file(file_path):
    """Load and parse JSON file with error handling"""
    try:
        with open(file_path, 'r') as file:
            return json.load(file)
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format in the file: {str(e)}")
        raise
    except PermissionError:
        print(f"Error: Permission denied when trying to read the file: {file_path}")
        raise
    except Exception as e:
        print(f"Error reading file: {str(e)}")
        raise

def analyze_json_data(data):
    """Analyze the JSON data structure and content"""
    analysis = defaultdict(int)
    field_types = defaultdict(set)
    
    if isinstance(data, list):
        analysis['total_items'] = len(data)
        for item in data:
            if isinstance(item, dict):
                for key, value in item.items():
                    analysis[f'items_with_{key}'] += 1
                    field_types[key].add(type(value).__name__)
    else:
        analysis['structure'] = type(data).__name__
        if isinstance(data, dict):
            for key, value in data.items():
                field_types[key].add(type(value).__name__)
    
    # Convert field types to strings
    field_type_info = {k: list(v) for k, v in field_types.items()}
    
    return dict(analysis), field_type_info

def display_analysis(analysis, field_types):
    """Display the analysis results in a readable format"""
    print("\nAnalysis of bulk_address_upload.json:")
    print("-" * 40)
    
    print("\nStructure Statistics:")
    for key, value in analysis.items():
        print(f"{key}: {value}")
    
    print("\nField Types:")
    for field, types in field_types.items():
        print(f"{field}: {', '.join(types)}")

def write_readable_json(data, output_file):
    """Write JSON data in a readable format"""
    try:
        with open(output_file, 'w') as file:
            json.dump(data, file, indent=2)
        print(f"\nReadable version saved to: {output_file}")
    except Exception as e:
        print(f"Error writing readable version: {str(e)}")

def main():
    try:
        # Get and validate input file path
        input_file = get_json_file_path()
        print(f"\nReading file: {input_file}")
        
        # Load and analyze data
        data = load_json_file(input_file)
        analysis, field_types = analyze_json_data(data)
        
        # Display results
        display_analysis(analysis, field_types)
        
        # Save readable version
        output_file = "readable_bulk_address_upload.json"
        write_readable_json(data, output_file)
        
    except Exception as e:
        print(f"\nError: {str(e)}")
        print("\nPress Enter to exit...")
        input()
        return

    print("\nAnalysis complete! Press Enter to exit...")
    input()

if __name__ == "__main__":
    main()