# Changeset Workflow

This repository uses [changesets](https://github.com/changesets/changesets) to manage versions, handle package publishing, and generate changelogs.

## Adding Changes

When making changes to packages:

1. Make your changes to the package(s)
2. Run `pnpm changeset` to create a changeset
3. Follow the prompts to:
   - Select the packages you've changed
   - Specify the type of change (major, minor, patch)
   - Provide a description of the changes

The changeset will be created in the `.changeset` directory and should be committed with your changes.

## Automated Workflow

### On Pull Requests

The GitHub workflow will:
1. Check for the presence of a changeset if packages have been modified
2. Automatically create a changeset if one is missing
3. Validate package configurations
4. Ensure the lockfile is up to date

### On Merge to Main

When changes are merged to main:
1. If there are changesets present:
   - A new PR will be created to version the packages
   - When this PR is merged, packages will be published
2. If no changesets are present:
   - No action will be taken

## Manual Commands

- `pnpm changeset` - Create a new changeset
- `pnpm version-packages` - Bump versions and update changelogs
- `pnpm release` - Publish packages to the registry

## Configuration

Changesets is configured to:
- Generate changelogs
- Auto-commit changes
- Handle restricted access packages
- Manage internal dependencies
- Version private packages
- Skip tagging private packages

## Publishing

Packages are published to the GitHub registry under the @telus scope. The release workflow handles authentication and publishing automatically.
