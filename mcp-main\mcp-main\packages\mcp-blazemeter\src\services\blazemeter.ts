import axios, { AxiosInstance } from "axios";
import config from "../config/config.js";
import {
  GetWorkspaceArgs,
  GetWorkspaceTestsArgs,
  RunTestArgs,
  GetTestExecutionResultsSummaryArgs,
  ApiResponse,
  Workspace,
  WorkspaceTest,
  TestExecutionResultsSummary,
  GetTestDetailsArgs,
  GetTestExecutionStatusArgs,
  TestExecutionStatus,
  UpdateMultiTestArgs,
  StartMultiTestArgs,
  GetTestExecutionDetailsArgs,
  TestExecutionDetails,
  ScenarioResultSummary,
  GetMultiTestDetailsArgs,
  MultiTestDetails,
} from "../types/blazemeter.js";

class BlazeMeterService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: config.blazeMeterApiUrl,
      auth: {
        username: config.blazeMeterApiKey,
        password: config.blazeMeterApiSecret,
      },
    });
  }

  async getWorkspace(args: GetWorkspaceArgs): Promise<ApiResponse<Workspace>> {
    try {
      const response = await this.api.get(`/workspaces/${args.workspaceId}`);
      const workspace = response.data.result;
      return {
        success: true,
        message: "Successfully retrieved workspace",
        data: {
          id: workspace.id,
          name: workspace.name,
          created: workspace.created,
          updated: workspace.updated,
          enabled: workspace.enabled,
          owner: {
            name: workspace.owner.displayName,
            email: workspace.owner.email,
          },
          membersCount: workspace.membersCount,
          allowance: workspace.allowance,
          activeMember: {
            name: workspace.activeMember.displayName,
            email: workspace.activeMember.email,
            role: workspace.activeMember.roles[0],
          },
        },
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to get workspace: ${error.response?.data?.message || error.message}`,
        error,
      };
    }
  }

  async getWorkspaceTests(
    args: GetWorkspaceTestsArgs,
  ): Promise<ApiResponse<WorkspaceTest[]>> {
    try {
      let url = `/tests?workspaceId=${args.workspaceId}`;
      if (args.type) {
        url += `&type=${args.type}`;
      }
      const response = await this.api.get(url);
      const tests = response.data.result;
      return {
        success: true,
        message: "Successfully retrieved workspace tests",
        data: tests.map((test: any) => ({
          id: test.id,
          name: test.name,
          isNewTest: test.isNewTest,
          userId: test.userId,
          created: test.created,
          updated: test.updated,
          creatorClientId: test.creatorClientId,
          overrideExecutions: test.overrideExecutions || [],
          executions: test.executions || [],
          hasThreadGroupsToOverride: test.hasThreadGroupsToOverride,
          hasNonRegularThreadGroup: test.hasNonRegularThreadGroup,
          shouldSendReportEmail: test.shouldSendReportEmail,
          dependencies: test.dependencies || [],
          tags: test.tags || [],
          shouldRemoveJmeter: test.shouldRemoveJmeter,
          projectId: test.projectId,
          lastUpdatedById: test.lastUpdatedById,
          configuration: {
            type: test.configuration?.type,
            dedicatedIpsEnabled: test.configuration?.dedicatedIpsEnabled,
            canControlRampup: test.configuration?.canControlRampup,
            targetThreads: test.configuration?.targetThreads,
            executionType: test.configuration?.executionType,
            enableFailureCriteria: test.configuration?.enableFailureCriteria,
            enableMockServices: test.configuration?.enableMockServices,
            enableLoadConfiguration:
              test.configuration?.enableLoadConfiguration,
            scriptType: test.configuration?.scriptType,
            threads: test.configuration?.threads,
            filename: test.configuration?.filename,
            testMode: test.configuration?.testMode,
            extraSlots: test.configuration?.extraSlots,
            plugins: {
              jmeter: test.configuration?.plugins?.jmeter,
              thresholds: test.configuration?.plugins?.thresholds,
            },
          },
        })),
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to get workspace tests: ${error.response?.data?.message || error.message}`,
        error,
      };
    }
  }

  async runPerformanceTest(
    args: RunTestArgs,
  ): Promise<ApiResponse<{ execution_id: string }>> {
    try {
      const response = await this.api.post(`/tests/${args.test_id}/start`, {
        numberOfUsers: args.virtual_users,
        testDuration: args.duration * 60, // Convert minutes to seconds
      });

      return {
        success: true,
        message: "Successfully started test execution",
        data: {
          execution_id: response.data.result.id,
        },
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to start test: ${error.response?.data?.message || error.message}`,
        error,
      };
    }
  }

  async getTestExecutionResultsSummary(
    args: GetTestExecutionResultsSummaryArgs,
  ): Promise<ApiResponse<TestExecutionResultsSummary>> {
    try {
      const scenarios = args.scenarios || ["ALL"];
      const summaries: ScenarioResultSummary[] = [];
      let maxUsers = 0;

      for (const scenario of scenarios) {
        const url = `/masters/${args.test_execution_id}/reports/aggregatereport/data?scenarios[]=${scenario}`;
        const response = await this.api.get(url);

        console.log(
          `API Response for scenario ${scenario}:`,
          JSON.stringify(response.data, null, 2),
        );

        if (response.data.error !== null) {
          throw new Error(response.data.error || "Unknown error occurred");
        }

        const result = response.data.result;

        // Handle filtering based on the filter parameter
        let summaryItems = [];
        if (!args.filter || args.filter === "ALL") {
          // Default behavior - only get 'ALL' summary
          const allSummary = result.find(
            (item: any) => item.labelName === "ALL",
          );
          if (!allSummary) {
            console.warn(`No summary data found for scenario ${scenario}`);
            continue;
          }
          summaryItems = [allSummary];
        } else if (args.filter === "FULL") {
          // Get all summaries
          summaryItems = result;
        } else {
          // Get specific label summary
          const specificSummary = result.find(
            (item: any) => item.labelName === args.filter,
          );
          if (!specificSummary) {
            console.warn(
              `No summary data found for label ${args.filter} in scenario ${scenario}`,
            );
            continue;
          }
          summaryItems = [specificSummary];
        }

        // Process each summary item
        for (const summaryItem of summaryItems) {
          const summary: ScenarioResultSummary = {
            scenarioId: scenario,
            scenarioName: summaryItem.labelName,
            summary: {
              avg: summaryItem.avgResponseTime,
              bytes: summaryItem.avgBytes * summaryItem.samples, // Total bytes
              concurrency: summaryItem.concurrency,
              duration: summaryItem.duration,
              failed: summaryItem.errorsCount,
              hits: summaryItem.samples,
              tp90: summaryItem["90line"],
              tp95: summaryItem["95line"],
              tp99: summaryItem["99line"],
              hits_avg: summaryItem.avgThroughput * 60, // Convert to hits per minute
              size_avg: summaryItem.avgBytes,
              latency: summaryItem.avgLatency,
              std: summaryItem.stDev,
              min: summaryItem.minResponseTime,
              max: summaryItem.maxResponseTime,
              first: 0, // Not provided in this API response
              last: 0, // Not provided in this API response
              histogram: { mean: summaryItem.medianResponseTime },
            },
          };

          summaries.push(summary);
          maxUsers = Math.max(maxUsers, summaryItem.concurrency);
        }
      }

      if (summaries.length === 0) {
        throw new Error("No summary data found for any scenario");
      }

      return {
        success: true,
        message: "Successfully retrieved test results",
        data: {
          summaries,
          maxUsers,
        },
      };
    } catch (error: any) {
      console.error("Error in getTestExecutionResultsSummary:", error);
      return {
        success: false,
        message: `Failed to get test results: ${error.message}`,
        error: error.stack,
      };
    }
  }

  async getTestDetails(
    args: GetTestDetailsArgs,
  ): Promise<ApiResponse<WorkspaceTest>> {
    try {
      const response = await this.api.get(`/tests/${args.test_id}`);
      const test = response.data.result;
      return {
        success: true,
        message: "Successfully retrieved test details",
        data: {
          id: test.id,
          name: test.name,
          isNewTest: test.isNewTest,
          userId: test.userId,
          created: test.created,
          updated: test.updated,
          creatorClientId: test.creatorClientId,
          overrideExecutions: test.overrideExecutions || [],
          executions: test.executions || [],
          hasThreadGroupsToOverride: test.hasThreadGroupsToOverride,
          hasNonRegularThreadGroup: test.hasNonRegularThreadGroup,
          shouldSendReportEmail: test.shouldSendReportEmail,
          dependencies: test.dependencies || [],
          tags: test.tags || [],
          shouldRemoveJmeter: test.shouldRemoveJmeter,
          projectId: test.projectId,
          lastUpdatedById: test.lastUpdatedById,
          configuration: {
            type: test.configuration?.type,
            dedicatedIpsEnabled: test.configuration?.dedicatedIpsEnabled,
            canControlRampup: test.configuration?.canControlRampup,
            targetThreads: test.configuration?.targetThreads,
            executionType: test.configuration?.executionType,
            enableFailureCriteria: test.configuration?.enableFailureCriteria,
            enableMockServices: test.configuration?.enableMockServices,
            enableLoadConfiguration:
              test.configuration?.enableLoadConfiguration,
            scriptType: test.configuration?.scriptType,
            threads: test.configuration?.threads,
            filename: test.configuration?.filename,
            testMode: test.configuration?.testMode,
            extraSlots: test.configuration?.extraSlots,
            plugins: {
              jmeter: test.configuration?.plugins?.jmeter,
              thresholds: test.configuration?.plugins?.thresholds,
            },
          },
        },
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to get test details: ${error.response?.data?.message || error.message}`,
        error,
      };
    }
  }

  async getTestExecutionStatus(
    args: GetTestExecutionStatusArgs,
  ): Promise<ApiResponse<TestExecutionStatus>> {
    try {
      const level = args.level || "INFO";
      const response = await this.api.get(
        `/masters/${args.test_execution_id}/status`,
      );
      const result = response.data.result;

      const testExecutionStatus: TestExecutionStatus = {
        id: args.test_execution_id,
        status: result.status || result.masterStatus?.status,
        progress: result.progress || result.masterStatus?.progress,
        currentDuration:
          result.currentDuration || result.masterStatus?.currentDuration,
        error: result.error || result.masterStatus?.error,
      };

      return {
        success: true,
        message: "Successfully retrieved test execution status",
        data: testExecutionStatus,
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to get test execution status: ${error.response?.data?.message || error.message}`,
        error,
      };
    }
  }

  async updateMultiTest(args: UpdateMultiTestArgs): Promise<ApiResponse<any>> {
    try {
      const response = await this.api.put(
        `/collections/${args.collectionId}/override-tests-executions`,
        {
          index: args.index,
          overrideExecutions: args.overrideExecutions,
        },
      );

      return {
        success: true,
        message: "Successfully updated multi-test configuration",
        data: response.data.result,
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to update multi-test: ${error.response?.data?.message || error.message}`,
        error,
      };
    }
  }

  async startMultiTest(
    args: StartMultiTestArgs,
  ): Promise<ApiResponse<{ execution_id: string }>> {
    try {
      const url = `/multi-tests/${args.collectionId}/start${args.delayedStart ? "?delayedStart=true" : ""}`;
      const response = await this.api.post(url);

      return {
        success: true,
        message: "Successfully started multi-test",
        data: {
          execution_id: response.data.result.id,
        },
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to start multi-test: ${error.response?.data?.message || error.message}`,
        error,
      };
    }
  }

  async getMultiTestDetails(
    args: GetMultiTestDetailsArgs,
  ): Promise<ApiResponse<MultiTestDetails>> {
    try {
      const url = `/multi-tests/${args.collectionId}${args.populateTests !== false ? "?populateTests=true" : ""}`;
      const response = await this.api.get(url);
      const result = response.data.result;

      const multiTestDetails: MultiTestDetails = {
        id: result.id,
        name: result.name,
        description: result.description,
        collectionType: result.collectionType,
        userId: result.userId,
        items: result.items || [],
        testsForExecutions: result.testsForExecutions,
        created: result.created,
        updated: result.updated,
        projectId: result.projectId,
        shouldSendReportEmail: result.shouldSendReportEmail,
        tests: result.tests,
      };

      return {
        success: true,
        message: "Successfully retrieved multi-test details",
        data: multiTestDetails,
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to get multi-test details: ${error.response?.data?.message || error.message}`,
        error,
      };
    }
  }

  async getTestExecutionDetails(
    args: GetTestExecutionDetailsArgs,
  ): Promise<ApiResponse<TestExecutionDetails>> {
    try {
      const url = `/masters/${args.masterId}`;
      const response = await this.api.get(url);
      const result = response.data.result;

      const testDetails: TestExecutionDetails = {
        id: result.id,
        name: result.name,
        created: result.created,
        ended: result.ended,
        maxUsers: result.maxUsers,
        scenariosMapping: result.scenariosMapping,
        executions: result.executions.map((execution: any) => ({
          concurrency: execution.concurrency,
          holdFor: execution.holdFor,
          executor: execution.executor,
          scenario: execution.scenario,
        })),
      };

      return {
        success: true,
        message: "Successfully retrieved test execution details",
        data: testDetails,
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Failed to get test execution details: ${error.response?.data?.message || error.message}`,
        error,
      };
    }
  }
}

export const blazeMeterService = new BlazeMeterService();
