
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/backend/04-integration-layer/">
      
      
        <link rel="prev" href="../03-service-layer/">
      
      
        <link rel="next" href="../05-development-guide/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Integration Layer - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#integration-layer" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Integration Layer
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" checked>
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#integration-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Overview
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Patterns
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#order-capture-product-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Order Capture Product Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Order Capture Product Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#order-capture-client-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Order Capture Client Implementation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#order-capture-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Order Capture Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bss-system-integration" class="md-nav__link">
    <span class="md-ellipsis">
      BSS System Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="BSS System Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#bss-integration-client" class="md-nav__link">
    <span class="md-ellipsis">
      BSS Integration Client
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#external-api-integration" class="md-nav__link">
    <span class="md-ellipsis">
      External API Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="External API Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#cip-platform-integration" class="md-nav__link">
    <span class="md-ellipsis">
      CIP Platform Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#event-driven-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Event-Driven Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Event-Driven Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#event-publishing" class="md-nav__link">
    <span class="md-ellipsis">
      Event Publishing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#event-handlers" class="md-nav__link">
    <span class="md-ellipsis">
      Event Handlers
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-handling-and-resilience" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling and Resilience
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Handling and Resilience">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#resilience-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Resilience Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-exception-handling" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Exception Handling
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#integration-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Overview
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Patterns
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#order-capture-product-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Order Capture Product Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Order Capture Product Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#order-capture-client-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Order Capture Client Implementation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#order-capture-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Order Capture Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bss-system-integration" class="md-nav__link">
    <span class="md-ellipsis">
      BSS System Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="BSS System Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#bss-integration-client" class="md-nav__link">
    <span class="md-ellipsis">
      BSS Integration Client
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#external-api-integration" class="md-nav__link">
    <span class="md-ellipsis">
      External API Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="External API Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#cip-platform-integration" class="md-nav__link">
    <span class="md-ellipsis">
      CIP Platform Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#event-driven-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Event-Driven Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Event-Driven Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#event-publishing" class="md-nav__link">
    <span class="md-ellipsis">
      Event Publishing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#event-handlers" class="md-nav__link">
    <span class="md-ellipsis">
      Event Handlers
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-handling-and-resilience" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling and Resilience
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Handling and Resilience">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#resilience-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Resilience Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-exception-handling" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Exception Handling
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="integration-layer">Integration Layer<a class="headerlink" href="#integration-layer" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#integration-architecture">Integration Architecture</a></li>
<li><a href="#order-capture-product-integration">Order Capture Product Integration</a></li>
<li><a href="#bss-system-integration">BSS System Integration</a></li>
<li><a href="#external-api-integration">External API Integration</a></li>
<li><a href="#event-driven-architecture">Event-Driven Architecture</a></li>
<li><a href="#error-handling-and-resilience">Error Handling and Resilience</a></li>
</ul>
<h2 id="integration-architecture">Integration Architecture<a class="headerlink" href="#integration-architecture" title="Permanent link">&para;</a></h2>
<h3 id="integration-overview">Integration Overview<a class="headerlink" href="#integration-overview" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Backend Application&quot;
        A[Service Layer]
        B[Integration Layer]
        C[Event Publisher]
    end

    subgraph &quot;Integration Clients&quot;
        D[Order Capture Client]
        E[BSS Integration Client]
        F[External API Client]
        G[Event Handler]
    end

    subgraph &quot;External Systems&quot;
        H[Order Capture Product]
        I[TELUS BSS Systems]
        J[CIP Platform]
        K[Third-Party APIs]
        L[Message Broker]
    end

    subgraph &quot;Resilience Patterns&quot;
        M[Circuit Breaker]
        N[Retry Logic]
        O[Bulkhead]
        P[Timeout]
    end

    A --&gt; B
    B --&gt; D
    B --&gt; E
    B --&gt; F
    B --&gt; G

    D --&gt; H
    E --&gt; I
    F --&gt; J
    F --&gt; K
    G --&gt; L

    C --&gt; L

    D --&gt; M
    E --&gt; N
    F --&gt; O
    G --&gt; P

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style H fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="integration-patterns">Integration Patterns<a class="headerlink" href="#integration-patterns" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Pattern</th>
<th>Purpose</th>
<th>Implementation</th>
<th>Use Cases</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Client Adapter</strong></td>
<td>System integration</td>
<td>Spring HTTP clients</td>
<td>Order Capture, BSS APIs</td>
</tr>
<tr>
<td><strong>Circuit Breaker</strong></td>
<td>Fault tolerance</td>
<td>Resilience4j</td>
<td>External system failures</td>
</tr>
<tr>
<td><strong>Retry</strong></td>
<td>Transient failures</td>
<td>Spring Retry</td>
<td>Network timeouts</td>
</tr>
<tr>
<td><strong>Event Publishing</strong></td>
<td>Async communication</td>
<td>Spring Events</td>
<td>Business events</td>
</tr>
<tr>
<td><strong>Bulkhead</strong></td>
<td>Resource isolation</td>
<td>Thread pools</td>
<td>System isolation</td>
</tr>
</tbody>
</table>
<h2 id="order-capture-product-integration">Order Capture Product Integration<a class="headerlink" href="#order-capture-product-integration" title="Permanent link">&para;</a></h2>
<h3 id="order-capture-client-implementation">Order Capture Client Implementation<a class="headerlink" href="#order-capture-client-implementation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">OrderCaptureClient</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">WebClient</span><span class="w"> </span><span class="n">webClient</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">OrderCaptureProperties</span><span class="w"> </span><span class="n">properties</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">MappingService</span><span class="w"> </span><span class="n">mappingService</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">CircuitBreaker</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">RetryTemplate</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">OrderCaptureClient</span><span class="p">(</span>
<span class="w">            </span><span class="n">WebClient</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">webClientBuilder</span><span class="p">,</span>
<span class="w">            </span><span class="n">OrderCaptureProperties</span><span class="w"> </span><span class="n">properties</span><span class="p">,</span>
<span class="w">            </span><span class="n">MappingService</span><span class="w"> </span><span class="n">mappingService</span><span class="p">,</span>
<span class="w">            </span><span class="n">CircuitBreakerFactory</span><span class="w"> </span><span class="n">circuitBreakerFactory</span><span class="p">,</span>
<span class="w">            </span><span class="n">RetryTemplate</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">webClient</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">webClientBuilder</span>
<span class="w">            </span><span class="p">.</span><span class="na">baseUrl</span><span class="p">(</span><span class="n">properties</span><span class="p">.</span><span class="na">getBaseUrl</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">defaultHeader</span><span class="p">(</span><span class="n">HttpHeaders</span><span class="p">.</span><span class="na">CONTENT_TYPE</span><span class="p">,</span><span class="w"> </span><span class="n">MediaType</span><span class="p">.</span><span class="na">APPLICATION_JSON_VALUE</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">defaultHeader</span><span class="p">(</span><span class="n">HttpHeaders</span><span class="p">.</span><span class="na">ACCEPT</span><span class="p">,</span><span class="w"> </span><span class="n">MediaType</span><span class="p">.</span><span class="na">APPLICATION_JSON_VALUE</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">codecs</span><span class="p">(</span><span class="n">configurer</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">configurer</span><span class="p">.</span><span class="na">defaultCodecs</span><span class="p">().</span><span class="na">maxInMemorySize</span><span class="p">(</span><span class="mi">10</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">1024</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">1024</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">properties</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">properties</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">mappingService</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">circuitBreaker</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">circuitBreakerFactory</span><span class="p">.</span><span class="na">create</span><span class="p">(</span><span class="s">&quot;order-capture&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">retryTemplate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">getQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Fetching quote from Order Capture: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">.</span><span class="na">executeSupplier</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">.</span><span class="na">execute</span><span class="p">(</span><span class="n">context</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">OrderCaptureQuote</span><span class="w"> </span><span class="n">ocQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">webClient</span>
<span class="w">                        </span><span class="p">.</span><span class="na">get</span><span class="p">()</span>
<span class="w">                        </span><span class="p">.</span><span class="na">uri</span><span class="p">(</span><span class="s">&quot;/quotes/{quoteId}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">)</span>
<span class="w">                        </span><span class="p">.</span><span class="na">headers</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">addAuthHeaders</span><span class="p">)</span>
<span class="w">                        </span><span class="p">.</span><span class="na">retrieve</span><span class="p">()</span>
<span class="w">                        </span><span class="p">.</span><span class="na">onStatus</span><span class="p">(</span><span class="n">HttpStatusCode</span><span class="p">::</span><span class="n">is4xxClientError</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">handle4xxError</span><span class="p">)</span>
<span class="w">                        </span><span class="p">.</span><span class="na">onStatus</span><span class="p">(</span><span class="n">HttpStatusCode</span><span class="p">::</span><span class="n">is5xxServerError</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">handle5xxError</span><span class="p">)</span>
<span class="w">                        </span><span class="p">.</span><span class="na">bodyToMono</span><span class="p">(</span><span class="n">OrderCaptureQuote</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">                        </span><span class="p">.</span><span class="na">timeout</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="n">properties</span><span class="p">.</span><span class="na">getTimeout</span><span class="p">()))</span>
<span class="w">                        </span><span class="p">.</span><span class="na">block</span><span class="p">();</span>

<span class="w">                    </span><span class="k">return</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">fromOrderCapture</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">);</span>

<span class="w">                </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">log</span><span class="p">.</span><span class="na">warn</span><span class="p">(</span><span class="s">&quot;Attempt {} failed for quote {}: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                            </span><span class="n">context</span><span class="p">.</span><span class="na">getRetryCount</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">                    </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">OrderCaptureException</span><span class="p">(</span><span class="s">&quot;Failed to fetch quote&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">                </span><span class="p">}</span>
<span class="w">            </span><span class="p">});</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">createQuote</span><span class="p">(</span><span class="n">CreateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Creating quote in Order Capture for customer: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">request</span><span class="p">.</span><span class="na">getCustomerId</span><span class="p">());</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">.</span><span class="na">executeSupplier</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">retryTemplate</span><span class="p">.</span><span class="na">execute</span><span class="p">(</span><span class="n">context</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">OrderCaptureQuoteRequest</span><span class="w"> </span><span class="n">ocRequest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">toOrderCaptureRequest</span><span class="p">(</span><span class="n">request</span><span class="p">);</span>

<span class="w">                    </span><span class="n">OrderCaptureQuote</span><span class="w"> </span><span class="n">ocQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">webClient</span>
<span class="w">                        </span><span class="p">.</span><span class="na">post</span><span class="p">()</span>
<span class="w">                        </span><span class="p">.</span><span class="na">uri</span><span class="p">(</span><span class="s">&quot;/quotes&quot;</span><span class="p">)</span>
<span class="w">                        </span><span class="p">.</span><span class="na">headers</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">addAuthHeaders</span><span class="p">)</span>
<span class="w">                        </span><span class="p">.</span><span class="na">bodyValue</span><span class="p">(</span><span class="n">ocRequest</span><span class="p">)</span>
<span class="w">                        </span><span class="p">.</span><span class="na">retrieve</span><span class="p">()</span>
<span class="w">                        </span><span class="p">.</span><span class="na">onStatus</span><span class="p">(</span><span class="n">HttpStatusCode</span><span class="p">::</span><span class="n">is4xxClientError</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">handle4xxError</span><span class="p">)</span>
<span class="w">                        </span><span class="p">.</span><span class="na">onStatus</span><span class="p">(</span><span class="n">HttpStatusCode</span><span class="p">::</span><span class="n">is5xxServerError</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">handle5xxError</span><span class="p">)</span>
<span class="w">                        </span><span class="p">.</span><span class="na">bodyToMono</span><span class="p">(</span><span class="n">OrderCaptureQuote</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">                        </span><span class="p">.</span><span class="na">timeout</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="n">properties</span><span class="p">.</span><span class="na">getTimeout</span><span class="p">()))</span>
<span class="w">                        </span><span class="p">.</span><span class="na">block</span><span class="p">();</span>

<span class="w">                    </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">fromOrderCapture</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">);</span>
<span class="w">                    </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Quote created successfully in Order Capture: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">                    </span><span class="k">return</span><span class="w"> </span><span class="n">response</span><span class="p">;</span>

<span class="w">                </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to create quote in Order Capture: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">                    </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">OrderCaptureException</span><span class="p">(</span><span class="s">&quot;Failed to create quote&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">                </span><span class="p">}</span>
<span class="w">            </span><span class="p">});</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">updateQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">UpdateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Updating quote in Order Capture: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">.</span><span class="na">executeSupplier</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">OrderCaptureQuoteRequest</span><span class="w"> </span><span class="n">ocRequest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">toOrderCaptureUpdateRequest</span><span class="p">(</span><span class="n">request</span><span class="p">);</span>

<span class="w">            </span><span class="n">OrderCaptureQuote</span><span class="w"> </span><span class="n">ocQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">webClient</span>
<span class="w">                </span><span class="p">.</span><span class="na">put</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">uri</span><span class="p">(</span><span class="s">&quot;/quotes/{quoteId}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">headers</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">addAuthHeaders</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">bodyValue</span><span class="p">(</span><span class="n">ocRequest</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">retrieve</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">onStatus</span><span class="p">(</span><span class="n">HttpStatusCode</span><span class="p">::</span><span class="n">is4xxClientError</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">handle4xxError</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">onStatus</span><span class="p">(</span><span class="n">HttpStatusCode</span><span class="p">::</span><span class="n">is5xxServerError</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">handle5xxError</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">bodyToMono</span><span class="p">(</span><span class="n">OrderCaptureQuote</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">timeout</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="n">properties</span><span class="p">.</span><span class="na">getTimeout</span><span class="p">()))</span>
<span class="w">                </span><span class="p">.</span><span class="na">block</span><span class="p">();</span>

<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">fromOrderCapture</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">);</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">approveQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteApprovalRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Approving quote in Order Capture: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">.</span><span class="na">executeSupplier</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">OrderCaptureApprovalRequest</span><span class="w"> </span><span class="n">ocRequest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">toOrderCaptureApprovalRequest</span><span class="p">(</span><span class="n">request</span><span class="p">);</span>

<span class="w">            </span><span class="n">OrderCaptureQuote</span><span class="w"> </span><span class="n">ocQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">webClient</span>
<span class="w">                </span><span class="p">.</span><span class="na">post</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">uri</span><span class="p">(</span><span class="s">&quot;/quotes/{quoteId}/approve&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">headers</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">addAuthHeaders</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">bodyValue</span><span class="p">(</span><span class="n">ocRequest</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">retrieve</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">onStatus</span><span class="p">(</span><span class="n">HttpStatusCode</span><span class="p">::</span><span class="n">is4xxClientError</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">handle4xxError</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">onStatus</span><span class="p">(</span><span class="n">HttpStatusCode</span><span class="p">::</span><span class="n">is5xxServerError</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">handle5xxError</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">bodyToMono</span><span class="p">(</span><span class="n">OrderCaptureQuote</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">timeout</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="n">properties</span><span class="p">.</span><span class="na">getTimeout</span><span class="p">()))</span>
<span class="w">                </span><span class="p">.</span><span class="na">block</span><span class="p">();</span>

<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">fromOrderCapture</span><span class="p">(</span><span class="n">ocQuote</span><span class="p">);</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">addAuthHeaders</span><span class="p">(</span><span class="n">HttpHeaders</span><span class="w"> </span><span class="n">headers</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">headers</span><span class="p">.</span><span class="na">setBearerAuth</span><span class="p">(</span><span class="n">getAccessToken</span><span class="p">());</span>
<span class="w">        </span><span class="n">headers</span><span class="p">.</span><span class="na">set</span><span class="p">(</span><span class="s">&quot;X-Client-ID&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">properties</span><span class="p">.</span><span class="na">getClientId</span><span class="p">());</span>
<span class="w">        </span><span class="n">headers</span><span class="p">.</span><span class="na">set</span><span class="p">(</span><span class="s">&quot;X-Request-ID&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">generateRequestId</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Mono</span><span class="o">&lt;?</span><span class="w"> </span><span class="kd">extends</span><span class="w"> </span><span class="n">Throwable</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">handle4xxError</span><span class="p">(</span><span class="n">ClientResponse</span><span class="w"> </span><span class="n">response</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">bodyToMono</span><span class="p">(</span><span class="n">String</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">flatMap</span><span class="p">(</span><span class="n">body</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Order Capture 4xx error: {} - {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">statusCode</span><span class="p">(),</span><span class="w"> </span><span class="n">body</span><span class="p">);</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="n">Mono</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">OrderCaptureClientException</span><span class="p">(</span>
<span class="w">                    </span><span class="s">&quot;Client error: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">statusCode</span><span class="p">(),</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">statusCode</span><span class="p">().</span><span class="na">value</span><span class="p">()));</span>
<span class="w">            </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Mono</span><span class="o">&lt;?</span><span class="w"> </span><span class="kd">extends</span><span class="w"> </span><span class="n">Throwable</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">handle5xxError</span><span class="p">(</span><span class="n">ClientResponse</span><span class="w"> </span><span class="n">response</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">bodyToMono</span><span class="p">(</span><span class="n">String</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">flatMap</span><span class="p">(</span><span class="n">body</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Order Capture 5xx error: {} - {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">statusCode</span><span class="p">(),</span><span class="w"> </span><span class="n">body</span><span class="p">);</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="n">Mono</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="k">new</span><span class="w"> </span><span class="n">OrderCaptureServerException</span><span class="p">(</span>
<span class="w">                    </span><span class="s">&quot;Server error: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">statusCode</span><span class="p">(),</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">statusCode</span><span class="p">().</span><span class="na">value</span><span class="p">()));</span>
<span class="w">            </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="order-capture-configuration">Order Capture Configuration<a class="headerlink" href="#order-capture-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Configuration</span>
<span class="nd">@EnableConfigurationProperties</span><span class="p">(</span><span class="n">OrderCaptureProperties</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">OrderCaptureConfig</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="nd">@ConditionalOnMissingBean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">WebClient</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="nf">orderCaptureWebClientBuilder</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">WebClient</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">filter</span><span class="p">(</span><span class="n">logRequest</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">filter</span><span class="p">(</span><span class="n">logResponse</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">filter</span><span class="p">(</span><span class="n">addCorrelationId</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">CircuitBreakerFactory</span><span class="w"> </span><span class="nf">orderCaptureCircuitBreakerFactory</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">CircuitBreakerFactory</span><span class="p">.</span><span class="na">create</span><span class="p">(</span>
<span class="w">            </span><span class="n">CircuitBreakerConfig</span><span class="p">.</span><span class="na">custom</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">failureRateThreshold</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">waitDurationInOpenState</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="mi">30</span><span class="p">))</span>
<span class="w">                </span><span class="p">.</span><span class="na">slidingWindowSize</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">minimumNumberOfCalls</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">()</span>
<span class="w">        </span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">RetryTemplate</span><span class="w"> </span><span class="nf">orderCaptureRetryTemplate</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">RetryTemplate</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">maxAttempts</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">exponentialBackoff</span><span class="p">(</span><span class="mi">1000</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">10000</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">retryOn</span><span class="p">(</span><span class="n">OrderCaptureException</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">ExchangeFilterFunction</span><span class="w"> </span><span class="nf">logRequest</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ExchangeFilterFunction</span><span class="p">.</span><span class="na">ofRequestProcessor</span><span class="p">(</span><span class="n">clientRequest</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Request: {} {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">clientRequest</span><span class="p">.</span><span class="na">method</span><span class="p">(),</span><span class="w"> </span><span class="n">clientRequest</span><span class="p">.</span><span class="na">url</span><span class="p">());</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">Mono</span><span class="p">.</span><span class="na">just</span><span class="p">(</span><span class="n">clientRequest</span><span class="p">);</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">ExchangeFilterFunction</span><span class="w"> </span><span class="nf">logResponse</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ExchangeFilterFunction</span><span class="p">.</span><span class="na">ofResponseProcessor</span><span class="p">(</span><span class="n">clientResponse</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Response: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">clientResponse</span><span class="p">.</span><span class="na">statusCode</span><span class="p">());</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">Mono</span><span class="p">.</span><span class="na">just</span><span class="p">(</span><span class="n">clientResponse</span><span class="p">);</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="bss-system-integration">BSS System Integration<a class="headerlink" href="#bss-system-integration" title="Permanent link">&para;</a></h2>
<h3 id="bss-integration-client">BSS Integration Client<a class="headerlink" href="#bss-integration-client" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">BssIntegrationClient</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">RestTemplate</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">BssProperties</span><span class="w"> </span><span class="n">bssProperties</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">CircuitBreaker</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">BulkheadRegistry</span><span class="w"> </span><span class="n">bulkheadRegistry</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">BssIntegrationClient</span><span class="p">(</span>
<span class="w">            </span><span class="n">RestTemplate</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">,</span>
<span class="w">            </span><span class="n">BssProperties</span><span class="w"> </span><span class="n">bssProperties</span><span class="p">,</span>
<span class="w">            </span><span class="n">CircuitBreakerFactory</span><span class="w"> </span><span class="n">circuitBreakerFactory</span><span class="p">,</span>
<span class="w">            </span><span class="n">BulkheadRegistry</span><span class="w"> </span><span class="n">bulkheadRegistry</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">restTemplate</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">bssProperties</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bssProperties</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">circuitBreaker</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">circuitBreakerFactory</span><span class="p">.</span><span class="na">create</span><span class="p">(</span><span class="s">&quot;bss-integration&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">bulkheadRegistry</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bulkheadRegistry</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">CustomerInfo</span><span class="w"> </span><span class="nf">getCustomerInfo</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">customerId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Fetching customer info from BSS: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">customerId</span><span class="p">);</span>

<span class="w">        </span><span class="n">Bulkhead</span><span class="w"> </span><span class="n">bulkhead</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bulkheadRegistry</span><span class="p">.</span><span class="na">bulkhead</span><span class="p">(</span><span class="s">&quot;bss-customer&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">bulkhead</span><span class="p">.</span><span class="na">executeSupplier</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">.</span><span class="na">executeSupplier</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">String</span><span class="w"> </span><span class="n">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bssProperties</span><span class="p">.</span><span class="na">getBaseUrl</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;/customers/{customerId}&quot;</span><span class="p">;</span>

<span class="w">                    </span><span class="n">HttpHeaders</span><span class="w"> </span><span class="n">headers</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createBssHeaders</span><span class="p">();</span>
<span class="w">                    </span><span class="n">HttpEntity</span><span class="o">&lt;</span><span class="n">Void</span><span class="o">&gt;</span><span class="w"> </span><span class="n">entity</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HttpEntity</span><span class="o">&lt;&gt;</span><span class="p">(</span><span class="n">headers</span><span class="p">);</span>

<span class="w">                    </span><span class="n">ResponseEntity</span><span class="o">&lt;</span><span class="n">BssCustomerResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="n">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">.</span><span class="na">exchange</span><span class="p">(</span>
<span class="w">                        </span><span class="n">url</span><span class="p">,</span><span class="w"> </span><span class="n">HttpMethod</span><span class="p">.</span><span class="na">GET</span><span class="p">,</span><span class="w"> </span><span class="n">entity</span><span class="p">,</span><span class="w"> </span><span class="n">BssCustomerResponse</span><span class="p">.</span><span class="na">class</span><span class="p">,</span><span class="w"> </span><span class="n">customerId</span><span class="p">);</span>

<span class="w">                    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getStatusCode</span><span class="p">().</span><span class="na">is2xxSuccessful</span><span class="p">()</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                        </span><span class="k">return</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">fromBssCustomer</span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">());</span>
<span class="w">                    </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">                        </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BssIntegrationException</span><span class="p">(</span><span class="s">&quot;Invalid response from BSS customer service&quot;</span><span class="p">);</span>
<span class="w">                    </span><span class="p">}</span>

<span class="w">                </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">RestClientException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;BSS customer service error for {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">customerId</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">                    </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BssIntegrationException</span><span class="p">(</span><span class="s">&quot;Failed to fetch customer info&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">                </span><span class="p">}</span>
<span class="w">            </span><span class="p">});</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">OrderResponse</span><span class="w"> </span><span class="nf">createOrder</span><span class="p">(</span><span class="n">OrderRequest</span><span class="w"> </span><span class="n">orderRequest</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Creating order in BSS system&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="n">Bulkhead</span><span class="w"> </span><span class="n">bulkhead</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bulkheadRegistry</span><span class="p">.</span><span class="na">bulkhead</span><span class="p">(</span><span class="s">&quot;bss-order&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">bulkhead</span><span class="p">.</span><span class="na">executeSupplier</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">.</span><span class="na">executeSupplier</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">String</span><span class="w"> </span><span class="n">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bssProperties</span><span class="p">.</span><span class="na">getBaseUrl</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;/orders&quot;</span><span class="p">;</span>

<span class="w">                    </span><span class="n">BssOrderRequest</span><span class="w"> </span><span class="n">bssRequest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">toBssOrderRequest</span><span class="p">(</span><span class="n">orderRequest</span><span class="p">);</span>

<span class="w">                    </span><span class="n">HttpHeaders</span><span class="w"> </span><span class="n">headers</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createBssHeaders</span><span class="p">();</span>
<span class="w">                    </span><span class="n">HttpEntity</span><span class="o">&lt;</span><span class="n">BssOrderRequest</span><span class="o">&gt;</span><span class="w"> </span><span class="n">entity</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HttpEntity</span><span class="o">&lt;&gt;</span><span class="p">(</span><span class="n">bssRequest</span><span class="p">,</span><span class="w"> </span><span class="n">headers</span><span class="p">);</span>

<span class="w">                    </span><span class="n">ResponseEntity</span><span class="o">&lt;</span><span class="n">BssOrderResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="n">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">.</span><span class="na">exchange</span><span class="p">(</span>
<span class="w">                        </span><span class="n">url</span><span class="p">,</span><span class="w"> </span><span class="n">HttpMethod</span><span class="p">.</span><span class="na">POST</span><span class="p">,</span><span class="w"> </span><span class="n">entity</span><span class="p">,</span><span class="w"> </span><span class="n">BssOrderResponse</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>

<span class="w">                    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getStatusCode</span><span class="p">().</span><span class="na">is2xxSuccessful</span><span class="p">()</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                        </span><span class="n">OrderResponse</span><span class="w"> </span><span class="n">orderResponse</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">fromBssOrder</span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">());</span>
<span class="w">                        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Order created successfully in BSS: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">orderResponse</span><span class="p">.</span><span class="na">getOrderId</span><span class="p">());</span>
<span class="w">                        </span><span class="k">return</span><span class="w"> </span><span class="n">orderResponse</span><span class="p">;</span>
<span class="w">                    </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">                        </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BssIntegrationException</span><span class="p">(</span><span class="s">&quot;Failed to create order in BSS&quot;</span><span class="p">);</span>
<span class="w">                    </span><span class="p">}</span>

<span class="w">                </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">RestClientException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;BSS order creation error: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">                    </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BssIntegrationException</span><span class="p">(</span><span class="s">&quot;Failed to create order&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">                </span><span class="p">}</span>
<span class="w">            </span><span class="p">});</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ServiceAvailability</span><span class="w"> </span><span class="nf">checkServiceAvailability</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">customerId</span><span class="p">,</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">serviceType</span><span class="p">,</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">location</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Checking service availability in BSS for customer: {}, service: {}, location: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">customerId</span><span class="p">,</span><span class="w"> </span><span class="n">serviceType</span><span class="p">,</span><span class="w"> </span><span class="n">location</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">circuitBreaker</span><span class="p">.</span><span class="na">executeSupplier</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">String</span><span class="w"> </span><span class="n">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bssProperties</span><span class="p">.</span><span class="na">getBaseUrl</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;/services/availability&quot;</span><span class="p">;</span>

<span class="w">                </span><span class="n">BssServiceAvailabilityRequest</span><span class="w"> </span><span class="n">request</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">BssServiceAvailabilityRequest</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">                    </span><span class="p">.</span><span class="na">customerId</span><span class="p">(</span><span class="n">customerId</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">serviceType</span><span class="p">(</span><span class="n">serviceType</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">location</span><span class="p">(</span><span class="n">location</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">                </span><span class="n">HttpHeaders</span><span class="w"> </span><span class="n">headers</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createBssHeaders</span><span class="p">();</span>
<span class="w">                </span><span class="n">HttpEntity</span><span class="o">&lt;</span><span class="n">BssServiceAvailabilityRequest</span><span class="o">&gt;</span><span class="w"> </span><span class="n">entity</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HttpEntity</span><span class="o">&lt;&gt;</span><span class="p">(</span><span class="n">request</span><span class="p">,</span><span class="w"> </span><span class="n">headers</span><span class="p">);</span>

<span class="w">                </span><span class="n">ResponseEntity</span><span class="o">&lt;</span><span class="n">BssServiceAvailabilityResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="n">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">.</span><span class="na">exchange</span><span class="p">(</span>
<span class="w">                    </span><span class="n">url</span><span class="p">,</span><span class="w"> </span><span class="n">HttpMethod</span><span class="p">.</span><span class="na">POST</span><span class="p">,</span><span class="w"> </span><span class="n">entity</span><span class="p">,</span><span class="w"> </span><span class="n">BssServiceAvailabilityResponse</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>

<span class="w">                </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getStatusCode</span><span class="p">().</span><span class="na">is2xxSuccessful</span><span class="p">()</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="k">return</span><span class="w"> </span><span class="n">mappingService</span><span class="p">.</span><span class="na">fromBssAvailability</span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">());</span>
<span class="w">                </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BssIntegrationException</span><span class="p">(</span><span class="s">&quot;Invalid response from BSS availability service&quot;</span><span class="p">);</span>
<span class="w">                </span><span class="p">}</span>

<span class="w">            </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">RestClientException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;BSS availability check error: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BssIntegrationException</span><span class="p">(</span><span class="s">&quot;Failed to check service availability&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">HttpHeaders</span><span class="w"> </span><span class="nf">createBssHeaders</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">HttpHeaders</span><span class="w"> </span><span class="n">headers</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HttpHeaders</span><span class="p">();</span>
<span class="w">        </span><span class="n">headers</span><span class="p">.</span><span class="na">setContentType</span><span class="p">(</span><span class="n">MediaType</span><span class="p">.</span><span class="na">APPLICATION_JSON</span><span class="p">);</span>
<span class="w">        </span><span class="n">headers</span><span class="p">.</span><span class="na">setAccept</span><span class="p">(</span><span class="n">Collections</span><span class="p">.</span><span class="na">singletonList</span><span class="p">(</span><span class="n">MediaType</span><span class="p">.</span><span class="na">APPLICATION_JSON</span><span class="p">));</span>
<span class="w">        </span><span class="n">headers</span><span class="p">.</span><span class="na">setBearerAuth</span><span class="p">(</span><span class="n">getBssAccessToken</span><span class="p">());</span>
<span class="w">        </span><span class="n">headers</span><span class="p">.</span><span class="na">set</span><span class="p">(</span><span class="s">&quot;X-Client-ID&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">bssProperties</span><span class="p">.</span><span class="na">getClientId</span><span class="p">());</span>
<span class="w">        </span><span class="n">headers</span><span class="p">.</span><span class="na">set</span><span class="p">(</span><span class="s">&quot;X-Request-ID&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">UUID</span><span class="p">.</span><span class="na">randomUUID</span><span class="p">().</span><span class="na">toString</span><span class="p">());</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">headers</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="external-api-integration">External API Integration<a class="headerlink" href="#external-api-integration" title="Permanent link">&para;</a></h2>
<h3 id="cip-platform-integration">CIP Platform Integration<a class="headerlink" href="#cip-platform-integration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">CipPlatformClient</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">WebClient</span><span class="w"> </span><span class="n">webClient</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">CipProperties</span><span class="w"> </span><span class="n">cipProperties</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">TimeLimiter</span><span class="w"> </span><span class="n">timeLimiter</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">CipPlatformClient</span><span class="p">(</span>
<span class="w">            </span><span class="n">WebClient</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">webClientBuilder</span><span class="p">,</span>
<span class="w">            </span><span class="n">CipProperties</span><span class="w"> </span><span class="n">cipProperties</span><span class="p">,</span>
<span class="w">            </span><span class="n">TimeLimiterRegistry</span><span class="w"> </span><span class="n">timeLimiterRegistry</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">webClient</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">webClientBuilder</span>
<span class="w">            </span><span class="p">.</span><span class="na">baseUrl</span><span class="p">(</span><span class="n">cipProperties</span><span class="p">.</span><span class="na">getBaseUrl</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">cipProperties</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">cipProperties</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">timeLimiter</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">timeLimiterRegistry</span><span class="p">.</span><span class="na">timeLimiter</span><span class="p">(</span><span class="s">&quot;cip-platform&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">CompletableFuture</span><span class="o">&lt;</span><span class="n">ServiceCatalogResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">getServiceCatalog</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">category</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Fetching service catalog from CIP: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">category</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">timeLimiter</span><span class="p">.</span><span class="na">executeCompletionStage</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">webClient</span>
<span class="w">                </span><span class="p">.</span><span class="na">get</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">uri</span><span class="p">(</span><span class="s">&quot;/service-catalog?category={category}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">category</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">headers</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">addCipHeaders</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">retrieve</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">bodyToMono</span><span class="p">(</span><span class="n">ServiceCatalogResponse</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">timeout</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="n">cipProperties</span><span class="p">.</span><span class="na">getTimeout</span><span class="p">()))</span>
<span class="w">                </span><span class="p">.</span><span class="na">toFuture</span><span class="p">();</span>
<span class="w">        </span><span class="p">}).</span><span class="na">toCompletableFuture</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">CompletableFuture</span><span class="o">&lt;</span><span class="n">PricingResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">calculatePricing</span><span class="p">(</span><span class="n">PricingRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Calculating pricing via CIP platform&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">timeLimiter</span><span class="p">.</span><span class="na">executeCompletionStage</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">webClient</span>
<span class="w">                </span><span class="p">.</span><span class="na">post</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">uri</span><span class="p">(</span><span class="s">&quot;/pricing/calculate&quot;</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">headers</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">addCipHeaders</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">bodyValue</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">retrieve</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">bodyToMono</span><span class="p">(</span><span class="n">PricingResponse</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">timeout</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="n">cipProperties</span><span class="p">.</span><span class="na">getTimeout</span><span class="p">()))</span>
<span class="w">                </span><span class="p">.</span><span class="na">toFuture</span><span class="p">();</span>
<span class="w">        </span><span class="p">}).</span><span class="na">toCompletableFuture</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">addCipHeaders</span><span class="p">(</span><span class="n">HttpHeaders</span><span class="w"> </span><span class="n">headers</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">headers</span><span class="p">.</span><span class="na">setBearerAuth</span><span class="p">(</span><span class="n">getCipAccessToken</span><span class="p">());</span>
<span class="w">        </span><span class="n">headers</span><span class="p">.</span><span class="na">set</span><span class="p">(</span><span class="s">&quot;X-API-Key&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">cipProperties</span><span class="p">.</span><span class="na">getApiKey</span><span class="p">());</span>
<span class="w">        </span><span class="n">headers</span><span class="p">.</span><span class="na">set</span><span class="p">(</span><span class="s">&quot;X-Client-Version&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">cipProperties</span><span class="p">.</span><span class="na">getClientVersion</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="event-driven-architecture">Event-Driven Architecture<a class="headerlink" href="#event-driven-architecture" title="Permanent link">&para;</a></h2>
<h3 id="event-publishing">Event Publishing<a class="headerlink" href="#event-publishing" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">BusinessEventPublisher</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ApplicationEventPublisher</span><span class="w"> </span><span class="n">applicationEventPublisher</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">MessageTemplate</span><span class="w"> </span><span class="n">messageTemplate</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">EventMappingService</span><span class="w"> </span><span class="n">eventMappingService</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">publishQuoteCreatedEvent</span><span class="p">(</span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">quote</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Publishing quote created event: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Publish internal application event</span>
<span class="w">            </span><span class="n">QuoteCreatedEvent</span><span class="w"> </span><span class="n">internalEvent</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">QuoteCreatedEvent</span><span class="p">(</span><span class="k">this</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">);</span>
<span class="w">            </span><span class="n">applicationEventPublisher</span><span class="p">.</span><span class="na">publishEvent</span><span class="p">(</span><span class="n">internalEvent</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Publish external message</span>
<span class="w">            </span><span class="n">QuoteCreatedMessage</span><span class="w"> </span><span class="n">externalMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">eventMappingService</span><span class="p">.</span><span class="na">toQuoteCreatedMessage</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">            </span><span class="n">messageTemplate</span><span class="p">.</span><span class="na">send</span><span class="p">(</span><span class="s">&quot;quote.created&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">externalMessage</span><span class="p">);</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Quote created event published successfully: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to publish quote created event for {}: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                     </span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="c1">// Don&#39;t throw exception to avoid breaking the main flow</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">publishOrderStatusChangedEvent</span><span class="p">(</span><span class="n">OrderResponse</span><span class="w"> </span><span class="n">order</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Publishing order status changed event: {} -&gt; {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">order</span><span class="p">.</span><span class="na">getOrderId</span><span class="p">(),</span><span class="w"> </span><span class="n">order</span><span class="p">.</span><span class="na">getStatus</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">OrderStatusChangedEvent</span><span class="w"> </span><span class="n">internalEvent</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">OrderStatusChangedEvent</span><span class="p">(</span><span class="k">this</span><span class="p">,</span><span class="w"> </span><span class="n">order</span><span class="p">);</span>
<span class="w">            </span><span class="n">applicationEventPublisher</span><span class="p">.</span><span class="na">publishEvent</span><span class="p">(</span><span class="n">internalEvent</span><span class="p">);</span>

<span class="w">            </span><span class="n">OrderStatusChangedMessage</span><span class="w"> </span><span class="n">externalMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">eventMappingService</span><span class="p">.</span><span class="na">toOrderStatusChangedMessage</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">            </span><span class="n">messageTemplate</span><span class="p">.</span><span class="na">send</span><span class="p">(</span><span class="s">&quot;order.status.changed&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">externalMessage</span><span class="p">);</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Order status changed event published: {} -&gt; {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                    </span><span class="n">order</span><span class="p">.</span><span class="na">getOrderId</span><span class="p">(),</span><span class="w"> </span><span class="n">order</span><span class="p">.</span><span class="na">getStatus</span><span class="p">());</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to publish order status changed event for {}: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                     </span><span class="n">order</span><span class="p">.</span><span class="na">getOrderId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="event-handlers">Event Handlers<a class="headerlink" href="#event-handlers" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">BusinessEventHandler</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">NotificationService</span><span class="w"> </span><span class="n">notificationService</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">AuditService</span><span class="w"> </span><span class="n">auditService</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">WorkflowService</span><span class="w"> </span><span class="n">workflowService</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@EventListener</span>
<span class="w">    </span><span class="nd">@Async</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">handleQuoteCreatedEvent</span><span class="p">(</span><span class="n">QuoteCreatedEvent</span><span class="w"> </span><span class="n">event</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Handling quote created event: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">event</span><span class="p">.</span><span class="na">getQuote</span><span class="p">().</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">event</span><span class="p">.</span><span class="na">getQuote</span><span class="p">();</span>

<span class="w">            </span><span class="c1">// Send notifications</span>
<span class="w">            </span><span class="n">notificationService</span><span class="p">.</span><span class="na">sendQuoteCreatedNotification</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Log audit event</span>
<span class="w">            </span><span class="n">auditService</span><span class="p">.</span><span class="na">logQuoteCreatedEvent</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Initialize quote workflow</span>
<span class="w">            </span><span class="n">workflowService</span><span class="p">.</span><span class="na">initializeQuoteWorkflow</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Quote created event handled successfully: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error handling quote created event: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@EventListener</span>
<span class="w">    </span><span class="nd">@Async</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">handleOrderStatusChangedEvent</span><span class="p">(</span><span class="n">OrderStatusChangedEvent</span><span class="w"> </span><span class="n">event</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Handling order status changed event: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">event</span><span class="p">.</span><span class="na">getOrder</span><span class="p">().</span><span class="na">getOrderId</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">OrderResponse</span><span class="w"> </span><span class="n">order</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">event</span><span class="p">.</span><span class="na">getOrder</span><span class="p">();</span>

<span class="w">            </span><span class="c1">// Send status change notifications</span>
<span class="w">            </span><span class="n">notificationService</span><span class="p">.</span><span class="na">sendOrderStatusChangeNotification</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Update workflow state</span>
<span class="w">            </span><span class="n">workflowService</span><span class="p">.</span><span class="na">updateOrderWorkflowState</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Trigger status-specific actions</span>
<span class="w">            </span><span class="n">triggerStatusSpecificActions</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Order status changed event handled: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">order</span><span class="p">.</span><span class="na">getOrderId</span><span class="p">());</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error handling order status changed event: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">triggerStatusSpecificActions</span><span class="p">(</span><span class="n">OrderResponse</span><span class="w"> </span><span class="n">order</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">order</span><span class="p">.</span><span class="na">getStatus</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">COMPLETED</span><span class="p">:</span>
<span class="w">                </span><span class="n">workflowService</span><span class="p">.</span><span class="na">completeOrderWorkflow</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">                </span><span class="n">notificationService</span><span class="p">.</span><span class="na">sendOrderCompletionNotification</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">FAILED</span><span class="p">:</span>
<span class="w">                </span><span class="n">workflowService</span><span class="p">.</span><span class="na">handleOrderFailure</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">                </span><span class="n">notificationService</span><span class="p">.</span><span class="na">sendOrderFailureNotification</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">CANCELLED</span><span class="p">:</span>
<span class="w">                </span><span class="n">workflowService</span><span class="p">.</span><span class="na">cancelOrderWorkflow</span><span class="p">(</span><span class="n">order</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="error-handling-and-resilience">Error Handling and Resilience<a class="headerlink" href="#error-handling-and-resilience" title="Permanent link">&para;</a></h2>
<h3 id="resilience-configuration">Resilience Configuration<a class="headerlink" href="#resilience-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Configuration</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">ResilienceConfig</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">CircuitBreakerConfig</span><span class="w"> </span><span class="nf">defaultCircuitBreakerConfig</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">CircuitBreakerConfig</span><span class="p">.</span><span class="na">custom</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">failureRateThreshold</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">waitDurationInOpenState</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="mi">30</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">slidingWindowSize</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">minimumNumberOfCalls</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">permittedNumberOfCallsInHalfOpenState</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">automaticTransitionFromOpenToHalfOpenEnabled</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">RetryConfig</span><span class="w"> </span><span class="nf">defaultRetryConfig</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">RetryConfig</span><span class="p">.</span><span class="na">custom</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">maxAttempts</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">waitDuration</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="mi">1</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">exponentialBackoffMultiplier</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">retryExceptions</span><span class="p">(</span><span class="n">IOException</span><span class="p">.</span><span class="na">class</span><span class="p">,</span><span class="w"> </span><span class="n">TimeoutException</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">ignoreExceptions</span><span class="p">(</span><span class="n">IllegalArgumentException</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">BulkheadConfig</span><span class="w"> </span><span class="nf">defaultBulkheadConfig</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">BulkheadConfig</span><span class="p">.</span><span class="na">custom</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">maxConcurrentCalls</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">maxWaitDuration</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="mi">5</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Bean</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">TimeLimiterConfig</span><span class="w"> </span><span class="nf">defaultTimeLimiterConfig</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">TimeLimiterConfig</span><span class="p">.</span><span class="na">custom</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">timeoutDuration</span><span class="p">(</span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="mi">30</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">cancelRunningFuture</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="integration-exception-handling">Integration Exception Handling<a class="headerlink" href="#integration-exception-handling" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">IntegrationExceptionHandler</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="w"> </span><span class="n">T</span><span class="w"> </span><span class="nf">handleIntegrationCall</span><span class="p">(</span><span class="n">Supplier</span><span class="o">&lt;</span><span class="n">T</span><span class="o">&gt;</span><span class="w"> </span><span class="n">integrationCall</span><span class="p">,</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">operationName</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">integrationCall</span><span class="p">.</span><span class="na">get</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">OrderCaptureException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Order Capture integration error in {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">operationName</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IntegrationException</span><span class="p">(</span><span class="s">&quot;Order Capture service unavailable&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">BssIntegrationException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;BSS integration error in {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">operationName</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IntegrationException</span><span class="p">(</span><span class="s">&quot;BSS service unavailable&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">TimeoutException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Timeout error in {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">operationName</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IntegrationException</span><span class="p">(</span><span class="s">&quot;Service timeout&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Unexpected error in {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">operationName</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IntegrationException</span><span class="p">(</span><span class="s">&quot;Integration service error&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../05-development-guide/">Development Guide →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>