site_name: TELUS B2B Order Capture Ecosystem Documentation
site_description: Comprehensive documentation for the TELUS B2B Order Capture ecosystem consisting of frontend, backend, and extension components
site_author: TELUS Cloud BSS Team
site_url: http://localhost:8000

# Repository
repo_name: TELUS B2B Order Capture
repo_url: https://github.com/telus/nc-cloud-bss-oc-ui-frontend-b2b
edit_uri: ""

# Copyright
copyright: Copyright &copy; 2024 TELUS Communications Inc.

# Documentation directory
docs_dir: docs

# Configuration
theme:
  name: material
  palette:
    # Palette toggle for light mode
    - scheme: default
      primary: purple
      accent: purple
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    # Palette toggle for dark mode
    - scheme: slate
      primary: purple
      accent: purple
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  font:
    text: Roboto
    code: Roboto Mono
  features:
    - announce.dismiss
    - content.action.edit
    - content.action.view
    - content.code.annotate
    - content.code.copy
    - content.tabs.link
    - content.tooltips
    - header.autohide
    - navigation.expand
    - navigation.footer
    - navigation.indexes
    - navigation.instant
    - navigation.instant.prefetch
    - navigation.instant.progress
    - navigation.prune
    - navigation.sections
    - navigation.tabs
    - navigation.tabs.sticky
    - navigation.top
    - navigation.tracking
    - search.highlight
    - search.share
    - search.suggest
    - toc.follow
    - toc.integrate

# Plugins
plugins:
  - search

# Customization
extra:
  status:
    new: Recently added
    deprecated: Deprecated
  analytics:
    provider: google
    property: G-XXXXXXXXXX
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/telus
    - icon: fontawesome/brands/docker
      link: https://hub.docker.com/u/telus
    - icon: fontawesome/solid/paper-plane
      link: mailto:<EMAIL>

extra_css:
  - stylesheets/extra.css
  - stylesheets/telus-theme.css

extra_javascript:
  - javascripts/extra.js
  - javascripts/mermaid.js

# Extensions
markdown_extensions:
  - abbr
  - admonition
  - attr_list
  - def_list
  - footnotes
  - md_in_html
  - toc:
      permalink: true
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.details

  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.magiclink:
      repo_url_shorthand: true
      user: telus
      repo: nc-cloud-bss-oc-ui-frontend-b2b
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.tilde

# Page tree
nav:
  - Home: README.md
  - Overview & Architecture: 00-overview.md
  - Mermaid Test: mermaid-test.md
  - Frontend Application:
    - Overview: frontend/00-overview.md
    - Architecture: frontend/01-architecture.md
    - Custom Components: frontend/02-custom-components.md
    - State Management: frontend/03-state-management.md
    - API Integration: frontend/04-api-integration.md
    - Development Guide: frontend/05-development-guide.md
  - Backend Application:
    - Overview: backend/00-overview.md
    - Architecture: backend/01-architecture.md
    - REST API Design: backend/02-rest-api-design.md
    - Service Layer: backend/03-service-layer.md
    - Integration Layer: backend/04-integration-layer.md
    - Development Guide: backend/05-development-guide.md
  - Extensions Application:
    - Overview: extensions/00-overview.md
    - Plugin Architecture: extensions/01-plugin-architecture.md
    - Quote Modificators: extensions/02-quote-modificators.md
    - Bandwidth Validators: extensions/03-bandwidth-validators.md
    - State Validators: extensions/04-state-validators.md
    - Development Guide: extensions/05-development-guide.md
  - QSS Extension:
    - Overview: qss-extension/00-overview.md
    - Plugin Architecture: qss-extension/01-plugin-architecture.md
    - Snapshot Generation: qss-extension/02-snapshot-generation.md
    - Storage Management: qss-extension/03-storage-management.md
    - Development Guide: qss-extension/04-development-guide.md
  - Bulk Operation Extension:
    - Overview: bulk-operation-extension/00-overview.md
    - NiFi Flow Architecture: bulk-operation-extension/01-nifi-flow-architecture.md
    - API Integration: bulk-operation-extension/02-api-integration.md
    - Deployment Configuration: bulk-operation-extension/03-deployment-configuration.md
    - Development Guide: bulk-operation-extension/04-development-guide.md
  - CIP Configuration:
    - Overview: cip-config/00-overview.md
    - Architecture: cip-config/01-architecture.md
    - Configuration Management: cip-config/02-configuration-management.md
    - Integration Chains: cip-config/03-integration-chains.md
    - Service Catalog: cip-config/04-service-catalog.md
    - Deployment Guide: cip-config/05-deployment-guide.md
    - Development Guide: cip-config/06-development-guide.md
    - Troubleshooting: cip-config/07-troubleshooting.md
    - Reference: cip-config/08-reference.md
  - CPQ Configuration:
    - Overview: cpq-config/00-overview.md
    - Configuration Management: cpq-config/01-configuration-management.md
    - Integration Chains: cpq-config/02-integration-chains.md
    - Service Catalog: cpq-config/03-service-catalog.md
    - Security Configuration: cpq-config/04-security-configuration.md
    - Development Guide: cpq-config/05-development-guide.md
  - Integration Guide: 04-integration-guide.md
  - Development Guide: 05-development-guide.md
  - Deployment Guide: 06-deployment-guide.md
  - Reference: 07-reference.md
  - Documentation Summary: DOCUMENTATION_SUMMARY.md
  - Setup Guide: SETUP.md
