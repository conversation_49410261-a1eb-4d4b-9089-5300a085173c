
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/bulk-operation-extension/02-api-integration/">
      
      
        <link rel="prev" href="../01-nifi-flow-architecture/">
      
      
        <link rel="next" href="../03-deployment-configuration/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>API Integration - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#api-integration" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              API Integration
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" checked>
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#api-overview" class="md-nav__link">
    <span class="md-ellipsis">
      API Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="API Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rest-api-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      REST API Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#api-endpoints" class="md-nav__link">
    <span class="md-ellipsis">
      API Endpoints
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#base-url-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Base URL Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#authentication" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Authentication">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#oauth2-authentication-flow" class="md-nav__link">
    <span class="md-ellipsis">
      OAuth2 Authentication Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#authentication-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#client-credentials-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Client Credentials Setup
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bulk-address-validation-api" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Address Validation API
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Bulk Address Validation API">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#submit-bulk-validation-request" class="md-nav__link">
    <span class="md-ellipsis">
      Submit Bulk Validation Request
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Submit Bulk Validation Request">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#request-specification" class="md-nav__link">
    <span class="md-ellipsis">
      Request Specification
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#response-specification" class="md-nav__link">
    <span class="md-ellipsis">
      Response Specification
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#get-request-status" class="md-nav__link">
    <span class="md-ellipsis">
      Get Request Status
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Get Request Status">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#request-specification_1" class="md-nav__link">
    <span class="md-ellipsis">
      Request Specification
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#response-specification_1" class="md-nav__link">
    <span class="md-ellipsis">
      Response Specification
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#get-validation-report" class="md-nav__link">
    <span class="md-ellipsis">
      Get Validation Report
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Get Validation Report">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#request-specification_2" class="md-nav__link">
    <span class="md-ellipsis">
      Request Specification
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#response-specification_2" class="md-nav__link">
    <span class="md-ellipsis">
      Response Specification
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#status-values" class="md-nav__link">
    <span class="md-ellipsis">
      Status Values
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#external-system-integration" class="md-nav__link">
    <span class="md-ellipsis">
      External System Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="External System Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-engine-service-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Engine Service Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#qes-integration-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      QES Integration Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cip-gateway-integration" class="md-nav__link">
    <span class="md-ellipsis">
      CIP Gateway Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#database-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Database Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-handling" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Handling">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#error-response-format" class="md-nav__link">
    <span class="md-ellipsis">
      Error Response Format
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#error-codes" class="md-nav__link">
    <span class="md-ellipsis">
      Error Codes
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#error-handling-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-considerations" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Considerations
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Considerations">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rate-limiting" class="md-nav__link">
    <span class="md-ellipsis">
      Rate Limiting
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#caching-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Caching Strategy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Metrics
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#api-overview" class="md-nav__link">
    <span class="md-ellipsis">
      API Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="API Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rest-api-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      REST API Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#api-endpoints" class="md-nav__link">
    <span class="md-ellipsis">
      API Endpoints
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#base-url-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Base URL Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#authentication" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Authentication">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#oauth2-authentication-flow" class="md-nav__link">
    <span class="md-ellipsis">
      OAuth2 Authentication Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#authentication-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#client-credentials-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Client Credentials Setup
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bulk-address-validation-api" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Address Validation API
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Bulk Address Validation API">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#submit-bulk-validation-request" class="md-nav__link">
    <span class="md-ellipsis">
      Submit Bulk Validation Request
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Submit Bulk Validation Request">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#request-specification" class="md-nav__link">
    <span class="md-ellipsis">
      Request Specification
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#response-specification" class="md-nav__link">
    <span class="md-ellipsis">
      Response Specification
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#get-request-status" class="md-nav__link">
    <span class="md-ellipsis">
      Get Request Status
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Get Request Status">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#request-specification_1" class="md-nav__link">
    <span class="md-ellipsis">
      Request Specification
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#response-specification_1" class="md-nav__link">
    <span class="md-ellipsis">
      Response Specification
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#get-validation-report" class="md-nav__link">
    <span class="md-ellipsis">
      Get Validation Report
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Get Validation Report">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#request-specification_2" class="md-nav__link">
    <span class="md-ellipsis">
      Request Specification
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#response-specification_2" class="md-nav__link">
    <span class="md-ellipsis">
      Response Specification
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#status-values" class="md-nav__link">
    <span class="md-ellipsis">
      Status Values
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#external-system-integration" class="md-nav__link">
    <span class="md-ellipsis">
      External System Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="External System Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-engine-service-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Engine Service Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#qes-integration-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      QES Integration Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cip-gateway-integration" class="md-nav__link">
    <span class="md-ellipsis">
      CIP Gateway Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#database-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Database Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-handling" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Handling">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#error-response-format" class="md-nav__link">
    <span class="md-ellipsis">
      Error Response Format
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#error-codes" class="md-nav__link">
    <span class="md-ellipsis">
      Error Codes
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#error-handling-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-considerations" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Considerations
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Considerations">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rate-limiting" class="md-nav__link">
    <span class="md-ellipsis">
      Rate Limiting
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#caching-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Caching Strategy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Metrics
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="api-integration">API Integration<a class="headerlink" href="#api-integration" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#api-overview">API Overview</a></li>
<li><a href="#authentication">Authentication</a></li>
<li><a href="#bulk-address-validation-api">Bulk Address Validation API</a></li>
<li><a href="#external-system-integration">External System Integration</a></li>
<li><a href="#error-handling">Error Handling</a></li>
<li><a href="#performance-considerations">Performance Considerations</a></li>
</ul>
<h2 id="api-overview">API Overview<a class="headerlink" href="#api-overview" title="Permanent link">&para;</a></h2>
<h3 id="rest-api-architecture">REST API Architecture<a class="headerlink" href="#rest-api-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Client Applications&quot;
        A[Web Applications]
        B[Mobile Apps]
        C[Third-party Systems]
        D[Internal Services]
    end

    subgraph &quot;API Gateway Layer&quot;
        E[Load Balancer]
        F[Rate Limiting]
        G[Authentication]
        H[Request Routing]
    end

    subgraph &quot;Bulk Operation Extension API&quot;
        I[Address Management API]
        J[Status Management API]
        K[Report Generation API]
        L[Error Management API]
    end

    subgraph &quot;NiFi Processing Layer&quot;
        M[HTTP Request Handler]
        N[Request Processor]
        O[Response Generator]
        P[Error Handler]
    end

    A --&gt; E
    B --&gt; E
    C --&gt; E
    D --&gt; E

    E --&gt; F
    F --&gt; G
    G --&gt; H
    H --&gt; I
    H --&gt; J
    H --&gt; K
    H --&gt; L

    I --&gt; M
    J --&gt; M
    K --&gt; M
    L --&gt; M

    M --&gt; N
    N --&gt; O
    O --&gt; P

    style E fill:#673ab7,stroke:#512da8,color:#ffffff
    style I fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style M fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="api-endpoints">API Endpoints<a class="headerlink" href="#api-endpoints" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Method</th>
<th>Endpoint</th>
<th>Purpose</th>
<th>Authentication</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>POST</strong></td>
<td><code>/addressManagement/v1/bulkValidate/request</code></td>
<td>Submit bulk address validation request</td>
<td>OAuth2 Bearer Token</td>
</tr>
<tr>
<td><strong>GET</strong></td>
<td><code>/addressManagement/v1/bulkValidate/{id}/status</code></td>
<td>Get request processing status</td>
<td>OAuth2 Bearer Token</td>
</tr>
<tr>
<td><strong>GET</strong></td>
<td><code>/addressManagement/v1/bulkValidate/{id}/report</code></td>
<td>Get validation report</td>
<td>OAuth2 Bearer Token</td>
</tr>
<tr>
<td><strong>GET</strong></td>
<td><code>/addressManagement/v1/bulkValidate/{id}/errors</code></td>
<td>Get error details</td>
<td>OAuth2 Bearer Token</td>
</tr>
</tbody>
</table>
<h3 id="base-url-configuration">Base URL Configuration<a class="headerlink" href="#base-url-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Environment-specific base URLs</span>
<span class="nt">environments</span><span class="p">:</span>
<span class="w">  </span><span class="nt">development</span><span class="p">:</span>
<span class="w">    </span><span class="nt">base_url</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://bulk-ops-dev.telus.com:21014&quot;</span>

<span class="w">  </span><span class="nt">staging</span><span class="p">:</span>
<span class="w">    </span><span class="nt">base_url</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;http://bulk-ops-staging.telus.com:21014&quot;</span>

<span class="w">  </span><span class="nt">production</span><span class="p">:</span>
<span class="w">    </span><span class="nt">base_url</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://bulk-ops.telus.com:21014&quot;</span>
</code></pre></div>
<h2 id="authentication">Authentication<a class="headerlink" href="#authentication" title="Permanent link">&para;</a></h2>
<h3 id="oauth2-authentication-flow">OAuth2 Authentication Flow<a class="headerlink" href="#oauth2-authentication-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Client as API Client
    participant Auth as Identity Provider
    participant API as Bulk Operations API
    participant NiFi as NiFi Processor

    Client-&gt;&gt;Auth: POST /auth/realms/cloud-common/protocol/openid-connect/token
    Note over Client,Auth: grant_type=client_credentials&lt;br/&gt;client_id=&lt;client_id&gt;&lt;br/&gt;client_secret=&lt;client_secret&gt;
    Auth--&gt;&gt;Client: Access Token Response

    Client-&gt;&gt;API: API Request with Bearer Token
    Note over Client,API: Authorization: Bearer &lt;access_token&gt;
    API-&gt;&gt;NiFi: Forward Request
    NiFi-&gt;&gt;Auth: Validate Token
    Auth--&gt;&gt;NiFi: Token Validation Result

    alt Token Valid
        NiFi-&gt;&gt;NiFi: Process Request
        NiFi--&gt;&gt;API: Success Response
        API--&gt;&gt;Client: API Response
    else Token Invalid
        NiFi--&gt;&gt;API: 401 Unauthorized
        API--&gt;&gt;Client: 401 Unauthorized
    end
</code></pre></div>
<h3 id="authentication-configuration">Authentication Configuration<a class="headerlink" href="#authentication-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;oauth2_configuration&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;identity_provider_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;http://identity-provider:8080/auth&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;realm&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;cloud-common&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;token_endpoint&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/realms/cloud-common/protocol/openid-connect/token&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;grant_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;client_credentials&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;scope&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bulk-operations&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;token_validation&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;cache_duration&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;300 seconds&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;retry_attempts&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;timeout&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;10 seconds&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="client-credentials-setup">Client Credentials Setup<a class="headerlink" href="#client-credentials-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Environment Variables for Client Authentication</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">OAUTH2_CLIENT_ID</span><span class="o">=</span><span class="s2">&quot;bulk-operations-client&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">OAUTH2_CLIENT_SECRET</span><span class="o">=</span><span class="s2">&quot;&lt;client-secret&gt;&quot;</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">OAUTH2_SCOPE</span><span class="o">=</span><span class="s2">&quot;bulk-operations&quot;</span>

<span class="c1"># Example Token Request</span>
curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span><span class="s2">&quot;http://identity-provider:8080/auth/realms/cloud-common/protocol/openid-connect/token&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/x-www-form-urlencoded&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s2">&quot;grant_type=client_credentials&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s2">&quot;client_id=</span><span class="si">${</span><span class="nv">OAUTH2_CLIENT_ID</span><span class="si">}</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s2">&quot;client_secret=</span><span class="si">${</span><span class="nv">OAUTH2_CLIENT_SECRET</span><span class="si">}</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s2">&quot;scope=</span><span class="si">${</span><span class="nv">OAUTH2_SCOPE</span><span class="si">}</span><span class="s2">&quot;</span>
</code></pre></div>
<h2 id="bulk-address-validation-api">Bulk Address Validation API<a class="headerlink" href="#bulk-address-validation-api" title="Permanent link">&para;</a></h2>
<h3 id="submit-bulk-validation-request">Submit Bulk Validation Request<a class="headerlink" href="#submit-bulk-validation-request" title="Permanent link">&para;</a></h3>
<h4 id="request-specification">Request Specification<a class="headerlink" href="#request-specification" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="err">POST /addressManagement/v1/bulkValidate/request</span>
<span class="err">Authorization: Bearer &lt;access_token&gt;</span>
<span class="err">Content-Type: application/json</span>

<span class="err">{</span>
<span class="err">  &quot;addresses&quot;: [</span>
<span class="err">    {</span>
<span class="err">      &quot;streetNumber&quot;: &quot;123&quot;,</span>
<span class="err">      &quot;streetName&quot;: &quot;Main Street&quot;,</span>
<span class="err">      &quot;city&quot;: &quot;Toronto&quot;,</span>
<span class="err">      &quot;province&quot;: &quot;ON&quot;,</span>
<span class="err">      &quot;postalCode&quot;: &quot;M5V 3A8&quot;,</span>
<span class="err">      &quot;country&quot;: &quot;CA&quot;</span>
<span class="err">    },</span>
<span class="err">    {</span>
<span class="err">      &quot;streetNumber&quot;: &quot;456&quot;, </span>
<span class="err">      &quot;streetName&quot;: &quot;Queen Street West&quot;,</span>
<span class="err">      &quot;city&quot;: &quot;Toronto&quot;,</span>
<span class="err">      &quot;province&quot;: &quot;ON&quot;,</span>
<span class="err">      &quot;postalCode&quot;: &quot;M5V 2A1&quot;,</span>
<span class="err">      &quot;country&quot;: &quot;CA&quot;</span>
<span class="err">    }</span>
<span class="err">  ],</span>
<span class="err">  &quot;validationType&quot;: &quot;standard&quot;,</span>
<span class="err">  &quot;options&quot;: {</span>
<span class="err">    &quot;includeGeocoding&quot;: true,</span>
<span class="err">    &quot;validateServiceability&quot;: true,</span>
<span class="err">    &quot;maxResults&quot;: 5</span>
<span class="err">  }</span>
<span class="err">}</span>
</code></pre></div>
<h4 id="response-specification">Response Specification<a class="headerlink" href="#response-specification" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;requestId&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bulk-req-12345678-1234-1234-1234-123456789012&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ACCEPTED&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;submittedAt&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2023-12-01T10:30:00.000Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;estimatedCompletionTime&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2023-12-01T10:35:00.000Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;addressCount&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;links&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/addressManagement/v1/bulkValidate/bulk-req-12345678-1234-1234-1234-123456789012/status&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;report&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/addressManagement/v1/bulkValidate/bulk-req-12345678-1234-1234-1234-123456789012/report&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;errors&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/addressManagement/v1/bulkValidate/bulk-req-12345678-1234-1234-1234-123456789012/errors&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="get-request-status">Get Request Status<a class="headerlink" href="#get-request-status" title="Permanent link">&para;</a></h3>
<h4 id="request-specification_1">Request Specification<a class="headerlink" href="#request-specification_1" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="err">GET /addressManagement/v1/bulkValidate/{requestId}/status</span>
<span class="err">Authorization: Bearer &lt;access_token&gt;</span>
</code></pre></div>
<h4 id="response-specification_1">Response Specification<a class="headerlink" href="#response-specification_1" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;requestId&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bulk-req-12345678-1234-1234-1234-123456789012&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;PROCESSING&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;progress&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;totalAddresses&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;processedAddresses&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;validatedAddresses&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;failedAddresses&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;percentComplete&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">50</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;submittedAt&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2023-12-01T10:30:00.000Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;startedAt&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2023-12-01T10:30:15.000Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;estimatedCompletionTime&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2023-12-01T10:35:00.000Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;links&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;report&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/addressManagement/v1/bulkValidate/bulk-req-12345678-1234-1234-1234-123456789012/report&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;errors&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/addressManagement/v1/bulkValidate/bulk-req-12345678-1234-1234-1234-123456789012/errors&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="get-validation-report">Get Validation Report<a class="headerlink" href="#get-validation-report" title="Permanent link">&para;</a></h3>
<h4 id="request-specification_2">Request Specification<a class="headerlink" href="#request-specification_2" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="err">GET /addressManagement/v1/bulkValidate/{requestId}/report</span>
<span class="err">Authorization: Bearer &lt;access_token&gt;</span>
<span class="err">Accept: application/json</span>
</code></pre></div>
<h4 id="response-specification_2">Response Specification<a class="headerlink" href="#response-specification_2" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;requestId&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bulk-req-12345678-1234-1234-1234-123456789012&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;COMPLETED&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;summary&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;totalAddresses&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;validatedAddresses&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;failedAddresses&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;processingTime&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;00:04:32&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;results&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;inputAddress&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;streetNumber&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;streetName&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Main Street&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;city&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Toronto&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;province&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ON&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;postalCode&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;M5V 3A8&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;country&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CA&quot;</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;validatedAddress&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;streetNumber&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;123&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;streetName&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Main St&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;city&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Toronto&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;province&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ON&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;postalCode&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;M5V 3A8&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;country&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;CA&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;standardized&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;validationResult&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;VALID&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;confidence&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">0.95</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;geocoding&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;latitude&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">43.6426</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;longitude&quot;</span><span class="p">:</span><span class="w"> </span><span class="mf">-79.3871</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;serviceability&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;available&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;services&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;FIBER&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;DSL&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;WIRELESS&quot;</span><span class="p">]</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;completedAt&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2023-12-01T10:34:32.000Z&quot;</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="status-values">Status Values<a class="headerlink" href="#status-values" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nt">status_definitions</span><span class="p">:</span>
<span class="w">  </span><span class="nt">ACCEPTED</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Request</span><span class="nv"> </span><span class="s">has</span><span class="nv"> </span><span class="s">been</span><span class="nv"> </span><span class="s">accepted</span><span class="nv"> </span><span class="s">and</span><span class="nv"> </span><span class="s">queued</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">processing&quot;</span>
<span class="w">    </span><span class="nt">next_states</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;PROCESSING&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;FAILED&quot;</span><span class="p p-Indicator">]</span>

<span class="w">  </span><span class="nt">PROCESSING</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Request</span><span class="nv"> </span><span class="s">is</span><span class="nv"> </span><span class="s">currently</span><span class="nv"> </span><span class="s">being</span><span class="nv"> </span><span class="s">processed&quot;</span>
<span class="w">    </span><span class="nt">next_states</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;COMPLETED&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;FAILED&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;PARTIALLY_COMPLETED&quot;</span><span class="p p-Indicator">]</span>

<span class="w">  </span><span class="nt">COMPLETED</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;All</span><span class="nv"> </span><span class="s">addresses</span><span class="nv"> </span><span class="s">have</span><span class="nv"> </span><span class="s">been</span><span class="nv"> </span><span class="s">successfully</span><span class="nv"> </span><span class="s">processed&quot;</span>
<span class="w">    </span><span class="nt">final_state</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">  </span><span class="nt">PARTIALLY_COMPLETED</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Some</span><span class="nv"> </span><span class="s">addresses</span><span class="nv"> </span><span class="s">processed</span><span class="nv"> </span><span class="s">successfully,</span><span class="nv"> </span><span class="s">others</span><span class="nv"> </span><span class="s">failed&quot;</span>
<span class="w">    </span><span class="nt">final_state</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">  </span><span class="nt">FAILED</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Request</span><span class="nv"> </span><span class="s">processing</span><span class="nv"> </span><span class="s">failed&quot;</span>
<span class="w">    </span><span class="nt">final_state</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">  </span><span class="nt">CANCELLED</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Request</span><span class="nv"> </span><span class="s">was</span><span class="nv"> </span><span class="s">cancelled</span><span class="nv"> </span><span class="s">by</span><span class="nv"> </span><span class="s">user</span><span class="nv"> </span><span class="s">or</span><span class="nv"> </span><span class="s">system&quot;</span>
<span class="w">    </span><span class="nt">final_state</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</code></pre></div>
<h2 id="external-system-integration">External System Integration<a class="headerlink" href="#external-system-integration" title="Permanent link">&para;</a></h2>
<h3 id="quote-engine-service-integration">Quote Engine Service Integration<a class="headerlink" href="#quote-engine-service-integration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;Bulk Operations&quot;
        A[NiFi Processor]
        B[Address Validator]
        C[Request Manager]
    end

    subgraph &quot;Quote Engine Service&quot;
        D[Address Validation API]
        E[Market Resolver]
        F[Serviceability Check]
    end

    subgraph &quot;Cloud Integration Platform&quot;
        G[CIP Gateway]
        H[Service Registry]
        I[Load Balancer]
    end

    A --&gt; B
    B --&gt; C
    C --&gt; G
    G --&gt; H
    H --&gt; I
    I --&gt; D
    D --&gt; E
    E --&gt; F

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style D fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style G fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="qes-integration-configuration">QES Integration Configuration<a class="headerlink" href="#qes-integration-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;qes_integration&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;base_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;http://quotation-engine-service:8080&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;endpoints&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;address_validation&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/api/v1/addresses/validate&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;market_resolution&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/api/v1/addresses/market&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;serviceability&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/api/v1/addresses/serviceability&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;authentication&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;oauth2&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;token_provider&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Request OAuth2 Access Token Provider&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;timeout&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;connection&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;10 seconds&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;read&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;30 seconds&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;retry&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;max_attempts&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;backoff_strategy&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;exponential&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;initial_delay&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;1 second&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="cip-gateway-integration">CIP Gateway Integration<a class="headerlink" href="#cip-gateway-integration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;cip_integration&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;base_url&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;http://cloud-integration-platform-engine:8080&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;endpoints&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;service_discovery&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/api/v1/services&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;routing&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/api/v1/route&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;monitoring&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;/api/v1/health&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;headers&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;X-Source-System&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bulk-operations&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;X-Request-ID&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;${uuid()}&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;Content-Type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;application/json&quot;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="database-integration">Database Integration<a class="headerlink" href="#database-integration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">-- Request Status Table</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">bulk_request_status</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">request_id</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">255</span><span class="p">)</span><span class="w"> </span><span class="k">PRIMARY</span><span class="w"> </span><span class="k">KEY</span><span class="p">,</span>
<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">total_addresses</span><span class="w"> </span><span class="nb">INTEGER</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">processed_addresses</span><span class="w"> </span><span class="nb">INTEGER</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="n">validated_addresses</span><span class="w"> </span><span class="nb">INTEGER</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="n">failed_addresses</span><span class="w"> </span><span class="nb">INTEGER</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="n">submitted_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">started_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="p">,</span>
<span class="w">    </span><span class="n">completed_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="p">,</span>
<span class="w">    </span><span class="n">error_message</span><span class="w"> </span><span class="nb">TEXT</span><span class="p">,</span>
<span class="w">    </span><span class="n">created_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">,</span>
<span class="w">    </span><span class="n">updated_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span>
<span class="p">);</span>

<span class="c1">-- Address Validation Results Table</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">address_validation_results</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">id</span><span class="w"> </span><span class="nb">SERIAL</span><span class="w"> </span><span class="k">PRIMARY</span><span class="w"> </span><span class="k">KEY</span><span class="p">,</span>
<span class="w">    </span><span class="n">request_id</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">255</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">input_address</span><span class="w"> </span><span class="n">JSONB</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">validated_address</span><span class="w"> </span><span class="n">JSONB</span><span class="p">,</span>
<span class="w">    </span><span class="n">validation_result</span><span class="w"> </span><span class="n">JSONB</span><span class="p">,</span>
<span class="w">    </span><span class="n">status</span><span class="w"> </span><span class="nb">VARCHAR</span><span class="p">(</span><span class="mi">50</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">error_message</span><span class="w"> </span><span class="nb">TEXT</span><span class="p">,</span>
<span class="w">    </span><span class="n">processed_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="k">CURRENT_TIMESTAMP</span><span class="p">,</span>
<span class="w">    </span><span class="k">FOREIGN</span><span class="w"> </span><span class="k">KEY</span><span class="w"> </span><span class="p">(</span><span class="n">request_id</span><span class="p">)</span><span class="w"> </span><span class="k">REFERENCES</span><span class="w"> </span><span class="n">bulk_request_status</span><span class="p">(</span><span class="n">request_id</span><span class="p">)</span>
<span class="p">);</span>

<span class="c1">-- Indexes for performance</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_bulk_request_status_status</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">bulk_request_status</span><span class="p">(</span><span class="n">status</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_bulk_request_status_submitted_at</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">bulk_request_status</span><span class="p">(</span><span class="n">submitted_at</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_address_validation_results_request_id</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">address_validation_results</span><span class="p">(</span><span class="n">request_id</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_address_validation_results_status</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">address_validation_results</span><span class="p">(</span><span class="n">status</span><span class="p">);</span>
</code></pre></div>
<h2 id="error-handling">Error Handling<a class="headerlink" href="#error-handling" title="Permanent link">&para;</a></h2>
<h3 id="error-response-format">Error Response Format<a class="headerlink" href="#error-response-format" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;error_response_schema&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;object&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;errors&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;array&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;items&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;object&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;properties&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;pattern&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;^AM-BAV-[0-9]{4}$&quot;</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Standardized error code&quot;</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="nt">&quot;reason&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Human-readable error description&quot;</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;object&quot;</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Additional error context&quot;</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">            </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;format&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;date-time&quot;</span><span class="p">,</span>
<span class="w">              </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Error occurrence timestamp&quot;</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">          </span><span class="p">},</span>
<span class="w">          </span><span class="nt">&quot;required&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;code&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;reason&quot;</span><span class="p">]</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;requestId&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;string&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;description&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Request identifier for tracking&quot;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;required&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;errors&quot;</span><span class="p">]</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="error-codes">Error Codes<a class="headerlink" href="#error-codes" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nt">error_codes</span><span class="p">:</span>
<span class="w">  </span><span class="c1"># Authentication Errors (4xx)</span>
<span class="w">  </span><span class="nt">AM-BAV-0401</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Unauthorized</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">Invalid</span><span class="nv"> </span><span class="s">or</span><span class="nv"> </span><span class="s">missing</span><span class="nv"> </span><span class="s">authentication</span><span class="nv"> </span><span class="s">token&quot;</span>
<span class="w">    </span><span class="nt">http_status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">401</span>

<span class="w">  </span><span class="nt">AM-BAV-0403</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Forbidden</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">Insufficient</span><span class="nv"> </span><span class="s">permissions&quot;</span>
<span class="w">    </span><span class="nt">http_status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">403</span>

<span class="w">  </span><span class="c1"># Client Errors (4xx)</span>
<span class="w">  </span><span class="nt">AM-BAV-0400</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Bad</span><span class="nv"> </span><span class="s">Request</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">Invalid</span><span class="nv"> </span><span class="s">request</span><span class="nv"> </span><span class="s">format</span><span class="nv"> </span><span class="s">or</span><span class="nv"> </span><span class="s">parameters&quot;</span>
<span class="w">    </span><span class="nt">http_status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">400</span>

<span class="w">  </span><span class="nt">AM-BAV-0404</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Not</span><span class="nv"> </span><span class="s">Found</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">Request</span><span class="nv"> </span><span class="s">ID</span><span class="nv"> </span><span class="s">not</span><span class="nv"> </span><span class="s">found&quot;</span>
<span class="w">    </span><span class="nt">http_status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">404</span>

<span class="w">  </span><span class="nt">AM-BAV-0409</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Conflict</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">Request</span><span class="nv"> </span><span class="s">already</span><span class="nv"> </span><span class="s">exists&quot;</span>
<span class="w">    </span><span class="nt">http_status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">409</span>

<span class="w">  </span><span class="nt">AM-BAV-0422</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Unprocessable</span><span class="nv"> </span><span class="s">Entity</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">Invalid</span><span class="nv"> </span><span class="s">address</span><span class="nv"> </span><span class="s">data&quot;</span>
<span class="w">    </span><span class="nt">http_status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">422</span>

<span class="w">  </span><span class="c1"># Server Errors (5xx)</span>
<span class="w">  </span><span class="nt">AM-BAV-0500</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Internal</span><span class="nv"> </span><span class="s">Server</span><span class="nv"> </span><span class="s">Error</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">Unexpected</span><span class="nv"> </span><span class="s">system</span><span class="nv"> </span><span class="s">error&quot;</span>
<span class="w">    </span><span class="nt">http_status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">500</span>

<span class="w">  </span><span class="nt">AM-BAV-0502</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Bad</span><span class="nv"> </span><span class="s">Gateway</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">External</span><span class="nv"> </span><span class="s">service</span><span class="nv"> </span><span class="s">unavailable&quot;</span>
<span class="w">    </span><span class="nt">http_status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">502</span>

<span class="w">  </span><span class="nt">AM-BAV-0503</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Service</span><span class="nv"> </span><span class="s">Unavailable</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">System</span><span class="nv"> </span><span class="s">temporarily</span><span class="nv"> </span><span class="s">unavailable&quot;</span>
<span class="w">    </span><span class="nt">http_status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">503</span>

<span class="w">  </span><span class="nt">AM-BAV-0504</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Gateway</span><span class="nv"> </span><span class="s">Timeout</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">External</span><span class="nv"> </span><span class="s">service</span><span class="nv"> </span><span class="s">timeout&quot;</span>
<span class="w">    </span><span class="nt">http_status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">504</span>

<span class="w">  </span><span class="c1"># Business Logic Errors (4xx)</span>
<span class="w">  </span><span class="nt">AM-BAV-0100</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Failed</span><span class="nv"> </span><span class="s">to</span><span class="nv"> </span><span class="s">initiate</span><span class="nv"> </span><span class="s">bulk</span><span class="nv"> </span><span class="s">address</span><span class="nv"> </span><span class="s">validate</span><span class="nv"> </span><span class="s">request&quot;</span>
<span class="w">    </span><span class="nt">http_status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">400</span>

<span class="w">  </span><span class="nt">AM-BAV-0101</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Request</span><span class="nv"> </span><span class="s">processing</span><span class="nv"> </span><span class="s">failed&quot;</span>
<span class="w">    </span><span class="nt">http_status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">500</span>

<span class="w">  </span><span class="nt">AM-BAV-0102</span><span class="p">:</span>
<span class="w">    </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Address</span><span class="nv"> </span><span class="s">validation</span><span class="nv"> </span><span class="s">service</span><span class="nv"> </span><span class="s">unavailable&quot;</span>
<span class="w">    </span><span class="nt">http_status</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">502</span>
</code></pre></div>
<h3 id="error-handling-flow">Error Handling Flow<a class="headerlink" href="#error-handling-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Error Detection&quot;
        A[Authentication Error]
        B[Validation Error]
        C[Processing Error]
        D[System Error]
    end

    subgraph &quot;Error Processing&quot;
        E[Determine Error Type]
        F[Generate Error Code]
        G[Create Error Response]
        H[Log Error Details]
    end

    subgraph &quot;Error Response&quot;
        I[Set HTTP Status]
        J[Format Error JSON]
        K[Add Request Context]
        L[Return Response]
    end

    A --&gt; E
    B --&gt; E
    C --&gt; E
    D --&gt; E

    E --&gt; F
    F --&gt; G
    G --&gt; H

    H --&gt; I
    I --&gt; J
    J --&gt; K
    K --&gt; L

    style A fill:#ff6b6b,stroke:#e55656,color:#ffffff
    style E fill:#ffa726,stroke:#ff9800,color:#ffffff
    style L fill:#66bb6a,stroke:#4caf50,color:#ffffff
</code></pre></div>
<h2 id="performance-considerations">Performance Considerations<a class="headerlink" href="#performance-considerations" title="Permanent link">&para;</a></h2>
<h3 id="rate-limiting">Rate Limiting<a class="headerlink" href="#rate-limiting" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nt">rate_limiting</span><span class="p">:</span>
<span class="w">  </span><span class="nt">requests_per_minute</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span>
<span class="w">  </span><span class="nt">requests_per_hour</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
<span class="w">  </span><span class="nt">concurrent_requests</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">  </span><span class="nt">max_addresses_per_request</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>

<span class="nt">throttling</span><span class="p">:</span>
<span class="w">  </span><span class="nt">queue_size</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">500</span>
<span class="w">  </span><span class="nt">processing_timeout</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;15</span><span class="nv"> </span><span class="s">minutes&quot;</span>
<span class="w">  </span><span class="nt">retry_delay</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;30</span><span class="nv"> </span><span class="s">seconds&quot;</span>
</code></pre></div>
<h3 id="caching-strategy">Caching Strategy<a class="headerlink" href="#caching-strategy" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;caching_configuration&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;address_validation_cache&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;ttl&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;1 hour&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;max_entries&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">10000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;eviction_policy&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;LRU&quot;</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;token_cache&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;ttl&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;5 minutes&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;max_entries&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;service_discovery_cache&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;ttl&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;10 minutes&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;max_entries&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">50</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="performance-metrics">Performance Metrics<a class="headerlink" href="#performance-metrics" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nt">performance_metrics</span><span class="p">:</span>
<span class="w">  </span><span class="nt">response_time</span><span class="p">:</span>
<span class="w">    </span><span class="nt">target</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;&lt;</span><span class="nv"> </span><span class="s">5</span><span class="nv"> </span><span class="s">seconds&quot;</span>
<span class="w">    </span><span class="nt">sla</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;&lt;</span><span class="nv"> </span><span class="s">10</span><span class="nv"> </span><span class="s">seconds&quot;</span>

<span class="w">  </span><span class="nt">throughput</span><span class="p">:</span>
<span class="w">    </span><span class="nt">target</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;100</span><span class="nv"> </span><span class="s">requests/minute&quot;</span>
<span class="w">    </span><span class="nt">peak</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;500</span><span class="nv"> </span><span class="s">requests/minute&quot;</span>

<span class="w">  </span><span class="nt">availability</span><span class="p">:</span>
<span class="w">    </span><span class="nt">target</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;99.9%&quot;</span>
<span class="w">    </span><span class="nt">measurement_period</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;monthly&quot;</span>

<span class="w">  </span><span class="nt">error_rate</span><span class="p">:</span>
<span class="w">    </span><span class="nt">target</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;&lt;</span><span class="nv"> </span><span class="s">1%&quot;</span>
<span class="w">    </span><span class="nt">threshold</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;&lt;</span><span class="nv"> </span><span class="s">5%&quot;</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../03-deployment-configuration/">Deployment Configuration →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>