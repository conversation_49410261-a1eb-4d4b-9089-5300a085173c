import { NiFiClient } from '../nifi-client.js';
import { z } from 'zod';

type ToolRequest = {
  method: string;
  params?: {
    arguments?: Record<string, unknown>;
  };
};

type ToolResponse = {
  content: Array<{ type: string; text: string }>;
  isError?: boolean;
};

const toolSchema = z.object({
  method: z.string(),
  params: z.object({
    arguments: z.record(z.unknown())
  }).optional()
});

export const tools = {
  get_flow: {
    description: 'Retrieve the data flow configuration',
    inputSchema: {
      type: 'object',
      properties: {
        processGroupId: {
          type: 'string',
          description: 'ID of the process group (defaults to root)'
        }
      }
    }
  },
  get_connection_status: {
    description: 'Get status for a connection',
    inputSchema: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'The connection id'
        },
        nodewise: {
          type: 'boolean',
          description: 'Whether or not to include the breakdown per node. Optional, defaults to false'
        },
        clusterNodeId: {
          type: 'string',
          description: 'The id of the node where to get the status'
        }
      },
      required: ['id']
    }
  },
  get_connection_statistics: {
    description: 'Get statistics for a connection',
    inputSchema: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'The connection id'
        },
        nodewise: {
          type: 'boolean',
          description: 'Whether or not to include the breakdown per node. Optional, defaults to false'
        },
        clusterNodeId: {
          type: 'string',
          description: 'The id of the node where to get the statistics'
        }
      },
      required: ['id','clusterNodeId'],
    }
  },
  get_component_status: {
    description: 'Get status of a specific component',
    inputSchema: {
      type: 'object',
      properties: {
        componentId: {
          type: 'string',
          description: 'ID of the component'
        },
        componentType: {
          type: 'string',
          enum: ['processor', 'process_group', 'connection', 'input_port', 'output_port'],
          description: 'Type of the component'
        }
      },
      required: ['componentId', 'componentType']
    }
  },
  create_connection: {
    description: 'Create a connection in a process group',
    inputSchema: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'ID of the process group'
        }
      },
      required: ['id']
    }
  }
};

export const getFlow = (client: NiFiClient) => async (request: ToolRequest): Promise<ToolResponse> => {
  const { processGroupId = 'root' } = (request.params?.arguments || {}) as { processGroupId?: string };
  try {
    const response = await client.get(`/flow/process-groups/${processGroupId}`);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2)
        }
      ]
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      content: [
        {
          type: 'text',
          text: `Error retrieving flow: ${errorMessage}`
        }
      ],
      isError: true
    };
  }
};

export const getComponentStatus = (client: NiFiClient) => async (request: ToolRequest): Promise<ToolResponse> => {
  const { componentId, componentType } = (request.params?.arguments || {}) as { componentId: string; componentType: string };
  try {
    const response = await client.get(`/flow/history/components/${componentId}`);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2)
        }
      ]
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      content: [
        {
          type: 'text',
          text: `Error retrieving component status: ${errorMessage}`
        }
      ],
      isError: true
    };
  }
};

export const createConnection = (client: NiFiClient) => async (request: ToolRequest): Promise<ToolResponse> => {
  const { id } = (request.params?.arguments || {}) as {
    id: string;
  };
  try {
    const response = await client.post(`/process-groups/${id}/connections`);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2)
        }
      ]
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      content: [
        {
          type: 'text',
          text: `Error creating connection: ${errorMessage}`
        }
      ],
      isError: true
    };
  }
};

export const getConnectionStatistics = (client: NiFiClient) => async (request: ToolRequest): Promise<ToolResponse> => {
  const { id, nodewise = false, clusterNodeId } = (request.params?.arguments || {}) as {
    id: string;
    nodewise?: boolean;
    clusterNodeId: string;
  };
  try {
    const queryParams = new URLSearchParams();
    if (nodewise) {
      queryParams.append('nodewise', 'true');
    }
    if (clusterNodeId) {
      queryParams.append('clusterNodeId', clusterNodeId);
    }
    const url = `/flow/connections/${id}/statistics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await client.get(url);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2)
        }
      ]
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      content: [
        {
          type: 'text',
          text: `Error getting connection statistics: ${errorMessage}`
        }
      ],
      isError: true
    };
  }
};

export const getConnectionStatus = (client: NiFiClient) => async (request: ToolRequest): Promise<ToolResponse> => {
  const { id, nodewise = false, clusterNodeId } = (request.params?.arguments || {}) as {
    id: string;
    nodewise?: boolean;
    clusterNodeId: string;
  };
  try {
    const queryParams = new URLSearchParams();
    if (nodewise) {
      queryParams.append('nodewise', 'true');
    }
    if (clusterNodeId) {
      queryParams.append('clusterNodeId', clusterNodeId);
    }
    const url = `/flow/connections/${id}/status${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await client.get(url);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2)
        }
      ]
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      content: [
        {
          type: 'text',
          text: `Error getting connection status: ${errorMessage}`
        }
      ],
      isError: true
    };
  }
};
