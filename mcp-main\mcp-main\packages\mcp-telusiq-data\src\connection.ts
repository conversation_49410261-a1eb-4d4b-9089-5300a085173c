import { DatabaseConfig } from './config.js';

import type { Pool, PoolConfig, QueryResult, QueryResultRow } from 'pg';

type QueryOptions = {
  text: string;
  values?: any[];
};

export class DatabaseConnection {
  private pool: Pool | null = null;
  private config: DatabaseConfig;

  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  async connect(): Promise<void> {
    try {
      console.error('[Database] Initializing connection...');
      
      // Import pg module
      const pg = await import('pg');
      const Pool = pg.default.Pool;

      const poolConfig: PoolConfig = {
        host: 'localhost', // Connect to local port forward
        port: this.config.targetPort,
        user: this.config.dbUser,
        password: this.config.dbPassword,
        database: this.config.dbName,
        max: 5 // Maximum number of clients in the pool
      };

      console.error('[Database] Connecting with config:', {
        ...poolConfig,
        password: '[REDACTED]'
      });

      this.pool = new Pool(poolConfig);

      // Test the connection
      await this.pool.query('SELECT NOW()');
      console.error('[Database] Successfully connected to PostgreSQL');
    } catch (error) {
      console.error('[Database] Connection error:', error);
      throw error;
    }
  }

  async query<T = any>(sql: string, params?: any[]): Promise<any> {
    if (!this.pool) {
      throw new Error('Database connection not initialized');
    }

    // Ensure query is read-only
    const normalizedSql = sql.trim().toLowerCase();
    if (!normalizedSql.startsWith('select') && !normalizedSql.startsWith('show')) {
      throw new Error('Only SELECT and SHOW queries are allowed');
    }

    try {
      console.error('[Database] Executing query:', sql, params);
      const result = await this.pool.query(sql, params);
      console.error('[Database] Query completed, rows:', result.rowCount);
      return result;
    } catch (error) {
      console.error('[Database] Query error:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.pool) {
        await this.pool.end();
        this.pool = null;
      }
      console.error('[Database] Disconnected');
    } catch (error) {
      console.error('[Database] Error during disconnect:', error);
      throw error;
    }
  }
}
