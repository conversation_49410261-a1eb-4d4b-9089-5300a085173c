# @telus/mcp-vectors-query

A Model Context Protocol (MCP) server implementation for querying vector embeddings. This read-only server provides a search tool for performing semantic searches through vector operations. It connects to existing vector databases but does not modify or create new vectors.

## Overview

Vector search enables semantic searching beyond traditional keyword matching. By converting text into high-dimensional vector embeddings, the system can find content that is conceptually similar even when the exact terminology differs. This MCP server abstracts the complexities of vector operations, providing a simple interface for AI assistants and applications to perform semantic searches.

Key benefits include:
- **Semantic Understanding**: Find conceptually related content, not just keyword matches
- **Contextual Relevance**: Results ranked by semantic similarity
- **Simple Integration**: Easy to use with any MCP-compatible client

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-vectors-query
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for Claude Des<PERSON>):

   ```json
   {
     "mcpServers": {
       "vectors-query": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-vectors-query"],
         "env": {
           "VECTORS_API_BASE": "https://vectors.fuelix.ai",
           "OPENAI_API_BASE": "https://proxy.fuelix.ai",
           "VECTORS_USER_ID": "your_vectors_user_id",
           "VECTORS_KEY": "your_vectors_api_key",
           "OPENAI_API_KEY": "your_openai_api_key",
           "TURBOPUFFER_NAMESPACE": "your_namespace",
           "TURBOPUFFER_REGION": "your_region",
           "OPENAI_MODEL_ID": "text-embedding-3-small"
         }
       }
     }
   }
   ```

   Replace the placeholder values with your actual credentials and configuration.

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-vectors-query
   pnpm install
   ```

2. Configure environment variables:
   - Copy `.env.example` to `.env`
   - Fill in your credentials and configuration values

3. Build the server:

   ```bash
   pnpm build
   ```

4. Start the server:

   ```bash
   pnpm start
   ```

## Configuration

The following environment variables are required for the project:

### API Configuration
- `VECTORS_API_BASE`: The base URL for Vectors API requests (e.g., https://vectors.fuelix.ai)
- `OPENAI_API_BASE`: The base URL for OpenAI API requests (e.g., https://proxy.fuelix.ai)

### Authentication
- `VECTORS_USER_ID`: Your unique user ID for the Vectors API
- `VECTORS_KEY`: Your API key for authenticating with the Vectors API
- `OPENAI_API_KEY`: Your OpenAI API key for accessing OpenAI services

### Vector Store Configuration
- `TURBOPUFFER_NAMESPACE`: The namespace for your Turbopuffer operations
- `TURBOPUFFER_REGION`: The region where your Turbopuffer resources are located

### Model Configuration
- `OPENAI_MODEL_ID`: The OpenAI model to use for generating embeddings. This should match the model used to vectorize the documents in your vector database. Supported models:
  - `text-embedding-3-small`: Latest model, best balance of performance and cost
  - `text-embedding-3-large`: Higher accuracy but more expensive
  - `text-embedding-ada-002`: Legacy model, still supported

Each model has different characteristics:
- text-embedding-3-small: 1536 dimensions, best for most use cases
- text-embedding-3-large: 3072 dimensions, higher accuracy for specialized tasks
- text-embedding-ada-002: 1536 dimensions, legacy model

IMPORTANT: Ensure that the OPENAI_MODEL_ID matches the model used to create the vectors in your database. Mixing different models can lead to poor search results due to incompatible vector spaces.

## Available Tools

The server provides the following MCP tools:

### search_vectors

Performs a semantic search using vector embeddings.

**Parameters:**
- `query` (string, required): The text query to search for
- `top_k` (number, optional, default: 5): Maximum number of results to return

**Returns:**
An array of search results, each containing:
- `document`: Object containing document details
  - `id`: Unique identifier of the document
  - `filename`: Name of the document file
  - `content`: Full text content of the document
- `similarity`: Similarity score between 0 and 1, where higher values indicate greater similarity

**Example Usage:**
```javascript
// Example MCP client code
const results = await mcpClient.callTool('vectors-query', 'search_vectors', {
  query: "What is vector search?",
  top_k: 3
});

// Example results
[
  {
    "document": {
      "id": "doc123",
      "filename": "vector_search_intro.md",
      "content": "Vector search is a technique that..."
    },
    "similarity": 0.89
  },
  // Additional results...
]
```

## Usage Examples

### Basic Search
```javascript
// Search for documents about vector databases
const results = await mcpClient.callTool('vectors-query', 'search_vectors', {
  query: "How do vector databases work?"
});
```

### Limiting Results
```javascript
// Get only the top 2 most relevant results
const results = await mcpClient.callTool('vectors-query', 'search_vectors', {
  query: "Semantic search techniques",
  top_k: 2
});
```

## Performance Considerations

- **Query Complexity**: Longer, more complex queries may take more time to process
- **Result Size**: Requesting a large number of results (high top_k) increases processing time
- **Model Selection**: Different embedding models offer different trade-offs between accuracy and performance
- **Vector Database Size**: Performance may degrade with very large vector databases

## Troubleshooting

Common issues and solutions:

1. **Invalid Results**: Ensure the OPENAI_MODEL_ID matches the model used to create the vectors in your database
2. **Authentication Errors**: Verify all API keys and credentials are correctly configured
3. **Missing Results**: Check that your vector database contains the expected documents
4. **Slow Performance**: Consider using a smaller embedding model or reducing the top_k parameter

## Integration with Vector Management Tools

This MCP server can be used in conjunction with [fuelix-vectors-mgmt](https://github.com/telus/fuelix-vectors-mgmt), which provides tools for processing documents, generating embeddings, and managing vector namespaces.

### Vector Management Features

The fuelix-vectors-mgmt repository includes:

1. **vectorize_and_search.py**: Processes text-based files, generates embeddings, and stores them in a vector database
2. **manage_vectors.py**: Provides a menu-driven interface for managing vector namespaces

These tools can be used to:
- Create and populate vector databases that this MCP server can query
- Manage the vector namespaces used by this MCP server
- Process various file types (Markdown, code, text) into searchable vectors
- Evaluate recall performance of your vector search setup

Both tools use the same environment variables for configuration, making it easy to use them together with this MCP server.

For more information on setting up and using these vector management tools, visit the [fuelix-vectors-mgmt repository](https://github.com/telus/fuelix-vectors-mgmt).

## Dependencies

Main dependencies include:
- [@modelcontextprotocol/sdk](https://www.npmjs.com/package/@modelcontextprotocol/sdk) - MCP SDK for server implementation
- [@turbopuffer/turbopuffer](https://www.npmjs.com/package/@turbopuffer/turbopuffer) - Vector operations library
- [openai](https://www.npmjs.com/package/openai) - OpenAI API client
- [axios](https://www.npmjs.com/package/axios) - HTTP client
- [dotenv](https://www.npmjs.com/package/dotenv) - Environment configuration
