# Project Brief: MCP Vectors Query

## Overview
The MCP Vectors Query project is a Model Context Protocol (MCP) server implementation that provides vector search capabilities. It allows semantic searching through vector embeddings by connecting to existing vector databases. This is a read-only server that does not modify or create new vectors.

## Core Objectives
- Provide a simple, efficient way to perform semantic searches using vector operations
- Integrate with the Model Context Protocol ecosystem
- Connect to existing vector databases without modifying them
- Offer a standardized interface for vector search operations

## Key Features
- Semantic search through vector embeddings
- Integration with OpenAI for generating embeddings
- Connection to Turbopuffer/Vectors API for vector storage and retrieval
- MCP-compliant server implementation

## Target Users
- AI assistants using the Model Context Protocol
- Developers building applications that need semantic search capabilities
- TELUS teams requiring vector search functionality

## Success Criteria
- Successful integration with MCP clients
- Accurate and relevant search results
- Efficient performance with minimal latency
- Secure handling of API credentials and data

## Constraints
- Read-only operations (no vector creation or modification)
- Requires proper configuration of environment variables
- Depends on external services (OpenAI API, Vectors API)
