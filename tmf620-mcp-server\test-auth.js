#!/usr/bin/env node

// Simple script to test authentication with the TMF620 API
const axios = require('axios');
const https = require('https');
const dotenv = require('dotenv');
const { HttpsProxyAgent } = require('https-proxy-agent');

// Load environment variables
console.log('Current working directory:', process.cwd());
const envPath = require('path').resolve(__dirname, '.env');
console.log('Loading .env file from:', envPath);
const dotenvResult = dotenv.config({ path: envPath });
if (dotenvResult.error) {
  console.error('Error loading .env file:', dotenvResult.error.message);
} else {
  console.log('.env file loaded successfully');
  console.log('Loaded environment variables:', Object.keys(dotenvResult.parsed || {}));
}

// Log configuration (without sensitive data)
console.log('Testing authentication with the following configuration:');
console.log('OAUTH_CLIENT_ID:', process.env.OAUTH_CLIENT_ID);
console.log('OAUTH_TOKEN_URL:', process.env.OAUTH_TOKEN_URL);
console.log('OAUTH_SCOPE:', process.env.OAUTH_SCOPE);
// Don't log OAUTH_CLIENT_SECRET for security

// Check for proxy settings in environment
console.log('\nChecking for proxy settings in environment:');
console.log('HTTP_PROXY:', process.env.HTTP_PROXY || '(not set)');
console.log('HTTPS_PROXY:', process.env.HTTPS_PROXY || '(not set)');
console.log('http_proxy:', process.env.http_proxy || '(not set)');
console.log('https_proxy:', process.env.https_proxy || '(not set)');

// Try to detect TELUS proxy settings
// Common TELUS proxy settings
const TELUS_PROXIES = [
  'http://proxy.tsl.telus.com:8080',
  'http://proxy.telus.com:8080',
  'http://proxy-dev.tsl.telus.com:8080'
];

console.log('\nTrying TELUS proxy settings...');
for (const proxy of TELUS_PROXIES) {
  console.log(`- ${proxy}`);
}

async function testAuthentication() {
  try {
    console.log('Attempting to authenticate...');
    
    // Prepare token request parameters - include client_id and client_secret in the body
    const params = new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: process.env.OAUTH_CLIENT_ID,
      client_secret: process.env.OAUTH_CLIENT_SECRET,
      scope: process.env.OAUTH_SCOPE,
    });
    
    console.log('Making token request to:', process.env.OAUTH_TOKEN_URL);
    
    // Get proxy configuration from environment variables
    const getProxyConfig = () => {
      const proxyUrl = process.env.https_proxy || process.env.HTTPS_PROXY || process.env.http_proxy || process.env.HTTP_PROXY;
      if (!proxyUrl) {
        console.log('No proxy configuration found in environment variables');
        return false;
      }

      try {
        const url = new URL(proxyUrl);
        console.log('Using proxy configuration:', {
          host: url.hostname,
          port: url.port,
          protocol: url.protocol
        });
        
        return {
          host: url.hostname,
          port: parseInt(url.port, 10),
          protocol: url.protocol.replace(':', '')
        };
      } catch (error) {
        console.warn('Failed to parse proxy URL:', proxyUrl, error);
        return false;
      }
    };

    // Skip proxy for now since we're having issues with it
    console.log('Skipping proxy configuration due to connectivity issues');
    // Disable SSL verification for testing
    console.log('Disabling SSL verification for testing (not recommended for production)');
    let httpsAgent = new https.Agent({
      rejectUnauthorized: false // Only for debugging - remove in production
    });
    
    // Configure axios instance with proxy and SSL settings
    const axiosConfig = {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      timeout: 60000, // 60 seconds
      httpsAgent: httpsAgent
    };

    console.log('Request configuration:', {
      url: process.env.OAUTH_TOKEN_URL,
      method: 'POST',
      timeout: axiosConfig.timeout,
      proxy: axiosConfig.proxy ? 'Configured' : 'None',
      headers: {
        'Content-Type': axiosConfig.headers['Content-Type']
      },
      params: 'grant_type=client_credentials&client_id=[REDACTED]&client_secret=[REDACTED]&scope=' + process.env.OAUTH_SCOPE
    });

    // Request new token
    const response = await axios.post(
      process.env.OAUTH_TOKEN_URL,
      params,
      axiosConfig
    );

    console.log('Authentication successful!');
    console.log('Response status:', response.status);
    console.log('Token type:', response.data.token_type);
    console.log('Expires in:', response.data.expires_in, 'seconds');
    // Don't log the actual token for security
    console.log('Access token received (first few characters):', response.data.access_token.substring(0, 10) + '...');
    
    return true;
  } catch (error) {
    console.error('Authentication failed:');
    
    if (axios.isAxiosError(error)) {
      console.error('Error code:', error.code);
      console.error('Error message:', error.message);
      
      if (error.response) {
        // The server responded with a status code outside the 2xx range
        console.error('Response status:', error.response.status);
        console.error('Response headers:', error.response.headers);
        console.error('Response data:', error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        console.error('No response received');
        console.error('Request details:', error.request._currentUrl);
      }
    } else {
      // Something else happened
      console.error('Unexpected error:', error);
    }
    
    return false;
  }
}

// Test API connection
async function testApiConnection() {
  try {
    // First authenticate to get a token
    console.log('\nStep 1: Authenticating to get a token...');
    
    // Prepare token request parameters - include client_id and client_secret in the body
    const params = new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: process.env.OAUTH_CLIENT_ID,
      client_secret: process.env.OAUTH_CLIENT_SECRET,
      scope: process.env.OAUTH_SCOPE,
    });
    
    // Get proxy configuration from environment variables
    const getProxyConfig = () => {
      const proxyUrl = process.env.https_proxy || process.env.HTTPS_PROXY || process.env.http_proxy || process.env.HTTP_PROXY;
      if (!proxyUrl) {
        console.log('No proxy configuration found in environment variables');
        return false;
      }

      try {
        const url = new URL(proxyUrl);
        console.log('Using proxy configuration:', {
          host: url.hostname,
          port: url.port,
          protocol: url.protocol
        });
        
        return {
          host: url.hostname,
          port: parseInt(url.port, 10),
          protocol: url.protocol.replace(':', '')
        };
      } catch (error) {
        console.warn('Failed to parse proxy URL:', proxyUrl, error);
        return false;
      }
    };

    // Skip proxy for now since we're having issues with it
    console.log('Skipping proxy configuration due to connectivity issues');
    let httpsAgent = new https.Agent({
      rejectUnauthorized: false // Only for debugging - remove in production
    });
    
    // Configure axios instance with proxy and SSL settings
    const authConfig = {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      timeout: 60000, // 60 seconds
      httpsAgent: httpsAgent
    };

    console.log('Request configuration:', {
      url: process.env.OAUTH_TOKEN_URL,
      method: 'POST',
      timeout: authConfig.timeout,
      proxy: authConfig.proxy ? 'Configured' : 'None',
      headers: {
        'Content-Type': authConfig.headers['Content-Type']
      },
      params: 'grant_type=client_credentials&client_id=[REDACTED]&client_secret=[REDACTED]&scope=' + process.env.OAUTH_SCOPE
    });

    // Request new token
    const authResponse = await axios.post(
      process.env.OAUTH_TOKEN_URL,
      params,
      authConfig
    );

    const token = authResponse.data.access_token;
    console.log('Token obtained successfully!');
    
    // Now test the API connection
    console.log('\nStep 2: Testing API connection...');
    
    // Use a public API endpoint for testing instead of the private one
    // This is just for testing connectivity with the same auth token
    const apiUrl = 'https://apigw-st.telus.com/st/token';
    console.log('Using public API URL for testing:', apiUrl);
    
    // Use the same proxy agent for API requests
    // Use 'test' as the environment parameter to match the environment in the URL
    const apiConfig = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'env': 'itn05'
      },
      timeout: 30000, // 30 seconds
      httpsAgent: httpsAgent
    };

    console.log('API request configuration:', {
      url: apiUrl,
      method: 'POST',
      timeout: apiConfig.timeout,
      proxy: apiConfig.proxy ? 'Configured' : 'None',
      headers: {
        'Authorization': 'Bearer [REDACTED]',
        'env': apiConfig.headers['env'],
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      params: 'grant_type=client_credentials&client_id=[REDACTED]&client_secret=[REDACTED]&scope=' + process.env.OAUTH_SCOPE
    });
    
    // Make a POST request instead of GET since the token endpoint only accepts POST
    const testParams = new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: process.env.OAUTH_CLIENT_ID,
      client_secret: process.env.OAUTH_CLIENT_SECRET,
      scope: process.env.OAUTH_SCOPE,
    });
    
    const apiResponse = await axios.post(
      apiUrl,
      testParams,
      apiConfig
    );
    
    console.log('API connection successful!');
    console.log('Response status:', apiResponse.status);
    console.log('Response data (sample):', JSON.stringify(apiResponse.data).substring(0, 200) + '...');
    
    return true;
  } catch (error) {
    console.error('API connection test failed:');
    
    if (axios.isAxiosError(error)) {
      console.error('Error code:', error.code);
      console.error('Error message:', error.message);
      
      if (error.response) {
        // The server responded with a status code outside the 2xx range
        console.error('Response status:', error.response.status);
        console.error('Response headers:', error.response.headers);
        console.error('Response data:', error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        console.error('No response received');
        console.error('Request details:', error.request._currentUrl);
      }
    } else {
      // Something else happened
      console.error('Unexpected error:', error);
    }
    
    return false;
  }
}

// Run the tests
async function runTests() {
  console.log('=== TMF620 Authentication Test ===\n');
  
  const authSuccess = await testAuthentication();
  
  if (authSuccess) {
    console.log('\n=== Authentication test PASSED ===');
    
    console.log('\n=== TMF620 API Connection Test ===\n');
    const apiSuccess = await testApiConnection();
    
    if (apiSuccess) {
      console.log('\n=== API connection test PASSED ===');
      console.log('\nAll tests PASSED! The TMF620 MCP server should work correctly.');
    } else {
      console.log('\n=== API connection test FAILED ===');
      console.log('\nAuthentication works, but API connection failed. Check your API URL and environment settings.');
    }
  } else {
    console.log('\n=== Authentication test FAILED ===');
    console.log('\nFix authentication issues before proceeding.');
  }
}

runTests().catch(error => {
  console.error('Test script error:', error);
  process.exit(1);
});
