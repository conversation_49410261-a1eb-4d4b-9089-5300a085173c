import type { PlopTypes } from "@turbo/gen";

export default function generator(plop: PlopTypes.NodePlopAPI): void {
  plop.setGenerator("mcp-server", {
    description: "Generate a new MCP server package",
    prompts: [
      {
        type: "input",
        name: "name",
        message: "What is the name of your MCP server? (without mcp- prefix)",
        validate: (input: string) => {
          if (input.startsWith("mcp-")) {
            return "Please enter the name without the mcp- prefix";
          }
          if (!/^[a-z0-9-]+$/.test(input)) {
            return "Name must contain only lowercase letters, numbers, and hyphens";
          }
          return true;
        },
      },
      {
        type: "input",
        name: "description",
        message: "What is the description of your MCP server?",
        validate: (input: string) => {
          if (!input.trim()) {
            return "Description is required";
          }
          return true;
        },
      },
    ],
    actions: [
      {
        type: "add",
        path: "packages/mcp-{{name}}/package.json",
        templateFile: "templates/package.json.hbs",
      },
      {
        type: "add",
        path: "packages/mcp-{{name}}/tsconfig.json",
        templateFile: "templates/tsconfig.json.hbs",
      },
      {
        type: "add",
        path: "packages/mcp-{{name}}/src/index.ts",
        templateFile: "templates/index.ts.hbs",
      },
      {
        type: "add",
        path: "packages/mcp-{{name}}/README.md",
        templateFile: "templates/README.md.hbs",
      },
    ],
  });
}
