# TMF620 API Connection Analysis

## Overview

This document provides a detailed analysis of the API connection issues in the TMF620 MCP server and the solutions implemented to resolve them.

## Problem Statement

The TMF620 MCP server was unable to connect to the TMF620 API, resulting in failed requests and inability to retrieve product offering data. This prevented the server from functioning properly as an MCP tool provider.

## Root Cause Analysis

### 1. API Endpoint Configuration

**Issue:**
The TMF620 server was configured to use private API endpoints that are only accessible within the TELUS internal network. These endpoints follow the pattern `apigw-private-nane-np-001.tsl.telus.com` which are not accessible from outside the TELUS network.

**Impact:**
Attempts to connect to these private endpoints from outside the TELUS network would fail with connection timeouts or network unreachable errors.

**Solution:**
Updated the API URLs to use public endpoints (`https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2`) that are accessible from anywhere with proper authentication.

### 2. Proxy Configuration

**Issue:**
The TMF620 server did not explicitly disable proxies in its HTTP requests. In corporate environments, HTTP requests are often routed through proxy servers by default.

**Impact:**
Requests to the API were being routed through corporate proxies, which may have been blocking or modifying the requests, leading to authentication or connection failures.

**Solution:**
- Added explicit proxy settings to disable proxies by setting HTTP_PROXY and HTTPS_PROXY to empty values
- Set NO_PROXY to "*" to bypass proxy for all connections
- Added `proxy: false` to axios request configurations to ensure direct connections

### 3. OAuth Authentication

**Issue:**
The TMF620 server was missing critical OAuth configuration parameters:
- No OAuth token URL specified
- Missing OAuth scope
- Incomplete implementation of the client credentials flow

**Impact:**
Without proper OAuth configuration, the server could not authenticate with the API, resulting in 401 Unauthorized errors.

**Solution:**
- Added OAuth token URL (`https://apigw-st.telus.com/st/token`)
- Added OAuth scope ("4823")
- Implemented proper client credentials flow
- Ensured Authorization header is correctly formatted with Base64 encoded client ID and secret

### 4. Environment Header

**Issue:**
The TMF620 server was missing the required 'env' header with value 'itn05' in API requests.

**Impact:**
Without the correct environment header, the API would return a 400 error with the message "Environment not supported".

**Solution:**
- Added 'env': 'itn05' header to all API requests
- Updated the makeRequest method in tmf620Api.ts to include this header
- Verified that the API connection works correctly with this header

### 5. Error Handling and Logging

**Issue:**
The TMF620 server had insufficient error handling and logging, making it difficult to diagnose the root causes of connection failures.

**Impact:**
Troubleshooting was hindered by lack of detailed error information, leading to prolonged debugging cycles.

**Solution:**
- Added comprehensive error logging
- Improved error context with detailed information about request and response
- Implemented proper error propagation to ensure errors are not silently caught

## Implementation Details

### 1. Environment Configuration Updates

Updated the `.env` file with the following changes:

```
# API URLs
DEV_API_URL=https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2/productOffering
TEST_API_URL=https://apigw-st.telus.com/marketsales/b2bproductcatalogmanagement/v2/productOffering
PROD_API_URL=https://prod-api-url.example.com
TEST_QUOTE_ID=a2db6e1a-27ea-419e-a7a5-10575717f8b7

# Proxy Configuration
# Disable proxy to allow direct connections to the API
HTTP_PROXY=
HTTPS_PROXY=
NO_PROXY=*

# OAuth Configuration
OAUTH_CLIENT_ID=your-client-id
OAUTH_CLIENT_SECRET=your-client-secret
OAUTH_TOKEN_URL=https://apigw-st.telus.com/st/token
OAUTH_SCOPE=4823
```

### 2. API Request Updates

Updated the makeRequest method in tmf620Api.ts to include the 'env': 'itn05' header:

```typescript
private async makeRequest<T>(
  method: 'get' | 'post',
  url: string,
  environment: Environment,
  params?: Record<string, any>,
  data?: any
): Promise<T> {
  logger.debug(`Making ${method.toUpperCase()} request`, { url, environment, params, data });

  try {
    // Wait for a token from the rate limiter
    await this.rateLimiter.waitForToken();

    logger.startTimer('getAuthHeader');
    const authHeader = await this.authManager.getAuthHeader();
    logger.endTimer('getAuthHeader');

    const apiUrl = this.getApiUrl(environment);
    const fullUrl = `${apiUrl}${url}`;
    
    logger.debug('Request details', { fullUrl, headers: { ...authHeader, 'env': 'itn05' } });

    logger.startTimer('apiRequest');
    const response = await this.axiosInstance.request<T | ApiErrorData>({
      method,
      url: fullUrl,
      headers: {
        ...authHeader,
        'env': 'itn05', // Use the required environment value
      },
      params,
      data,
      timeout: this.REQUEST_TIMEOUT,
      proxy: false // Explicitly disable proxy for this request
    });
    const requestDuration = logger.endTimer('apiRequest');
    
    // Response handling...
  } catch (error) {
    // Error handling...
  }
}
```

### 3. Test Script Updates

Updated the test-api-connection.js script to include the 'env': 'itn05' header:

```javascript
const response = await axios.get(apiUrl, {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Accept': 'application/json',
    'env': 'itn05'  // Add the environment parameter
  },
  proxy: false
});
```

## Testing and Verification

Created a test script to verify the API connection is working correctly:

```javascript
// Function to get an OAuth token
async function getOAuthToken() {
  console.log(`\n${colors.blue}Getting OAuth token...${colors.reset}`);
  
  const tokenUrl = process.env.OAUTH_TOKEN_URL;
  const clientId = process.env.OAUTH_CLIENT_ID;
  const clientSecret = process.env.OAUTH_CLIENT_SECRET;
  const scope = process.env.OAUTH_SCOPE;
  
  const params = new URLSearchParams();
  params.append('grant_type', 'client_credentials');
  params.append('scope', scope);
  
  try {
    console.log(`${colors.blue}Requesting token from ${tokenUrl}${colors.reset}`);
    
    const response = await axios.post(tokenUrl, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`
      },
      proxy: false
    });
    
    console.log(`${colors.green}Successfully obtained OAuth token${colors.reset}`);
    return response.data.access_token;
  } catch (error) {
    // Error handling...
    throw error;
  }
}

// Function to test the API connection
async function testApiConnection(token) {
  console.log(`\n${colors.blue}Testing API connection...${colors.reset}`);
  
  // Use the TEST_QUOTE_ID to get a specific product offering
  const quoteId = process.env.TEST_QUOTE_ID;
  const apiUrl = `${process.env.DEV_API_URL}/${quoteId}`;
  
  try {
    console.log(`${colors.blue}Requesting data from ${apiUrl}${colors.reset}`);
    console.log(`${colors.blue}Using TEST_QUOTE_ID: ${quoteId}${colors.reset}`);
    
    const response = await axios.get(apiUrl, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
        'env': 'itn05'  // Add the environment parameter
      },
      proxy: false
    });
    
    console.log(`${colors.green}Successfully connected to the API${colors.reset}`);
    // ...
    return response.data;
  } catch (error) {
    // Error handling...
    throw error;
  }
}
```

## Lessons Learned

1. **API Endpoint Selection**: Always use public API endpoints for services that need to be accessed from outside the corporate network.

2. **Proxy Configuration**: In corporate environments, explicitly disable proxies for direct API connections to avoid routing issues.

3. **OAuth Implementation**: Ensure all required OAuth parameters are properly configured and the client credentials flow is correctly implemented.

4. **Required Headers**: Always check API documentation for required headers. In this case, the 'env': 'itn05' header was critical for the API to function correctly.

5. **Error Handling**: Implement comprehensive error handling and logging to facilitate troubleshooting.

6. **Configuration Sharing**: When multiple services use the same API credentials, consider centralizing the configuration to ensure consistency.

## Conclusion

The API connection issues in the TMF620 MCP server were resolved by:

1. Updating API endpoints from private to public
2. Explicitly disabling proxies for direct connections
3. Completing the OAuth configuration
4. Adding the required 'env': 'itn05' header to all API requests
5. Enhancing error handling and logging

These changes have enabled the TMF620 MCP server to successfully connect to the API and function as intended.

## Next Steps

1. **Documentation**: Update the server documentation to include information about the required 'env': 'itn05' header.

2. **Monitoring**: Implement monitoring to detect and alert on API connection issues.

3. **Error Handling**: Continue to improve error handling and logging to make troubleshooting easier.

4. **Configuration Management**: Consider implementing a centralized configuration management system to ensure consistency across services.

5. **Testing**: Implement automated tests to verify API connectivity and functionality.
