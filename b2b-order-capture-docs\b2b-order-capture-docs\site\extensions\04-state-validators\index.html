
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/extensions/04-state-validators/">
      
      
        <link rel="prev" href="../03-bandwidth-validators/">
      
      
        <link rel="next" href="../05-development-guide/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>State Validators - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#state-validators" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              State Validators
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" checked>
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#state-validation-overview" class="md-nav__link">
    <span class="md-ellipsis">
      State Validation Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="State Validation Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#state-machine-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      State Machine Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#validation-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#telusstatemachineextensionvalidator" class="md-nav__link">
    <span class="md-ellipsis">
      TelusStateMachineExtensionValidator
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusStateMachineExtensionValidator">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Core Implementation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#state-transition-rules" class="md-nav__link">
    <span class="md-ellipsis">
      State Transition Rules
    </span>
  </a>
  
    <nav class="md-nav" aria-label="State Transition Rules">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-rule-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Business Rule Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#business-logic-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Business Logic Validation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Business Logic Validation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rule-engine-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Rule Engine Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-and-customization" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration and Customization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration and Customization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#dynamic-state-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Dynamic State Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#state-validation-overview" class="md-nav__link">
    <span class="md-ellipsis">
      State Validation Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="State Validation Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#state-machine-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      State Machine Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#validation-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#telusstatemachineextensionvalidator" class="md-nav__link">
    <span class="md-ellipsis">
      TelusStateMachineExtensionValidator
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusStateMachineExtensionValidator">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Core Implementation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#state-transition-rules" class="md-nav__link">
    <span class="md-ellipsis">
      State Transition Rules
    </span>
  </a>
  
    <nav class="md-nav" aria-label="State Transition Rules">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-rule-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Business Rule Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#business-logic-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Business Logic Validation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Business Logic Validation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rule-engine-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Rule Engine Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-and-customization" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration and Customization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration and Customization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#dynamic-state-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Dynamic State Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="state-validators">State Validators<a class="headerlink" href="#state-validators" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#state-validation-overview">State Validation Overview</a></li>
<li><a href="#telusstatemachineextensionvalidator">TelusStateMachineExtensionValidator</a></li>
<li><a href="#state-transition-rules">State Transition Rules</a></li>
<li><a href="#business-logic-validation">Business Logic Validation</a></li>
<li><a href="#configuration-and-customization">Configuration and Customization</a></li>
</ul>
<h2 id="state-validation-overview">State Validation Overview<a class="headerlink" href="#state-validation-overview" title="Permanent link">&para;</a></h2>
<h3 id="state-machine-architecture">State Machine Architecture<a class="headerlink" href="#state-machine-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>stateDiagram-v2
    [*] --&gt; DRAFT
    DRAFT --&gt; SUBMITTED: submit()
    DRAFT --&gt; CANCELLED: cancel()

    SUBMITTED --&gt; APPROVED: approve()
    SUBMITTED --&gt; REJECTED: reject()
    SUBMITTED --&gt; CANCELLED: cancel()

    APPROVED --&gt; ORDERED: createOrder()
    APPROVED --&gt; CANCELLED: cancel()

    REJECTED --&gt; DRAFT: revise()
    REJECTED --&gt; CANCELLED: cancel()

    ORDERED --&gt; COMPLETED: complete()
    ORDERED --&gt; CANCELLED: cancel()

    COMPLETED --&gt; [*]
    CANCELLED --&gt; [*]

    note right of DRAFT
        Initial state
        Editable
    end note

    note right of SUBMITTED
        Under review
        Read-only
    end note

    note right of APPROVED
        Ready for order
        Can create order
    end note

    note right of COMPLETED
        Final state
        Archived
    end note
</code></pre></div>
<h3 id="validation-flow">Validation Flow<a class="headerlink" href="#validation-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant QE as Quote Engine
    participant SMV as State Machine Validator
    participant BRE as Business Rules Engine
    participant AE as Authorization Engine
    participant AL as Audit Logger

    QE-&gt;&gt;SMV: validateStateTransition(context)
    SMV-&gt;&gt;SMV: validateTransitionAllowed(from, to)
    SMV-&gt;&gt;BRE: validateBusinessRules(context)
    BRE--&gt;&gt;SMV: BusinessRuleResult
    SMV-&gt;&gt;AE: validateUserPermissions(user, transition)
    AE--&gt;&gt;SMV: AuthorizationResult
    SMV-&gt;&gt;SMV: aggregateValidationResults()
    SMV--&gt;&gt;QE: ValidationResult

    alt Validation Successful
        QE-&gt;&gt;AL: logStateTransition(context)
        QE-&gt;&gt;QE: executeStateTransition()
    else Validation Failed
        QE-&gt;&gt;AL: logValidationFailure(context)
        QE--&gt;&gt;QE: rejectStateTransition()
    end
</code></pre></div>
<h2 id="telusstatemachineextensionvalidator">TelusStateMachineExtensionValidator<a class="headerlink" href="#telusstatemachineextensionvalidator" title="Permanent link">&para;</a></h2>
<h3 id="core-implementation">Core Implementation<a class="headerlink" href="#core-implementation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusStateMachineExtensionValidator</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">StateMachineExtensionValidator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ValidationUtils</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">CommonUtils</span><span class="w"> </span><span class="n">commonUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">PluginConfigurationProperties</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">BusinessRulesEngine</span><span class="w"> </span><span class="n">businessRulesEngine</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">AuthorizationService</span><span class="w"> </span><span class="n">authorizationService</span><span class="p">;</span>

<span class="w">    </span><span class="c1">// Define allowed state transitions</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">QuoteState</span><span class="p">,</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">QuoteState</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">ALLOWED_TRANSITIONS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">DRAFT</span><span class="p">,</span><span class="w"> </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="n">QuoteState</span><span class="p">.</span><span class="na">SUBMITTED</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">CANCELLED</span><span class="p">),</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">SUBMITTED</span><span class="p">,</span><span class="w"> </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="n">QuoteState</span><span class="p">.</span><span class="na">APPROVED</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">REJECTED</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">CANCELLED</span><span class="p">),</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">APPROVED</span><span class="p">,</span><span class="w"> </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="n">QuoteState</span><span class="p">.</span><span class="na">ORDERED</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">CANCELLED</span><span class="p">),</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">REJECTED</span><span class="p">,</span><span class="w"> </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="n">QuoteState</span><span class="p">.</span><span class="na">DRAFT</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">CANCELLED</span><span class="p">),</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">ORDERED</span><span class="p">,</span><span class="w"> </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="n">QuoteState</span><span class="p">.</span><span class="na">COMPLETED</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">CANCELLED</span><span class="p">),</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">COMPLETED</span><span class="p">,</span><span class="w"> </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(),</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">CANCELLED</span><span class="p">,</span><span class="w"> </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">()</span>
<span class="w">    </span><span class="p">);</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">TelusStateMachineExtensionValidator</span><span class="p">(</span>
<span class="w">            </span><span class="n">ValidationUtils</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">,</span>
<span class="w">            </span><span class="n">CommonUtils</span><span class="w"> </span><span class="n">commonUtils</span><span class="p">,</span>
<span class="w">            </span><span class="n">PluginConfigurationProperties</span><span class="w"> </span><span class="n">config</span><span class="p">,</span>
<span class="w">            </span><span class="n">BusinessRulesEngine</span><span class="w"> </span><span class="n">businessRulesEngine</span><span class="p">,</span>
<span class="w">            </span><span class="n">AuthorizationService</span><span class="w"> </span><span class="n">authorizationService</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">validationUtils</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">commonUtils</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">commonUtils</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">config</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">businessRulesEngine</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">businessRulesEngine</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">authorizationService</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">authorizationService</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validateStateTransition</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Validating state transition for quote: {} from {} to {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getFromState</span><span class="p">(),</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getToState</span><span class="p">());</span>

<span class="w">        </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">quoteId</span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">validatorName</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="na">getClass</span><span class="p">().</span><span class="na">getSimpleName</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">fromState</span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getFromState</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">toState</span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getToState</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Validate transition is allowed</span>
<span class="w">            </span><span class="n">validateTransitionAllowed</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate user permissions</span>
<span class="w">            </span><span class="n">validateUserPermissions</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate business rules</span>
<span class="w">            </span><span class="n">validateBusinessRules</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate quote completeness for specific transitions</span>
<span class="w">            </span><span class="n">validateQuoteCompleteness</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate timing constraints</span>
<span class="w">            </span><span class="n">validateTimingConstraints</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate approval requirements</span>
<span class="w">            </span><span class="n">validateApprovalRequirements</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;State transition validation completed for quote: {} - Valid: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                     </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="n">result</span><span class="p">.</span><span class="na">isValid</span><span class="p">());</span>

<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">result</span><span class="p">;</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error validating state transition for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">resultBuilder</span>
<span class="w">                </span><span class="p">.</span><span class="na">addError</span><span class="p">(</span><span class="s">&quot;VALIDATION_ERROR&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;State transition validation failed: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateTransitionAllowed</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                         </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">fromState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getFromState</span><span class="p">();</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">toState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getToState</span><span class="p">();</span>

<span class="w">        </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">QuoteState</span><span class="o">&gt;</span><span class="w"> </span><span class="n">allowedStates</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ALLOWED_TRANSITIONS</span><span class="p">.</span><span class="na">get</span><span class="p">(</span><span class="n">fromState</span><span class="p">);</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">allowedStates</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="o">!</span><span class="n">allowedStates</span><span class="p">.</span><span class="na">contains</span><span class="p">(</span><span class="n">toState</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;INVALID_STATE_TRANSITION&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;Transition from %s to %s is not allowed&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">fromState</span><span class="p">,</span><span class="w"> </span><span class="n">toState</span><span class="p">)</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Check for configuration overrides</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isStrictTransitionsEnabled</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">validateStrictTransitionRules</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;State transition allowed: {} -&gt; {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">fromState</span><span class="p">,</span><span class="w"> </span><span class="n">toState</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateUserPermissions</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                       </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">userId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getUserId</span><span class="p">();</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">fromState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getFromState</span><span class="p">();</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">toState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getToState</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Check if user has permission for this transition</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">authorizationService</span><span class="p">.</span><span class="na">hasTransitionPermission</span><span class="p">(</span><span class="n">userId</span><span class="p">,</span><span class="w"> </span><span class="n">fromState</span><span class="p">,</span><span class="w"> </span><span class="n">toState</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;INSUFFICIENT_PERMISSIONS&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;User %s does not have permission to transition from %s to %s&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                             </span><span class="n">userId</span><span class="p">,</span><span class="w"> </span><span class="n">fromState</span><span class="p">,</span><span class="w"> </span><span class="n">toState</span><span class="p">)</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate role-specific permissions</span>
<span class="w">        </span><span class="n">validateRoleSpecificPermissions</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;User permissions validated for transition: {} -&gt; {} by user: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">fromState</span><span class="p">,</span><span class="w"> </span><span class="n">toState</span><span class="p">,</span><span class="w"> </span><span class="n">userId</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateBusinessRules</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                     </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">toState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getToState</span><span class="p">();</span>

<span class="w">        </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">toState</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">SUBMITTED</span><span class="p">:</span>
<span class="w">                </span><span class="n">validateSubmissionRules</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">APPROVED</span><span class="p">:</span>
<span class="w">                </span><span class="n">validateApprovalRules</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">REJECTED</span><span class="p">:</span>
<span class="w">                </span><span class="n">validateRejectionRules</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">ORDERED</span><span class="p">:</span>
<span class="w">                </span><span class="n">validateOrderCreationRules</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">COMPLETED</span><span class="p">:</span>
<span class="w">                </span><span class="n">validateCompletionRules</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">CANCELLED</span><span class="p">:</span>
<span class="w">                </span><span class="n">validateCancellationRules</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateSubmissionRules</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                       </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuote</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Validate quote has required information</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getCustomerId</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getCustomerId</span><span class="p">().</span><span class="na">trim</span><span class="p">().</span><span class="na">isEmpty</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;SUBMISSION_VALIDATION_FAILED&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Customer ID is required for quote submission&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteItems</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getQuoteItems</span><span class="p">().</span><span class="na">isEmpty</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;SUBMISSION_VALIDATION_FAILED&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Quote must contain at least one service item for submission&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate all quote items have valid pricing</span>
<span class="w">        </span><span class="n">validateQuoteItemPricing</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Validate customer eligibility</span>
<span class="w">        </span><span class="n">validateCustomerEligibilityForSubmission</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Validate quote expiration</span>
<span class="w">        </span><span class="n">validateQuoteNotExpired</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Submission rules validation completed for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateApprovalRules</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                     </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuote</span><span class="p">();</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">userId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getUserId</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Validate user has approval authority</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">authorizationService</span><span class="p">.</span><span class="na">hasApprovalAuthority</span><span class="p">(</span><span class="n">userId</span><span class="p">,</span><span class="w"> </span><span class="n">quote</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;APPROVAL_AUTHORITY_REQUIRED&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;User does not have approval authority for this quote&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate quote value is within approval limits</span>
<span class="w">        </span><span class="n">validateApprovalLimits</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">userId</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Validate all validations have passed</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">hasValidationErrors</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;VALIDATION_ERRORS_EXIST&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Quote cannot be approved while validation errors exist&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate credit check if required</span>
<span class="w">        </span><span class="n">validateCreditCheckRequirements</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Approval rules validation completed for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateOrderCreationRules</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                          </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuote</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Validate quote is approved</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getStatus</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">APPROVED</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;QUOTE_NOT_APPROVED&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Quote must be approved before creating an order&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate quote has not expired</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">isExpired</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;QUOTE_EXPIRED&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Cannot create order from expired quote&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate service availability</span>
<span class="w">        </span><span class="n">validateServiceAvailabilityForOrder</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Validate customer account status</span>
<span class="w">        </span><span class="n">validateCustomerAccountStatus</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Order creation rules validation completed for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateCancellationRules</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                         </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuote</span><span class="p">();</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">fromState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getFromState</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Check if cancellation is allowed from current state</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">fromState</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">COMPLETED</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;CANCELLATION_NOT_ALLOWED&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Cannot cancel a completed quote&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate cancellation reason if required</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">cancellationReason</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getCancellationReason</span><span class="p">();</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isCancellationReasonRequired</span><span class="p">(</span><span class="n">fromState</span><span class="p">)</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>
<span class="w">            </span><span class="p">(</span><span class="n">cancellationReason</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">cancellationReason</span><span class="p">.</span><span class="na">trim</span><span class="p">().</span><span class="na">isEmpty</span><span class="p">()))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;CANCELLATION_REASON_REQUIRED&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Cancellation reason is required for this state transition&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate user has cancellation authority</span>
<span class="w">        </span><span class="n">validateCancellationAuthority</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Cancellation rules validation completed for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateQuoteCompleteness</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                         </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuote</span><span class="p">();</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">toState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getToState</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Different completeness requirements for different states</span>
<span class="w">        </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">toState</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">SUBMITTED</span><span class="p">:</span>
<span class="w">                </span><span class="n">validateSubmissionCompleteness</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">APPROVED</span><span class="p">:</span>
<span class="w">                </span><span class="n">validateApprovalCompleteness</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">ORDERED</span><span class="p">:</span>
<span class="w">                </span><span class="n">validateOrderCompleteness</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateTimingConstraints</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                         </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuote</span><span class="p">();</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">fromState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getFromState</span><span class="p">();</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">toState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getToState</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Validate minimum time in current state</span>
<span class="w">        </span><span class="n">Duration</span><span class="w"> </span><span class="n">minimumStateTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getMinimumStateTime</span><span class="p">(</span><span class="n">fromState</span><span class="p">);</span>
<span class="w">        </span><span class="n">Duration</span><span class="w"> </span><span class="n">timeInState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Duration</span><span class="p">.</span><span class="na">between</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getLastStateChangeTime</span><span class="p">(),</span><span class="w"> </span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">());</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">timeInState</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">minimumStateTime</span><span class="p">)</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addWarning</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;MINIMUM_STATE_TIME_NOT_MET&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;Quote has been in %s state for only %s, minimum is %s&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                             </span><span class="n">fromState</span><span class="p">,</span><span class="w"> </span><span class="n">timeInState</span><span class="p">,</span><span class="w"> </span><span class="n">minimumStateTime</span><span class="p">)</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate business hours for certain transitions</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">requiresBusinessHours</span><span class="p">(</span><span class="n">toState</span><span class="p">)</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="o">!</span><span class="n">isBusinessHours</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;BUSINESS_HOURS_REQUIRED&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;Transition to %s is only allowed during business hours&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">toState</span><span class="p">)</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateApprovalRequirements</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                            </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuote</span><span class="p">();</span>
<span class="w">        </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">toState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getToState</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">toState</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">APPROVED</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Check if multiple approvals are required</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">requiresMultipleApprovals</span><span class="p">(</span><span class="n">quote</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="kt">int</span><span class="w"> </span><span class="n">requiredApprovals</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getRequiredApprovalCount</span><span class="p">(</span><span class="n">quote</span><span class="p">);</span>
<span class="w">                </span><span class="kt">int</span><span class="w"> </span><span class="n">currentApprovals</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quote</span><span class="p">.</span><span class="na">getApprovals</span><span class="p">().</span><span class="na">size</span><span class="p">();</span>

<span class="w">                </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">currentApprovals</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="n">requiredApprovals</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                        </span><span class="s">&quot;INSUFFICIENT_APPROVALS&quot;</span><span class="p">,</span>
<span class="w">                        </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;Quote requires %d approvals, currently has %d&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                     </span><span class="n">requiredApprovals</span><span class="p">,</span><span class="w"> </span><span class="n">currentApprovals</span><span class="p">)</span>
<span class="w">                    </span><span class="p">);</span>
<span class="w">                </span><span class="p">}</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">            </span><span class="c1">// Validate approval hierarchy</span>
<span class="w">            </span><span class="n">validateApprovalHierarchy</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getUserId</span><span class="p">(),</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">QuoteState</span><span class="p">,</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">QuoteState</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="nf">getSupportedTransitions</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ALLOWED_TRANSITIONS</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isApplicable</span><span class="p">(</span><span class="n">QuoteState</span><span class="w"> </span><span class="n">fromState</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteState</span><span class="w"> </span><span class="n">toState</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">QuoteState</span><span class="o">&gt;</span><span class="w"> </span><span class="n">allowedStates</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ALLOWED_TRANSITIONS</span><span class="p">.</span><span class="na">get</span><span class="p">(</span><span class="n">fromState</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">allowedStates</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">allowedStates</span><span class="p">.</span><span class="na">contains</span><span class="p">(</span><span class="n">toState</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isStrictTransitionsEnabled</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">config</span><span class="p">.</span><span class="na">getStateValidators</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">getComponents</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;state-machine-validator&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">getProperties</span><span class="p">().</span><span class="na">getOrDefault</span><span class="p">(</span><span class="s">&quot;strict-transitions&quot;</span><span class="p">,</span><span class="w"> </span><span class="kc">false</span><span class="p">).</span><span class="na">equals</span><span class="p">(</span><span class="kc">true</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Duration</span><span class="w"> </span><span class="nf">getMinimumStateTime</span><span class="p">(</span><span class="n">QuoteState</span><span class="w"> </span><span class="n">state</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">QuoteState</span><span class="p">,</span><span class="w"> </span><span class="n">Duration</span><span class="o">&gt;</span><span class="w"> </span><span class="n">minimumTimes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span>
<span class="w">            </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">DRAFT</span><span class="p">,</span><span class="w"> </span><span class="n">Duration</span><span class="p">.</span><span class="na">ofMinutes</span><span class="p">(</span><span class="mi">5</span><span class="p">),</span>
<span class="w">            </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">SUBMITTED</span><span class="p">,</span><span class="w"> </span><span class="n">Duration</span><span class="p">.</span><span class="na">ofHours</span><span class="p">(</span><span class="mi">1</span><span class="p">),</span>
<span class="w">            </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">APPROVED</span><span class="p">,</span><span class="w"> </span><span class="n">Duration</span><span class="p">.</span><span class="na">ofMinutes</span><span class="p">(</span><span class="mi">30</span><span class="p">)</span>
<span class="w">        </span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">minimumTimes</span><span class="p">.</span><span class="na">getOrDefault</span><span class="p">(</span><span class="n">state</span><span class="p">,</span><span class="w"> </span><span class="n">Duration</span><span class="p">.</span><span class="na">ZERO</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">requiresBusinessHours</span><span class="p">(</span><span class="n">QuoteState</span><span class="w"> </span><span class="n">toState</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">toState</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">APPROVED</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">toState</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteState</span><span class="p">.</span><span class="na">ORDERED</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isBusinessHours</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">LocalTime</span><span class="w"> </span><span class="n">now</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">now</span><span class="p">();</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">now</span><span class="p">.</span><span class="na">isAfter</span><span class="p">(</span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">))</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">now</span><span class="p">.</span><span class="na">isBefore</span><span class="p">(</span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">18</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="state-transition-rules">State Transition Rules<a class="headerlink" href="#state-transition-rules" title="Permanent link">&para;</a></h2>
<h3 id="business-rule-configuration">Business Rule Configuration<a class="headerlink" href="#business-rule-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># state-validation-rules.yml</span>
<span class="nt">state-validation</span><span class="p">:</span>
<span class="w">  </span><span class="nt">transitions</span><span class="p">:</span>
<span class="w">    </span><span class="nt">strict-mode</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">allowed-transitions</span><span class="p">:</span>
<span class="w">      </span><span class="nt">DRAFT</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">SUBMITTED</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">CANCELLED</span>
<span class="w">      </span><span class="nt">SUBMITTED</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">APPROVED</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">REJECTED</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">CANCELLED</span>
<span class="w">      </span><span class="nt">APPROVED</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ORDERED</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">CANCELLED</span>
<span class="w">      </span><span class="nt">REJECTED</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DRAFT</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">CANCELLED</span>
<span class="w">      </span><span class="nt">ORDERED</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">COMPLETED</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">CANCELLED</span>
<span class="w">      </span><span class="nt">COMPLETED</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[]</span>
<span class="w">      </span><span class="nt">CANCELLED</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[]</span>

<span class="w">  </span><span class="nt">business-rules</span><span class="p">:</span>
<span class="w">    </span><span class="nt">submission</span><span class="p">:</span>
<span class="w">      </span><span class="nt">required-fields</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">customerId</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">quoteItems</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">serviceLocations</span>
<span class="w">      </span><span class="nt">validation-checks</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">pricing-complete</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">customer-eligible</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">quote-not-expired</span>

<span class="w">    </span><span class="nt">approval</span><span class="p">:</span>
<span class="w">      </span><span class="nt">authority-levels</span><span class="p">:</span>
<span class="w">        </span><span class="nt">STANDARD</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">50000</span>
<span class="w">        </span><span class="nt">MANAGER</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">250000</span>
<span class="w">        </span><span class="nt">DIRECTOR</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000000</span>
<span class="w">        </span><span class="nt">VP</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5000000</span>
<span class="w">      </span><span class="nt">multiple-approvals</span><span class="p">:</span>
<span class="w">        </span><span class="nt">threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">500000</span>
<span class="w">        </span><span class="nt">required-count</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
<span class="w">      </span><span class="nt">credit-check</span><span class="p">:</span>
<span class="w">        </span><span class="nt">required-threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100000</span>

<span class="w">    </span><span class="nt">order-creation</span><span class="p">:</span>
<span class="w">      </span><span class="nt">prerequisites</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">quote-approved</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">quote-not-expired</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">service-available</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">customer-account-active</span>

<span class="w">    </span><span class="nt">cancellation</span><span class="p">:</span>
<span class="w">      </span><span class="nt">reason-required-states</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">SUBMITTED</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">APPROVED</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ORDERED</span>
<span class="w">      </span><span class="nt">authority-required</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">  </span><span class="nt">timing-constraints</span><span class="p">:</span>
<span class="w">    </span><span class="nt">minimum-state-times</span><span class="p">:</span>
<span class="w">      </span><span class="nt">DRAFT</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">PT5M</span><span class="w">      </span><span class="c1"># 5 minutes</span>
<span class="w">      </span><span class="nt">SUBMITTED</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">PT1H</span><span class="w">  </span><span class="c1"># 1 hour</span>
<span class="w">      </span><span class="nt">APPROVED</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">PT30M</span><span class="w">  </span><span class="c1"># 30 minutes</span>

<span class="w">    </span><span class="nt">business-hours</span><span class="p">:</span>
<span class="w">      </span><span class="nt">required-for</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">APPROVED</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ORDERED</span>
<span class="w">      </span><span class="nt">start-time</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;08:00&quot;</span>
<span class="w">      </span><span class="nt">end-time</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;18:00&quot;</span>
<span class="w">      </span><span class="nt">timezone</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;America/Toronto&quot;</span>
</code></pre></div>
<h2 id="business-logic-validation">Business Logic Validation<a class="headerlink" href="#business-logic-validation" title="Permanent link">&para;</a></h2>
<h3 id="rule-engine-integration">Rule Engine Integration<a class="headerlink" href="#rule-engine-integration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">StateTransitionBusinessRulesEngine</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">RuleEngine</span><span class="w"> </span><span class="n">ruleEngine</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">CustomerService</span><span class="w"> </span><span class="n">customerService</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ServiceAvailabilityService</span><span class="w"> </span><span class="n">serviceAvailabilityService</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">BusinessRuleResult</span><span class="w"> </span><span class="nf">validateBusinessRules</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Validating business rules for state transition: {} -&gt; {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">context</span><span class="p">.</span><span class="na">getFromState</span><span class="p">(),</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getToState</span><span class="p">());</span>

<span class="w">        </span><span class="n">BusinessRuleResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">BusinessRuleResult</span><span class="p">.</span><span class="na">builder</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Load applicable rules</span>
<span class="w">        </span><span class="n">List</span><span class="o">&lt;</span><span class="n">BusinessRule</span><span class="o">&gt;</span><span class="w"> </span><span class="n">rules</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">loadApplicableRules</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Execute rules</span>
<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">BusinessRule</span><span class="w"> </span><span class="n">rule</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">rules</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">RuleExecutionResult</span><span class="w"> </span><span class="n">ruleResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ruleEngine</span><span class="p">.</span><span class="na">execute</span><span class="p">(</span><span class="n">rule</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">);</span>

<span class="w">                </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">ruleResult</span><span class="p">.</span><span class="na">isSuccess</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addRuleViolation</span><span class="p">(</span>
<span class="w">                        </span><span class="n">rule</span><span class="p">.</span><span class="na">getName</span><span class="p">(),</span>
<span class="w">                        </span><span class="n">ruleResult</span><span class="p">.</span><span class="na">getErrorMessage</span><span class="p">(),</span>
<span class="w">                        </span><span class="n">ruleResult</span><span class="p">.</span><span class="na">getSeverity</span><span class="p">()</span>
<span class="w">                    </span><span class="p">);</span>
<span class="w">                </span><span class="p">}</span>

<span class="w">            </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error executing business rule: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">rule</span><span class="p">.</span><span class="na">getName</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">                </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addRuleViolation</span><span class="p">(</span>
<span class="w">                    </span><span class="n">rule</span><span class="p">.</span><span class="na">getName</span><span class="p">(),</span>
<span class="w">                    </span><span class="s">&quot;Rule execution failed: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">(),</span>
<span class="w">                    </span><span class="n">Severity</span><span class="p">.</span><span class="na">ERROR</span>
<span class="w">                </span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">BusinessRuleResult</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Business rules validation completed. Violations: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">result</span><span class="p">.</span><span class="na">getViolations</span><span class="p">().</span><span class="na">size</span><span class="p">());</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">result</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">BusinessRule</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">loadApplicableRules</span><span class="p">(</span><span class="n">StateTransitionContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ruleEngine</span><span class="p">.</span><span class="na">findRules</span><span class="p">(</span>
<span class="w">            </span><span class="n">RuleCriteria</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">                </span><span class="p">.</span><span class="na">fromState</span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getFromState</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">toState</span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getToState</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">quoteType</span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getQuote</span><span class="p">().</span><span class="na">getQuoteType</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">customerType</span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getQuote</span><span class="p">().</span><span class="na">getCustomerType</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">()</span>
<span class="w">        </span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="configuration-and-customization">Configuration and Customization<a class="headerlink" href="#configuration-and-customization" title="Permanent link">&para;</a></h2>
<h3 id="dynamic-state-configuration">Dynamic State Configuration<a class="headerlink" href="#dynamic-state-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@ConfigurationProperties</span><span class="p">(</span><span class="n">prefix</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;telus.plugin.state-validators&quot;</span><span class="p">)</span>
<span class="nd">@Data</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">StateValidatorConfiguration</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">enabled</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">ValidatorConfig</span><span class="o">&gt;</span><span class="w"> </span><span class="n">components</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HashMap</span><span class="o">&lt;&gt;</span><span class="p">();</span>

<span class="w">    </span><span class="nd">@Data</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">class</span> <span class="nc">ValidatorConfig</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">enabled</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">strictTransitions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">allowedTransitions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HashMap</span><span class="o">&lt;&gt;</span><span class="p">();</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">BusinessRuleConfig</span><span class="o">&gt;</span><span class="w"> </span><span class="n">businessRules</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HashMap</span><span class="o">&lt;&gt;</span><span class="p">();</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">TimingConstraintsConfig</span><span class="w"> </span><span class="n">timingConstraints</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">TimingConstraintsConfig</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Data</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">class</span> <span class="nc">BusinessRuleConfig</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">requiredFields</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ArrayList</span><span class="o">&lt;&gt;</span><span class="p">();</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">validationChecks</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HashMap</span><span class="o">&lt;&gt;</span><span class="p">();</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">BigDecimal</span><span class="o">&gt;</span><span class="w"> </span><span class="n">authorityLevels</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HashMap</span><span class="o">&lt;&gt;</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Data</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TimingConstraintsConfig</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Duration</span><span class="o">&gt;</span><span class="w"> </span><span class="n">minimumStateTimes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HashMap</span><span class="o">&lt;&gt;</span><span class="p">();</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">businessHoursRequired</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ArrayList</span><span class="o">&lt;&gt;</span><span class="p">();</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">LocalTime</span><span class="w"> </span><span class="n">businessHoursStart</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">LocalTime</span><span class="w"> </span><span class="n">businessHoursEnd</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalTime</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="mi">18</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../05-development-guide/">Development Guide →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>