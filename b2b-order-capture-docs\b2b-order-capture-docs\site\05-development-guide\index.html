
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/05-development-guide/">
      
      
        <link rel="prev" href="../04-integration-guide/">
      
      
        <link rel="next" href="../06-deployment-guide/">
      
      
      <link rel="icon" href="../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Development Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#development-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Development Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href=".." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Prerequisites">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#required-software" class="md-nav__link">
    <span class="md-ellipsis">
      Required Software
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#ide-recommendations" class="md-nav__link">
    <span class="md-ellipsis">
      IDE Recommendations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#required-extensionsplugins" class="md-nav__link">
    <span class="md-ellipsis">
      Required Extensions/Plugins
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Environment Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#environment-variables" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Variables
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#repository-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Repository Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Repository Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#clone-all-repositories" class="md-nav__link">
    <span class="md-ellipsis">
      Clone All Repositories
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#frontend-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Setup
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#build-processes" class="md-nav__link">
    <span class="md-ellipsis">
      Build Processes
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Build Processes">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-build-order" class="md-nav__link">
    <span class="md-ellipsis">
      Development Build Order
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#individual-component-builds" class="md-nav__link">
    <span class="md-ellipsis">
      Individual Component Builds
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Individual Component Builds">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Build Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Build Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Build Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-strategies" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategies
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Strategies">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Testing
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Frontend Testing">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#e2e-testing" class="md-nav__link">
    <span class="md-ellipsis">
      E2E Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-testing-example" class="md-nav__link">
    <span class="md-ellipsis">
      Component Testing Example
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Testing
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Backend Testing">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing_1" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-testing-example" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Testing Example
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Testing
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Extensions Testing">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Testing
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#debugging-troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging &amp; Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Debugging &amp; Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-debugging" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Debugging
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Frontend Debugging">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#browser-devtools-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Browser DevTools Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#common-frontend-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Frontend Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-debugging" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Debugging
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Backend Debugging">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#ide-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      IDE Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#remote-debugging" class="md-nav__link">
    <span class="md-ellipsis">
      Remote Debugging
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#common-backend-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Backend Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-debugging" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Debugging
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Extensions Debugging">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-debugging" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Debugging
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#code-quality-standards" class="md-nav__link">
    <span class="md-ellipsis">
      Code Quality &amp; Standards
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Code Quality &amp; Standards">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#code-formatting" class="md-nav__link">
    <span class="md-ellipsis">
      Code Formatting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Code Formatting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-prettier" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend (Prettier)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-checkstyle" class="md-nav__link">
    <span class="md-ellipsis">
      Backend (Checkstyle)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pre-commit-hooks" class="md-nav__link">
    <span class="md-ellipsis">
      Pre-commit Hooks
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#continuous-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Continuous Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Continuous Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#gitlab-ci-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      GitLab CI Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Prerequisites">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#required-software" class="md-nav__link">
    <span class="md-ellipsis">
      Required Software
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#ide-recommendations" class="md-nav__link">
    <span class="md-ellipsis">
      IDE Recommendations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#required-extensionsplugins" class="md-nav__link">
    <span class="md-ellipsis">
      Required Extensions/Plugins
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Environment Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#environment-variables" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Variables
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#repository-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Repository Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Repository Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#clone-all-repositories" class="md-nav__link">
    <span class="md-ellipsis">
      Clone All Repositories
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#frontend-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Setup
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#build-processes" class="md-nav__link">
    <span class="md-ellipsis">
      Build Processes
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Build Processes">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-build-order" class="md-nav__link">
    <span class="md-ellipsis">
      Development Build Order
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#individual-component-builds" class="md-nav__link">
    <span class="md-ellipsis">
      Individual Component Builds
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Individual Component Builds">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Build Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Build Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Build Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-strategies" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategies
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Strategies">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Testing
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Frontend Testing">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#e2e-testing" class="md-nav__link">
    <span class="md-ellipsis">
      E2E Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-testing-example" class="md-nav__link">
    <span class="md-ellipsis">
      Component Testing Example
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Testing
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Backend Testing">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing_1" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-testing-example" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Testing Example
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Testing
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Extensions Testing">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Testing
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#debugging-troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging &amp; Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Debugging &amp; Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-debugging" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Debugging
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Frontend Debugging">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#browser-devtools-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Browser DevTools Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#common-frontend-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Frontend Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-debugging" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Debugging
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Backend Debugging">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#ide-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      IDE Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#remote-debugging" class="md-nav__link">
    <span class="md-ellipsis">
      Remote Debugging
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#common-backend-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Backend Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extensions-debugging" class="md-nav__link">
    <span class="md-ellipsis">
      Extensions Debugging
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Extensions Debugging">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-debugging" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Debugging
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#code-quality-standards" class="md-nav__link">
    <span class="md-ellipsis">
      Code Quality &amp; Standards
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Code Quality &amp; Standards">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#code-formatting" class="md-nav__link">
    <span class="md-ellipsis">
      Code Formatting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Code Formatting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-prettier" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend (Prettier)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-checkstyle" class="md-nav__link">
    <span class="md-ellipsis">
      Backend (Checkstyle)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#pre-commit-hooks" class="md-nav__link">
    <span class="md-ellipsis">
      Pre-commit Hooks
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#continuous-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Continuous Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Continuous Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#gitlab-ci-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      GitLab CI Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="development-guide">Development Guide<a class="headerlink" href="#development-guide" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#development-environment-setup">Development Environment Setup</a></li>
<li><a href="#repository-setup">Repository Setup</a></li>
<li><a href="#build-processes">Build Processes</a></li>
<li><a href="#testing-strategies">Testing Strategies</a></li>
<li><a href="#debugging--troubleshooting">Debugging &amp; Troubleshooting</a></li>
<li><a href="#code-quality--standards">Code Quality &amp; Standards</a></li>
</ul>
<h2 id="development-environment-setup">Development Environment Setup<a class="headerlink" href="#development-environment-setup" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<h4 id="required-software">Required Software<a class="headerlink" href="#required-software" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Node.js and npm (for frontend)</span>
node<span class="w"> </span>--version<span class="w">  </span><span class="c1"># v16.x or higher</span>
npm<span class="w"> </span>--version<span class="w">   </span><span class="c1"># v8.x or higher</span>

<span class="c1"># Java Development Kit</span>
java<span class="w"> </span>--version<span class="w">  </span><span class="c1"># OpenJDK 17 or higher</span>
javac<span class="w"> </span>--version

<span class="c1"># Maven</span>
mvn<span class="w"> </span>--version<span class="w">   </span><span class="c1"># v3.6.x or higher</span>

<span class="c1"># Git</span>
git<span class="w"> </span>--version<span class="w">   </span><span class="c1"># v2.x or higher</span>

<span class="c1"># Docker (optional, for containerized development)</span>
docker<span class="w"> </span>--version
docker-compose<span class="w"> </span>--version
</code></pre></div>
<h4 id="ide-recommendations">IDE Recommendations<a class="headerlink" href="#ide-recommendations" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Frontend</strong>: Visual Studio Code with Angular extensions</li>
<li><strong>Backend</strong>: IntelliJ IDEA or Eclipse with Spring Boot plugins</li>
<li><strong>Extensions</strong>: IntelliJ IDEA with Maven and Lombok plugins</li>
</ul>
<h4 id="required-extensionsplugins">Required Extensions/Plugins<a class="headerlink" href="#required-extensionsplugins" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// VS Code extensions for frontend</span>
<span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;recommendations&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;angular.ng-template&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;ms-vscode.vscode-typescript-next&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;bradlc.vscode-tailwindcss&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;esbenp.prettier-vscode&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;ms-vscode.vscode-eslint&quot;</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="environment-configuration">Environment Configuration<a class="headerlink" href="#environment-configuration" title="Permanent link">&para;</a></h3>
<h4 id="environment-variables">Environment Variables<a class="headerlink" href="#environment-variables" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Create .env file for local development</span>
cat<span class="w"> </span>&gt;<span class="w"> </span>.env<span class="w"> </span><span class="s">&lt;&lt; EOF</span>
<span class="s"># Backend Configuration</span>
<span class="s">SPRING_PROFILES_ACTIVE=development</span>
<span class="s">SERVER_PORT=8080</span>
<span class="s">DATABASE_URL=******************************************</span>
<span class="s">DATABASE_USERNAME=telus_user</span>
<span class="s">DATABASE_PASSWORD=telus_password</span>

<span class="s"># Order Capture Product Integration</span>
<span class="s">OCP_BASE_URL=https://dev-order-capture.telus.com/api/v2</span>
<span class="s">OCP_CLIENT_ID=your_client_id</span>
<span class="s">OCP_CLIENT_SECRET=your_client_secret</span>

<span class="s"># External Services</span>
<span class="s">GEOCODING_API_KEY=your_geocoding_api_key</span>
<span class="s">GOOGLE_MAPS_API_KEY=your_google_maps_key</span>

<span class="s"># Frontend Configuration</span>
<span class="s">ANGULAR_ENV=development</span>
<span class="s">API_BASE_URL=http://localhost:8080/api/v1</span>
<span class="s">EOF</span>
</code></pre></div>
<h2 id="repository-setup">Repository Setup<a class="headerlink" href="#repository-setup" title="Permanent link">&para;</a></h2>
<h3 id="clone-all-repositories">Clone All Repositories<a class="headerlink" href="#clone-all-repositories" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Create workspace directory</span>
mkdir<span class="w"> </span>telus-b2b-workspace
<span class="nb">cd</span><span class="w"> </span>telus-b2b-workspace

<span class="c1"># Clone all three repositories</span>
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/telus/nc-cloud-bss-oc-ui-frontend-b2b.git
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/telus/nc-cloud-bss-oc-ui-backend-b2b.git
git<span class="w"> </span>clone<span class="w"> </span>https://github.com/telus/nc-cloud-bss-oc-ui-be-extension-b2b.git

<span class="c1"># Verify repository structure</span>
ls<span class="w"> </span>-la
<span class="c1"># Expected output:</span>
<span class="c1"># nc-cloud-bss-oc-ui-frontend-b2b/</span>
<span class="c1"># nc-cloud-bss-oc-ui-backend-b2b/</span>
<span class="c1"># nc-cloud-bss-oc-ui-be-extension-b2b/</span>
</code></pre></div>
<h3 id="frontend-setup">Frontend Setup<a class="headerlink" href="#frontend-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-frontend-b2b

<span class="c1"># Install dependencies</span>
npm<span class="w"> </span>install

<span class="c1"># Verify installation</span>
npm<span class="w"> </span>list<span class="w"> </span>--depth<span class="o">=</span><span class="m">0</span>

<span class="c1"># Install additional development tools</span>
npm<span class="w"> </span>install<span class="w"> </span>-g<span class="w"> </span>@angular/cli@15.2.8

<span class="c1"># Verify Angular CLI</span>
ng<span class="w"> </span>version
</code></pre></div>
<h3 id="backend-setup">Backend Setup<a class="headerlink" href="#backend-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-backend-b2b

<span class="c1"># Verify Maven configuration</span>
mvn<span class="w"> </span>--version

<span class="c1"># Download dependencies (without building)</span>
mvn<span class="w"> </span>dependency:resolve

<span class="c1"># Verify project structure</span>
mvn<span class="w"> </span>help:effective-pom
</code></pre></div>
<h3 id="extensions-setup">Extensions Setup<a class="headerlink" href="#extensions-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-be-extension-b2b

<span class="c1"># Build extensions first (required by backend)</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install

<span class="c1"># Verify plugin compilation</span>
mvn<span class="w"> </span>compile
</code></pre></div>
<h2 id="build-processes">Build Processes<a class="headerlink" href="#build-processes" title="Permanent link">&para;</a></h2>
<h3 id="development-build-order">Development Build Order<a class="headerlink" href="#development-build-order" title="Permanent link">&para;</a></h3>
<p>The components must be built in the correct order due to dependencies:</p>
<div class="highlight"><pre><span></span><code><span class="ch">#!/bin/bash</span>
<span class="c1"># build-all.sh - Complete build script</span>

<span class="nb">set</span><span class="w"> </span>-e<span class="w">  </span><span class="c1"># Exit on any error</span>

<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Building TELUS B2B Order Capture Ecosystem...&quot;</span>

<span class="c1"># 1. Build Extensions (required by backend)</span>
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Step 1: Building QES Extensions...&quot;</span>
<span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-be-extension-b2b
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w"> </span>-DskipTests
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;✓ Extensions built successfully&quot;</span>

<span class="c1"># 2. Build Backend</span>
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Step 2: Building Backend...&quot;</span>
<span class="nb">cd</span><span class="w"> </span>../nc-cloud-bss-oc-ui-backend-b2b
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w"> </span>-DskipTests
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;✓ Backend built successfully&quot;</span>

<span class="c1"># 3. Build Frontend</span>
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Step 3: Building Frontend...&quot;</span>
<span class="nb">cd</span><span class="w"> </span>../nc-cloud-bss-oc-ui-frontend-b2b
npm<span class="w"> </span>install
npm<span class="w"> </span>run<span class="w"> </span>build:dev
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;✓ Frontend built successfully&quot;</span>

<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;🎉 All components built successfully!&quot;</span>
</code></pre></div>
<h3 id="individual-component-builds">Individual Component Builds<a class="headerlink" href="#individual-component-builds" title="Permanent link">&para;</a></h3>
<h4 id="frontend-build-commands">Frontend Build Commands<a class="headerlink" href="#frontend-build-commands" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-frontend-b2b

<span class="c1"># Development build</span>
npm<span class="w"> </span>run<span class="w"> </span>build:dev

<span class="c1"># Production build</span>
npm<span class="w"> </span>run<span class="w"> </span>build

<span class="c1"># Watch mode for development</span>
npm<span class="w"> </span>run<span class="w"> </span>serve:dev

<span class="c1"># Build with proxy for backend integration</span>
npm<span class="w"> </span>run<span class="w"> </span>serve:proxy

<span class="c1"># Generate SVG components</span>
npm<span class="w"> </span>run<span class="w"> </span>generate-images

<span class="c1"># Build meta information</span>
npm<span class="w"> </span>run<span class="w"> </span>build:meta-information
</code></pre></div>
<h4 id="backend-build-commands">Backend Build Commands<a class="headerlink" href="#backend-build-commands" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-backend-b2b

<span class="c1"># Clean and compile</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile

<span class="c1"># Package without tests</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w"> </span>-DskipTests

<span class="c1"># Full build with tests</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install

<span class="c1"># Run specific module</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w"> </span>-pl<span class="w"> </span>cloud-bss-oc-ui-backend-b2b-application

<span class="c1"># Generate sources</span>
mvn<span class="w"> </span>generate-sources
</code></pre></div>
<h4 id="extensions-build-commands">Extensions Build Commands<a class="headerlink" href="#extensions-build-commands" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-be-extension-b2b

<span class="c1"># Build plugin</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install

<span class="c1"># Build without tests</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w"> </span>-DskipTests

<span class="c1"># Package for deployment</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package

<span class="c1"># Install to local repository</span>
mvn<span class="w"> </span>install
</code></pre></div>
<h2 id="testing-strategies">Testing Strategies<a class="headerlink" href="#testing-strategies" title="Permanent link">&para;</a></h2>
<h3 id="frontend-testing">Frontend Testing<a class="headerlink" href="#frontend-testing" title="Permanent link">&para;</a></h3>
<h4 id="unit-testing">Unit Testing<a class="headerlink" href="#unit-testing" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Run all unit tests</span>
npm<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Run tests in watch mode</span>
npm<span class="w"> </span><span class="nb">test</span><span class="w"> </span>--<span class="w"> </span>--watch

<span class="c1"># Run tests with coverage</span>
npm<span class="w"> </span><span class="nb">test</span><span class="w"> </span>--<span class="w"> </span>--code-coverage

<span class="c1"># Run specific test file</span>
npm<span class="w"> </span><span class="nb">test</span><span class="w"> </span>--<span class="w"> </span>--include<span class="o">=</span><span class="s2">&quot;**/quote.service.spec.ts&quot;</span>
</code></pre></div>
<h4 id="e2e-testing">E2E Testing<a class="headerlink" href="#e2e-testing" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Run end-to-end tests</span>
npm<span class="w"> </span>run<span class="w"> </span>e2e

<span class="c1"># Run E2E tests against specific environment</span>
npm<span class="w"> </span>run<span class="w"> </span>e2e<span class="w"> </span>--<span class="w"> </span>--base-url<span class="o">=</span>http://localhost:4200
</code></pre></div>
<h4 id="component-testing-example">Component Testing Example<a class="headerlink" href="#component-testing-example" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// quote.service.spec.ts</span>
<span class="nx">describe</span><span class="p">(</span><span class="s1">&#39;QuoteService&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">let</span><span class="w"> </span><span class="nx">service</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteService</span><span class="p">;</span>
<span class="w">  </span><span class="kd">let</span><span class="w"> </span><span class="nx">httpMock</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpTestingController</span><span class="p">;</span>

<span class="w">  </span><span class="nx">beforeEach</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">TestBed</span><span class="p">.</span><span class="nx">configureTestingModule</span><span class="p">({</span>
<span class="w">      </span><span class="nx">imports</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="nx">HttpClientTestingModule</span><span class="p">],</span>
<span class="w">      </span><span class="nx">providers</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="nx">QuoteService</span><span class="p">]</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">    </span><span class="nx">service</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">TestBed</span><span class="p">.</span><span class="nx">inject</span><span class="p">(</span><span class="nx">QuoteService</span><span class="p">);</span>
<span class="w">    </span><span class="nx">httpMock</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">TestBed</span><span class="p">.</span><span class="nx">inject</span><span class="p">(</span><span class="nx">HttpTestingController</span><span class="p">);</span>
<span class="w">  </span><span class="p">});</span>

<span class="w">  </span><span class="nx">it</span><span class="p">(</span><span class="s1">&#39;should create quote&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">mockQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;123&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">customerId</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;customer1&#39;</span><span class="w"> </span><span class="p">};</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">request</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">customerId</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;customer1&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">items</span><span class="o">:</span><span class="w"> </span><span class="p">[]</span><span class="w"> </span><span class="p">};</span>

<span class="w">    </span><span class="nx">service</span><span class="p">.</span><span class="nx">createQuote</span><span class="p">(</span><span class="nx">request</span><span class="p">).</span><span class="nx">subscribe</span><span class="p">(</span><span class="nx">quote</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">expect</span><span class="p">(</span><span class="nx">quote</span><span class="p">).</span><span class="nx">toEqual</span><span class="p">(</span><span class="nx">mockQuote</span><span class="p">);</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">req</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">httpMock</span><span class="p">.</span><span class="nx">expectOne</span><span class="p">(</span><span class="s1">&#39;/api/v1/quotes&#39;</span><span class="p">);</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">req</span><span class="p">.</span><span class="nx">request</span><span class="p">.</span><span class="nx">method</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="s1">&#39;POST&#39;</span><span class="p">);</span>
<span class="w">    </span><span class="nx">req</span><span class="p">.</span><span class="nx">flush</span><span class="p">(</span><span class="nx">mockQuote</span><span class="p">);</span>
<span class="w">  </span><span class="p">});</span>
<span class="p">});</span>
</code></pre></div>
<h3 id="backend-testing">Backend Testing<a class="headerlink" href="#backend-testing" title="Permanent link">&para;</a></h3>
<h4 id="unit-testing_1">Unit Testing<a class="headerlink" href="#unit-testing_1" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Run all tests</span>
mvn<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Run specific test class</span>
mvn<span class="w"> </span><span class="nb">test</span><span class="w"> </span>-Dtest<span class="o">=</span>QuoteServiceTest

<span class="c1"># Run tests with coverage</span>
mvn<span class="w"> </span><span class="nb">test</span><span class="w"> </span>jacoco:report

<span class="c1"># Run integration tests</span>
mvn<span class="w"> </span>verify
</code></pre></div>
<h4 id="integration-testing-example">Integration Testing Example<a class="headerlink" href="#integration-testing-example" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="nd">@SpringBootTest</span><span class="p">(</span><span class="n">webEnvironment</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">SpringBootTest</span><span class="p">.</span><span class="na">WebEnvironment</span><span class="p">.</span><span class="na">RANDOM_PORT</span><span class="p">)</span>
<span class="nd">@TestPropertySource</span><span class="p">(</span><span class="n">properties</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s">&quot;spring.datasource.url=jdbc:h2:mem:testdb&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s">&quot;spring.jpa.hibernate.ddl-auto=create-drop&quot;</span>
<span class="p">})</span>
<span class="kd">class</span> <span class="nc">QuoteControllerIntegrationTest</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">TestRestTemplate</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldCreateQuote</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">CreateQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">CreateQuoteRequest</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">customerId</span><span class="p">(</span><span class="s">&quot;customer1&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">items</span><span class="p">(</span><span class="n">List</span><span class="p">.</span><span class="na">of</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">        </span><span class="n">ResponseEntity</span><span class="o">&lt;</span><span class="n">QuoteResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="n">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">restTemplate</span><span class="p">.</span><span class="na">postForEntity</span><span class="p">(</span>
<span class="w">            </span><span class="s">&quot;/api/v1/quotes&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">request</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="p">.</span><span class="na">class</span><span class="p">);</span>

<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getStatusCode</span><span class="p">()).</span><span class="na">isEqualTo</span><span class="p">(</span><span class="n">HttpStatus</span><span class="p">.</span><span class="na">CREATED</span><span class="p">);</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getBody</span><span class="p">().</span><span class="na">getCustomerId</span><span class="p">()).</span><span class="na">isEqualTo</span><span class="p">(</span><span class="s">&quot;customer1&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="extensions-testing">Extensions Testing<a class="headerlink" href="#extensions-testing" title="Permanent link">&para;</a></h3>
<h4 id="plugin-testing">Plugin Testing<a class="headerlink" href="#plugin-testing" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Run plugin tests</span>
mvn<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Test specific validator</span>
mvn<span class="w"> </span><span class="nb">test</span><span class="w"> </span>-Dtest<span class="o">=</span>TelusWanL2CirBandwidthValidatorTest

<span class="c1"># Integration test with QES framework</span>
mvn<span class="w"> </span>verify<span class="w"> </span>-P<span class="w"> </span>integration-tests
</code></pre></div>
<h2 id="debugging-troubleshooting">Debugging &amp; Troubleshooting<a class="headerlink" href="#debugging-troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="frontend-debugging">Frontend Debugging<a class="headerlink" href="#frontend-debugging" title="Permanent link">&para;</a></h3>
<h4 id="browser-devtools-setup">Browser DevTools Setup<a class="headerlink" href="#browser-devtools-setup" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// Enable Redux DevTools in development</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">StoreDevtoolsModule</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;@ngrx/store-devtools&#39;</span><span class="p">;</span>

<span class="kd">@NgModule</span><span class="p">({</span>
<span class="w">  </span><span class="nx">imports</span><span class="o">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="nx">StoreDevtoolsModule</span><span class="p">.</span><span class="nx">instrument</span><span class="p">({</span>
<span class="w">      </span><span class="nx">maxAge</span><span class="o">:</span><span class="w"> </span><span class="kt">25</span><span class="p">,</span>
<span class="w">      </span><span class="nx">logOnly</span><span class="o">:</span><span class="w"> </span><span class="kt">environment.production</span>
<span class="w">    </span><span class="p">})</span>
<span class="w">  </span><span class="p">]</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">AppModule</span><span class="w"> </span><span class="p">{}</span>
</code></pre></div>
<h4 id="common-frontend-issues">Common Frontend Issues<a class="headerlink" href="#common-frontend-issues" title="Permanent link">&para;</a></h4>
<p><strong>Issue: CORS Errors</strong>
<div class="highlight"><pre><span></span><code><span class="c1"># Solution: Use proxy configuration</span>
npm<span class="w"> </span>run<span class="w"> </span>serve:proxy

<span class="c1"># Or configure proxy in angular.json</span>
<span class="o">{</span>
<span class="w">  </span><span class="s2">&quot;serve&quot;</span>:<span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="s2">&quot;builder&quot;</span>:<span class="w"> </span><span class="s2">&quot;@angular-devkit/build-angular:dev-server&quot;</span>,
<span class="w">    </span><span class="s2">&quot;options&quot;</span>:<span class="w"> </span><span class="o">{</span>
<span class="w">      </span><span class="s2">&quot;proxyConfig&quot;</span>:<span class="w"> </span><span class="s2">&quot;proxy.conf.json&quot;</span>
<span class="w">    </span><span class="o">}</span>
<span class="w">  </span><span class="o">}</span>
<span class="o">}</span>
</code></pre></div></p>
<p><strong>Issue: Memory Issues</strong>
<div class="highlight"><pre><span></span><code><span class="c1"># Increase Node.js memory limit</span>
node<span class="w"> </span>--max_old_space_size<span class="o">=</span><span class="m">8000</span><span class="w"> </span>./node_modules/@angular/cli/bin/ng<span class="w"> </span>serve
</code></pre></div></p>
<h3 id="backend-debugging">Backend Debugging<a class="headerlink" href="#backend-debugging" title="Permanent link">&para;</a></h3>
<h4 id="ide-configuration">IDE Configuration<a class="headerlink" href="#ide-configuration" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># application-debug.yml</span>
<span class="nt">logging</span><span class="p">:</span>
<span class="w">  </span><span class="nt">level</span><span class="p">:</span>
<span class="w">    </span><span class="nt">com.netcracker.solutions.telus</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DEBUG</span>
<span class="w">    </span><span class="nt">org.springframework.web</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DEBUG</span>
<span class="w">    </span><span class="nt">org.springframework.security</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DEBUG</span>

<span class="nt">spring</span><span class="p">:</span>
<span class="w">  </span><span class="nt">jpa</span><span class="p">:</span>
<span class="w">    </span><span class="nt">show-sql</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">      </span><span class="nt">hibernate</span><span class="p">:</span>
<span class="w">        </span><span class="nt">format_sql</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</code></pre></div>
<h4 id="remote-debugging">Remote Debugging<a class="headerlink" href="#remote-debugging" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Start with remote debugging enabled</span>
mvn<span class="w"> </span>spring-boot:run<span class="w"> </span>-Dspring-boot.run.jvmArguments<span class="o">=</span><span class="s2">&quot;-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005&quot;</span>

<span class="c1"># Connect IDE debugger to localhost:5005</span>
</code></pre></div>
<h4 id="common-backend-issues">Common Backend Issues<a class="headerlink" href="#common-backend-issues" title="Permanent link">&para;</a></h4>
<p><strong>Issue: Database Connection</strong>
<div class="highlight"><pre><span></span><code><span class="c1"># Check database connectivity</span>
telnet<span class="w"> </span>localhost<span class="w"> </span><span class="m">5432</span>

<span class="c1"># Verify connection string</span>
psql<span class="w"> </span>-h<span class="w"> </span>localhost<span class="w"> </span>-U<span class="w"> </span>telus_user<span class="w"> </span>-d<span class="w"> </span>telus_b2b
</code></pre></div></p>
<p><strong>Issue: Order Capture Integration</strong>
<div class="highlight"><pre><span></span><code><span class="c1"># Test OCP connectivity</span>
curl<span class="w"> </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="nv">$TOKEN</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>https://dev-order-capture.telus.com/api/v2/health

<span class="c1"># Check authentication</span>
curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span>https://auth.telus.com/oauth/token<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s2">&quot;grant_type=client_credentials&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s2">&quot;client_id=</span><span class="nv">$CLIENT_ID</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-d<span class="w"> </span><span class="s2">&quot;client_secret=</span><span class="nv">$CLIENT_SECRET</span><span class="s2">&quot;</span>
</code></pre></div></p>
<h3 id="extensions-debugging">Extensions Debugging<a class="headerlink" href="#extensions-debugging" title="Permanent link">&para;</a></h3>
<h4 id="plugin-debugging">Plugin Debugging<a class="headerlink" href="#plugin-debugging" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// Add debug logging to plugins</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusQuoteDeltaModificatorImpl</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="nf">modifyQuote</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Processing quote modification for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Add breakpoint here for debugging</span>
<span class="w">        </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="n">delta</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">processModifications</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Quote modification completed with {} changes&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">delta</span><span class="p">.</span><span class="na">getChanges</span><span class="p">().</span><span class="na">size</span><span class="p">());</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">delta</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="code-quality-standards">Code Quality &amp; Standards<a class="headerlink" href="#code-quality-standards" title="Permanent link">&para;</a></h2>
<h3 id="code-formatting">Code Formatting<a class="headerlink" href="#code-formatting" title="Permanent link">&para;</a></h3>
<h4 id="frontend-prettier">Frontend (Prettier)<a class="headerlink" href="#frontend-prettier" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// .prettierrc</span>
<span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;semi&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;trailingComma&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;es5&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;singleQuote&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;printWidth&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;tabWidth&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;useTabs&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="backend-checkstyle">Backend (Checkstyle)<a class="headerlink" href="#backend-checkstyle" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="cm">&lt;!-- checkstyle.xml --&gt;</span>
<span class="nt">&lt;module</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Checker&quot;</span><span class="nt">&gt;</span>
<span class="w">  </span><span class="nt">&lt;module</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;TreeWalker&quot;</span><span class="nt">&gt;</span>
<span class="w">    </span><span class="nt">&lt;module</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Indentation&quot;</span><span class="nt">&gt;</span>
<span class="w">      </span><span class="nt">&lt;property</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;basicOffset&quot;</span><span class="w"> </span><span class="na">value=</span><span class="s">&quot;4&quot;</span><span class="nt">/&gt;</span>
<span class="w">    </span><span class="nt">&lt;/module&gt;</span>
<span class="w">    </span><span class="nt">&lt;module</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;LineLength&quot;</span><span class="nt">&gt;</span>
<span class="w">      </span><span class="nt">&lt;property</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;max&quot;</span><span class="w"> </span><span class="na">value=</span><span class="s">&quot;120&quot;</span><span class="nt">/&gt;</span>
<span class="w">    </span><span class="nt">&lt;/module&gt;</span>
<span class="w">  </span><span class="nt">&lt;/module&gt;</span>
<span class="nt">&lt;/module&gt;</span>
</code></pre></div>
<h3 id="pre-commit-hooks">Pre-commit Hooks<a class="headerlink" href="#pre-commit-hooks" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Install Husky for Git hooks</span>
npm<span class="w"> </span>install<span class="w"> </span>--save-dev<span class="w"> </span>husky

<span class="c1"># Setup pre-commit hook</span>
npx<span class="w"> </span>husky<span class="w"> </span>add<span class="w"> </span>.husky/pre-commit<span class="w"> </span><span class="s2">&quot;npm run lint &amp;&amp; npm run test&quot;</span>

<span class="c1"># For backend, add Maven validation</span>
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;mvn validate&quot;</span><span class="w"> </span>&gt;&gt;<span class="w"> </span>.husky/pre-commit
</code></pre></div>
<h3 id="continuous-integration">Continuous Integration<a class="headerlink" href="#continuous-integration" title="Permanent link">&para;</a></h3>
<h4 id="gitlab-ci-configuration">GitLab CI Configuration<a class="headerlink" href="#gitlab-ci-configuration" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># .gitlab-ci.yml</span>
<span class="nt">stages</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">build</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">test</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">deploy</span>

<span class="nt">variables</span><span class="p">:</span>
<span class="w">  </span><span class="nt">MAVEN_OPTS</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;-Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository&quot;</span>

<span class="nt">cache</span><span class="p">:</span>
<span class="w">  </span><span class="nt">paths</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">.m2/repository/</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">node_modules/</span>

<span class="nt">build-extensions</span><span class="p">:</span>
<span class="w">  </span><span class="nt">stage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">build</span>
<span class="w">  </span><span class="nt">script</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cd nc-cloud-bss-oc-ui-be-extension-b2b</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mvn clean compile</span>

<span class="nt">build-backend</span><span class="p">:</span>
<span class="w">  </span><span class="nt">stage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">build</span>
<span class="w">  </span><span class="nt">script</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cd nc-cloud-bss-oc-ui-backend-b2b</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mvn clean compile</span>
<span class="w">  </span><span class="nt">dependencies</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">build-extensions</span>

<span class="nt">build-frontend</span><span class="p">:</span>
<span class="w">  </span><span class="nt">stage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">build</span>
<span class="w">  </span><span class="nt">script</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cd nc-cloud-bss-oc-ui-frontend-b2b</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">npm ci</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">npm run build</span>

<span class="nt">test-all</span><span class="p">:</span>
<span class="w">  </span><span class="nt">stage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">test</span>
<span class="w">  </span><span class="nt">script</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cd nc-cloud-bss-oc-ui-be-extension-b2b &amp;&amp; mvn test</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cd ../nc-cloud-bss-oc-ui-backend-b2b &amp;&amp; mvn test</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cd ../nc-cloud-bss-oc-ui-frontend-b2b &amp;&amp; npm test -- --watch=false</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: Explore the <a href="../06-deployment-guide/">Deployment Guide</a> for production deployment strategies.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "..", "features": [], "search": "../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>