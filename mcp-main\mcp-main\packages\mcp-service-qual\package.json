{"name": "@telus/mcp-service-qual", "version": "0.0.3", "description": "MCP server to call TELUS Service Qualification V2 API", "type": "module", "author": "<PERSON> (@jonathan-dunn-telus)", "bin": {"mcp-telus-service-qual": "./dist/index.js"}, "keywords": ["telus", "mcp", "service-qualification", "sqv2", "sqv4"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-service-qual"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "prepare": "pnpm run build", "start": "node dist/index.js", "dev": "pnpm run build && pnpm run start"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "axios": "^1.8.4", "dotenv": "~16.4.7"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}