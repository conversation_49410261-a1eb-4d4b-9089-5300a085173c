#!/usr/bin/env node
import { SRE_RISK_ENGINE_URL, GITHUB_APP_INSTALLATION_ID } from './constants.js';
import { startServer } from './server/index.js';

// Log only the environment variables that are used
console.log('Environment variables:');
console.log('GITHUB_APP_INSTALLATION_ID:', GITHUB_APP_INSTALLATION_ID);
console.log('SRE_RISK_ENGINE_URL:', SRE_RISK_ENGINE_URL); // Should point to /queued-assessment

try {
  // No webhook secret needed since dispatcher skips validation for MCP events
  console.log('Using constants:');
  console.log(`SRE_RISK_ENGINE_URL: ${SRE_RISK_ENGINE_URL}`);
} catch (error) {
  console.error('Error initializing environment variables:', error);
  throw error;
}

// Start the MCP server
startServer().catch(error => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
