export interface Service {
    entityId: string;
    displayName: string;
}

export interface ServicesResponse {
    entities: Service[];
}

export interface ServicesRelationships {
    entityId: string;
    type: string;
    displayName: string;
    fromRelationships: {
        calls?: Array<{
            id: string;
            type: string;
        }>;
    };
    toRelationships: {
        calls?: Array<{
            id: string;
            type: string;
        }>;
    };
}

export interface EntityIcon {
    customIconPath?: string;
    primaryIconType?: string;
    secondaryIconType?: string;
}

export interface ManagementZone {
    id: string;
    name: string;
}

export interface METag {
    context: string;
    key: string;
    stringRepresentation: string;
    value: string;
}

export interface EntityDetails {
    displayName: string;
    entityId: string;
    firstSeenTms: number;
    fromRelationships: {
        [key: string]: Array<{
            id: string;
            type: string;
        }>;
    };
    icon?: EntityIcon;
    lastSeenTms: number;
    managementZones?: ManagementZone[];
    properties: {
        [key: string]: any;
    };
    tags?: METag[];
    toRelationships: {
        [key: string]: Array<{
            id: string;
            type: string;
        }>;
    };
    type: string;
}

export interface MetricsResponse {
    result: Array<{
        data: Array<{
            dimensions: string[];
            timestamps: number[];
            values: number[];
        }>;
    }>;
}

export interface ProblemDetails {
    displayId: string;
    title: string;
    impactLevel: string;
    severityLevel: string;
    affectedEntities: Array<{
        entityId: {
            id: string;
            type: string;
        };
        name: string;
    }>;
    impactedEntities: Array<{
        entityId: {
            id: string;
            type: string;
        };
        name: string;
    }>;
    rootCauseEntity: {
        entityId: {
            id: string;
            type: string;
        };
        name: string;
    } | null;
    startTime: number;
    endTime: number;
}

export interface ExchangeRateResponse {
    rates: {
        CAD: number;
    };
}

export interface Endpoint {
    endpointName: string;
    requestCounts: number;
}

export interface EndpointResponse {
    records: Endpoint[];
}

export interface Trace {
    traceId: string;
    spanCounts: number;
}

export interface TraceResponse {
    records: Trace[];
}

export interface Span {
    "span.id": string;
    "span.parent_id"?: string;
    start_time: string;
    Service?: string;
    "server.address"?: string;
    "host.name"?: string;
    "span.kind"?: string;
    "service.name"?: string;
    "db.system"?: string;
    "endpoint.name"?: string;
    "http.request.method"?: string;
    "url.path"?: string;
    "db.operation.name"?: string;
    "span.name"?: string;
    "http.response.status_code"?: string;
    "db.result.execution_count"?: string;
    "nodejs.app.name"?: string;
    children?: Span[];
  }
export interface TreeSpan extends Span {
    children: TreeSpan[];
}

export interface SpanResponse {
    records: Span[];
}
