
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/00-overview/">
      
      
        <link rel="prev" href="..">
      
      
        <link rel="next" href="../frontend/00-overview/">
      
      
      <link rel="icon" href="../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Overview - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#overview-architecture" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Overview
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href=".." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-overview" class="md-nav__link">
    <span class="md-ellipsis">
      System Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="System Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-purpose" class="md-nav__link">
    <span class="md-ellipsis">
      Business Purpose
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#key-capabilities" class="md-nav__link">
    <span class="md-ellipsis">
      Key Capabilities
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Capabilities">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#order-capture-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Order Capture Features
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#business-logic" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Business Logic
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-points" class="md-nav__link">
    <span class="md-ellipsis">
      🌐 Integration Points
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#architecture-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Architecture Patterns
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Architecture Patterns">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#microservices-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Microservices Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Component Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#repository-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Repository Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Repository Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-repository-nc-cloud-bss-oc-ui-frontend-b2b" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Repository: nc-cloud-bss-oc-ui-frontend-b2b
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-repository-nc-cloud-bss-oc-ui-backend-b2b" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Repository: nc-cloud-bss-oc-ui-backend-b2b
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-repository-nc-cloud-bss-oc-ui-be-extension-b2b" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Repository: nc-cloud-bss-oc-ui-be-extension-b2b
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Technologies
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#external-system-integration" class="md-nav__link">
    <span class="md-ellipsis">
      External System Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-flow-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Data Flow Patterns
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Flow Patterns">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-generation-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Generation Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#order-processing-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Order Processing Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-scalability" class="md-nav__link">
    <span class="md-ellipsis">
      Performance &amp; Scalability
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance &amp; Scalability">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-performance" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Performance
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-scalability" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Scalability
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-efficiency" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Efficiency
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-overview" class="md-nav__link">
    <span class="md-ellipsis">
      System Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="System Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-purpose" class="md-nav__link">
    <span class="md-ellipsis">
      Business Purpose
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#key-capabilities" class="md-nav__link">
    <span class="md-ellipsis">
      Key Capabilities
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Capabilities">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#order-capture-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Order Capture Features
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#business-logic" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Business Logic
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-points" class="md-nav__link">
    <span class="md-ellipsis">
      🌐 Integration Points
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#architecture-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Architecture Patterns
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Architecture Patterns">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#microservices-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Microservices Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#component-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Component Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#repository-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Repository Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Repository Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-repository-nc-cloud-bss-oc-ui-frontend-b2b" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Repository: nc-cloud-bss-oc-ui-frontend-b2b
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-repository-nc-cloud-bss-oc-ui-backend-b2b" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Repository: nc-cloud-bss-oc-ui-backend-b2b
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-repository-nc-cloud-bss-oc-ui-be-extension-b2b" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Repository: nc-cloud-bss-oc-ui-be-extension-b2b
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Technologies
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#integration-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#external-system-integration" class="md-nav__link">
    <span class="md-ellipsis">
      External System Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-flow-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Data Flow Patterns
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Flow Patterns">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-generation-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Generation Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#order-processing-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Order Processing Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-scalability" class="md-nav__link">
    <span class="md-ellipsis">
      Performance &amp; Scalability
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance &amp; Scalability">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-performance" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend Performance
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-scalability" class="md-nav__link">
    <span class="md-ellipsis">
      Backend Scalability
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-efficiency" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Efficiency
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="overview-architecture">Overview &amp; Architecture<a class="headerlink" href="#overview-architecture" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#system-overview">System Overview</a></li>
<li><a href="#architecture-patterns">Architecture Patterns</a></li>
<li><a href="#repository-structure">Repository Structure</a></li>
<li><a href="#technology-stack">Technology Stack</a></li>
<li><a href="#integration-architecture">Integration Architecture</a></li>
<li><a href="#data-flow">Data Flow</a></li>
</ul>
<h2 id="system-overview">System Overview<a class="headerlink" href="#system-overview" title="Permanent link">&para;</a></h2>
<p>The TELUS B2B Order Capture ecosystem is a comprehensive, enterprise-grade solution designed to handle business-to-business order capture, quote generation, and service configuration for TELUS telecommunications services.</p>
<h3 id="business-purpose">Business Purpose<a class="headerlink" href="#business-purpose" title="Permanent link">&para;</a></h3>
<p>This system enables TELUS business customers to:
- <strong>Configure Services</strong>: Select and customize TELUS telecommunications services
- <strong>Generate Quotes</strong>: Create detailed pricing quotes with business rules
- <strong>Process Orders</strong>: Submit and track service orders
- <strong>Manage Locations</strong>: Handle service delivery addresses and locations
- <strong>Apply Discounts</strong>: Process business-specific pricing and discounts</p>
<h3 id="key-capabilities">Key Capabilities<a class="headerlink" href="#key-capabilities" title="Permanent link">&para;</a></h3>
<h4 id="order-capture-features">🎯 Order Capture Features<a class="headerlink" href="#order-capture-features" title="Permanent link">&para;</a></h4>
<ul>
<li>Multi-step order configuration workflow</li>
<li>Real-time quote generation and pricing</li>
<li>Service eligibility validation</li>
<li>Location-based service configuration</li>
<li>Cart management and order review</li>
</ul>
<h4 id="business-logic">🔧 Business Logic<a class="headerlink" href="#business-logic" title="Permanent link">&para;</a></h4>
<ul>
<li>Custom TELUS business rules</li>
<li>Bandwidth validation for network services</li>
<li>Quote expiration and lifecycle management</li>
<li>Service compatibility validation</li>
<li>Pricing calculation with discounts</li>
</ul>
<h4 id="integration-points">🌐 Integration Points<a class="headerlink" href="#integration-points" title="Permanent link">&para;</a></h4>
<ul>
<li>TELUS Order Capture Product (2023.3.2.x)</li>
<li>External geocoding services</li>
<li>TELUS BSS (Business Support Systems)</li>
<li>Authentication and authorization systems</li>
</ul>
<h2 id="architecture-patterns">Architecture Patterns<a class="headerlink" href="#architecture-patterns" title="Permanent link">&para;</a></h2>
<h3 id="microservices-architecture">Microservices Architecture<a class="headerlink" href="#microservices-architecture" title="Permanent link">&para;</a></h3>
<p>The system follows a distributed microservices pattern with clear separation of concerns:</p>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Frontend Layer&quot;
        FE[Angular Frontend]
        FE --&gt; |HTTP/REST| API
    end

    subgraph &quot;Backend Layer&quot;
        API[Spring Boot Backend]
        API --&gt; |Plugin Interface| EXT
        API --&gt; |Integration| OCP
    end

    subgraph &quot;Extension Layer&quot;
        EXT[QES Plugins]
        EXT --&gt; |Business Rules| QES
    end

    subgraph &quot;External Systems&quot;
        OCP[Order Capture Product]
        BSS[TELUS BSS]
        GEO[Geocoding Services]
    end

    API --&gt; BSS
    API --&gt; GEO
    OCP --&gt; BSS
</code></pre></div>
<h3 id="component-architecture">Component Architecture<a class="headerlink" href="#component-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;Frontend Components&quot;
        A[Custom TELUS Components]
        B[NgRx Store]
        C[Angular Services]
        D[Routing Module]
    end

    subgraph &quot;Backend Components&quot;
        E[REST Controllers]
        F[Business Services]
        G[Configuration]
        H[Integration Layer]
    end

    subgraph &quot;Extension Components&quot;
        I[Quote Modificators]
        J[Bandwidth Validators]
        K[State Machine Extensions]
        L[Common Utilities]
    end

    A --&gt; E
    B --&gt; C
    C --&gt; E
    E --&gt; F
    F --&gt; H
    F --&gt; I
    I --&gt; J
    I --&gt; K
    J --&gt; L
</code></pre></div>
<h2 id="repository-structure">Repository Structure<a class="headerlink" href="#repository-structure" title="Permanent link">&para;</a></h2>
<h3 id="frontend-repository-nc-cloud-bss-oc-ui-frontend-b2b">Frontend Repository: <code>nc-cloud-bss-oc-ui-frontend-b2b</code><a class="headerlink" href="#frontend-repository-nc-cloud-bss-oc-ui-frontend-b2b" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>nc-cloud-bss-oc-ui-frontend-b2b/
├── src/app/
│   ├── custom/                          # Custom TELUS components
│   │   ├── telus-assign-location/       # Location assignment
│   │   ├── telus-cart-total-prices/     # Cart pricing
│   │   ├── telus-quote-view/            # Quote display
│   │   ├── telus-start-quotation/       # Quote initiation
│   │   └── [18 more components...]      # Additional TELUS components
│   ├── modules/                         # Feature modules
│   ├── store/                           # NgRx state management
│   ├── interceptors/                    # HTTP interceptors
│   ├── environments/                    # Environment configs
│   └── configuration/                   # App configuration
├── deployments/                         # Kubernetes configs
├── scripts/                             # Build scripts
├── package.json                         # Dependencies (Angular 15.2.9)
├── angular.json                         # Angular CLI config
└── webpack.config.js                    # Webpack configuration
</code></pre></div>
<h3 id="backend-repository-nc-cloud-bss-oc-ui-backend-b2b">Backend Repository: <code>nc-cloud-bss-oc-ui-backend-b2b</code><a class="headerlink" href="#backend-repository-nc-cloud-bss-oc-ui-backend-b2b" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>nc-cloud-bss-oc-ui-backend-b2b/
├── cloud-bss-oc-ui-backend-b2b-application/
│   └── src/main/java/com/netcracker/solutions/telus/ordercapture/backend/
│       ├── OrderEntryWebApplication.java    # Main Spring Boot app
│       └── config/                          # Configuration classes
├── cloud-bss-oc-ui-backend-b2b-resources/  # Resource definitions
├── nc-cloud-bss-oc-ui-be-b2b-impl/        # Implementation modules
├── deployments/                             # Kubernetes configs
├── pom.xml                                  # Maven parent POM
└── Dockerfile                               # Container definition
</code></pre></div>
<h3 id="extension-repository-nc-cloud-bss-oc-ui-be-extension-b2b">Extension Repository: <code>nc-cloud-bss-oc-ui-be-extension-b2b</code><a class="headerlink" href="#extension-repository-nc-cloud-bss-oc-ui-be-extension-b2b" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>nc-cloud-bss-oc-ui-be-extension-b2b/
├── b2b-qes-plugin/
│   └── src/main/java/com/netcracker/solutions/telus/qes/extension/plugin/
│       ├── TelusQuoteDeltaModificatorImpl.java      # Quote modifications
│       ├── TelusQuoteExpirationPeriodProvider.java # Quote lifecycle
│       ├── TelusStateMachineExtensionValidator.java # State validation
│       ├── TelusWan*BandwidthValidator.java         # Bandwidth validators
│       └── common/utils/                            # Utility classes
├── deployments/charts/                              # Helm charts
└── pom.xml                                          # Maven configuration
</code></pre></div>
<h2 id="technology-stack">Technology Stack<a class="headerlink" href="#technology-stack" title="Permanent link">&para;</a></h2>
<h3 id="frontend-technologies">Frontend Technologies<a class="headerlink" href="#frontend-technologies" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Technology</th>
<th>Version</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Angular</strong></td>
<td>15.2.9</td>
<td>Core framework</td>
</tr>
<tr>
<td><strong>TypeScript</strong></td>
<td>4.9.5</td>
<td>Type-safe development</td>
</tr>
<tr>
<td><strong>NgRx</strong></td>
<td>15.4.0</td>
<td>State management</td>
</tr>
<tr>
<td><strong>LESS</strong></td>
<td>3.13.1</td>
<td>CSS preprocessing</td>
</tr>
<tr>
<td><strong>Webpack</strong></td>
<td>-</td>
<td>Module bundling</td>
</tr>
<tr>
<td><strong>Netcracker UX</strong></td>
<td>2023.3.9</td>
<td>UI component library</td>
</tr>
</tbody>
</table>
<h3 id="backend-technologies">Backend Technologies<a class="headerlink" href="#backend-technologies" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Technology</th>
<th>Version</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Spring Boot</strong></td>
<td>3.1.12</td>
<td>Application framework</td>
</tr>
<tr>
<td><strong>Java</strong></td>
<td>17</td>
<td>Programming language</td>
</tr>
<tr>
<td><strong>Maven</strong></td>
<td>-</td>
<td>Build management</td>
</tr>
<tr>
<td><strong>Order Capture Product</strong></td>
<td>2023.3.2.63</td>
<td>Core business platform</td>
</tr>
<tr>
<td><strong>OpenAPI/Swagger</strong></td>
<td>2.2.7</td>
<td>API documentation</td>
</tr>
</tbody>
</table>
<h3 id="extension-technologies">Extension Technologies<a class="headerlink" href="#extension-technologies" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Technology</th>
<th>Version</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>QES Framework</strong></td>
<td>2023.3.2.0</td>
<td>Quote Engine Services</td>
</tr>
<tr>
<td><strong>Java</strong></td>
<td>17</td>
<td>Programming language</td>
</tr>
<tr>
<td><strong>Maven</strong></td>
<td>-</td>
<td>Build management</td>
</tr>
<tr>
<td><strong>Lombok</strong></td>
<td>1.18.30</td>
<td>Code generation</td>
</tr>
</tbody>
</table>
<h3 id="deployment-technologies">Deployment Technologies<a class="headerlink" href="#deployment-technologies" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Technology</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Docker</strong></td>
<td>Containerization</td>
</tr>
<tr>
<td><strong>Kubernetes</strong></td>
<td>Container orchestration</td>
</tr>
<tr>
<td><strong>Helm</strong></td>
<td>Package management</td>
</tr>
<tr>
<td><strong>GitLab CI</strong></td>
<td>Continuous integration</td>
</tr>
<tr>
<td><strong>Nginx</strong></td>
<td>Frontend web server</td>
</tr>
</tbody>
</table>
<h2 id="integration-architecture">Integration Architecture<a class="headerlink" href="#integration-architecture" title="Permanent link">&para;</a></h2>
<h3 id="external-system-integration">External System Integration<a class="headerlink" href="#external-system-integration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;TELUS B2B Order Capture&quot;
        FE[Frontend Angular App]
        BE[Backend Spring Boot]
        EXT[QES Extensions]
    end

    subgraph &quot;TELUS Core Systems&quot;
        OCP[Order Capture Product]
        BSS[Business Support Systems]
        AUTH[Authentication Service]
    end

    subgraph &quot;External Services&quot;
        GEO[Geocoding Service]
        MAPS[Google Maps API]
    end

    FE --&gt; |REST API| BE
    BE --&gt; |Plugin Interface| EXT
    BE --&gt; |Integration API| OCP
    OCP --&gt; |Business Logic| BSS
    FE --&gt; |Direct API| MAPS
    BE --&gt; |HTTP API| GEO
    FE --&gt; |OAuth/JWT| AUTH
</code></pre></div>
<h3 id="data-flow-patterns">Data Flow Patterns<a class="headerlink" href="#data-flow-patterns" title="Permanent link">&para;</a></h3>
<h4 id="quote-generation-flow">Quote Generation Flow<a class="headerlink" href="#quote-generation-flow" title="Permanent link">&para;</a></h4>
<ol>
<li><strong>Frontend</strong>: User configures services and location</li>
<li><strong>Backend</strong>: Validates configuration and calls QES</li>
<li><strong>Extensions</strong>: Apply business rules and bandwidth validation</li>
<li><strong>Order Capture</strong>: Generates pricing and quote details</li>
<li><strong>Frontend</strong>: Displays quote with pricing breakdown</li>
</ol>
<h4 id="order-processing-flow">Order Processing Flow<a class="headerlink" href="#order-processing-flow" title="Permanent link">&para;</a></h4>
<ol>
<li><strong>Frontend</strong>: User reviews and submits order</li>
<li><strong>Backend</strong>: Validates order and initiates processing</li>
<li><strong>Extensions</strong>: Apply final validation rules</li>
<li><strong>BSS Integration</strong>: Creates service orders in TELUS systems</li>
<li><strong>Frontend</strong>: Displays order confirmation and tracking</li>
</ol>
<h2 id="performance-scalability">Performance &amp; Scalability<a class="headerlink" href="#performance-scalability" title="Permanent link">&para;</a></h2>
<h3 id="frontend-performance">Frontend Performance<a class="headerlink" href="#frontend-performance" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Lazy Loading</strong>: Feature modules loaded on demand</li>
<li><strong>OnPush Strategy</strong>: Optimized change detection</li>
<li><strong>Virtual Scrolling</strong>: Efficient large list rendering</li>
<li><strong>Bundle Optimization</strong>: Tree shaking and code splitting</li>
</ul>
<h3 id="backend-scalability">Backend Scalability<a class="headerlink" href="#backend-scalability" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Stateless Design</strong>: Horizontal scaling capability</li>
<li><strong>Connection Pooling</strong>: Efficient database connections</li>
<li><strong>Caching Strategy</strong>: Reduced external API calls</li>
<li><strong>Async Processing</strong>: Non-blocking operations</li>
</ul>
<h3 id="extension-efficiency">Extension Efficiency<a class="headerlink" href="#extension-efficiency" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Plugin Architecture</strong>: Modular business logic</li>
<li><strong>Validation Caching</strong>: Reduced computation overhead</li>
<li><strong>Rule Engine</strong>: Efficient business rule evaluation</li>
</ul>
<hr />
<p><strong>Next</strong>: Explore the <a href="../01-frontend-guide/">Frontend Guide</a> for detailed Angular development information.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "..", "features": [], "search": "../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>