#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import dotenv from 'dotenv';
import AuthManager from './utils/auth.js';
import TMF620Api from './tmf620Api.js';
import { Environment } from './types.js';

// Load environment variables from .env file
dotenv.config();

console.log('Loading environment variables...');
// Log non-sensitive environment variables
console.log('Environment configuration:');
console.log('DEV_API_URL:', process.env.DEV_API_URL);
console.log('TEST_API_URL:', process.env.TEST_API_URL);
console.log('PROD_API_URL:', process.env.PROD_API_URL);
console.log('OAUTH_CLIENT_ID:', process.env.OAUTH_CLIENT_ID);
console.log('OAUTH_TOKEN_URL:', process.env.OAUTH_TOKEN_URL);
console.log('OAUTH_SCOPE:', process.env.OAUTH_SCOPE);
// Don't log OAUTH_CLIENT_SECRET for security

// Initialize authentication manager with OAuth credentials
const authManager = new AuthManager(
  process.env.OAUTH_CLIENT_ID!,
  process.env.OAUTH_CLIENT_SECRET!,
  process.env.OAUTH_TOKEN_URL!,
  process.env.OAUTH_SCOPE!
);

// Load rate limiting configuration from environment or use default
const MAX_REQUESTS_PER_SECOND = parseInt(process.env.MAX_REQUESTS_PER_SECOND || '10', 10);
console.log('MAX_REQUESTS_PER_SECOND:', MAX_REQUESTS_PER_SECOND);

// Configure API URLs and rate limiting
const apiConfig = {
  devApiUrl: process.env.DEV_API_URL!,
  testApiUrl: process.env.TEST_API_URL!,
  prodApiUrl: process.env.PROD_API_URL!,
  maxRequestsPerSecond: MAX_REQUESTS_PER_SECOND,
};

// Initialize TMF620 API client
let tmf620Api: TMF620Api;

/**
 * TMF620Server class implements an MCP server for the TMF620 Product Catalog Management API.
 * This server provides tools for interacting with product catalogs, offerings, and prices.
 */
class TMF620Server {
  private server: Server;
  private tools: Array<{ name: string; description: string; inputSchema: any }>;

  /**
   * Creates an instance of TMF620Server.
   * Initializes the MCP server and sets up tool handlers.
   */
  constructor() {
    console.log('Initializing TMF620Server...');
    
    // Define available tools
    this.tools = [
      {
        name: 'get_product_offering',
        description: 'Fetch a specific product offering by ID',
        inputSchema: {
          type: 'object',
          properties: {
            id: { type: 'string', description: 'The ID of the product offering to fetch' },
            environment: { type: 'string', enum: ['dev', 'test', 'prod'], description: 'The environment to query' },
            fields: { type: 'string', description: 'Comma-separated properties to include in the response', optional: true },
          },
          required: ['id', 'environment'],
        },
      },
      {
        name: 'get_product_offering_by_external_id',
        description: 'Fetch a specific product offering by external ID',
        inputSchema: {
          type: 'object',
          properties: {
            externalId: { type: 'string', description: 'The external ID of the product offering to fetch' },
            environment: { type: 'string', enum: ['dev', 'test', 'prod'], description: 'The environment to query' },
            fields: { type: 'string', description: 'Comma-separated properties to include in the response', optional: true },
          },
          required: ['externalId', 'environment'],
        },
      },
      {
        name: 'list_product_offerings',
        description: 'List or search for product offerings with filtering',
        inputSchema: {
          type: 'object',
          properties: {
            environment: { type: 'string', enum: ['dev', 'test', 'prod'], description: 'The environment to query' },
            filters: { type: 'object', description: 'Object containing filter criteria', optional: true },
            fields: { type: 'string', description: 'Comma-separated properties to include in the response', optional: true },
            offset: { type: 'number', description: 'Starting index for pagination', optional: true },
            limit: { type: 'number', description: 'Maximum number of results to return', optional: true },
          },
          required: ['environment'],
        },
      },
      {
        name: 'bulk_retrieve_product_offerings',
        description: 'Retrieve multiple product offerings by their IDs',
        inputSchema: {
          type: 'object',
          properties: {
            ids: { type: 'array', items: { type: 'string' }, description: 'Array of product offering IDs to retrieve' },
            environment: { type: 'string', enum: ['dev', 'test', 'prod'], description: 'The environment to query' },
            fields: { type: 'string', description: 'Comma-separated properties to include in the response', optional: true },
          },
          required: ['ids', 'environment'],
        },
      },
      {
        name: 'get_catalog',
        description: 'Fetch a specific catalog by ID',
        inputSchema: {
          type: 'object',
          properties: {
            id: { type: 'string', description: 'The ID of the catalog to fetch' },
            environment: { type: 'string', enum: ['dev', 'test', 'prod'], description: 'The environment to query' },
            fields: { type: 'string', description: 'Comma-separated properties to include in the response', optional: true },
          },
          required: ['id', 'environment'],
        },
      },
      {
        name: 'list_catalogs',
        description: 'List or search for catalogs with filtering',
        inputSchema: {
          type: 'object',
          properties: {
            environment: { type: 'string', enum: ['dev', 'test', 'prod'], description: 'The environment to query' },
            filters: { type: 'object', description: 'Object containing filter criteria', optional: true },
            fields: { type: 'string', description: 'Comma-separated properties to include in the response', optional: true },
            offset: { type: 'number', description: 'Starting index for pagination', optional: true },
            limit: { type: 'number', description: 'Maximum number of results to return', optional: true },
          },
          required: ['environment'],
        },
      },
    ];

    // Initialize server with tools in capabilities
    this.server = new Server(
      {
        name: 'tmf620-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: Object.fromEntries(
            this.tools.map(tool => [
              tool.name,
              {
                description: tool.description,
                inputSchema: tool.inputSchema,
              },
            ])
          ),
        },
      }
    );

    this.setupToolHandlers();
    
    this.server.onerror = (error) => console.error('[MCP Error]', error);
    process.on('SIGINT', async () => {
      console.log('Received SIGINT, shutting down...');
      await this.server.close();
      process.exit(0);
    });
  }

  /**
   * Sets up handlers for the MCP tools provided by this server.
   * Configures available tools and their input schemas.
   */
  private setupToolHandlers() {
    console.log('Setting up tool handlers...');
    
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      console.log('Handling ListToolsRequest');
      return { tools: this.tools };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      console.log('Handling CallToolRequest:', request.params);
      const { name, arguments: args } = request.params;
      
      try {
        switch (name) {
          case 'get_product_offering':
            return await this.getProductOffering(args);
          case 'get_product_offering_by_external_id':
            return await this.getProductOfferingByExternalId(args);
          case 'list_product_offerings':
            return await this.listProductOfferings(args);
          case 'bulk_retrieve_product_offerings':
            return await this.bulkRetrieveProductOfferings(args);
          case 'get_catalog':
            return await this.getCatalog(args);
          case 'list_catalogs':
            return await this.listCatalogs(args);
          default:
            throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${name}`);
        }
      } catch (error) {
        console.error(`Error in ${name}:`, error);
        return {
          content: [{ type: 'text', text: `Error: ${(error as Error).message}` }],
          isError: true,
        };
      }
    });
  }

  /**
   * Handles requests to get a product offering by ID.
   */
  private async getProductOffering(args: any) {
    console.log('Getting product offering:', args);
    const { id, environment, fields } = args;
    const result = await tmf620Api.getProductOffering(id, environment as Environment, fields);
    console.log('Product offering result:', result);
    return {
      content: [{ type: 'text', text: JSON.stringify(result, null, 2) }],
    };
  }

  /**
   * Handles requests to get a product offering by external ID.
   */
  private async getProductOfferingByExternalId(args: any) {
    console.log('Getting product offering by external ID:', args);
    const { externalId, environment, fields } = args;
    const result = await tmf620Api.getProductOfferingByExternalId(externalId, environment as Environment, fields);
    console.log('Product offering result:', result);
    return {
      content: [{ type: 'text', text: JSON.stringify(result, null, 2) }],
    };
  }

  /**
   * Handles requests to list product offerings with optional filtering and pagination.
   */
  private async listProductOfferings(args: any) {
    console.log('Listing product offerings:', args);
    const { environment, filters, fields, offset, limit } = args;
    const result = await tmf620Api.listProductOfferings(environment as Environment, filters, fields, offset, limit);
    console.log('Product offerings result:', result);
    return {
      content: [{ type: 'text', text: JSON.stringify(result, null, 2) }],
    };
  }

  /**
   * Handles requests to bulk retrieve multiple product offerings by their IDs.
   */
  private async bulkRetrieveProductOfferings(args: any) {
    console.log('Bulk retrieving product offerings:', args);
    const { ids, environment, fields } = args;
    const result = await tmf620Api.bulkRetrieveProductOfferings(ids, environment as Environment, fields);
    console.log('Bulk product offerings result:', result);
    return {
      content: [{ type: 'text', text: JSON.stringify(result, null, 2) }],
    };
  }

  /**
   * Handles requests to get a catalog by ID.
   */
  private async getCatalog(args: any) {
    console.log('Getting catalog:', args);
    const { id, environment, fields } = args;
    const result = await tmf620Api.getCatalog(id, environment as Environment, fields);
    console.log('Catalog result:', result);
    return {
      content: [{ type: 'text', text: JSON.stringify(result, null, 2) }],
    };
  }
  
  /**
   * Handles requests to list catalogs with optional filtering and pagination.
   */
  private async listCatalogs(args: any) {
    console.log('Listing catalogs:', args);
    const { environment, filters, fields, offset, limit } = args;
    const result = await tmf620Api.listCatalogs(environment as Environment, filters, fields, offset, limit);
    console.log('Catalogs result:', result);
    return {
      content: [{ type: 'text', text: JSON.stringify(result, null, 2) }],
    };
  }

  /**
   * Starts the MCP server.
   * Initializes the transport layer and begins listening for requests.
   */
  async run() {
    console.log('Starting TMF620 MCP server...');
    console.log('Current working directory:', process.cwd());
    console.log('Node version:', process.version);
    console.log('Environment:', process.env.NODE_ENV);

    // Log API configuration (without sensitive data)
    console.log('API Configuration:', {
      devApiUrl: apiConfig.devApiUrl,
      testApiUrl: apiConfig.testApiUrl,
      prodApiUrl: apiConfig.prodApiUrl,
      maxRequestsPerSecond: apiConfig.maxRequestsPerSecond
    });

    const transport = new StdioServerTransport();
    console.log('Created StdioServerTransport');

    try {
      console.log('Initializing TMF620Api...');
      tmf620Api = new TMF620Api(authManager, apiConfig);
      await tmf620Api.initialize();
      console.log('TMF620Api initialized successfully');

      console.log('Attempting to connect to transport...');
      const connectStart = Date.now();
      
            // Set up connection timeout with increased timeout value
            const connectionPromise = this.server.connect(transport);
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Connection timeout after 120 seconds')), 120000);
            });

      try {
        await Promise.race([connectionPromise, timeoutPromise]);
        console.log(`Transport connection established in ${Date.now() - connectStart}ms`);
      } catch (error) {
        console.error('Transport connection failed:', error);
        throw error;
      }

      console.log('Successfully connected to transport');
      console.log('TMF620 MCP server started successfully');
      
      // Test API connection with timeout
      console.log('Testing API connection...');
      try {
        const apiStart = Date.now();
        const apiPromise = tmf620Api.listProductOfferings('test', {}, '', 0, 1);
                const apiTimeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('API request timeout after 120 seconds')), 120000);
                });

        const testResult = await Promise.race([apiPromise, apiTimeoutPromise]);
        console.log(`API connection test successful in ${Date.now() - apiStart}ms:`, testResult);
      } catch (error: unknown) {
        console.error('API connection test failed:', error);
        if (error instanceof Error) {
          if (error.message.includes('timeout')) {
            console.error('API connection timed out. Please check:');
            console.error('1. API endpoints are correct and accessible');
            console.error('2. Network connectivity and firewall settings');
            console.error('3. VPN connection if required');
            console.error('4. API service status');
          } else {
            console.error('API Error:', error.message);
          }
        } else {
          console.error('Unknown error occurred during API connection test');
        }
      }
      
      // Log registered tools and their schemas
      console.log('Registered tools:', this.tools.map(tool => ({
        name: tool.name,
        schema: tool.inputSchema
      })));
      
      // Set up periodic health check
      setInterval(async () => {
        const now = new Date().toISOString();
        console.log(`Health check at ${now}`);
        try {
          await Promise.race([
            tmf620Api.listProductOfferings('test', {}, '', 0, 1),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Health check timeout')), 5000))
          ]);
          console.log('Health check: API connection OK');
        } catch (error) {
          console.error('Health check: API connection failed:', error);
        }
      }, 60000); // Check every minute
    } catch (error) {
      console.error('Failed to start TMF620 MCP server:', error);
      throw error;
    }
  }
}

// Create and start the server
const server = new TMF620Server();
server.run().catch((error) => {
  console.error('Failed to start TMF620 MCP server:', error);
  process.exit(1);
});
