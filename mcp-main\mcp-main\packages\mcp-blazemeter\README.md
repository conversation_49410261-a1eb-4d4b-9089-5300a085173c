# @telus/mcp-blazemeter

This MCP server provides integration with BlazeMeter's performance testing platform, allowing you to manage workspaces, run performance tests, and retrieve test results.

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-blazemeter
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for <PERSON> Desktop):

   ```json
   {
     "mcpServers": {
       "blazemeter": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-blazemeter"],
         "env": {
           "BLAZEMETER_API_KEY": "your-api-key",
           "BLAZEMETER_API_SECRET": "your-api-secret",
           "BLAZEMETER_API_URL": "https://api.blazemeter.com/v4" // Optional
         }
       }
     }
   }
   ```

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-blazemeter
   pnpm install
   ```

2. Build the server:

   ```bash
   pnpm build
   ```

3. Start the server:

   ```bash
   pnpm start
   ```

## Configuration

The server requires the following environment variables:

- `BLAZEMETER_API_KEY`: Your BlazeMeter API key
- `BLAZEMETER_API_SECRET`: Your BlazeMeter API secret
- `BLAZEMETER_API_URL` (optional): BlazeMeter API URL (defaults to 'https://api.blazemeter.com/v4')

You can obtain your API key and secret from the BlazeMeter website:

1. Log in to your BlazeMeter account
2. Go to Settings > API Keys
3. Generate a new API key if you don't have one
4. Copy both the API key and API secret

## Available Functions

### get_workspace

Retrieves information about a specific BlazeMeter workspace.

Parameters:

- `workspaceId` (string, required): ID of the workspace to retrieve

Sample prompt:

```
Can you get the workspace information for workspace ID "123456"?
```

Example usage:

```json
{
  "server_name": "blazemeter",
  "tool_name": "get_workspace",
  "arguments": {
    "workspaceId": "123456"
  }
}
```

### get_workspace_tests

Retrieves all tests within a specified workspace.

Parameters:

- `workspaceId` (string, required): ID of the workspace to retrieve tests from
- `type` (string, optional): Type of tests to retrieve (e.g., "taurus"). Defaults to "taurus"

Sample prompt:

```
List all tests in workspace "123456", filtering for taurus tests only.
```

Example usage:

```json
{
  "server_name": "blazemeter",
  "tool_name": "get_workspace_tests",
  "arguments": {
    "workspaceId": "123456",
    "type": "taurus"
  }
}
```

### get_test_details

Retrieves detailed information about a specific test.

Parameters:

- `test_id` (string, required): ID of the test to retrieve information for

Sample prompt:

```
Can you get the details for test ID "789012"?
```

Example usage:

```json
{
  "server_name": "blazemeter",
  "tool_name": "get_test_details",
  "arguments": {
    "test_id": "789012"
  }
}
```

### run_performance_test

Starts a performance test with specified parameters.

Parameters:

- `test_id` (string, required): ID of the test to run
- `virtual_users` (number, required): Number of virtual users for the test (minimum: 1)
- `duration` (number, required): Test duration in minutes (minimum: 1)

Sample prompt:

```
Run a performance test for test ID "789012" with 100 users for 5 minutes.
```

Example usage:

```json
{
  "server_name": "blazemeter",
  "tool_name": "run_performance_test",
  "arguments": {
    "test_id": "789012",
    "virtual_users": 100,
    "duration": 5
  }
}
```

### get_test_execution_results_summary

Retrieves summary results of a test execution with optional scenario filtering.

Parameters:

- `test_execution_id` (string, required): ID of the test execution
- `scenarios` (string[], optional): Array of scenario IDs to filter results
- `filter` (string, optional): Filter option for the results. Possible values:
  - "ALL" (default): Returns only the summary for the "ALL" scenario
  - "FULL": Returns the full breakdown of all scenarios
  - Any other string: Returns the summary for the specified label name

Sample prompts:

```
Get the test execution results summary for execution ID "345678".
```

```
Get the full breakdown of test execution results for execution ID "345678".
```

```
Get the test execution results summary for execution ID "345678", focusing on scenario "abc123".
```

Example usage:

```json
{
  "server_name": "blazemeter",
  "tool_name": "get_test_execution_results_summary",
  "arguments": {
    "test_execution_id": "345678",
    "filter": "FULL"
  }
}
```

```json
{
  "server_name": "blazemeter",
  "tool_name": "get_test_execution_results_summary",
  "arguments": {
    "test_execution_id": "345678",
    "scenarios": ["abc123"],
    "filter": "ALL"
  }
}
```

### get_test_execution_status

Retrieves the current status of a test execution.

Parameters:

- `test_execution_id` (string, required): ID of the test execution to check status for

Sample prompt:

```
What's the current status of test execution "345678"?
```

Example usage:

```json
{
  "server_name": "blazemeter",
  "tool_name": "get_test_execution_status",
  "arguments": {
    "test_execution_id": "345678"
  }
}
```

### get_test_execution_details

Retrieves detailed information about a test execution including scenarios and configuration.

Parameters:

- `masterId` (string, required): ID of the test execution (master) to retrieve details for

Sample prompt:

```
Get the detailed execution information for test execution ID "345678".
```

Example usage:

```json
{
  "server_name": "blazemeter",
  "tool_name": "get_test_execution_details",
  "arguments": {
    "masterId": "345678"
  }
}
```

### update_multi_test

Updates the configuration of a multi-test.

Parameters:

- `collectionId` (string, required): ID of the multi-test to update
- `index` (number, required): Index of the test to update within the multi-test
- `overrideExecutions` (array, required): Array of execution override configurations
  - `concurrency` (number, optional): Number of concurrent users
  - `holdFor` (string, optional): Duration to hold load (e.g., "5m")
  - `rampUp` (string, optional): Duration for ramping up load
  - `steps` (number, optional): Number of steps in the load profile
  - `executor` (string, optional): Test executor type
  - `locations` (object, optional): Test locations configuration
  - `locationsPercents` (object, optional): Distribution percentage across locations

Sample prompt:

```
Update multi-test "901234" at index 0 to run with 50 users, holding for 10 minutes.
```

Example usage:

```json
{
  "server_name": "blazemeter",
  "tool_name": "update_multi_test",
  "arguments": {
    "collectionId": "901234",
    "index": 0,
    "overrideExecutions": [
      {
        "concurrency": 50,
        "holdFor": "10m",
        "executor": "jmeter"
      }
    ]
  }
}
```

### start_multi_test

Starts a multi-test execution.

Parameters:

- `collectionId` (string, required): ID of the multi-test to start
- `delayedStart` (boolean, optional): Whether to use delayed start. Defaults to false

Sample prompt:

```
Start multi-test "901234" with delayed start enabled.
```

Example usage:

```json
{
  "server_name": "blazemeter",
  "tool_name": "start_multi_test",
  "arguments": {
    "collectionId": "901234",
    "delayedStart": true
  }
}
```

### get_multi_test_details

Retrieves detailed information about a multi-test.

Parameters:

- `collectionId` (string, required): ID of the multi-test to retrieve details for
- `populateTests` (boolean, optional): Whether to include test details in the response. Defaults to true

Sample prompt:

```
Get the details for multi-test "901234", including all test information.
```

Example usage:

```json
{
  "server_name": "blazemeter",
  "tool_name": "get_multi_test_details",
  "arguments": {
    "collectionId": "901234",
    "populateTests": true
  }
}
```

## Response Format

All functions return responses in the following format:

```typescript
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  error?: any;
}
```

## Error Handling

The server handles various error cases:

- Invalid API credentials
- Network errors
- Invalid test IDs
- Invalid parameters
- BlazeMeter API errors

Error responses include detailed messages to help diagnose and resolve issues.

## Sample Workflow

Here's a typical workflow for running a performance test and getting results:

1. Get workspace tests to find your test ID:

```
Can you list all tests in workspace "123456"?
```

2. Start a performance test:

```
Run a performance test for test ID "789012" with 100 users for 5 minutes.
```

3. Check test execution status:

```
What's the status of test execution "345678"?
```

4. Get test results once completed:

```
Get the execution results summary for test "345678".
```
