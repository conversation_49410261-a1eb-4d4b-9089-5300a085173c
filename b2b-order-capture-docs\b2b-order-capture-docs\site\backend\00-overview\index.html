
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/backend/00-overview/">
      
      
        <link rel="prev" href="../../frontend/05-development-guide/">
      
      
        <link rel="next" href="../01-architecture/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Overview - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#backend-application-overview" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Overview
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" checked>
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#introduction" class="md-nav__link">
    <span class="md-ellipsis">
      Introduction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Introduction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#purpose-and-scope" class="md-nav__link">
    <span class="md-ellipsis">
      Purpose and Scope
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#high-level-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      High-Level Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#multi-module-maven-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Multi-Module Maven Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Core Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-and-data-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Integration and Data Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-and-build-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Development and Build Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Business Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Business Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-management-apis" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Management APIs
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#order-processing-apis" class="md-nav__link">
    <span class="md-ellipsis">
      Order Processing APIs
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-management" class="md-nav__link">
    <span class="md-ellipsis">
      Service Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technical-features" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Technical Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Technical Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rest-api-design" class="md-nav__link">
    <span class="md-ellipsis">
      REST API Design
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-capabilities" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Capabilities
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-and-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Security and Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-module-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Module Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#package-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Package Structure
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-profiles" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Profiles
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#available-maven-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Available Maven Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-urls" class="md-nav__link">
    <span class="md-ellipsis">
      Development URLs
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#docker-development" class="md-nav__link">
    <span class="md-ellipsis">
      Docker Development
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#introduction" class="md-nav__link">
    <span class="md-ellipsis">
      Introduction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Introduction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#purpose-and-scope" class="md-nav__link">
    <span class="md-ellipsis">
      Purpose and Scope
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#high-level-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      High-Level Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#multi-module-maven-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Multi-Module Maven Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Core Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-and-data-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Integration and Data Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-and-build-tools" class="md-nav__link">
    <span class="md-ellipsis">
      Development and Build Tools
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Business Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Business Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-management-apis" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Management APIs
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#order-processing-apis" class="md-nav__link">
    <span class="md-ellipsis">
      Order Processing APIs
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-management" class="md-nav__link">
    <span class="md-ellipsis">
      Service Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technical-features" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Technical Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Technical Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rest-api-design" class="md-nav__link">
    <span class="md-ellipsis">
      REST API Design
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-capabilities" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Capabilities
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-and-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Security and Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-module-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Module Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#package-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Package Structure
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-profiles" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Profiles
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#available-maven-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Available Maven Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-urls" class="md-nav__link">
    <span class="md-ellipsis">
      Development URLs
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#docker-development" class="md-nav__link">
    <span class="md-ellipsis">
      Docker Development
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="backend-application-overview">Backend Application Overview<a class="headerlink" href="#backend-application-overview" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#introduction">Introduction</a></li>
<li><a href="#architecture-overview">Architecture Overview</a></li>
<li><a href="#technology-stack">Technology Stack</a></li>
<li><a href="#key-features">Key Features</a></li>
<li><a href="#project-structure">Project Structure</a></li>
<li><a href="#development-environment">Development Environment</a></li>
</ul>
<h2 id="introduction">Introduction<a class="headerlink" href="#introduction" title="Permanent link">&para;</a></h2>
<p>The <strong>nc-cloud-bss-oc-ui-backend-b2b</strong> is a sophisticated Spring Boot 3.1.12 application that serves as the core backend service for the TELUS B2B Order Capture ecosystem. This enterprise-grade backend application provides robust REST APIs, business logic processing, and seamless integration with the NetCracker Order Capture Product and TELUS BSS systems.</p>
<h3 id="purpose-and-scope">Purpose and Scope<a class="headerlink" href="#purpose-and-scope" title="Permanent link">&para;</a></h3>
<p>The backend application is designed to:
- <strong>Provide REST APIs</strong>: Comprehensive RESTful services for quote and order management
- <strong>Process Business Logic</strong>: Complex telecommunications service processing and validation
- <strong>Integrate Systems</strong>: Seamless connectivity with Order Capture Product and TELUS BSS
- <strong>Ensure Security</strong>: Enterprise-grade authentication and authorization
- <strong>Support Scalability</strong>: Cloud-native architecture for high availability</p>
<h2 id="architecture-overview">Architecture Overview<a class="headerlink" href="#architecture-overview" title="Permanent link">&para;</a></h2>
<h3 id="high-level-architecture">High-Level Architecture<a class="headerlink" href="#high-level-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Backend Application&quot;
        A[Spring Boot App] --&gt; B[REST Controllers]
        A --&gt; C[Service Layer]
        A --&gt; D[Integration Layer]
        A --&gt; E[Configuration]

        B --&gt; F[Quote Controller]
        B --&gt; G[Order Controller]
        B --&gt; H[Service Controller]
        B --&gt; I[Customer Controller]

        C --&gt; J[Quote Service]
        C --&gt; K[Order Service]
        C --&gt; L[Validation Service]
        C --&gt; M[Mapping Service]

        D --&gt; N[Order Capture Client]
        D --&gt; O[BSS Integration]
        D --&gt; P[External APIs]
    end

    subgraph &quot;External Systems&quot;
        Q[Order Capture Product]
        R[TELUS BSS Systems]
        S[CIP Platform]
        T[Frontend Application]
    end

    A --&gt; Q
    A --&gt; R
    A --&gt; S
    T --&gt; A

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style C fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="multi-module-maven-architecture">Multi-Module Maven Architecture<a class="headerlink" href="#multi-module-maven-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;Maven Multi-Module Project&quot;
        A[Parent POM]
        B[Application Module]
        C[Resources Module]
        D[Implementation Module]
    end

    subgraph &quot;Application Module&quot;
        E[OrderEntryWebApplication]
        F[Configuration Classes]
        G[REST Controllers]
    end

    subgraph &quot;Implementation Module&quot;
        H[Service Layer]
        I[Business Logic]
        J[Integration Clients]
        K[Data Models]
    end

    subgraph &quot;Resources Module&quot;
        L[Configuration Files]
        M[Application Properties]
        N[Static Resources]
    end

    A --&gt; B
    A --&gt; C
    A --&gt; D

    B --&gt; E
    B --&gt; F
    B --&gt; G

    D --&gt; H
    D --&gt; I
    D --&gt; J
    D --&gt; K

    C --&gt; L
    C --&gt; M
    C --&gt; N

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style D fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h2 id="technology-stack">Technology Stack<a class="headerlink" href="#technology-stack" title="Permanent link">&para;</a></h2>
<h3 id="core-technologies">Core Technologies<a class="headerlink" href="#core-technologies" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Technology</th>
<th>Version</th>
<th>Purpose</th>
<th>Key Features</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Spring Boot</strong></td>
<td>3.1.12</td>
<td>Application Framework</td>
<td>Auto-configuration, embedded server, production-ready features</td>
</tr>
<tr>
<td><strong>Java</strong></td>
<td>17</td>
<td>Programming Language</td>
<td>Modern language features, performance, enterprise support</td>
</tr>
<tr>
<td><strong>Maven</strong></td>
<td>3.x</td>
<td>Build Tool</td>
<td>Multi-module projects, dependency management, lifecycle management</td>
</tr>
<tr>
<td><strong>Spring Web</strong></td>
<td>6.x</td>
<td>REST Framework</td>
<td>RESTful services, HTTP handling, content negotiation</td>
</tr>
<tr>
<td><strong>Spring Security</strong></td>
<td>6.x</td>
<td>Security Framework</td>
<td>Authentication, authorization, CORS, CSRF protection</td>
</tr>
</tbody>
</table>
<h3 id="integration-and-data-technologies">Integration and Data Technologies<a class="headerlink" href="#integration-and-data-technologies" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Technology</th>
<th>Version</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Order Capture Product</strong></td>
<td>2023.3.2.63</td>
<td>Core Integration</td>
</tr>
<tr>
<td><strong>Jackson</strong></td>
<td>2.15.x</td>
<td>JSON Processing</td>
</tr>
<tr>
<td><strong>OpenAPI 3</strong></td>
<td>2.2.7</td>
<td>API Documentation</td>
</tr>
<tr>
<td><strong>Spring Validation</strong></td>
<td>6.x</td>
<td>Input Validation</td>
</tr>
<tr>
<td><strong>SLF4J + Logback</strong></td>
<td>2.x</td>
<td>Logging Framework</td>
</tr>
</tbody>
</table>
<h3 id="development-and-build-tools">Development and Build Tools<a class="headerlink" href="#development-and-build-tools" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;buildTool&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Maven 3.x&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;javaVersion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;17&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;springBootVersion&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;3.1.12&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;testingFramework&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;JUnit 5 + Mockito&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;containerization&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Docker&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;orchestration&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Kubernetes&quot;</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="key-features">Key Features<a class="headerlink" href="#key-features" title="Permanent link">&para;</a></h2>
<h3 id="business-features">🎯 <strong>Business Features</strong><a class="headerlink" href="#business-features" title="Permanent link">&para;</a></h3>
<h4 id="quote-management-apis">Quote Management APIs<a class="headerlink" href="#quote-management-apis" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Quote Creation</strong>: Comprehensive quote generation with validation</li>
<li><strong>Quote Retrieval</strong>: Efficient quote lookup and filtering</li>
<li><strong>Quote Modification</strong>: Business rule-driven quote updates</li>
<li><strong>Quote Approval</strong>: Multi-level approval workflow support</li>
</ul>
<h4 id="order-processing-apis">Order Processing APIs<a class="headerlink" href="#order-processing-apis" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Order Creation</strong>: Streamlined order initiation from quotes</li>
<li><strong>Order Tracking</strong>: Real-time order status and progress</li>
<li><strong>Order Modification</strong>: Change management with validation</li>
<li><strong>Order Fulfillment</strong>: Integration with fulfillment systems</li>
</ul>
<h4 id="service-management">Service Management<a class="headerlink" href="#service-management" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Service Catalog</strong>: Dynamic service offering management</li>
<li><strong>Service Validation</strong>: Business rule validation for services</li>
<li><strong>Service Configuration</strong>: Complex service parameter handling</li>
<li><strong>Service Pricing</strong>: Real-time pricing calculations</li>
</ul>
<h3 id="technical-features">🔧 <strong>Technical Features</strong><a class="headerlink" href="#technical-features" title="Permanent link">&para;</a></h3>
<h4 id="rest-api-design">REST API Design<a class="headerlink" href="#rest-api-design" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>RESTful Architecture</strong>: Standard HTTP methods and status codes</li>
<li><strong>OpenAPI Documentation</strong>: Comprehensive API documentation</li>
<li><strong>Content Negotiation</strong>: JSON and XML support</li>
<li><strong>Versioning</strong>: API version management</li>
</ul>
<h4 id="integration-capabilities">Integration Capabilities<a class="headerlink" href="#integration-capabilities" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Order Capture Integration</strong>: Native OCP client integration</li>
<li><strong>BSS System Connectivity</strong>: TELUS backend system integration</li>
<li><strong>Event-Driven Architecture</strong>: Asynchronous processing support</li>
<li><strong>Error Handling</strong>: Comprehensive error management</li>
</ul>
<h4 id="security-and-validation">Security and Validation<a class="headerlink" href="#security-and-validation" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Authentication</strong>: JWT-based authentication</li>
<li><strong>Authorization</strong>: Role-based access control</li>
<li><strong>Input Validation</strong>: Bean validation with custom validators</li>
<li><strong>CORS Support</strong>: Cross-origin resource sharing</li>
</ul>
<h2 id="project-structure">Project Structure<a class="headerlink" href="#project-structure" title="Permanent link">&para;</a></h2>
<h3 id="maven-module-organization">Maven Module Organization<a class="headerlink" href="#maven-module-organization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>nc-cloud-bss-oc-ui-backend-b2b/
├── cloud-bss-oc-ui-backend-b2b-application/    # Main Spring Boot application
│   ├── src/main/java/
│   │   └── com/netcracker/solutions/telus/ordercapture/backend/
│   │       ├── OrderEntryWebApplication.java   # Main application class
│   │       ├── config/                         # Configuration classes
│   │       │   ├── JacksonConfig.java          # JSON serialization config
│   │       │   ├── OpenApiConfig.java          # API documentation config
│   │       │   ├── SecurityConfig.java         # Security configuration
│   │       │   └── CorsConfig.java             # CORS configuration
│   │       ├── controller/                     # REST controllers
│   │       │   ├── QuoteController.java        # Quote management APIs
│   │       │   ├── OrderController.java        # Order management APIs
│   │       │   ├── ServiceController.java      # Service catalog APIs
│   │       │   └── CustomerController.java     # Customer management APIs
│   │       └── exception/                      # Exception handling
│   │           ├── GlobalExceptionHandler.java # Global error handler
│   │           └── CustomExceptions.java       # Custom exception classes
│   └── pom.xml                                 # Application module POM
├── cloud-bss-oc-ui-backend-b2b-resources/      # Resource definitions
│   └── src/main/resources/                     # Configuration files
│       ├── application.yml                     # Main configuration
│       ├── application-dev.yml                 # Development config
│       ├── application-prod.yml                # Production config
│       ├── logback-spring.xml                  # Logging configuration
│       └── static/                             # Static resources
├── nc-cloud-bss-oc-ui-be-b2b-impl/            # Implementation modules
│   └── src/main/java/                          # Business logic implementation
│       └── com/netcracker/solutions/telus/ordercapture/impl/
│           ├── service/                        # Service layer
│           │   ├── QuoteServiceImpl.java       # Quote business logic
│           │   ├── OrderServiceImpl.java       # Order business logic
│           │   ├── ValidationService.java     # Validation logic
│           │   └── MappingService.java         # Data transformation
│           ├── client/                         # Integration clients
│           │   ├── OrderCaptureClient.java     # OCP integration
│           │   ├── BssIntegrationClient.java   # BSS system client
│           │   └── ExternalApiClient.java      # External API client
│           ├── model/                          # Data models
│           │   ├── dto/                        # Data Transfer Objects
│           │   ├── entity/                     # JPA entities
│           │   └── request/                    # Request models
│           └── util/                           # Utility classes
│               ├── DateUtils.java              # Date utilities
│               ├── ValidationUtils.java        # Validation helpers
│               └── MappingUtils.java           # Mapping utilities
├── deployments/                                # Kubernetes deployment configs
│   ├── charts/                                 # Helm charts
│   ├── configmaps/                             # Configuration maps
│   └── secrets/                                # Secret configurations
├── pom.xml                                     # Parent POM configuration
├── Dockerfile                                  # Container definition
├── docker-compose.yml                          # Local development setup
└── SUPPORT.md                                  # Support documentation
</code></pre></div>
<h3 id="package-structure">Package Structure<a class="headerlink" href="#package-structure" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Application Package&quot;
        A[com.netcracker.solutions.telus.ordercapture.backend]
        B[config]
        C[controller]
        D[exception]
    end

    subgraph &quot;Implementation Package&quot;
        E[com.netcracker.solutions.telus.ordercapture.impl]
        F[service]
        G[client]
        H[model]
        I[util]
    end

    subgraph &quot;Model Subpackages&quot;
        J[dto]
        K[entity]
        L[request]
        M[response]
    end

    A --&gt; B
    A --&gt; C
    A --&gt; D

    E --&gt; F
    E --&gt; G
    E --&gt; H
    E --&gt; I

    H --&gt; J
    H --&gt; K
    H --&gt; L
    H --&gt; M

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style E fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
</code></pre></div>
<h2 id="development-environment">Development Environment<a class="headerlink" href="#development-environment" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Required Software Versions</span>
Java:<span class="w"> </span>OpenJDK<span class="w"> </span><span class="m">17</span><span class="w"> </span>or<span class="w"> </span>higher
Maven:<span class="w"> </span><span class="m">3</span>.8.x<span class="w"> </span>or<span class="w"> </span>higher
Docker:<span class="w"> </span><span class="m">20</span>.x<span class="w"> </span>or<span class="w"> </span>higher
Git:<span class="w"> </span><span class="m">2</span>.x<span class="w"> </span>or<span class="w"> </span>higher
IDE:<span class="w"> </span>IntelliJ<span class="w"> </span>IDEA<span class="w"> </span>or<span class="w"> </span>Eclipse<span class="w"> </span><span class="o">(</span>recommended<span class="o">)</span>
</code></pre></div>
<h3 id="environment-setup">Environment Setup<a class="headerlink" href="#environment-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Clone repository</span>
git<span class="w"> </span>clone<span class="w"> </span>&lt;repository-url&gt;
<span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-backend-b2b

<span class="c1"># 2. Verify Java version</span>
java<span class="w"> </span>-version
mvn<span class="w"> </span>-version

<span class="c1"># 3. Build the application</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install

<span class="c1"># 4. Run the application</span>
mvn<span class="w"> </span>spring-boot:run<span class="w"> </span>-pl<span class="w"> </span>cloud-bss-oc-ui-backend-b2b-application

<span class="c1"># 5. Verify application startup</span>
curl<span class="w"> </span>http://localhost:8080/actuator/health
</code></pre></div>
<h3 id="configuration-profiles">Configuration Profiles<a class="headerlink" href="#configuration-profiles" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># application.yml - Base configuration</span>
<span class="nt">spring</span><span class="p">:</span>
<span class="w">  </span><span class="nt">application</span><span class="p">:</span>
<span class="w">    </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-order-capture-backend</span>
<span class="w">  </span><span class="nt">profiles</span><span class="p">:</span>
<span class="w">    </span><span class="nt">active</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">dev</span>

<span class="nt">server</span><span class="p">:</span>
<span class="w">  </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8080</span>
<span class="w">  </span><span class="nt">servlet</span><span class="p">:</span>
<span class="w">    </span><span class="nt">context-path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/api/v1</span>

<span class="nt">management</span><span class="p">:</span>
<span class="w">  </span><span class="nt">endpoints</span><span class="p">:</span>
<span class="w">    </span><span class="nt">web</span><span class="p">:</span>
<span class="w">      </span><span class="nt">exposure</span><span class="p">:</span>
<span class="w">        </span><span class="nt">include</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">health,info,metrics</span>
<span class="w">  </span><span class="nt">endpoint</span><span class="p">:</span>
<span class="w">    </span><span class="nt">health</span><span class="p">:</span>
<span class="w">      </span><span class="nt">show-details</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">when-authorized</span>

<span class="nt">logging</span><span class="p">:</span>
<span class="w">  </span><span class="nt">level</span><span class="p">:</span>
<span class="w">    </span><span class="nt">com.netcracker.solutions.telus</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DEBUG</span>
<span class="w">    </span><span class="nt">org.springframework.security</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DEBUG</span>
</code></pre></div>
<h3 id="available-maven-commands">Available Maven Commands<a class="headerlink" href="#available-maven-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Build commands</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile<span class="w">                    </span><span class="c1"># Compile source code</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w">                    </span><span class="c1"># Package without tests</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w">                    </span><span class="c1"># Full build with tests</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w"> </span>-DskipTests<span class="w">       </span><span class="c1"># Build without tests</span>

<span class="c1"># Module-specific builds</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w"> </span>-pl<span class="w"> </span>cloud-bss-oc-ui-backend-b2b-application
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w"> </span>-pl<span class="w"> </span>nc-cloud-bss-oc-ui-be-b2b-impl

<span class="c1"># Development commands</span>
mvn<span class="w"> </span>spring-boot:run<span class="w">                  </span><span class="c1"># Run application</span>
mvn<span class="w"> </span>spring-boot:run<span class="w"> </span>-Dspring-boot.run.profiles<span class="o">=</span>dev
mvn<span class="w"> </span><span class="nb">test</span><span class="w">                            </span><span class="c1"># Run tests</span>
mvn<span class="w"> </span>verify<span class="w">                          </span><span class="c1"># Run integration tests</span>

<span class="c1"># Code quality</span>
mvn<span class="w"> </span>checkstyle:check<span class="w">                </span><span class="c1"># Code style validation</span>
mvn<span class="w"> </span>spotbugs:check<span class="w">                  </span><span class="c1"># Static analysis</span>
mvn<span class="w"> </span>jacoco:report<span class="w">                   </span><span class="c1"># Test coverage report</span>
</code></pre></div>
<h3 id="development-urls">Development URLs<a class="headerlink" href="#development-urls" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Environment</th>
<th>URL</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Local Development</strong></td>
<td>http://localhost:8080</td>
<td>Application base URL</td>
</tr>
<tr>
<td><strong>API Documentation</strong></td>
<td>http://localhost:8080/swagger-ui.html</td>
<td>OpenAPI documentation</td>
</tr>
<tr>
<td><strong>Health Check</strong></td>
<td>http://localhost:8080/actuator/health</td>
<td>Application health</td>
</tr>
<tr>
<td><strong>Metrics</strong></td>
<td>http://localhost:8080/actuator/metrics</td>
<td>Application metrics</td>
</tr>
</tbody>
</table>
<h3 id="docker-development">Docker Development<a class="headerlink" href="#docker-development" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Build Docker image</span>
docker<span class="w"> </span>build<span class="w"> </span>-t<span class="w"> </span>telus-b2b-backend:latest<span class="w"> </span>.

<span class="c1"># Run with Docker Compose</span>
docker-compose<span class="w"> </span>up<span class="w"> </span>-d

<span class="c1"># View logs</span>
docker-compose<span class="w"> </span>logs<span class="w"> </span>-f<span class="w"> </span>backend

<span class="c1"># Stop services</span>
docker-compose<span class="w"> </span>down
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../01-architecture/">Architecture Details →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>