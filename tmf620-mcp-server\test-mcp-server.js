#!/usr/bin/env node
/**
 * TMF620 MCP Server Test Script
 * 
 * This script tests the TMF620 MCP server by simulating MCP tool calls.
 * It verifies that the server can be started and that the tools work correctly.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { setTimeout } = require('timers/promises');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

console.log(`${colors.blue}TMF620 MCP Server Test Script${colors.reset}`);
console.log(`${colors.blue}=============================${colors.reset}`);

// Define paths
const serverDir = __dirname;
const startServerPath = path.join(serverDir, 'start-server.js');

// Test data
const testData = {
  productOfferingId: 'a2db6e1a-27ea-419e-a7a5-10575717f8b7',
  environment: 'test'
};

// Function to simulate an MCP tool call
async function simulateMcpToolCall(serverProcess, toolName, params) {
  return new Promise((resolve, reject) => {
    console.log(`${colors.blue}Testing tool: ${toolName}${colors.reset}`);
    console.log(`${colors.blue}Parameters: ${JSON.stringify(params)}${colors.reset}`);
    
    // Create the MCP request
    const request = {
      jsonrpc: '2.0',
      id: Date.now().toString(),
      method: 'callTool',
      params: {
        name: toolName,
        arguments: params
      }
    };
    
    // Convert the request to a string
    const requestStr = JSON.stringify(request);
    
    // Write the request to the server's stdin
    serverProcess.stdin.write(requestStr + '\n');
    
    // Set up a timeout for the response
    const timeout = setTimeout(() => {
      reject(new Error(`Timeout waiting for response to ${toolName}`));
    }, 30000);
    
    // Set up a handler for the server's stdout
    const responseHandler = (data) => {
      try {
        // Try to parse the response as JSON
        const responseStr = data.toString().trim();
        const responseLines = responseStr.split('\n');
        
        for (const line of responseLines) {
          try {
            const response = JSON.parse(line);
            
            // Check if this is a response to our request
            if (response.id === request.id) {
              clearTimeout(timeout);
              serverProcess.stdout.removeListener('data', responseHandler);
              
              if (response.error) {
                reject(new Error(`Error from ${toolName}: ${JSON.stringify(response.error)}`));
              } else {
                resolve(response.result);
              }
              return;
            }
          } catch (e) {
            // Not a JSON response or not our response, ignore
          }
        }
      } catch (e) {
        // Not a valid JSON response, ignore
      }
    };
    
    // Add the response handler to the server's stdout
    serverProcess.stdout.on('data', responseHandler);
  });
}

// Main function to run the tests
async function main() {
  let serverProcess = null;
  
  try {
    console.log(`${colors.blue}Starting TMF620 MCP server...${colors.reset}`);
    
    // Start the server process
    serverProcess = spawn('node', [startServerPath], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: serverDir
    });
    
    // Set up error handler
    serverProcess.on('error', (err) => {
      console.error(`${colors.red}Failed to start server: ${err}${colors.reset}`);
      process.exit(1);
    });
    
    // Set up exit handler
    serverProcess.on('exit', (code) => {
      if (code !== null && code !== 0) {
        console.error(`${colors.red}Server process exited with code ${code}${colors.reset}`);
      }
    });
    
    // Forward stderr to console
    serverProcess.stderr.on('data', (data) => {
      console.error(`${colors.yellow}[Server Error] ${data}${colors.reset}`);
    });
    
    // Wait for the server to start
    console.log(`${colors.blue}Waiting for server to initialize...${colors.reset}`);
    await setTimeout(5000);
    
    // Test the get_product_offering tool
    console.log(`${colors.blue}Testing get_product_offering tool...${colors.reset}`);
    const getProductOfferingResult = await simulateMcpToolCall(serverProcess, 'get_product_offering', {
      id: testData.productOfferingId,
      environment: testData.environment
    });
    console.log(`${colors.green}get_product_offering succeeded${colors.reset}`);
    
    // Test the list_product_offerings tool
    console.log(`${colors.blue}Testing list_product_offerings tool...${colors.reset}`);
    const listProductOfferingsResult = await simulateMcpToolCall(serverProcess, 'list_product_offerings', {
      environment: testData.environment,
      limit: 1
    });
    console.log(`${colors.green}list_product_offerings succeeded${colors.reset}`);
    
    // Test the get_catalog tool (this might fail if there's no catalog available)
    try {
      console.log(`${colors.blue}Testing get_catalog tool...${colors.reset}`);
      const getCatalogResult = await simulateMcpToolCall(serverProcess, 'get_catalog', {
        id: 'default', // This might need to be changed to a valid catalog ID
        environment: testData.environment
      });
      console.log(`${colors.green}get_catalog succeeded${colors.reset}`);
    } catch (error) {
      console.log(`${colors.yellow}get_catalog test skipped: ${error.message}${colors.reset}`);
    }
    
    // Test the list_catalogs tool
    console.log(`${colors.blue}Testing list_catalogs tool...${colors.reset}`);
    const listCatalogsResult = await simulateMcpToolCall(serverProcess, 'list_catalogs', {
      environment: testData.environment,
      limit: 1
    });
    console.log(`${colors.green}list_catalogs succeeded${colors.reset}`);
    
    console.log(`${colors.green}All tests completed successfully!${colors.reset}`);
    
    // Save test results to a file
    const testResults = {
      timestamp: new Date().toISOString(),
      success: true,
      tools: {
        get_product_offering: true,
        list_product_offerings: true,
        list_catalogs: true
      }
    };
    
    const testResultsPath = path.join(serverDir, 'mcp-server-test-results.json');
    fs.writeFileSync(testResultsPath, JSON.stringify(testResults, null, 2));
    console.log(`${colors.green}Test results saved to ${testResultsPath}${colors.reset}`);
    
    return 0;
  } catch (error) {
    console.error(`${colors.red}Test failed: ${error.message}${colors.reset}`);
    
    // Save test results to a file
    const testResults = {
      timestamp: new Date().toISOString(),
      success: false,
      error: error.message
    };
    
    const testResultsPath = path.join(serverDir, 'mcp-server-test-results.json');
    fs.writeFileSync(testResultsPath, JSON.stringify(testResults, null, 2));
    console.log(`${colors.red}Test results saved to ${testResultsPath}${colors.reset}`);
    
    return 1;
  } finally {
    // Clean up the server process
    if (serverProcess) {
      console.log(`${colors.blue}Shutting down server...${colors.reset}`);
      serverProcess.kill();
    }
  }
}

// Run the main function
main().then((exitCode) => {
  process.exit(exitCode);
}).catch((error) => {
  console.error(`${colors.red}Unexpected error: ${error}${colors.reset}`);
  process.exit(1);
});
