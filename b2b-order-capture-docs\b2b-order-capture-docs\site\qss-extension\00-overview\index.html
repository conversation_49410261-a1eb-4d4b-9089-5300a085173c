
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/qss-extension/00-overview/">
      
      
        <link rel="prev" href="../../extensions/05-development-guide/">
      
      
        <link rel="next" href="../01-plugin-architecture/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Overview - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#qss-extension-application-overview" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Overview
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" checked>
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#introduction" class="md-nav__link">
    <span class="md-ellipsis">
      Introduction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Introduction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#purpose-and-scope" class="md-nav__link">
    <span class="md-ellipsis">
      Purpose and Scope
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#qss-architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      QSS Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="QSS Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#high-level-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      High-Level Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#quote-snapshot-generation-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Snapshot Generation Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Core Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Framework
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Technologies
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Business Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Business Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-snapshot-generation" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Snapshot Generation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#storage-management" class="md-nav__link">
    <span class="md-ellipsis">
      Storage Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#business-rules-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Business Rules Engine
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technical-features" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Technical Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Technical Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Optimization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#monitoring-and-observability" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring and Observability
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#repository-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Repository Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-architecture_1" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#available-maven-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Available Maven Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-urls" class="md-nav__link">
    <span class="md-ellipsis">
      Development URLs
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#introduction" class="md-nav__link">
    <span class="md-ellipsis">
      Introduction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Introduction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#purpose-and-scope" class="md-nav__link">
    <span class="md-ellipsis">
      Purpose and Scope
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#qss-architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      QSS Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="QSS Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#high-level-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      High-Level Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#quote-snapshot-generation-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Snapshot Generation Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#technology-stack" class="md-nav__link">
    <span class="md-ellipsis">
      Technology Stack
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Technology Stack">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Core Technologies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#extension-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Extension Framework
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-technologies" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Technologies
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      Key Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-features" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Business Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎯 Business Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-snapshot-generation" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Snapshot Generation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#storage-management" class="md-nav__link">
    <span class="md-ellipsis">
      Storage Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#business-rules-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Business Rules Engine
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#technical-features" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Technical Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Technical Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Optimization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#monitoring-and-observability" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring and Observability
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#project-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Project Structure
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Project Structure">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#repository-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Repository Organization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-architecture_1" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#local-development-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Local Development Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#available-maven-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Available Maven Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#development-urls" class="md-nav__link">
    <span class="md-ellipsis">
      Development URLs
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="qss-extension-application-overview">QSS Extension Application Overview<a class="headerlink" href="#qss-extension-application-overview" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#introduction">Introduction</a></li>
<li><a href="#qss-architecture-overview">QSS Architecture Overview</a></li>
<li><a href="#technology-stack">Technology Stack</a></li>
<li><a href="#key-features">Key Features</a></li>
<li><a href="#project-structure">Project Structure</a></li>
<li><a href="#development-environment">Development Environment</a></li>
</ul>
<h2 id="introduction">Introduction<a class="headerlink" href="#introduction" title="Permanent link">&para;</a></h2>
<p>The <strong>nc-cloud-bss-oc-ui-qss-extension-b2b</strong> is a specialized QSS (Quote Storage Service) plugin framework that extends the NetCracker Quote Storage Service with TELUS-specific business logic for quote snapshot generation and storage management. This enterprise-grade extension system provides custom quote snapshot triggers, storage optimization, and business rule enforcement tailored for TELUS B2B telecommunications services.</p>
<h3 id="purpose-and-scope">Purpose and Scope<a class="headerlink" href="#purpose-and-scope" title="Permanent link">&para;</a></h3>
<p>The QSS Extension Application is designed to:
- <strong>Extend Quote Storage Service</strong>: Custom business logic for quote snapshot generation and storage
- <strong>Implement TELUS Storage Rules</strong>: Telecommunications-specific storage and snapshot policies
- <strong>Provide Quote Snapshot Management</strong>: Intelligent snapshot generation based on business events
- <strong>Enable Storage Optimization</strong>: Efficient quote data storage and retrieval mechanisms
- <strong>Support Multi-Tenant Deployment</strong>: Seamless deployment across multiple TELUS business units
- <strong>Facilitate Audit and Compliance</strong>: Comprehensive quote change tracking and audit trails</p>
<h2 id="qss-architecture-overview">QSS Architecture Overview<a class="headerlink" href="#qss-architecture-overview" title="Permanent link">&para;</a></h2>
<h3 id="high-level-architecture">High-Level Architecture<a class="headerlink" href="#high-level-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;TELUS B2B Ecosystem&quot;
        A[Order Capture Frontend]
        B[Order Capture Backend]
        C[QES Extensions]
        D[External Systems]
    end

    subgraph &quot;Quote Storage Service&quot;
        E[QSS Engine]
        F[Quote Storage]
        G[Snapshot Manager]
        H[Extension Framework]
    end

    subgraph &quot;QSS Extension Components&quot;
        I[Snapshot Generation Plugin]
        J[Storage Optimization]
        K[Business Rules Engine]
        L[Audit Trail Manager]
    end

    subgraph &quot;Storage Layer&quot;
        M[Quote Database]
        N[Snapshot Storage]
        O[Audit Logs]
        P[Metadata Store]
    end

    A --&gt; E
    B --&gt; E
    C --&gt; E
    D --&gt; E

    E --&gt; F
    E --&gt; G
    E --&gt; H

    H --&gt; I
    H --&gt; J
    H --&gt; K
    H --&gt; L

    F --&gt; M
    G --&gt; N
    L --&gt; O
    K --&gt; P

    style E fill:#673ab7,stroke:#512da8,color:#ffffff
    style H fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style I fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="quote-snapshot-generation-flow">Quote Snapshot Generation Flow<a class="headerlink" href="#quote-snapshot-generation-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant QES as Quote Engine Service
    participant QSS as Quote Storage Service
    participant Plugin as QSS Extension Plugin
    participant Storage as Quote Storage
    participant Audit as Audit Service

    QES-&gt;&gt;QSS: Quote Change Event
    QSS-&gt;&gt;Plugin: Evaluate Snapshot Generation
    Plugin-&gt;&gt;Plugin: Check isPrime Attribute
    Plugin-&gt;&gt;Plugin: Check Description Changes

    alt Snapshot Required
        Plugin--&gt;&gt;QSS: Generate Snapshot = true
        QSS-&gt;&gt;Storage: Create Quote Snapshot
        Storage--&gt;&gt;QSS: Snapshot Created
        QSS-&gt;&gt;Audit: Log Snapshot Event
        Audit--&gt;&gt;QSS: Event Logged
        QSS--&gt;&gt;QES: Snapshot Generation Complete
    else No Snapshot Required
        Plugin--&gt;&gt;QSS: Generate Snapshot = false
        QSS--&gt;&gt;QES: No Snapshot Generated
    end
</code></pre></div>
<h2 id="technology-stack">Technology Stack<a class="headerlink" href="#technology-stack" title="Permanent link">&para;</a></h2>
<h3 id="core-technologies">Core Technologies<a class="headerlink" href="#core-technologies" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Technology</th>
<th>Version</th>
<th>Purpose</th>
<th>Key Features</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Java</strong></td>
<td>17</td>
<td>Programming Language</td>
<td>Modern Java features, performance, security</td>
</tr>
<tr>
<td><strong>Maven</strong></td>
<td>3.8.1</td>
<td>Build Tool</td>
<td>Dependency management, lifecycle management</td>
</tr>
<tr>
<td><strong>NetCracker QSS</strong></td>
<td>2023.3.2.1</td>
<td>Quote Storage Service</td>
<td>Quote persistence, snapshot management</td>
</tr>
<tr>
<td><strong>SmartPlug</strong></td>
<td>0.14.11.2</td>
<td>Plugin Framework</td>
<td>Extension point management, hot deployment</td>
</tr>
<tr>
<td><strong>Lombok</strong></td>
<td>1.18.24</td>
<td>Code Generation</td>
<td>Boilerplate reduction, annotation processing</td>
</tr>
</tbody>
</table>
<h3 id="extension-framework">Extension Framework<a class="headerlink" href="#extension-framework" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Component</th>
<th>Purpose</th>
<th>Usage</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>SmartPlug Annotations</strong></td>
<td>Plugin Registration</td>
<td>Extension point implementation</td>
</tr>
<tr>
<td><strong>QSS Extension API</strong></td>
<td>Storage Service Integration</td>
<td>Quote storage and snapshot APIs</td>
</tr>
<tr>
<td><strong>SLF4J Logging</strong></td>
<td>Logging Framework</td>
<td>Structured logging and monitoring</td>
</tr>
<tr>
<td><strong>JUnit Jupiter</strong></td>
<td>Testing Framework</td>
<td>Unit and integration testing</td>
</tr>
<tr>
<td><strong>Mockito</strong></td>
<td>Mocking Framework</td>
<td>Test isolation and behavior verification</td>
</tr>
</tbody>
</table>
<h3 id="deployment-technologies">Deployment Technologies<a class="headerlink" href="#deployment-technologies" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;containerization&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;packaging&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;SPAR (SmartPlug Archive)&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;deployment&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Kubernetes with Helm&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;orchestration&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;NetCracker Cloud Platform&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;configuration&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;management&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ConfigMaps and Secrets&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;templating&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Helm templates&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;environment&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Multi-environment support&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;monitoring&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;metrics&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Micrometer/Prometheus&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;logging&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;ELK Stack&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;tracing&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Distributed tracing support&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="key-features">Key Features<a class="headerlink" href="#key-features" title="Permanent link">&para;</a></h2>
<h3 id="business-features">🎯 <strong>Business Features</strong><a class="headerlink" href="#business-features" title="Permanent link">&para;</a></h3>
<h4 id="quote-snapshot-generation">Quote Snapshot Generation<a class="headerlink" href="#quote-snapshot-generation" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Intelligent Triggering</strong>: Automatic snapshot generation based on business rules</li>
<li><strong>Prime Quote Detection</strong>: Special handling for prime quotes with enhanced tracking</li>
<li><strong>Description Change Monitoring</strong>: Snapshot generation on quote description modifications</li>
<li><strong>Conditional Logic</strong>: Configurable rules for when snapshots should be created</li>
<li><strong>Performance Optimization</strong>: Efficient snapshot generation to minimize storage overhead</li>
</ul>
<h4 id="storage-management">Storage Management<a class="headerlink" href="#storage-management" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Optimized Storage</strong>: Intelligent quote data storage and retrieval</li>
<li><strong>Snapshot Lifecycle</strong>: Automated snapshot retention and cleanup policies</li>
<li><strong>Data Compression</strong>: Efficient storage of quote snapshots and metadata</li>
<li><strong>Multi-Tenant Support</strong>: Isolated storage per tenant with shared infrastructure</li>
<li><strong>Audit Trail</strong>: Comprehensive tracking of all quote storage operations</li>
</ul>
<h4 id="business-rules-engine">Business Rules Engine<a class="headerlink" href="#business-rules-engine" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>TELUS-Specific Logic</strong>: Custom business rules for TELUS B2B requirements</li>
<li><strong>Configurable Policies</strong>: Flexible configuration of storage and snapshot policies</li>
<li><strong>Compliance Support</strong>: Built-in support for regulatory and audit requirements</li>
<li><strong>Performance Monitoring</strong>: Real-time monitoring of storage operations and performance</li>
<li><strong>Error Handling</strong>: Robust error handling and recovery mechanisms</li>
</ul>
<h3 id="technical-features">🔧 <strong>Technical Features</strong><a class="headerlink" href="#technical-features" title="Permanent link">&para;</a></h3>
<h4 id="plugin-architecture">Plugin Architecture<a class="headerlink" href="#plugin-architecture" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Extension Points</strong>: Well-defined extension points for custom logic</li>
<li><strong>Hot Deployment</strong>: Dynamic plugin loading and unloading without service restart</li>
<li><strong>Version Management</strong>: Support for multiple plugin versions and rollback</li>
<li><strong>Dependency Injection</strong>: Spring-based dependency injection for plugin components</li>
<li><strong>Configuration Management</strong>: External configuration support for plugin behavior</li>
</ul>
<h4 id="performance-optimization">Performance Optimization<a class="headerlink" href="#performance-optimization" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Lazy Loading</strong>: Efficient loading of quote data and snapshots</li>
<li><strong>Caching Strategy</strong>: Intelligent caching of frequently accessed data</li>
<li><strong>Batch Processing</strong>: Optimized batch operations for bulk quote processing</li>
<li><strong>Connection Pooling</strong>: Efficient database connection management</li>
<li><strong>Memory Management</strong>: Optimized memory usage for large quote datasets</li>
</ul>
<h4 id="monitoring-and-observability">Monitoring and Observability<a class="headerlink" href="#monitoring-and-observability" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Metrics Collection</strong>: Comprehensive metrics for storage operations</li>
<li><strong>Health Checks</strong>: Built-in health checks for plugin and storage status</li>
<li><strong>Distributed Tracing</strong>: End-to-end tracing of quote storage operations</li>
<li><strong>Alerting</strong>: Configurable alerts for storage issues and performance degradation</li>
<li><strong>Dashboard Integration</strong>: Integration with monitoring dashboards and tools</li>
</ul>
<h2 id="project-structure">Project Structure<a class="headerlink" href="#project-structure" title="Permanent link">&para;</a></h2>
<h3 id="repository-organization">Repository Organization<a class="headerlink" href="#repository-organization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>nc-cloud-bss-oc-ui-qss-extension-b2b/
├── b2b-qss-plugin/                                    # Main plugin module
│   ├── src/main/java/                                 # Source code
│   │   └── com/netcracker/solutions/telus/qss/extension/plugin/
│   │       └── GenerateQuoteSnapshotOnChangePlugin.java # Main plugin class
│   ├── src/test/java/                                 # Test code
│   └── pom.xml                                        # Module Maven configuration
├── deployments/                                       # Deployment configurations
│   ├── deployment-configuration.json                  # Deployment settings
│   └── charts/                                        # Helm charts
│       └── b2b-qss-plugin/                           # Plugin Helm chart
│           ├── Chart.yaml                             # Chart metadata
│           ├── values.yaml                            # Default values
│           └── templates/                             # Kubernetes templates
│               └── ConfigMap.yaml                     # Plugin configuration
├── buildConfig.yaml                                   # Build configuration
├── pom.xml                                            # Root Maven configuration
└── README.md                                          # Project documentation
</code></pre></div>
<h3 id="plugin-architecture_1">Plugin Architecture<a class="headerlink" href="#plugin-architecture_1" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;QSS Extension Plugin&quot;
        A[GenerateQuoteSnapshotOnChangePlugin]
        B[Extension Point Implementation]
        C[Business Logic Engine]
        D[Configuration Manager]
    end

    subgraph &quot;NetCracker QSS Framework&quot;
        E[GenerateQuoteSnapshotOnChangeEP]
        F[Quote Storage Service]
        G[Snapshot Manager]
        H[Extension Registry]
    end

    subgraph &quot;Plugin Components&quot;
        I[isPrime Detection]
        J[Description Change Detection]
        K[Snapshot Decision Logic]
        L[Performance Optimization]
    end

    subgraph &quot;Configuration&quot;
        M[Plugin Metadata]
        N[Tenant Configuration]
        O[Environment Settings]
        P[Feature Flags]
    end

    A --&gt; B
    A --&gt; C
    A --&gt; D

    B --&gt; E
    C --&gt; F
    C --&gt; G
    D --&gt; H

    C --&gt; I
    C --&gt; J
    C --&gt; K
    C --&gt; L

    D --&gt; M
    D --&gt; N
    D --&gt; O
    D --&gt; P

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style E fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style C fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h2 id="development-environment">Development Environment<a class="headerlink" href="#development-environment" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Required Software Versions</span>
Java:<span class="w"> </span>OpenJDK<span class="w"> </span><span class="m">17</span><span class="w"> </span>or<span class="w"> </span>higher
Maven:<span class="w"> </span><span class="m">3</span>.8.1<span class="w"> </span>or<span class="w"> </span>higher
Docker:<span class="w"> </span><span class="m">20</span>.x<span class="w"> </span>or<span class="w"> </span>higher
Kubernetes:<span class="w"> </span><span class="m">1</span>.25+<span class="w"> </span><span class="o">(</span>minikube<span class="w"> </span>or<span class="w"> </span>kind<span class="w"> </span><span class="k">for</span><span class="w"> </span><span class="nb">local</span><span class="w"> </span>development<span class="o">)</span>
Helm:<span class="w"> </span><span class="m">3</span>.x<span class="w"> </span>or<span class="w"> </span>higher
Git:<span class="w"> </span><span class="m">2</span>.x<span class="w"> </span>or<span class="w"> </span>higher
IDE:<span class="w"> </span>IntelliJ<span class="w"> </span>IDEA<span class="w"> </span>or<span class="w"> </span>VS<span class="w"> </span>Code<span class="w"> </span><span class="o">(</span>recommended<span class="o">)</span>
</code></pre></div>
<h3 id="environment-setup">Environment Setup<a class="headerlink" href="#environment-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Clone repository</span>
git<span class="w"> </span>clone<span class="w"> </span>&lt;repository-url&gt;
<span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-qss-extension-b2b

<span class="c1"># 2. Verify prerequisites</span>
java<span class="w"> </span>-version
mvn<span class="w"> </span>-version
docker<span class="w"> </span>--version
kubectl<span class="w"> </span>version<span class="w"> </span>--client
helm<span class="w"> </span>version

<span class="c1"># 3. Build plugin</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package

<span class="c1"># 4. Validate plugin structure</span>
mvn<span class="w"> </span>validate

<span class="c1"># 5. Run tests</span>
mvn<span class="w"> </span><span class="nb">test</span>
</code></pre></div>
<h3 id="local-development-setup">Local Development Setup<a class="headerlink" href="#local-development-setup" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Start local Kubernetes cluster</span>
minikube<span class="w"> </span>start<span class="w"> </span>--memory<span class="o">=</span><span class="m">8192</span><span class="w"> </span>--cpus<span class="o">=</span><span class="m">4</span>

<span class="c1"># 2. Install NetCracker QSS (if available locally)</span>
helm<span class="w"> </span>repo<span class="w"> </span>add<span class="w"> </span>netcracker<span class="w"> </span>&lt;netcracker-helm-repo&gt;
helm<span class="w"> </span>install<span class="w"> </span>qss-service<span class="w"> </span>netcracker/quote-storage-service

<span class="c1"># 3. Deploy QSS plugin</span>
helm<span class="w"> </span>install<span class="w"> </span>b2b-qss-plugin<span class="w"> </span>deployments/charts/b2b-qss-plugin<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--values<span class="w"> </span>deployments/charts/b2b-qss-plugin/values.yaml<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--namespace<span class="w"> </span>qss-extensions<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--create-namespace

<span class="c1"># 4. Verify deployment</span>
kubectl<span class="w"> </span>get<span class="w"> </span>pods<span class="w"> </span>-l<span class="w"> </span><span class="nv">app</span><span class="o">=</span>b2b-qss-plugin
kubectl<span class="w"> </span>get<span class="w"> </span>configmaps<span class="w"> </span>-l<span class="w"> </span>config-type<span class="o">=</span>smartplug
</code></pre></div>
<h3 id="plugin-configuration">Plugin Configuration<a class="headerlink" href="#plugin-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Plugin configuration in values.yaml</span>
<span class="nt">SERVICE_NAME</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;b2b-qss-plugin&quot;</span>
<span class="nt">DEPLOYMENT_VERSION</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;v1&quot;</span>
<span class="nt">DEFAULT_PLUGIN_TENANTS</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;all&quot;</span>

<span class="c1"># ConfigMap configuration</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span>
<span class="w">    </span><span class="nt">config-type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">smartplug</span>
<span class="nt">data</span><span class="p">:</span>
<span class="w">  </span><span class="nt">group</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">com.netcracker.telus.test.cloudbss</span>
<span class="w">  </span><span class="nt">artifact</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">b2b-qss-plugin</span>
<span class="w">  </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1.2</span>
<span class="w">  </span><span class="nt">tenants</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">all</span>
<span class="w">  </span><span class="nt">XVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">v1</span>
</code></pre></div>
<h3 id="available-maven-commands">Available Maven Commands<a class="headerlink" href="#available-maven-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Build commands</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile<span class="w">                    </span><span class="c1"># Compile plugin source code</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w">                    </span><span class="c1"># Package plugin as SPAR archive</span>
mvn<span class="w"> </span>clean<span class="w"> </span>install<span class="w">                    </span><span class="c1"># Install to local repository</span>
mvn<span class="w"> </span>clean<span class="w"> </span>deploy<span class="w">                     </span><span class="c1"># Deploy to remote repository</span>

<span class="c1"># Testing commands</span>
mvn<span class="w"> </span><span class="nb">test</span><span class="w">                            </span><span class="c1"># Run unit tests</span>
mvn<span class="w"> </span>integration-test<span class="w">                </span><span class="c1"># Run integration tests</span>
mvn<span class="w"> </span>verify<span class="w">                          </span><span class="c1"># Run all tests and validations</span>

<span class="c1"># Quality commands</span>
mvn<span class="w"> </span>pmd:check<span class="w">                       </span><span class="c1"># Run PMD static analysis</span>
mvn<span class="w"> </span>jacoco:report<span class="w">                   </span><span class="c1"># Generate code coverage report</span>
mvn<span class="w"> </span>jacoco:check<span class="w">                    </span><span class="c1"># Verify code coverage thresholds</span>

<span class="c1"># Plugin commands</span>
mvn<span class="w"> </span>spar:package<span class="w">                    </span><span class="c1"># Package as SmartPlug archive</span>
mvn<span class="w"> </span>spar:deploy<span class="w">                     </span><span class="c1"># Deploy plugin to QSS</span>
mvn<span class="w"> </span>spar:validate<span class="w">                   </span><span class="c1"># Validate plugin structure</span>

<span class="c1"># Development commands</span>
mvn<span class="w"> </span>dependency:tree<span class="w">                 </span><span class="c1"># Show dependency tree</span>
mvn<span class="w"> </span>help:effective-pom<span class="w">              </span><span class="c1"># Show effective POM configuration</span>
mvn<span class="w"> </span>versions:display-dependency-updates<span class="w">  </span><span class="c1"># Check for dependency updates</span>
</code></pre></div>
<h3 id="development-urls">Development URLs<a class="headerlink" href="#development-urls" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Environment</th>
<th>URL</th>
<th>Purpose</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Local QSS Service</strong></td>
<td>http://localhost:8080</td>
<td>Local development server</td>
</tr>
<tr>
<td><strong>Plugin Management</strong></td>
<td>http://localhost:8080/plugins</td>
<td>Plugin management interface</td>
</tr>
<tr>
<td><strong>Quote Storage API</strong></td>
<td>http://localhost:8080/api/quotes</td>
<td>Quote storage REST API</td>
</tr>
<tr>
<td><strong>Health Check</strong></td>
<td>http://localhost:8080/health</td>
<td>Service health monitoring</td>
</tr>
<tr>
<td><strong>Metrics</strong></td>
<td>http://localhost:8080/metrics</td>
<td>Performance metrics endpoint</td>
</tr>
</tbody>
</table>
<hr />
<p><strong>Next</strong>: <a href="../01-plugin-architecture/">Plugin Architecture →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>