# @telus/mcp-api-marketplace

An MCP server that integrates with the [API Marketplace][1].

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-api-marketplace
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for Claude Desktop):

   ```json
   {
     "mcpServers": {
       "api-marketplace": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-api-marketplace"],
         "env": {
           "API_MARKETPLACE_CLIENT_ID": "[YOUR CLIENT ID]",
           "API_MARKETPLACE_CLIENT_SECRET": "[YOUR CLIENT SECRET]",
           "TELUS_EMAIL": "[YOUR TELUS EMAIL]"
         }
       }
     }
   }
   ```

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-api-marketplace
   pnpm install
   ```

2. Build the server:

   ```bash
   pnpm build
   ```

3. Start the server:

   ```bash
   pnpm start
   ```

## Pre-requisites

For now, using this MCP requires access to the API Marketplace [backend API][2].

This API has a lot of power, so an alternative endpoint with less access will need to be made available in order to enable general use. Stay tuned!

## Available Tools

[Add description of available tools and their usage]

<!-- Links -->
[1]: https://go.telus.com/dev
[2]: https://api-marketplace.cloudapps.telus.com/v1/apis/3dd23742-22f2-4540-9c0f-b2b34fda0aa1
