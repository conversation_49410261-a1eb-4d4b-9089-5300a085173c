{"name": "telus-mcp-monorepo", "version": "0.0.1", "private": true, "description": "Monorepo for TELUS MCP servers", "keywords": ["telus", "mcp", "monorepo"], "license": "MIT", "author": "TELUS", "scripts": {"build": "turbo run build", "changeset": "changeset", "dev": "turbo run dev --ui=tui --filter='registry'", "format": "prettier --write .", "format:check": "prettier --check .", "generate:mcp": "turbo gen mcp-server", "prepare": "turbo run prepare --filter='packages/*'", "release": "turbo run build && changeset publish", "start": "turbo run start", "version-packages": "changeset version"}, "devDependencies": {"@changesets/cli": "^2.26.2", "@turbo/gen": "^2.4.2", "prettier": "^3.4.2", "prettier-plugin-packagejson": "^2.4.3", "turbo": "^2.4.2"}, "packageManager": "pnpm@9.12.1"}