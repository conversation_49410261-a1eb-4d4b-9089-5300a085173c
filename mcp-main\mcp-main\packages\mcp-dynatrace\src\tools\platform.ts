import { z } from 'zod';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import fetch, { HeadersInit, RequestInit } from 'node-fetch';
import dotenv from 'dotenv';
import { dirname, resolve } from 'path';
import { fileURLToPath } from 'url';
import { ExchangeRateResponse, EndpointResponse, TraceResponse } from '../types.js';
import { traceIdsForEndpoint, endpointsForService, spansForTrace, networkAnalysisForTrace } from '../utils/dqls.js'
import { fromSpans } from '../utils/sequenceDiagram.js'

async function getUsdToCadRate(): Promise<number> {
    try {
        const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
        const data = await response.json() as ExchangeRateResponse;
        return data.rates.CAD;
    } catch (error) {
        console.error('Error fetching exchange rate:', error);
        return 1.4;
    }
}

function calculateCost(scannedBytes: number): { usdCost: number; cadCost: number } {
    const bytesInGB = scannedBytes / (1024 * 1024 * 1024);
    const usdCost = bytesInGB * 0.00365;
    return { usdCost, cadCost: 0 };
}

const __dirname = dirname(fileURLToPath(import.meta.url));
dotenv.config({ path: resolve(__dirname, '../../.env') });

const dynatracePlatformUrl = process.env.DYNATRACE_PLATFORM_URL || '';
const dynatracePlatformToken = process.env.DYNATRACE_PLATFORM_TOKEN || '';

export async function makeDQLRequest<T>(url: string, method: 'GET' | 'POST', body?: { query: string; [key: string]: any }): Promise<T | null> {
    const headers: HeadersInit = {
        "Authorization": `Bearer ${dynatracePlatformToken}`,
        "accept": "application/json"
    };

    if (method === 'POST') {
        headers["Content-Type"] = "application/json";
    }

    try {
        if (method === 'POST' && (!body || !body.query)) {
            throw new Error("The 'query' attribute is mandatory in the request body for POST requests");
        }

        const options: RequestInit = { 
            method,
            headers
        };

        if (method === 'POST' && body) {
            options.body = JSON.stringify(body);
        }

        const response = await fetch(url, options);
        console.error(`${method} request to ${url}`);
        if (method === 'POST' && body) {
            console.error("request body is ", body);
        }
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Error response body:', errorText);
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
        const responseData = await response.json() as T;
        return responseData;
    } catch (error) {
        if (error instanceof Error) {
            console.error("Error making Dynatrace request:", {
                message: error.message,
                stack: error.stack,
                url: url,
                body: body
            });
        } else {
            console.error("Unknown error making Dynatrace request:", error);
        }
        return null;
    }
}

export async function verifyDqlQuery(query: string): Promise<any> {
    try {
        const requestUrl = `${dynatracePlatformUrl}/storage/query/v1/query:verify`
        const result = await makeDQLRequest<any>(requestUrl, 'POST', { query });
        if (result === null) {
            return { error: "Failed to get response from Dynatrace API", Url: requestUrl };
        }
        return result;
    } catch (error) {
        console.error("Error verifying DQL query:", error);
        return { 
            error: error instanceof Error ? error.message : 'Unknown error occurred',
        };
    }
}

async function pollQueryResults(requestToken: string, timeoutMs: number = 5000): Promise<any> {
    const pollUrl = `${dynatracePlatformUrl}/storage/query/v1/query:poll?request-token=${encodeURIComponent(requestToken)}&request-timeout-milliseconds=${timeoutMs}&enrich=metric-metadata`;
    
    const result = await makeDQLRequest<any>(pollUrl, 'GET');
    
    if (result === null) {
        throw new Error("Failed to poll query results");
    }

    return result;
}

export async function executeDqlQuery(query: string, maxTries: number = 5): Promise<any> {
    const exchangeRate = await getUsdToCadRate();
    try {
        const requestUrl = `${dynatracePlatformUrl}/storage/query/v1/query:execute?enrich=metric-metadata`;
        const result = await makeDQLRequest<any>(requestUrl, 'POST', { query });
        
        if (result === null) {
            return { error: "Failed to get response from Dynatrace API", Url: requestUrl };
        }

        if (result.state === 'RUNNING') {
            const baseWaitTime = 2000;
            let currentTry = 0;

            while (currentTry < maxTries) {

                const waitTime = baseWaitTime * Math.pow(3, currentTry);
                await new Promise(resolve => setTimeout(resolve, waitTime));

                const pollResult = await pollQueryResults(result.requestToken);
                
                if (pollResult.state === 'SUCCEEDED' && pollResult.result) {
                    const scannedBytes = pollResult.metadata?.grail?.scannedBytes || 0;
                    const { usdCost, cadCost } = calculateCost(scannedBytes);
                    return {
                        status: 'SUCCEEDED',
                        records: pollResult.result.records,
                        metadata: pollResult.metadata,
                        cost: {
                            scannedBytes,
                            bytesInGB: (scannedBytes / (1024 * 1024 * 1024)).toFixed(6),
                            usdCost: usdCost.toFixed(6),
                            cadCost: (usdCost * exchangeRate).toFixed(6),
                            exchangeRate: exchangeRate.toFixed(4)
                        }
                    };
                } else if (pollResult.state !== 'RUNNING') {
                    return {
                        status: pollResult.state,
                        message: pollResult.error || 'Query Failed',
                        requestToken: result.requestToken
                    };
                }

                currentTry++;
                console.log(`Polling attempt ${currentTry} of ${maxTries}, next wait time: ${waitTime}ms`);
            }

            return {
                status: 'TIMEOUT',
                message: 'Query execution exceeded maximum number of tries',
                requestToken: result.requestToken
            };
        }

        if (result.state === 'SUCCEEDED' && result.result) {
            const scannedBytes = result.metadata?.grail?.scannedBytes || 0;
            const { usdCost, cadCost } = calculateCost(scannedBytes);
            return {
                status: 'SUCCEEDED',
                records: result.result.records,
                cost: {
                    scannedBytes,
                    bytesInGB: (scannedBytes / (1024 * 1024 * 1024)).toFixed(6),
                    usdCost: usdCost.toFixed(6),
                    cadCost: (usdCost * exchangeRate).toFixed(6),
                    exchangeRate: exchangeRate.toFixed(4)
                }
            };
        }

        return { error: "Unexpected response state", response: result };
    } catch (error) {
        console.error("Error executing DQL query:", error);
        return { 
            error: error instanceof Error ? error.message : 'Unknown error occurred',
            query: query
        };
    }
}

export const verifyDqlQueryApi = (server: McpServer) => {
    return server.tool(
        "verify-dql-query",
        "⚠️ WARNING: While verification itself is free, the subsequent execution of DQL queries may incur costs. Verify a DQL query before executing it.",
        {
            query: z.string().describe("The DQL query to verify"),
        },
        async ({ query }) => {
            const result = await verifyDqlQuery(query);
            return {
                content: [{ type: "text", text: JSON.stringify(result, null, 2) }],
            };
        }
    );
};

export const executeDqlQueryApi = (server: McpServer) => {
    return server.tool(
        "execute-dql-query",
        "Execute a DQL query and return the results, including cost estimation in USD and CAD. Some complex DQLs might take more time to run, try to increase the max number of retries (default 5) if it failed to return the result. The cost estimation is based on the amount of data scanned (in GB) at a rate of $0.00365 USD per GB. The CAD cost is calculated using the current exchange rate. ⚠️ WARNING: DQL queries may incur costs based on data scanned and query complexity. Please be cautious with large-scale or complex queries.",
        {
            query: z.string().describe("The DQL query to execute"),
            maxTries: z.number().optional().describe("Maximum number of polling attempts (default: 5)"),
        },
        async ({ query, maxTries }) => {
            const result = await executeDqlQuery(query, maxTries);
            return {
                content: [{ type: "text", text: JSON.stringify(result, null, 2) }],
            };
        }
    );
};

export const pollQueryResultApi = (server: McpServer) => {
    return server.tool(
        "poll-query-result",
        "Poll for results of a running DQL query with requestToken, default timeout wait for result is 5 seconds",
        {
            requestToken: z.string().describe("Request token from the execute query response"),
            timeoutMs: z.number().optional().describe("Request timeout in milliseconds")
        },
        async ({ requestToken, timeoutMs }) => {
            const result = await pollQueryResults(requestToken, timeoutMs);
            return {
                content: [{ type: "text", text: JSON.stringify(result, null, 2) }],
            };
        }
    );
};

export const getServiceEndpointsApi = (server: McpServer) => {
    return server.tool(
        "get-service-endpoints",
        "Retrieve all endpoints for a specific service using service id",
        {
            serviceId: z.string().describe("service ID to retrieve endpoints"),
        },
        async ({ serviceId }) => {
            const query = endpointsForService(serviceId)
            const result = await executeDqlQuery(query) as EndpointResponse;

            return {
                content: [{ type: "text", text: JSON.stringify(result, null, 2) }],
            };
        }
    );
};

export const getTraceIdssApi = (server: McpServer) => {
    return server.tool(
        "get-traceIds",
        "Retrieve all trace ids for a specific service and endpoint",
        {
            serviceId: z.string().describe("service ID to retrieve traces"),
            endpointName: z.string().describe("endpoint to retrieve traces"),
        },
        async ({ serviceId, endpointName }) => {
            const query = traceIdsForEndpoint(serviceId, endpointName)
            const result = await executeDqlQuery(query) as TraceResponse;
            return {
                content: [{ type: "text", text: JSON.stringify(result, null, 2) }],
            };
        }
    );
};

export const getSequenceDiagramForTrace = (server: McpServer) => {
    return server.tool(
        "get-sequence-diagram-for-traceId",
        "Get sequence diagram for a specific traceId",
        {
            traceId: z.string().describe("Trace ID for spans")
        },
        async ({ traceId }) => {
            const query = spansForTrace(traceId);
            const result = await executeDqlQuery(query);
            const sequenceDiagram = fromSpans(result.records);
            return {
                content: [{ type: "text", text: sequenceDiagram }],
            };
        }
    );
};

export const networkAnalysisApi = (server: McpServer) => {
    return server.tool(
        "network-analysis",
        "Analyze network connections for a specific trace with configurable time window. Returns detailed information about hosts, IP addresses, and status codes.",
        {
            traceId: z.string().describe("Trace ID to analyze"),
            timeWindow: z.string().optional().describe("Time window for analysis (e.g., '-12h', '-1d')")
        },
        async ({ traceId, timeWindow = "-12h" }) => {
            // Generate the DQL query
            const query = networkAnalysisForTrace(traceId, timeWindow);
            
            // Verify the query
            const verificationResult = await verifyDqlQuery(query);
            
            // Execute the query if verification passes
            const result = await executeDqlQuery(query);
            
            return {
                content: [{ 
                    type: "text", 
                    text: JSON.stringify({
                        query,
                        verification: verificationResult,
                        result
                    }, null, 2) 
                }],
            };
        }
    );
};
