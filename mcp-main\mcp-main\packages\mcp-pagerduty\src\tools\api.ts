import { z } from 'zod';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import fetch from 'node-fetch';
import { Incident, Service, User, PagerDutyResponse } from '../types.js';

let lastServiceSearchResults: Service[] = [];

const pagerDutyApiBase = process.env.PAGERDUTY_API_BASE || '';
const pagerDutyApiToken = process.env.PAGERDUTY_API_TOKEN || '';

if (!pagerDutyApiToken || !pagerDutyApiBase) {
    throw new Error('Missing required environment variable: PAGERDUTY_API_TOKEN');
}

async function makePagerDutyRequest<T>(endpoint: string, params: Record<string, string | string[]> = {}): Promise<T | null> {
    const url = new URL(`${pagerDutyApiBase}${endpoint}`);
    Object.entries(params).forEach(([key, value]) => {
        if (Array.isArray(value)) {
            value.forEach(v => url.searchParams.append(key + '[]', v));
        } else {
            url.searchParams.append(key, value);
        }
    });

    const headers = {
        'Authorization': `Token token=${pagerDutyApiToken}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
    };

    try {
        const response = await fetch(url.toString(), { headers });
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Error response body:', errorText);
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
        const responseData = await response.json() as T;
        return responseData;
    } catch (error) {
        if (error instanceof Error) {
            console.error("Error making PagerDuty request:", {
                message: error.message,
                stack: error.stack,
                url: url.toString(),
                endpoint,
                params
            });
        } else {
            console.error("Unknown error making PagerDuty request:", error);
        }
        throw error;
    }
}

async function listIncidents(params: {
    status?: string[],
    offset?: number,
    limit?: number,
    date_range?: string,
    incident_key?: string,
    include?: string[],
    service_ids?: string[],
    since?: string,
    sort_by?: string[],
    team_ids?: string[],
    time_zone?: string,
    until?: string,
    urgencies?: string[],
    user_ids?: string[]
} = {}): Promise<string> {
    const { offset = 0, limit = 50, ...otherParams } = params;
    const response = await makePagerDutyRequest<{ incidents: Incident[], limit: number, offset: number, total: number | null, more: boolean }>('/incidents', { ...otherParams, offset: offset.toString(), limit: limit.toString() });

    if (!response || !response.incidents) {
        return "Failed to retrieve incidents";
    }

    let result = `Found ${response.total ?? response.incidents.length} incidents (showing ${offset + 1} to ${offset + response.incidents.length}):\n\n`;
    response.incidents.forEach((incident, index) => {
        // Basic incident information
        result += `${offset + index + 1}. [#${incident.incident_number}] ${incident.title}\n`;
        result += `   Type: ${incident.incident_type.name}\n`;
        result += `   Status: ${incident.status.toUpperCase()}\n`;
        result += `   Urgency: ${incident.urgency.toUpperCase()}\n`;
        result += `   Service: ${incident.service.summary}\n`;
        
        // Timing information
        result += `   Created: ${incident.created_at}\n`;
        result += `   Last Updated: ${incident.updated_at}\n`;
        if (incident.resolved_at) {
            result += `   Resolved: ${incident.resolved_at}\n`;
        }
        
        // Alert information
        if (incident.alert_counts) {
            result += `   Alert Counts: ${incident.alert_counts.all} total (${incident.alert_counts.triggered} triggered, ${incident.alert_counts.resolved} resolved)\n`;
        }
        
        // Assignment and team information
        if (incident.assignments.length > 0) {
            result += `   Assigned To: ${incident.assignments.map(a => a.assignee.summary).join(', ')}\n`;
        }
        if (incident.teams.length > 0) {
            result += `   Teams: ${incident.teams.map(t => t.summary).join(', ')}\n`;
        }
        
        // Escalation and priority
        if (incident.escalation_policy) {
            result += `   Escalation Policy: ${incident.escalation_policy.summary}\n`;
        }
        if (incident.priority) {
            result += `   Priority: ${incident.priority.summary}\n`;
        }
        
        // Status change information
        if (incident.last_status_change_by) {
            result += `   Last Status Change By: ${incident.last_status_change_by.summary}\n`;
        }
        
        // Conference bridge
        if (incident.conference_bridge) {
            if (incident.conference_bridge.conference_url) {
                result += `   Conference URL: ${incident.conference_bridge.conference_url}\n`;
            }
            if (incident.conference_bridge.conference_number) {
                result += `   Conference Number: ${incident.conference_bridge.conference_number}\n`;
            }
        }
        
        // First trigger information
        if (incident.first_trigger_log_entry) {
            result += `   Trigger: ${incident.first_trigger_log_entry.summary}\n`;
        }
        
        // Links
        result += `   View Incident: ${incident.html_url}\n`;
        result += `   API URL: ${incident.self}\n\n`;
    });

    if (response.more) {
        result += `There are more incidents available. Use offset ${offset + limit} to get the next page.\n`;
    }

    return result;
}

async function listServices(offset: number = 0, limit: number = 50): Promise<string> {
    const response = await makePagerDutyRequest<{ services: Service[], limit: number, offset: number, total: number | null, more: boolean }>('/services', { offset: offset.toString(), limit: limit.toString() });

    if (!response) {
        return "Failed to retrieve services";
    }

    if (!response.services || !Array.isArray(response.services)) {
        return `Failed to parse services response: ${JSON.stringify(response)}`;
    }

    let result = `Found ${response.total ?? response.services.length} services (showing ${offset + 1} to ${offset + response.services.length}):\n\n`;
    response.services.forEach((service, index) => {
        result += `${offset + index + 1}. ID: ${service.id}\n`;
        result += `   Name: ${service.name}\n`;
        result += `   Description: ${service.description || 'No description'}\n`;
        result += `   Status: ${service.status}\n`;
        result += `   Created At: ${service.created_at}\n`;
        result += `   URL: ${service.html_url}\n\n`;
    });

    if (response.more) {
        result += `There are more services available. Use offset ${offset + limit} to get the next page.\n`;
    }

    return result;
}

async function listUsers(offset: number = 0, limit: number = 50): Promise<string> {
    const response = await makePagerDutyRequest<{ users: User[], limit: number, offset: number, total: number | null, more: boolean }>('/users', { offset: offset.toString(), limit: limit.toString() });

    if (!response || !response.users) {
        return "Failed to retrieve users";
    }

    let result = `Found ${response.total ?? response.users.length} users (showing ${offset + 1} to ${offset + response.users.length}):\n\n`;
    response.users.forEach((user, index) => {
        result += `${offset + index + 1}. ID: ${user.id}\n`;
        result += `   Name: ${user.name}\n`;
        result += `   Email: ${user.email}\n`;
        result += `   Role: ${user.role}\n`;
        result += `   URL: ${user.html_url}\n\n`;
    });

    if (response.more) {
        result += `There are more users available. Use offset ${offset + limit} to get the next page.\n`;
    }

    return result;
}

export const listIncidentsApi = (server: McpServer) => {
    return server.tool(
        "list-incidents",
        "List PagerDuty incidents with advanced filtering",
        {
            status: z.array(z.string()).optional().describe("Array of incident statuses (triggered/acknowledged/resolved)"),
            offset: z.number().int().nonnegative().optional().describe("Offset for pagination"),
            limit: z.number().int().positive().max(100).optional().describe("Limit for pagination (max 100)"),
            date_range: z.string().optional().describe("When set to 'all', ignores since/until parameters"),
            incident_key: z.string().optional().describe("Incident de-duplication key"),
            include: z.array(z.string()).optional().describe("Additional details to include (e.g., ['acknowledgers', 'assignments'])"),
            service_ids: z.array(z.string()).optional().describe("Array of service IDs to filter by"),
            since: z.string().optional().describe("Start of date range (ISO 8601)"),
            sort_by: z.array(z.string()).optional().describe("Sort fields (e.g., ['created_at:desc'])"),
            team_ids: z.array(z.string()).optional().describe("Array of team IDs to filter by"),
            time_zone: z.string().optional().describe("Time zone for response"),
            until: z.string().optional().describe("End of date range (ISO 8601)"),
            urgencies: z.array(z.string()).optional().describe("Array of urgencies (high/low)"),
            user_ids: z.array(z.string()).optional().describe("Array of user IDs to filter by")
        },
        async (params) => {
            const result = await listIncidents(params);
            return {
                content: [{ type: "text", text: result }],
            };
        }
    );
};

export const listServicesApi = (server: McpServer) => {
    return server.tool(
        "list-services",
        "List PagerDuty services",
        {
            offset: z.number().int().nonnegative().optional().describe("Offset for pagination"),
            limit: z.number().int().positive().max(100).optional().describe("Limit for pagination (max 100)")
        },
        async ({ offset, limit }) => {
            const result = await listServices(offset, limit);
            return {
                content: [{ type: "text", text: result }],
            };
        }
    );
};

export const listUsersApi = (server: McpServer) => {
    return server.tool(
        "list-users",
        "List PagerDuty users",
        {
            offset: z.number().int().nonnegative().optional().describe("Offset for pagination"),
            limit: z.number().int().positive().max(100).optional().describe("Limit for pagination (max 100)")
        },
        async ({ offset, limit }) => {
            const result = await listUsers(offset, limit);
            return {
                content: [{ type: "text", text: result }],
            };
        }
    );
};

async function findServiceByName(serviceName: string): Promise<string> {
    const limit = 100;
    let offset = 0;
    let allServices: Service[] = [];
    let hasMore = true;

    const normalizeText = (text: string): string => {
        return text
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, ' ')
            .trim();
    };

    while (hasMore) {
        const response = await makePagerDutyRequest<{ services: Service[], limit: number, offset: number, total: number | null, more: boolean }>(
            '/services',
            { offset: offset.toString(), limit: limit.toString() }
        );

        if (!response || !response.services) {
            return "Failed to retrieve services";
        }

        allServices = allServices.concat(response.services);
        hasMore = response.more || false;
        offset += limit;
    }

    const normalizedSearchName = normalizeText(serviceName);


    const matchingServices = allServices.filter(service => {
        const normalizedServiceName = normalizeText(service.name);
        return normalizedServiceName === normalizedSearchName || 
               normalizedServiceName.includes(normalizedSearchName);
    });

    if (matchingServices.length === 0) {
        lastServiceSearchResults = [];
        return `No services found matching "${serviceName}"`;
    }

    lastServiceSearchResults = matchingServices;

    let result = `Found ${matchingServices.length} matching services:\n\n`;
    matchingServices.forEach((service, index) => {
        result += `${index + 1}. Name: ${service.name}\n`;
        result += `   ID: ${service.id}\n`;
        result += `   Description: ${service.description || 'No description'}\n\n`;
    });

    result += "\nUse 'select-service' tool with the number of the service you want to select.";
    return result;
}

async function getServiceDetails(serviceId: string): Promise<string> {
    const response = await makePagerDutyRequest<{ service: Service }>(`/services/${serviceId}`);

    if (!response) {
        return "Failed to retrieve service details";
    }

    const service = response.service;
    let result = `Service Details:\n\n`;
    result += `Name: ${service.name}\n`;
    result += `ID: ${service.id}\n`;
    result += `Description: ${service.description || 'No description'}\n`;
    result += `Status: ${service.status}\n`;
    result += `Created At: ${service.created_at}\n`;
    result += `URL: ${service.html_url}\n`;

    return result;
}

export const findServiceApi = (server: McpServer) => {
    return server.tool(
        "find-service",
        "Find a PagerDuty service by name",
        {
            serviceName: z.string().describe("Name of the service to search for")
        },
        async ({ serviceName }) => {
            const result = await findServiceByName(serviceName);
            return {
                content: [{ type: "text", text: result }],
            };
        }
    );
};

async function getIncidentById(id: string, include?: string[]): Promise<string> {
    const params: Record<string, string | string[]> = {};
    if (include && include.length > 0) {
        params.include = include;
    }

    const response = await makePagerDutyRequest<{ incident: Incident }>(`/incidents/${id}`, params);

    if (!response || !response.incident) {
        return "Failed to retrieve incident";
    }

    const incident = response.incident;
    let result = `Incident Details [#${incident.incident_number}]:\n\n`;
    
    // Basic Information
    result += `Title: ${incident.title}\n`;
    result += `Status: ${incident.status.toUpperCase()}\n`;
    result += `Urgency: ${incident.urgency.toUpperCase()}\n`;
    result += `Incident Type: ${incident.incident_type ? incident.incident_type.name : 'N/A'}\n`;
    
    // Timing Information
    result += `Created: ${incident.created_at}\n`;
    result += `Last Updated: ${incident.updated_at}\n`;
    result += `Last Status Change: ${incident.last_status_change_at}\n`;
    if (incident.resolved_at) {
        result += `Resolved: ${incident.resolved_at}\n`;
    }
    
    // Service Information
    result += `Service: ${incident.service.summary}\n`;
    
    // Assignment Information
    if (incident.assignments.length > 0) {
        result += "\nAssignments:\n";
        incident.assignments.forEach(assignment => {
            result += `- ${assignment.assignee.summary}\n`;
        });
    }
    
    // Team Information
    if (incident.teams.length > 0) {
        result += "\nTeams:\n";
        incident.teams.map(team => {
            result += `- ${team.summary}\n`;
        });
    }
    
    // Escalation Policy
    if (incident.escalation_policy) {
        result += `\nEscalation Policy: ${incident.escalation_policy.summary}\n`;
    }
    
    // Priority
    if (incident.priority) {
        result += `Priority: ${incident.priority.summary}\n`;
    }
    
    // Alert Information
    if (incident.alert_counts) {
        result += `\nAlert Counts: ${incident.alert_counts.all} total (${incident.alert_counts.triggered} triggered, ${incident.alert_counts.resolved} resolved)\n`;
    }
    
    // Acknowledgements
    if (incident.acknowledgements && incident.acknowledgements.length > 0) {
        result += "\nAcknowledgements:\n";
        incident.acknowledgements.forEach(ack => {
            result += `- By ${ack.acknowledger.summary} at ${ack.at}\n`;
        });
    }
    
    // Conference Bridge
    if (incident.conference_bridge) {
        result += "\nConference Bridge:\n";
        if (incident.conference_bridge.conference_url) {
            result += `URL: ${incident.conference_bridge.conference_url}\n`;
        }
        if (incident.conference_bridge.conference_number) {
            result += `Number: ${incident.conference_bridge.conference_number}\n`;
        }
    }
    
    // Custom Fields
    if (incident.custom_fields && incident.custom_fields.length > 0) {
        result += "\nCustom Fields:\n";
        incident.custom_fields.forEach(field => {
            result += `${field.display_name}: ${field.value}\n`;
        });
    }
    
    // Pending Actions
    if (incident.pending_actions && incident.pending_actions.length > 0) {
        result += "\nPending Actions:\n";
        incident.pending_actions.forEach(action => {
            result += `- ${action.type} scheduled for ${action.at}\n`;
        });
    }
    
    // Fetch and add alerts
    const alertsResponse = await listAlertsForIncident(id);
    result += `\n${alertsResponse}`;
    
    return result;
}

export const getIncidentApi = (server: McpServer) => {
    return server.tool(
        "get-incident",
        "Get detailed information about a specific PagerDuty incident",
        {
            id: z.string().describe("The ID of the incident to retrieve"),
            include: z.array(z.string()).optional().describe(
                "Additional details to include (e.g., acknowledgers, assignments, teams)"
            )
        },
        async ({ id, include }) => {
            const result = await getIncidentById(id, include);
            return {
                content: [{ type: "text", text: result }],
            };
        }
    );
};

async function getAlertById(incidentId: string, alertId: string): Promise<string> {
    const response = await makePagerDutyRequest<{ alert: any }>(`/incidents/${incidentId}/alerts/${alertId}`);

    if (!response || !response.alert) {
        return "Failed to retrieve alert";
    }

    const alert = response.alert;
    let result = '';
    
    // Determine integration type from client field in cef_details
    const isDynatraceAlert = alert.body?.cef_details?.client === 'Dynatrace';
    
    // Extract problem ID from client_url for Dynatrace alerts
    let dynatraceProblemId = '';
    if (isDynatraceAlert && alert.body?.cef_details?.client_url) {
        const pidMatch = alert.body.cef_details.client_url.match(/pid=([^&]+)/);
        if (pidMatch) {
            dynatraceProblemId = pidMatch[1];
        }
    }
    
    result += `ID: ${alert.id}\n`;
    result += `Type: ${alert.type}\n`;
    result += `Summary: ${alert.summary}\n`;
    result += `Status: ${alert.status.toUpperCase()}\n`;
    result += `Created At: ${alert.created_at}\n`;
    
    // Integration and problem information
    result += `Integration: ${isDynatraceAlert ? 'Dynatrace' : (alert.integration ? alert.integration.type : 'Other')}\n`;
    if (isDynatraceAlert && dynatraceProblemId) {
        result += `Dynatrace Problem ID: ${dynatraceProblemId}\n`;
    }
    
    // Alert details
    result += `Severity: ${alert.severity.toUpperCase()}\n`;
    result += `Suppressed: ${alert.suppressed}\n`;
    
    if (alert.service) {
        result += `\nService:\n`;
        result += `  Name: ${alert.service.summary}\n`;
        result += `  ID: ${alert.service.id}\n`;
    }
    
    return result.trim();
}

export const getAlertApi = (server: McpServer) => {
    return server.tool(
        "get-alert",
        "Get detailed information about a specific alert within an incident",
        {
            incidentId: z.string().describe("The ID of the incident"),
            alertId: z.string().describe("The ID of the alert to retrieve")
        },
        async ({ incidentId, alertId }) => {
            const result = await getAlertById(incidentId, alertId);
            return {
                content: [{ type: "text", text: result }],
            };
        }
    );
};

async function listAlertsForIncident(incidentId: string, params: {
    limit?: number,
    offset?: number,
    total?: boolean,
    alert_key?: string,
    include?: string[],
    sort_by?: string,
    statuses?: string[]
} = {}): Promise<string> {
    const queryParams: Record<string, string | string[]> = {};
    if (params.limit) queryParams.limit = params.limit.toString();
    if (params.offset) queryParams.offset = params.offset.toString();
    if (params.total) queryParams.total = params.total.toString();
    if (params.alert_key) queryParams.alert_key = params.alert_key;
    if (params.include) queryParams.include = params.include;
    if (params.sort_by) queryParams.sort_by = params.sort_by;
    if (params.statuses) queryParams.statuses = params.statuses;

    const response = await makePagerDutyRequest<{ alerts: any[], limit: number, offset: number, more: boolean }>(`/incidents/${incidentId}/alerts`, queryParams);

    if (!response || !response.alerts) {
        return "Failed to retrieve alerts";
    }

    let result = `Alerts for Incident ${incidentId}:\n`;
    for (const [index, alert] of response.alerts.entries()) {
        const detailedAlert = await getAlertById(incidentId, alert.id);
        result += `\n${index + 1}. ${detailedAlert}`;
    }

    if (response.more) {
        result += `\n\nThere are more alerts available. Use offset ${response.offset + response.limit} to get the next page.`;
    }

    return result;
}

export const listAlertsForIncidentApi = (server: McpServer) => {
    return server.tool(
        "list-alerts-for-incident",
        "List alerts for a specific PagerDuty incident",
        {
            incidentId: z.string().describe("The ID of the incident"),
            limit: z.number().int().positive().max(100).optional().describe("The number of results per page (max 100)"),
            offset: z.number().int().nonnegative().optional().describe("Offset to start pagination search results"),
            total: z.boolean().optional().describe("If true, return total number of alerts"),
            alert_key: z.string().optional().describe("Alert de-duplication key"),
            include: z.array(z.string()).optional().describe("Additional details to include"),
            sort_by: z.string().optional().describe("Field to sort the results by"),
            statuses: z.array(z.string()).optional().describe("Return only alerts with the given statuses")
        },
        async (params) => {
            const { incidentId, ...queryParams } = params;
            const result = await listAlertsForIncident(incidentId, queryParams);
            return {
                content: [{ type: "text", text: result }],
            };
        }
    );
};

export const selectServiceApi = (server: McpServer) => {
    return server.tool(
        "select-service",
        "Select a service from the search results and get its details",
        {
            serviceNumber: z.number().int().positive().describe("Number of the service from the search results")
        },
        async ({ serviceNumber }) => {
            if (lastServiceSearchResults.length === 0) {
                return {
                    content: [{ type: "text", text: "No search results available. Please use the 'find-service' tool first." }],
                };
            }

            if (serviceNumber < 1 || serviceNumber > lastServiceSearchResults.length) {
                return {
                    content: [{ type: "text", text: `Invalid service number. Please choose a number between 1 and ${lastServiceSearchResults.length}.` }],
                };
            }

            const selectedService = lastServiceSearchResults[serviceNumber - 1];
            const details = await getServiceDetails(selectedService.id);
            return {
                content: [{ type: "text", text: details }],
            };
        }
    );
};