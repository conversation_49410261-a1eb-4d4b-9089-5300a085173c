#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import 'dotenv/config';

import {
  getEntityByIdApi,
  getProblemsApi,
  getEntitiesApi,
  getMetricsApi,
  getProblemByIdApi
} from "./tools/api.js";

import {
  searchServicesByNameApi,
  selectServiceApi,
  investigateServiceApi
} from "./tools/sro_api.js";

import { 
  verifyDqlQueryApi,
  executeDqlQueryApi,
  pollQueryResultApi,
  getServiceEndpointsApi,
  getTraceIdssApi,
  getSequenceDiagramForTrace,
  networkAnalysisApi,
} from "./tools/platform.js";

// Create server instance
const server = new McpServer({
  name: "mcp-dynatrace",
  version: "0.2.0",
});

// Register all tools
searchServicesByNameApi(server);
selectS<PERSON><PERSON><PERSON><PERSON>(server);
investigateServiceApi(server);
getEntityByIdApi(server);
getProblemsApi(server);
getEntitiesApi(server);
getMetricsApi(server);
getProblemByIdApi(server);
verifyDqlQueryApi(server);
executeDqlQueryApi(server);
pollQueryResultApi(server);
getServiceEndpointsApi(server);
getTraceIdssApi(server);
getSequenceDiagramForTrace(server);
networkAnalysisApi(server);

async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.log("Dynatrace MCP Server running on stdio");
}

main().catch((error) => {
  console.error("Fatal error in main():", error);
  process.exit(1);
});
