import axios from 'axios';
import { <PERSON>th<PERSON><PERSON><PERSON> } from './auth.js';
import { AmsAddress, AmsApiResponse, AmsConfig, Environment } from './types.js';

/**
 * TELUS AMS API client
 */
export class AmsApiClient {
  private config: AmsConfig;
  private authHandler: AuthHandler;
  private baseUrl: string;
  
  /**
   * Create a new AMS API client
   * @param config AMS API configuration
   */
  constructor(config: AmsConfig) {
    this.config = config;
    this.authHandler = new AuthHandler(config);
    
    // Set base URL based on environment
    this.baseUrl = config.environment === Environment.PROD
      ? 'https://apigw-pr.telus.com/common/spatialnetAddressManagement/v2'
      : 'https://apigw-st.telus.com/common/spatialnetAddressManagement/v2';
    
    console.error('[AMS API] Initialized with environment:', config.environment);
  }
  
  /**
   * Search for an address using smart match
   * @param address Formatted address string
   * @param environment Optional environment override
   * @returns Promise resolving to array of matching addresses
   */
  async smartSearchAddress(address: string, environment?: Environment): Promise<AmsAddress[]> {
    console.error('[AMS API] Smart search for address:', address);
    
    // Allow environment override
    if (environment && environment !== this.config.environment) {
      console.error('[AMS API] Environment override:', environment);
      // Create a new client with the overridden environment
      const tempConfig = { ...this.config, environment };
      const tempClient = new AmsApiClient(tempConfig);
      return tempClient.smartSearchAddress(address);
    }
    
    try {
      // Get access token
      const token = await this.authHandler.getAccessToken();
      
      // Encode the address for URL
      const encodedAddress = encodeURIComponent(address);
      
      // Build the URL
      const url = `${this.baseUrl}/address?a=query&src=T&searchType=smart_match&text=${encodedAddress}`;
      
      console.error('[AMS API] Request to endpoint:', url);
      
      // Make the request
      const response = await axios.get<AmsApiResponse>(url, {
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: 'application/json',
        },
      });
      
      // Check for API error
      if (response.data.error) {
        throw new Error(`AMS API error: ${response.data.error.message}`);
      }
      
      // Check if response is successful
      if (response.data.response !== 'SUCCESS' || !response.data.responseData) {
        console.error('[AMS API] Unexpected response format:', response.data);
        return [];
      }
      
      // Transform API response to AmsAddress format
      const hits = response.data.responseData.hit || [];
      const addresses: AmsAddress[] = hits.map(hit => ({
        id: hit.id,
        formattedAddress: hit.address,
        score: parseFloat(hit.score),
        source: hit.source,
        lastUpdated: hit.lastUpdated,
        telus_id: hit.payload?.id,
        // Additional fields could be extracted from hit.payload if needed
      }));
      
      console.error('[AMS API] Found', addresses.length, 'matching addresses');
      
      return addresses;
    } catch (error) {
      console.error('[AMS API] Error searching address:', error);
      
      if (axios.isAxiosError(error)) {
        // Handle rate limiting
        if (error.response?.status === 429) {
          throw new Error('AMS API rate limit exceeded. Please try again later.');
        }
        
        // Handle authentication errors
        if (error.response?.status === 401 || error.response?.status === 403) {
          // Clear token cache and try once more
          this.authHandler.clearTokenCache();
          throw new Error(`Authentication error: ${error.response?.data?.message || error.message}`);
        }
        
        throw new Error(`AMS API request failed: ${error.response?.data?.message || error.message}`);
      }
      
      throw new Error(`AMS API request failed: ${error}`);
    }
  }
}
