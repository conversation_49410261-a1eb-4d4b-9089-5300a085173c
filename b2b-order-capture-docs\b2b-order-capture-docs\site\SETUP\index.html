
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/SETUP/">
      
      
        <link rel="prev" href="../DOCUMENTATION_SUMMARY/">
      
      
      
      <link rel="icon" href="../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Setup Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#telus-b2b-order-capture-documentation-setup-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Setup Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href=".." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      📋 Prerequisites
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📋 Prerequisites">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#required-software" class="md-nav__link">
    <span class="md-ellipsis">
      Required Software
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#verify-installation" class="md-nav__link">
    <span class="md-ellipsis">
      Verify Installation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quick-start" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 Quick Start
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🚀 Quick Start">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#option-1-automated-setup-recommended" class="md-nav__link">
    <span class="md-ellipsis">
      Option 1: Automated Setup (Recommended)
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Option 1: Automated Setup (Recommended)">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#windows-powershell" class="md-nav__link">
    <span class="md-ellipsis">
      Windows (PowerShell)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#windows-command-prompt" class="md-nav__link">
    <span class="md-ellipsis">
      Windows (Command Prompt)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#linuxmacos-bash" class="md-nav__link">
    <span class="md-ellipsis">
      Linux/macOS (Bash)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#option-2-manual-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Option 2: Manual Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Option 2: Manual Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-create-virtual-environment" class="md-nav__link">
    <span class="md-ellipsis">
      1. Create Virtual Environment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-install-dependencies" class="md-nav__link">
    <span class="md-ellipsis">
      2. Install Dependencies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-build-documentation" class="md-nav__link">
    <span class="md-ellipsis">
      3. Build Documentation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#available-commands" class="md-nav__link">
    <span class="md-ellipsis">
      📖 Available Commands
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📖 Available Commands">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#setup-script-options" class="md-nav__link">
    <span class="md-ellipsis">
      Setup Script Options
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Setup Script Options">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#powershell-script-setup-docsps1" class="md-nav__link">
    <span class="md-ellipsis">
      PowerShell Script (setup-docs.ps1)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#batch-script-setup-docsbat" class="md-nav__link">
    <span class="md-ellipsis">
      Batch Script (setup-docs.bat)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bash-script-setup-docssh" class="md-nav__link">
    <span class="md-ellipsis">
      Bash Script (setup-docs.sh)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#direct-mkdocs-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Direct MkDocs Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#output-structure" class="md-nav__link">
    <span class="md-ellipsis">
      📁 Output Structure
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#accessing-documentation" class="md-nav__link">
    <span class="md-ellipsis">
      🌐 Accessing Documentation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🌐 Accessing Documentation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-server" class="md-nav__link">
    <span class="md-ellipsis">
      Development Server
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#static-files" class="md-nav__link">
    <span class="md-ellipsis">
      Static Files
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#features" class="md-nav__link">
    <span class="md-ellipsis">
      🎨 Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎨 Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#telus-branding" class="md-nav__link">
    <span class="md-ellipsis">
      TELUS Branding
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#interactive-elements" class="md-nav__link">
    <span class="md-ellipsis">
      Interactive Elements
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#enhanced-functionality" class="md-nav__link">
    <span class="md-ellipsis">
      Enhanced Functionality
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#customization" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Customization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Customization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#modifying-content" class="md-nav__link">
    <span class="md-ellipsis">
      Modifying Content
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#customizing-styling" class="md-nav__link">
    <span class="md-ellipsis">
      Customizing Styling
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      🐛 Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🐛 Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Common Issues">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#python-not-found" class="md-nav__link">
    <span class="md-ellipsis">
      Python Not Found
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#permission-denied-linuxmacos" class="md-nav__link">
    <span class="md-ellipsis">
      Permission Denied (Linux/macOS)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#port-already-in-use" class="md-nav__link">
    <span class="md-ellipsis">
      Port Already in Use
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-failures" class="md-nav__link">
    <span class="md-ellipsis">
      Build Failures
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#getting-help" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Help
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#support" class="md-nav__link">
    <span class="md-ellipsis">
      📞 Support
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#success" class="md-nav__link">
    <span class="md-ellipsis">
      🎉 Success!
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      📋 Prerequisites
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📋 Prerequisites">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#required-software" class="md-nav__link">
    <span class="md-ellipsis">
      Required Software
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#verify-installation" class="md-nav__link">
    <span class="md-ellipsis">
      Verify Installation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quick-start" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 Quick Start
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🚀 Quick Start">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#option-1-automated-setup-recommended" class="md-nav__link">
    <span class="md-ellipsis">
      Option 1: Automated Setup (Recommended)
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Option 1: Automated Setup (Recommended)">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#windows-powershell" class="md-nav__link">
    <span class="md-ellipsis">
      Windows (PowerShell)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#windows-command-prompt" class="md-nav__link">
    <span class="md-ellipsis">
      Windows (Command Prompt)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#linuxmacos-bash" class="md-nav__link">
    <span class="md-ellipsis">
      Linux/macOS (Bash)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#option-2-manual-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Option 2: Manual Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Option 2: Manual Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-create-virtual-environment" class="md-nav__link">
    <span class="md-ellipsis">
      1. Create Virtual Environment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-install-dependencies" class="md-nav__link">
    <span class="md-ellipsis">
      2. Install Dependencies
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-build-documentation" class="md-nav__link">
    <span class="md-ellipsis">
      3. Build Documentation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#available-commands" class="md-nav__link">
    <span class="md-ellipsis">
      📖 Available Commands
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📖 Available Commands">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#setup-script-options" class="md-nav__link">
    <span class="md-ellipsis">
      Setup Script Options
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Setup Script Options">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#powershell-script-setup-docsps1" class="md-nav__link">
    <span class="md-ellipsis">
      PowerShell Script (setup-docs.ps1)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#batch-script-setup-docsbat" class="md-nav__link">
    <span class="md-ellipsis">
      Batch Script (setup-docs.bat)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bash-script-setup-docssh" class="md-nav__link">
    <span class="md-ellipsis">
      Bash Script (setup-docs.sh)
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#direct-mkdocs-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Direct MkDocs Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#output-structure" class="md-nav__link">
    <span class="md-ellipsis">
      📁 Output Structure
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#accessing-documentation" class="md-nav__link">
    <span class="md-ellipsis">
      🌐 Accessing Documentation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🌐 Accessing Documentation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-server" class="md-nav__link">
    <span class="md-ellipsis">
      Development Server
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#static-files" class="md-nav__link">
    <span class="md-ellipsis">
      Static Files
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#features" class="md-nav__link">
    <span class="md-ellipsis">
      🎨 Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🎨 Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#telus-branding" class="md-nav__link">
    <span class="md-ellipsis">
      TELUS Branding
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#interactive-elements" class="md-nav__link">
    <span class="md-ellipsis">
      Interactive Elements
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#enhanced-functionality" class="md-nav__link">
    <span class="md-ellipsis">
      Enhanced Functionality
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#customization" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Customization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔧 Customization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#modifying-content" class="md-nav__link">
    <span class="md-ellipsis">
      Modifying Content
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#customizing-styling" class="md-nav__link">
    <span class="md-ellipsis">
      Customizing Styling
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      🐛 Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🐛 Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Common Issues">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#python-not-found" class="md-nav__link">
    <span class="md-ellipsis">
      Python Not Found
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#permission-denied-linuxmacos" class="md-nav__link">
    <span class="md-ellipsis">
      Permission Denied (Linux/macOS)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#port-already-in-use" class="md-nav__link">
    <span class="md-ellipsis">
      Port Already in Use
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-failures" class="md-nav__link">
    <span class="md-ellipsis">
      Build Failures
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#getting-help" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Help
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#support" class="md-nav__link">
    <span class="md-ellipsis">
      📞 Support
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#success" class="md-nav__link">
    <span class="md-ellipsis">
      🎉 Success!
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="telus-b2b-order-capture-documentation-setup-guide">TELUS B2B Order Capture Documentation Setup Guide<a class="headerlink" href="#telus-b2b-order-capture-documentation-setup-guide" title="Permanent link">&para;</a></h1>
<p>This guide will help you set up and generate the comprehensive HTML documentation for the TELUS B2B Order Capture ecosystem.</p>
<h2 id="prerequisites">📋 Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h2>
<h3 id="required-software">Required Software<a class="headerlink" href="#required-software" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Python 3.8+</strong> - <a href="https://www.python.org/downloads/">Download from python.org</a></li>
<li><strong>pip</strong> - Usually included with Python</li>
<li><strong>Git</strong> - <a href="https://git-scm.com/downloads/">Download from git-scm.com</a></li>
</ul>
<h3 id="verify-installation">Verify Installation<a class="headerlink" href="#verify-installation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Check Python version</span>
python<span class="w"> </span>--version<span class="w">  </span><span class="c1"># or python3 --version on Linux/macOS</span>

<span class="c1"># Check pip version</span>
pip<span class="w"> </span>--version<span class="w">     </span><span class="c1"># or pip3 --version on Linux/macOS</span>

<span class="c1"># Check Git version</span>
git<span class="w"> </span>--version
</code></pre></div>
<h2 id="quick-start">🚀 Quick Start<a class="headerlink" href="#quick-start" title="Permanent link">&para;</a></h2>
<h3 id="option-1-automated-setup-recommended">Option 1: Automated Setup (Recommended)<a class="headerlink" href="#option-1-automated-setup-recommended" title="Permanent link">&para;</a></h3>
<h4 id="windows-powershell">Windows (PowerShell)<a class="headerlink" href="#windows-powershell" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c"># Navigate to documentation directory</span>
<span class="nb">cd </span><span class="n">telus-b2b-order-capture-docs</span>

<span class="c"># Run setup script</span>
<span class="p">.\</span><span class="n">setup-docs</span><span class="p">.</span><span class="n">ps1</span>

<span class="c"># Or with specific action</span>
<span class="p">.\</span><span class="n">setup-docs</span><span class="p">.</span><span class="n">ps1</span> <span class="n">-Action</span> <span class="n">build</span>
</code></pre></div>
<h4 id="windows-command-prompt">Windows (Command Prompt)<a class="headerlink" href="#windows-command-prompt" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code># Navigate to documentation directory
cd telus-b2b-order-capture-docs

# Run setup script
setup-docs.bat

# Or with specific action
setup-docs.bat --action build
</code></pre></div>
<h4 id="linuxmacos-bash">Linux/macOS (Bash)<a class="headerlink" href="#linuxmacos-bash" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Navigate to documentation directory</span>
<span class="nb">cd</span><span class="w"> </span>telus-b2b-order-capture-docs

<span class="c1"># Make script executable (first time only)</span>
chmod<span class="w"> </span>+x<span class="w"> </span>setup-docs.sh

<span class="c1"># Run setup script</span>
./setup-docs.sh

<span class="c1"># Or with specific action</span>
./setup-docs.sh<span class="w"> </span>--action<span class="w"> </span>build
</code></pre></div>
<h3 id="option-2-manual-setup">Option 2: Manual Setup<a class="headerlink" href="#option-2-manual-setup" title="Permanent link">&para;</a></h3>
<h4 id="1-create-virtual-environment">1. Create Virtual Environment<a class="headerlink" href="#1-create-virtual-environment" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Create virtual environment</span>
python<span class="w"> </span>-m<span class="w"> </span>venv<span class="w"> </span>venv

<span class="c1"># Activate virtual environment</span>
<span class="c1"># Windows:</span>
venv<span class="se">\S</span>cripts<span class="se">\a</span>ctivate
<span class="c1"># Linux/macOS:</span>
<span class="nb">source</span><span class="w"> </span>venv/bin/activate
</code></pre></div>
<h4 id="2-install-dependencies">2. Install Dependencies<a class="headerlink" href="#2-install-dependencies" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Install from requirements file</span>
pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements.txt

<span class="c1"># Or install manually</span>
pip<span class="w"> </span>install<span class="w"> </span>mkdocs<span class="w"> </span>mkdocs-material<span class="w"> </span>mkdocs-mermaid2-plugin
</code></pre></div>
<h4 id="3-build-documentation">3. Build Documentation<a class="headerlink" href="#3-build-documentation" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Build static documentation</span>
mkdocs<span class="w"> </span>build

<span class="c1"># Or start development server</span>
mkdocs<span class="w"> </span>serve
</code></pre></div>
<h2 id="available-commands">📖 Available Commands<a class="headerlink" href="#available-commands" title="Permanent link">&para;</a></h2>
<h3 id="setup-script-options">Setup Script Options<a class="headerlink" href="#setup-script-options" title="Permanent link">&para;</a></h3>
<h4 id="powershell-script-setup-docsps1">PowerShell Script (<code>setup-docs.ps1</code>)<a class="headerlink" href="#powershell-script-setup-docsps1" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c"># Show help</span>
<span class="p">.\</span><span class="n">setup-docs</span><span class="p">.</span><span class="n">ps1</span> <span class="n">-Help</span>

<span class="c"># Install dependencies only</span>
<span class="p">.\</span><span class="n">setup-docs</span><span class="p">.</span><span class="n">ps1</span> <span class="n">-Install</span>

<span class="c"># Build documentation</span>
<span class="p">.\</span><span class="n">setup-docs</span><span class="p">.</span><span class="n">ps1</span> <span class="n">-Build</span>

<span class="c"># Start development server</span>
<span class="p">.\</span><span class="n">setup-docs</span><span class="p">.</span><span class="n">ps1</span> <span class="n">-Action</span> <span class="n">serve</span> <span class="n">-Port</span> <span class="n">8000</span>

<span class="c"># Clean build artifacts</span>
<span class="p">.\</span><span class="n">setup-docs</span><span class="p">.</span><span class="n">ps1</span> <span class="n">-Clean</span>
</code></pre></div>
<h4 id="batch-script-setup-docsbat">Batch Script (<code>setup-docs.bat</code>)<a class="headerlink" href="#batch-script-setup-docsbat" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code># Show help
setup-docs.bat --help

# Install dependencies only
setup-docs.bat --install

# Build documentation
setup-docs.bat --build

# Start development server
setup-docs.bat --action serve --port 8000

# Clean build artifacts
setup-docs.bat --clean
</code></pre></div>
<h4 id="bash-script-setup-docssh">Bash Script (<code>setup-docs.sh</code>)<a class="headerlink" href="#bash-script-setup-docssh" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Show help</span>
./setup-docs.sh<span class="w"> </span>--help

<span class="c1"># Install dependencies only</span>
./setup-docs.sh<span class="w"> </span>--install

<span class="c1"># Build documentation</span>
./setup-docs.sh<span class="w"> </span>--build

<span class="c1"># Start development server</span>
./setup-docs.sh<span class="w"> </span>--action<span class="w"> </span>serve<span class="w"> </span>--port<span class="w"> </span><span class="m">8000</span>

<span class="c1"># Clean build artifacts</span>
./setup-docs.sh<span class="w"> </span>--clean
</code></pre></div>
<h3 id="direct-mkdocs-commands">Direct MkDocs Commands<a class="headerlink" href="#direct-mkdocs-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Activate virtual environment first</span>
<span class="nb">source</span><span class="w"> </span>venv/bin/activate<span class="w">  </span><span class="c1"># Linux/macOS</span>
<span class="c1"># or</span>
venv<span class="se">\S</span>cripts<span class="se">\a</span>ctivate<span class="w">     </span><span class="c1"># Windows</span>

<span class="c1"># Start development server</span>
mkdocs<span class="w"> </span>serve

<span class="c1"># Build static documentation</span>
mkdocs<span class="w"> </span>build

<span class="c1"># Build with clean output</span>
mkdocs<span class="w"> </span>build<span class="w"> </span>--clean

<span class="c1"># Serve on specific port</span>
mkdocs<span class="w"> </span>serve<span class="w"> </span>--dev-addr<span class="w"> </span>localhost:9000

<span class="c1"># Build with strict mode (fail on warnings)</span>
mkdocs<span class="w"> </span>build<span class="w"> </span>--strict
</code></pre></div>
<h2 id="output-structure">📁 Output Structure<a class="headerlink" href="#output-structure" title="Permanent link">&para;</a></h2>
<p>After building, the documentation will be available in the <code>site/</code> directory:</p>
<div class="highlight"><pre><span></span><code>site/
├── index.html                 # Main documentation page
├── 00-overview/               # Overview &amp; Architecture
├── 01-frontend-guide/         # Frontend Guide
├── 02-backend-guide/          # Backend Guide
├── 03-extensions-guide/       # Extensions Guide
├── 04-integration-guide/      # Integration Guide
├── 05-development-guide/      # Development Guide
├── 06-deployment-guide/       # Deployment Guide
├── 07-reference/              # Reference Guide
├── assets/                    # Images and assets
├── stylesheets/               # CSS files
├── javascripts/               # JavaScript files
└── search/                    # Search index
</code></pre></div>
<h2 id="accessing-documentation">🌐 Accessing Documentation<a class="headerlink" href="#accessing-documentation" title="Permanent link">&para;</a></h2>
<h3 id="development-server">Development Server<a class="headerlink" href="#development-server" title="Permanent link">&para;</a></h3>
<p>When using <code>mkdocs serve</code>, the documentation will be available at:
- <strong>Default</strong>: http://localhost:8000
- <strong>Custom Port</strong>: http://localhost:PORT (where PORT is your specified port)</p>
<h3 id="static-files">Static Files<a class="headerlink" href="#static-files" title="Permanent link">&para;</a></h3>
<p>When using <code>mkdocs build</code>, open <code>site/index.html</code> in your web browser.</p>
<h2 id="features">🎨 Features<a class="headerlink" href="#features" title="Permanent link">&para;</a></h2>
<h3 id="telus-branding">TELUS Branding<a class="headerlink" href="#telus-branding" title="Permanent link">&para;</a></h3>
<ul>
<li>Custom TELUS purple and green color scheme</li>
<li>TELUS-specific component badges</li>
<li>Professional styling consistent with TELUS brand guidelines</li>
</ul>
<h3 id="interactive-elements">Interactive Elements<a class="headerlink" href="#interactive-elements" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Mermaid Diagrams</strong>: Interactive architecture and flow diagrams</li>
<li><strong>Code Copy Buttons</strong>: One-click code copying</li>
<li><strong>Search Functionality</strong>: Full-text search across all documentation</li>
<li><strong>Navigation</strong>: Breadcrumbs, previous/next links, and table of contents</li>
<li><strong>Responsive Design</strong>: Mobile-friendly layout</li>
</ul>
<h3 id="enhanced-functionality">Enhanced Functionality<a class="headerlink" href="#enhanced-functionality" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Component Badges</strong>: Automatic tagging of Frontend/Backend/Extension content</li>
<li><strong>Status Indicators</strong>: Visual status indicators for different states</li>
<li><strong>API Documentation</strong>: Enhanced API endpoint documentation</li>
<li><strong>Print Support</strong>: Optimized printing with proper page breaks</li>
</ul>
<h2 id="customization">🔧 Customization<a class="headerlink" href="#customization" title="Permanent link">&para;</a></h2>
<h3 id="modifying-content">Modifying Content<a class="headerlink" href="#modifying-content" title="Permanent link">&para;</a></h3>
<ol>
<li>Edit the Markdown files (<code>.md</code>) in the documentation directory</li>
<li>The documentation will automatically rebuild when using <code>mkdocs serve</code></li>
<li>For static builds, run <code>mkdocs build</code> after making changes</li>
</ol>
<h3 id="customizing-styling">Customizing Styling<a class="headerlink" href="#customizing-styling" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>CSS</strong>: Edit <code>stylesheets/telus-theme.css</code></li>
<li><strong>JavaScript</strong>: Edit <code>javascripts/extra.js</code></li>
<li><strong>Mermaid</strong>: Edit <code>javascripts/mermaid.js</code></li>
</ul>
<h3 id="configuration">Configuration<a class="headerlink" href="#configuration" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>MkDocs Settings</strong>: Edit <code>mkdocs.yml</code></li>
<li><strong>Dependencies</strong>: Edit <code>requirements.txt</code></li>
</ul>
<h2 id="troubleshooting">🐛 Troubleshooting<a class="headerlink" href="#troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="common-issues">Common Issues<a class="headerlink" href="#common-issues" title="Permanent link">&para;</a></h3>
<h4 id="python-not-found">Python Not Found<a class="headerlink" href="#python-not-found" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Error: &#39;python&#39; is not recognized</span>
<span class="c1"># Solution: Install Python or use python3</span>
python3<span class="w"> </span>--version
</code></pre></div>
<h4 id="permission-denied-linuxmacos">Permission Denied (Linux/macOS)<a class="headerlink" href="#permission-denied-linuxmacos" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Error: Permission denied</span>
<span class="c1"># Solution: Make script executable</span>
chmod<span class="w"> </span>+x<span class="w"> </span>setup-docs.sh
</code></pre></div>
<h4 id="port-already-in-use">Port Already in Use<a class="headerlink" href="#port-already-in-use" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Error: Address already in use</span>
<span class="c1"># Solution: Use different port</span>
mkdocs<span class="w"> </span>serve<span class="w"> </span>--dev-addr<span class="w"> </span>localhost:8001
</code></pre></div>
<h4 id="build-failures">Build Failures<a class="headerlink" href="#build-failures" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Error: Build failed</span>
<span class="c1"># Solution: Check for syntax errors in Markdown files</span>
mkdocs<span class="w"> </span>build<span class="w"> </span>--strict<span class="w"> </span>--verbose
</code></pre></div>
<h3 id="getting-help">Getting Help<a class="headerlink" href="#getting-help" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Check Prerequisites</strong>: Ensure Python 3.8+ and pip are installed</li>
<li><strong>Review Error Messages</strong>: Most errors include helpful information</li>
<li><strong>Use Verbose Mode</strong>: Add <code>--verbose</code> flag for detailed output</li>
<li><strong>Clean Build</strong>: Try cleaning and rebuilding
   <div class="highlight"><pre><span></span><code><span class="c1"># Clean and rebuild</span>
./setup-docs.sh<span class="w"> </span>--clean
./setup-docs.sh<span class="w"> </span>--build
</code></pre></div></li>
</ol>
<h2 id="support">📞 Support<a class="headerlink" href="#support" title="Permanent link">&para;</a></h2>
<p>For issues with the documentation setup:
1. Check this setup guide
2. Review the error messages carefully
3. Ensure all prerequisites are met
4. Contact the TELUS Cloud BSS team for additional support</p>
<h2 id="success">🎉 Success!<a class="headerlink" href="#success" title="Permanent link">&para;</a></h2>
<p>Once setup is complete, you'll have:
- ✅ Professional HTML documentation
- ✅ Interactive diagrams and code examples
- ✅ Full-text search functionality
- ✅ Mobile-responsive design
- ✅ TELUS brand styling
- ✅ Easy navigation and cross-references</p>
<p>The documentation provides comprehensive coverage of the entire TELUS B2B Order Capture ecosystem, making it easy for developers, DevOps teams, and integration teams to understand and work with the system.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "..", "features": [], "search": "../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>