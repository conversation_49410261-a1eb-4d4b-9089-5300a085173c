{"name": "@telus/mcp-guideline", "version": "0.2.0", "description": "MCP server for analyzing files and extracting guidelines", "keywords": ["telus", "mcp", "guideline"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-guideline"}, "license": "MIT", "author": "<PERSON><PERSON><PERSON> (@kurosh-sahraie)", "type": "module", "main": "dist/index.js", "bin": {"mcp-guideline": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "dev": "concurrently --names tsc,server \"tsc -w\" \"node --watch --env-file=.env dist/index.js\"", "prepare": "pnpm run build", "start": "node --env-file=.env dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}