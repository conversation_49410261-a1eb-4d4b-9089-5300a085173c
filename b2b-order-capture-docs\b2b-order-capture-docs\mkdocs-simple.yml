site_name: TELUS B2B Order Capture Documentation
site_description: Comprehensive documentation for TELUS B2B Order Capture ecosystem
site_author: TELUS Cloud BSS Team
site_url: http://localhost:8000

docs_dir: docs

theme:
  name: material
  palette:
    primary: deep purple
    accent: purple

plugins:
  - search

markdown_extensions:
  - toc:
      permalink: true
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format

nav:
  - Home: README.md
  - Overview: 00-overview.md
  - Frontend Application:
    - Overview: frontend/00-overview.md
    - Architecture: frontend/01-architecture.md
    - Custom Components: frontend/02-custom-components.md
    - State Management: frontend/03-state-management.md
    - API Integration: frontend/04-api-integration.md
    - Development Guide: frontend/05-development-guide.md
  - Backend Application:
    - Overview: backend/00-overview.md
    - Architecture: backend/01-architecture.md
    - REST API Design: backend/02-rest-api-design.md
    - Service Layer: backend/03-service-layer.md
    - Integration Layer: backend/04-integration-layer.md
    - Development Guide: backend/05-development-guide.md
  - Extensions Application:
    - Overview: extensions/00-overview.md
    - Plugin Architecture: extensions/01-plugin-architecture.md
    - Quote Modificators: extensions/02-quote-modificators.md
    - Bandwidth Validators: extensions/03-bandwidth-validators.md
    - State Validators: extensions/04-state-validators.md
    - Development Guide: extensions/05-development-guide.md
  - QSS Extension:
    - Overview: qss-extension/00-overview.md
    - Plugin Architecture: qss-extension/01-plugin-architecture.md
    - Snapshot Generation: qss-extension/02-snapshot-generation.md
    - Storage Management: qss-extension/03-storage-management.md
    - Development Guide: qss-extension/04-development-guide.md
  - Bulk Operation Extension:
    - Overview: bulk-operation-extension/00-overview.md
    - NiFi Flow Architecture: bulk-operation-extension/01-nifi-flow-architecture.md
    - API Integration: bulk-operation-extension/02-api-integration.md
    - Deployment Configuration: bulk-operation-extension/03-deployment-configuration.md
    - Development Guide: bulk-operation-extension/04-development-guide.md
  - CIP Configuration:
    - Overview: cip-config/00-overview.md
    - Architecture: cip-config/01-architecture.md
    - Configuration Management: cip-config/02-configuration-management.md
    - Integration Chains: cip-config/03-integration-chains.md
    - Service Catalog: cip-config/04-service-catalog.md
    - Deployment Guide: cip-config/05-deployment-guide.md
    - Development Guide: cip-config/06-development-guide.md
    - Troubleshooting: cip-config/07-troubleshooting.md
    - Reference: cip-config/08-reference.md
    - Chain Catalog: cip-config/09-chain-catalog.md
  - CPQ Configuration:
    - Overview: cpq-config/00-overview.md
    - Configuration Management: cpq-config/01-configuration-management.md
    - Integration Chains: cpq-config/02-integration-chains.md
    - Service Catalog: cpq-config/03-service-catalog.md
    - Security Configuration: cpq-config/04-security-configuration.md
    - Development Guide: cpq-config/05-development-guide.md
  - Frontend Guide: 01-frontend-guide.md
  - Backend Guide: 02-backend-guide.md
  - Extensions Guide: 03-extensions-guide.md
  - Integration Guide: 04-integration-guide.md
  - Development Guide: 05-development-guide.md
  - Deployment Guide: 06-deployment-guide.md
  - Reference: 07-reference.md
  - Documentation Summary: DOCUMENTATION_SUMMARY.md
  - Setup Guide: SETUP.md

extra_css:
  - stylesheets/extra.css
  - stylesheets/telus-theme.css

extra_javascript:
  - https://unpkg.com/mermaid@10.6.1/dist/mermaid.min.js
  - javascripts/mermaid-manual.js
