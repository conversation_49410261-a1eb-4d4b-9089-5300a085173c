#!/usr/bin/env node

const axios = require('axios');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
const envPath = path.resolve(__dirname, '.env');
console.log('Loading .env file from:', envPath);
const dotenvResult = dotenv.config({ path: envPath });
if (dotenvResult.error) {
  console.error('Error loading .env file:', dotenvResult.error.message);
} else {
  console.log('.env file loaded successfully');
  console.log('Loaded environment variables:', Object.keys(dotenvResult.parsed || {}));
}

// Test function to get a product offering by ID
async function testGetProductOffering() {
  try {
    console.log('\n=== Testing Get Product Offering ===\n');
    
    // First authenticate to get a token
    console.log('Step 1: Authenticating to get a token...');
    
    // Prepare token request parameters
    const params = new URLSearchParams({
      grant_type: 'client_credentials',
      client_id: process.env.OAUTH_CLIENT_ID,
      client_secret: process.env.OAUTH_CLIENT_SECRET,
      scope: process.env.OAUTH_SCOPE,
    });
    
    // Configure axios instance
    const authConfig = {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      timeout: 60000, // 60 seconds
      httpsAgent: new (require('https').Agent)({
        rejectUnauthorized: false // Only for debugging - remove in production
      })
    };

    // Request new token
    const authResponse = await axios.post(
      process.env.OAUTH_TOKEN_URL,
      params,
      authConfig
    );

    const token = authResponse.data.access_token;
    console.log('Token obtained successfully!');
    
    // Now test getting a product offering by ID
    console.log('\nStep 2: Getting product offering by ID...');
    
    const quoteId = process.env.TEST_QUOTE_ID;
    console.log(`Using TEST_QUOTE_ID: ${quoteId}`);
    
    const apiUrl = `${process.env.TEST_API_URL}/${quoteId}`;
    console.log('API URL:', apiUrl);
    
    // Configure API request
    const apiConfig = {
      headers: {
        'Authorization': `Bearer ${token}`,
        'env': 'itn05'
      },
      timeout: 30000, // 30 seconds
      httpsAgent: new (require('https').Agent)({
        rejectUnauthorized: false // Only for debugging - remove in production
      })
    };

    // Make the API request
    const apiResponse = await axios.get(apiUrl, apiConfig);
    
    console.log('\nAPI request successful!');
    console.log('Response status:', apiResponse.status);
    
    // Extract and display the product offering name
    if (apiResponse.data && Array.isArray(apiResponse.data) && apiResponse.data.length > 0) {
      const productOffering = apiResponse.data[0];
      console.log('\nProduct Offering Details:');
      console.log('ID:', productOffering.id);
      console.log('Name:', productOffering.name);
      console.log('Lifecycle Status:', productOffering.lifecycleStatus);
      
      if (productOffering.productFamily) {
        console.log('Product Family:', productOffering.productFamily.name);
      }
      
      if (productOffering.validFor && productOffering.validFor.startDateTime) {
        console.log('Valid From:', productOffering.validFor.startDateTime);
      }
      
      console.log('\nFull Product Offering Data:');
      console.log(JSON.stringify(productOffering, null, 2).substring(0, 1000) + '...');
    } else {
      console.log('No product offering data found in the response');
      console.log('Response data:', apiResponse.data);
    }
    
    return true;
  } catch (error) {
    console.error('Test failed:');
    
    if (axios.isAxiosError(error)) {
      console.error('Error code:', error.code);
      console.error('Error message:', error.message);
      
      if (error.response) {
        // The server responded with a status code outside the 2xx range
        console.error('Response status:', error.response.status);
        console.error('Response headers:', error.response.headers);
        console.error('Response data:', error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        console.error('No response received');
        console.error('Request details:', error.request._currentUrl);
      }
    } else {
      // Something else happened
      console.error('Unexpected error:', error);
    }
    
    return false;
  }
}

// Run the test
testGetProductOffering().catch(error => {
  console.error('Test script error:', error);
  process.exit(1);
});
