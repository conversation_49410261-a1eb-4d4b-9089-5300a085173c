"use client";

import { useEffect } from "react";
import ReactMarkdown from "react-markdown";
import type { MCPServer } from "@/types/servers";
import { motion, AnimatePresence } from "motion/react";

interface ServerModalProps {
  server: MCPServer;
  isOpen: boolean;
  onClose: () => void;
}

export function ServerModal({ server, isOpen, onClose }: ServerModalProps) {
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEsc);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEsc);
      document.body.style.overflow = "auto";
    };
  }, [isOpen, onClose]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={onClose}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <motion.div
            className="bg-white dark:bg-gray-900 rounded-lg max-w-5xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
            initial={{ scale: 0.95, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.95, opacity: 0, y: 20 }}
            transition={{ duration: 0.2 }}
          >
            <div className="sticky top-0 bg-white dark:bg-gray-900 p-4 border-b border-gray-200 dark:border-gray-800 flex justify-between items-center">
              <h2 className="text-2xl font-bold">{server.name}</h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>

            <div className="p-6">
              <div className="mb-6">
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {server.description}
                </p>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <h3 className="font-semibold">Version</h3>
                    <p>{server.version}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold">Author</h3>
                    <p>{server.author}</p>
                  </div>
                </div>

                {server.repository && (
                  <div className="mb-4">
                    <h3 className="font-semibold">Repository</h3>
                    <a
                      href={server.repository}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {server.repository}
                    </a>
                  </div>
                )}

                {server.packageUrl && (
                  <div className="mb-4">
                    <h3 className="font-semibold">Package</h3>
                    <a
                      href={server.packageUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {server.packageUrl}
                    </a>
                  </div>
                )}

                <div className="mb-4">
                  <h3 className="font-semibold">Tags</h3>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {server.tags.map((tag) => (
                      <span
                        key={tag}
                        className="bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-100 px-2 py-1 rounded-full text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {server.readme && (
                <div>
                  <h3 className="text-xl font-semibold mb-4">README</h3>
                  <div className="prose dark:prose-invert max-w-none">
                    <ReactMarkdown>{server.readme}</ReactMarkdown>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
