"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

export function ServerSearch() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.get("q") || "");

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);

    // Update URL query parameter
    const params = new URLSearchParams(searchParams.toString());
    if (query.trim() === "") {
      params.delete("q");
    } else {
      params.set("q", query);
    }
    router.push(`?${params.toString()}`);
  };

  return (
    <div className="w-full min-w-96 space-y-4">
      <div className="relative">
        <input
          type="text"
          placeholder="Search servers by name or description..."
          value={searchQuery}
          onChange={handleSearch}
          className="w-full outline-none rounded-md border-0 ring-2 ring-gray-300 dark:ring-gray-700 bg-white dark:bg-gray-900 p-2 focus:ring-2 focus:ring-purple-900 dark:focus:ring-purple-500"
        />
      </div>
    </div>
  );
}
