#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  ListToolsRequestSchema,
  CallToolRequestSchema,
  ListResourcesRequestSchema,
  ListResourceTemplatesRequestSchema,
  ReadResourceRequestSchema,
  McpError,
  ErrorCode,
  ServerResult,
} from '@modelcontextprotocol/sdk/types.js';
import { Firestore, DocumentData, Query, CollectionReference } from '@google-cloud/firestore';

const GCP_PROJECT_ID = process.env.GCP_PROJECT_ID;
const DEFAULT_DATABASE_ID = process.env.DEFAULT_DATABASE_ID;
const DEFAULT_COLLECTION_ID = process.env.DEFAULT_COLLECTION_ID;

if (!GCP_PROJECT_ID) {
  throw new Error('GCP_PROJECT_ID environment variable is required');
}

// Type definitions for tool arguments
interface QueryCollectionArgs {
  databaseId?: string;
  collectionId?: string;
  whereClause?: {
    field: string;
    operator: '=='|'!='|'>'|'>='|'<'|'<='|'array-contains'|'array-contains-any'|'in'|'not-in';
    value: any;
  };
  limit?: number;
}

interface CreateCollectionArgs {
  databaseId?: string;
  collectionId?: string;
}

interface AddDocumentArgs {
  databaseId?: string;
  collectionId?: string;
  documentData: Record<string, any>;
}

interface GetDocumentArgs {
  databaseId?: string;
  collectionId?: string;
  documentId: string;
}

interface UpdateDocumentArgs {
  databaseId?: string;
  collectionId?: string;
  documentId: string;
  documentData: Record<string, any>;
}

interface DeleteDocumentArgs {
  databaseId?: string;
  collectionId?: string;
  documentId: string;
}

// Type guards for argument validation
const isQueryCollectionArgs = (args: any): args is QueryCollectionArgs => {
  return (
    typeof args === 'object' &&
    args !== null &&
    (args.databaseId === undefined || typeof args.databaseId === 'string') &&
    (args.collectionId === undefined || typeof args.collectionId === 'string') &&
    (args.whereClause === undefined ||
      (typeof args.whereClause === 'object' &&
        typeof args.whereClause.field === 'string' &&
        typeof args.whereClause.operator === 'string' &&
        args.whereClause.value !== undefined)) &&
    (args.limit === undefined || typeof args.limit === 'number')
  );
};

const isCreateCollectionArgs = (args: any): args is CreateCollectionArgs => {
  return (
    typeof args === 'object' &&
    args !== null &&
    (args.databaseId === undefined || typeof args.databaseId === 'string') &&
    (args.collectionId === undefined || typeof args.collectionId === 'string')
  );
};

const isAddDocumentArgs = (args: any): args is AddDocumentArgs => {
  return (
    typeof args === 'object' &&
    args !== null &&
    (args.databaseId === undefined || typeof args.databaseId === 'string') &&
    (args.collectionId === undefined || typeof args.collectionId === 'string') &&
    typeof args.documentData === 'object'
  );
};

const isGetDocumentArgs = (args: any): args is GetDocumentArgs => {
  return (
    typeof args === 'object' &&
    args !== null &&
    (args.databaseId === undefined || typeof args.databaseId === 'string') &&
    (args.collectionId === undefined || typeof args.collectionId === 'string') &&
    typeof args.documentId === 'string'
  );
};

const isUpdateDocumentArgs = (args: any): args is UpdateDocumentArgs => {
  return (
    typeof args === 'object' &&
    args !== null &&
    (args.databaseId === undefined || typeof args.databaseId === 'string') &&
    (args.collectionId === undefined || typeof args.collectionId === 'string') &&
    typeof args.documentId === 'string' &&
    typeof args.documentData === 'object'
  );
};

const isDeleteDocumentArgs = (args: any): args is DeleteDocumentArgs => {
  return (
    typeof args === 'object' &&
    args !== null &&
    (args.databaseId === undefined || typeof args.databaseId === 'string') &&
    (args.collectionId === undefined || typeof args.collectionId === 'string') &&
    typeof args.documentId === 'string'
  );
};

class FirestoreServer {
  private server: Server;
  private firestoreClients: Map<string, Firestore>;

  constructor() {
    this.server = new Server(
      {
        name: 'mcp-firestore',
        version: '0.0.1',
      },
      {
        capabilities: {
          resources: {},
          tools: {},
        },
      }
    );

    this.firestoreClients = new Map();

    this.setupResourceHandlers();
    this.setupToolHandlers();
    
    // Error handling
    this.server.onerror = (error) => console.error('[MCP Error]', error);
    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private getFirestoreClient(databaseId?: string): Firestore {
    const actualDatabaseId = databaseId || DEFAULT_DATABASE_ID;
    if (!actualDatabaseId) {
      throw new McpError(ErrorCode.InvalidParams, 'Database ID is required');
    }
    const clientKey = `${GCP_PROJECT_ID}:${actualDatabaseId}`;
    if (!this.firestoreClients.has(clientKey)) {
      this.firestoreClients.set(
        clientKey,
        new Firestore({
          projectId: GCP_PROJECT_ID,
          databaseId: actualDatabaseId,
          // Uses GOOGLE_APPLICATION_CREDENTIALS environment variable
        })
      );
    }
    return this.firestoreClients.get(clientKey)!;
  }

  private getCollectionId(collectionId?: string): string {
    const actualCollectionId = collectionId || DEFAULT_COLLECTION_ID;
    if (!actualCollectionId) {
      throw new McpError(ErrorCode.InvalidParams, 'Collection ID is required');
    }
    return actualCollectionId;
  }

  private setupResourceHandlers() {
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => ({
      resources: [],
    }));

    this.server.setRequestHandler(ListResourceTemplatesRequestSchema, async () => ({
      resourceTemplates: [],
    }));

    this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      throw new McpError(
        ErrorCode.InvalidRequest,
        `Invalid URI format: ${request.params.uri}`
      );
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'query_collection',
          description: 'Query documents from a collection with optional filtering and limit',
          inputSchema: {
            type: 'object',
            properties: {
              databaseId: {
                type: 'string',
                description: 'Firestore database ID (optional if DEFAULT_DATABASE_ID is set)',
              },
              collectionId: {
                type: 'string',
                description: 'Collection ID to query (optional if DEFAULT_COLLECTION_ID is set)',
              },
              whereClause: {
                type: 'object',
                description: 'Optional where clause for filtering',
                properties: {
                  field: { type: 'string' },
                  operator: { 
                    type: 'string',
                    enum: ['==', '!=', '>', '>=', '<', '<=', 'array-contains', 'array-contains-any', 'in', 'not-in']
                  },
                  value: { type: 'any' }
                },
                required: ['field', 'operator', 'value']
              },
              limit: {
                type: 'number',
                description: 'Maximum number of documents to return (default: 20)',
                default: 20
              }
            }
          }
        },
        {
          name: 'create_collection',
          description: 'Create a new collection',
          inputSchema: {
            type: 'object',
            properties: {
              databaseId: {
                type: 'string',
                description: 'Firestore database ID (optional if DEFAULT_DATABASE_ID is set)'
              },
              collectionId: {
                type: 'string',
                description: 'Collection ID to create (optional if DEFAULT_COLLECTION_ID is set)'
              }
            }
          }
        },
        {
          name: 'add_document',
          description: 'Add a new document to a collection',
          inputSchema: {
            type: 'object',
            properties: {
              databaseId: {
                type: 'string',
                description: 'Firestore database ID (optional if DEFAULT_DATABASE_ID is set)'
              },
              collectionId: {
                type: 'string',
                description: 'Collection ID to add document to (optional if DEFAULT_COLLECTION_ID is set)'
              },
              documentData: {
                type: 'object',
                description: 'Document data to add'
              }
            },
            required: ['documentData']
          }
        },
        {
          name: 'get_document',
          description: 'Get a specific document by ID',
          inputSchema: {
            type: 'object',
            properties: {
              databaseId: {
                type: 'string',
                description: 'Firestore database ID (optional if DEFAULT_DATABASE_ID is set)'
              },
              collectionId: {
                type: 'string',
                description: 'Collection ID containing the document (optional if DEFAULT_COLLECTION_ID is set)'
              },
              documentId: {
                type: 'string',
                description: 'Document ID to retrieve'
              }
            },
            required: ['documentId']
          }
        },
        {
          name: 'update_document',
          description: 'Update an existing document',
          inputSchema: {
            type: 'object',
            properties: {
              databaseId: {
                type: 'string',
                description: 'Firestore database ID (optional if DEFAULT_DATABASE_ID is set)'
              },
              collectionId: {
                type: 'string',
                description: 'Collection ID containing the document (optional if DEFAULT_COLLECTION_ID is set)'
              },
              documentId: {
                type: 'string',
                description: 'Document ID to update'
              },
              documentData: {
                type: 'object',
                description: 'Updated document data'
              }
            },
            required: ['documentId', 'documentData']
          }
        },
        {
          name: 'delete_document',
          description: 'Delete a document',
          inputSchema: {
            type: 'object',
            properties: {
              databaseId: {
                type: 'string',
                description: 'Firestore database ID (optional if DEFAULT_DATABASE_ID is set)'
              },
              collectionId: {
                type: 'string',
                description: 'Collection ID containing the document (optional if DEFAULT_COLLECTION_ID is set)'
              },
              documentId: {
                type: 'string',
                description: 'Document ID to delete'
              }
            },
            required: ['documentId']
          }
        }
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request, extra): Promise<ServerResult> => {
      try {
        console.error(`Handling tool request: ${request.params.name}`);
        
        switch (request.params.name) {
          case 'query_collection': {
            if (!isQueryCollectionArgs(request.params.arguments)) {
              throw new McpError(ErrorCode.InvalidParams, 'Invalid query_collection arguments');
            }

            const { databaseId, collectionId, whereClause, limit = 20 } = request.params.arguments;
            const firestore = this.getFirestoreClient(databaseId);
            const actualCollectionId = this.getCollectionId(collectionId);
            let query: Query<DocumentData> = firestore.collection(actualCollectionId);

            if (whereClause) {
              query = query.where(whereClause.field, whereClause.operator, whereClause.value);
            }

            const snapshot = await query.limit(limit).get();
            const documents = snapshot.docs.map(doc => ({
              id: doc.id,
              data: doc.data()
            }));

            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(documents, null, 2)
                }
              ]
            };
          }

          case 'create_collection': {
            if (!isCreateCollectionArgs(request.params.arguments)) {
              throw new McpError(ErrorCode.InvalidParams, 'Invalid create_collection arguments');
            }

            const { databaseId, collectionId } = request.params.arguments;
            const firestore = this.getFirestoreClient(databaseId);
            const actualCollectionId = this.getCollectionId(collectionId);
            
            // In Firestore, collections are created implicitly when the first document is added
            // We'll add a temporary document and then delete it to "create" the collection
            const tempDoc = await firestore.collection(actualCollectionId).add({
              _temp: true,
              _createdAt: new Date().toISOString()
            });
            await tempDoc.delete();

            return {
              content: [
                {
                  type: 'text',
                  text: `Collection "${collectionId}" created successfully`
                }
              ]
            };
          }

          case 'add_document': {
            if (!isAddDocumentArgs(request.params.arguments)) {
              throw new McpError(ErrorCode.InvalidParams, 'Invalid add_document arguments');
            }

            const { databaseId, collectionId, documentData } = request.params.arguments;
            const firestore = this.getFirestoreClient(databaseId);
            const actualCollectionId = this.getCollectionId(collectionId);
            
            const docRef = await firestore.collection(actualCollectionId).add(documentData);

            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify({
                    id: docRef.id,
                    message: 'Document added successfully'
                  }, null, 2)
                }
              ]
            };
          }

          case 'get_document': {
            if (!isGetDocumentArgs(request.params.arguments)) {
              throw new McpError(ErrorCode.InvalidParams, 'Invalid get_document arguments');
            }

            const { databaseId, collectionId, documentId } = request.params.arguments;
            const firestore = this.getFirestoreClient(databaseId);
            const actualCollectionId = this.getCollectionId(collectionId);
            
            const doc = await firestore.collection(actualCollectionId).doc(documentId).get();
            
            if (!doc.exists) {
              throw new McpError(ErrorCode.InvalidRequest, `Document ${documentId} not found`);
            }

            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify({
                    id: doc.id,
                    data: doc.data()
                  }, null, 2)
                }
              ]
            };
          }

          case 'update_document': {
            if (!isUpdateDocumentArgs(request.params.arguments)) {
              throw new McpError(ErrorCode.InvalidParams, 'Invalid update_document arguments');
            }

            const { databaseId, collectionId, documentId, documentData } = request.params.arguments;
            const firestore = this.getFirestoreClient(databaseId);
            const actualCollectionId = this.getCollectionId(collectionId);
            
            const docRef = firestore.collection(actualCollectionId).doc(documentId);
            const doc = await docRef.get();
            
            if (!doc.exists) {
              throw new McpError(ErrorCode.InvalidRequest, `Document ${documentId} not found`);
            }

            await docRef.update(documentData);

            return {
              content: [
                {
                  type: 'text',
                  text: `Document ${documentId} updated successfully`
                }
              ]
            };
          }

          case 'delete_document': {
            if (!isDeleteDocumentArgs(request.params.arguments)) {
              throw new McpError(ErrorCode.InvalidParams, 'Invalid delete_document arguments');
            }

            const { databaseId, collectionId, documentId } = request.params.arguments;
            const firestore = this.getFirestoreClient(databaseId);
            const actualCollectionId = this.getCollectionId(collectionId);
            
            const docRef = firestore.collection(actualCollectionId).doc(documentId);
            const doc = await docRef.get();
            
            if (!doc.exists) {
              throw new McpError(ErrorCode.InvalidRequest, `Document ${documentId} not found`);
            }

            await docRef.delete();

            return {
              content: [
                {
                  type: 'text',
                  text: `Document ${documentId} deleted successfully`
                }
              ]
            };
          }
          default:
            throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${request.params.name}`);
        }
      } catch (error) {
        console.error('[MCP Error]', error);
        if (error instanceof McpError) {
          throw error;
        }
        throw new McpError(ErrorCode.InternalError, 'An unexpected error occurred');
      }
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Firestore MCP server running on stdio');
  }
}

const server = new FirestoreServer();
server.run().catch(console.error);
