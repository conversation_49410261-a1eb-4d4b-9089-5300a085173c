{"name": "@telus/mcp-github-pr", "version": "0.1.0", "description": "An MCP server that provides tools for managing GitHub Pull Requests", "keywords": ["telus", "mcp", "github-pr"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-github-pr"}, "license": "MIT", "author": "<PERSON> (@raphberube)", "type": "module", "main": "dist/index.js", "bin": {"mcp-github-pr": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "prepare": "pnpm run build", "start": "node dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "@octokit/rest": "^21.1.0", "dotenv": "~16.4.7", "node-fetch": "~3.3.2"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}