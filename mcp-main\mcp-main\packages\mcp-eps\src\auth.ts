import { AxiosError, AxiosInstance } from 'axios';
import type { EpsConfig, TokenCache, TokenResponse } from './types.js';

/**
 * KONG authentication handler for EPS APIs
 */
export class AuthHandler {
  private config: EpsConfig;
  private tokenCache: TokenCache | null = null;
  private tokenEndpoint: string;
  private axiosInstance: AxiosInstance;
  
  /**
   * Create a new AuthHandler instance
   * @param config EPS API configuration
   * @param axiosInstance Axios instance for making HTTP requests
   * @param baseUrl Base URL for the API
   */
  constructor(config: EpsConfig, axiosInstance: AxiosInstance, baseUrl: string) {
    this.config = config;
    const tokenPath = process.env.ACCESS_TOKEN_URL;
    if (!tokenPath) {
      throw new Error('ACCESS_TOKEN_URL environment variable is required');
    }
    this.tokenEndpoint = new URL(tokenPath, baseUrl).toString();
    this.axiosInstance = axiosInstance;
    console.error('[Auth] Initialized');
  }
  
  /**
   * Get a valid access token, refreshing if necessary
   * @returns Promise resolving to access token
   */
  async getAccessToken(): Promise<string> {
    console.error('[Auth] Starting getAccessToken');
    // Check if we have a cached token that's still valid
    if (this.tokenCache && this.tokenCache.expiresAt > Date.now()) {
      console.error('[Auth] Using cached token');
      return this.tokenCache.token;
    }
    
    console.error('[Auth] Requesting new access token');
    
    try {
      // Prepare token request
      const params = new URLSearchParams();
      params.append('grant_type', 'client_credentials');
      params.append('client_id', this.config.clientId);
      params.append('client_secret', this.config.clientSecret);
      params.append('scope',this.config.scope);
      
      console.error('[Auth] Token request params:', params.toString());
      console.error('[Auth] Token endpoint:', this.tokenEndpoint);
      
      // Make token request
      const response = await this.axiosInstance.post<TokenResponse>(
        this.tokenEndpoint,
        params.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );
      
      console.error('[Auth] Token response received');
      
      // Cache the token with expiration time (subtract 60 seconds for safety margin)
      const expiresAt = Date.now() + (response.data.expires_in - 60) * 1000;
      this.tokenCache = {
        token: response.data.access_token,
        expiresAt,
      };
      
      console.error('[Auth] Token acquired successfully');
      return this.tokenCache.token;
    } catch (error: unknown) {
      console.error('[Auth] Error acquiring token:', error);
      if (error instanceof AxiosError) {
        const axiosError = error as AxiosError<{ error?: string }>;
        console.error('[Auth] Axios error details:', {
          message: axiosError.message,
          code: axiosError.code,
          config: axiosError.config,
          response: axiosError.response,
        });
        console.error('[Auth] Request config:', axiosError.config);
        console.error('[Auth] Response data:', axiosError.response?.data);
        const errorMessage = axiosError.response?.data?.error || axiosError.message;
        throw new Error(`KONG authentication failed: ${errorMessage}`);
      }
      console.error('[Auth] Non-Axios error:', error);
      throw new Error(`KONG authentication failed: ${String(error)}`);
    }
  }
  
  /**
   * Clear the token cache
   */
  clearTokenCache(): void {
    this.tokenCache = null;
    console.error('[Auth] Token cache cleared');
  }
}
