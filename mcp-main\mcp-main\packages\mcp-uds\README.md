# @telus/mcp-uds

A Model Context Protocol (MCP) server that provides UDS component resources for development.

## Installation

### Prerequisites

Git installed and access to TELUS GitHub repositories.

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-uds
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for Claude Desktop):

   ```json
   {
     "mcpServers": {
       "uds": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-uds"]
       }
     }
   }
   ```

## Usage

This MCP server can be used with the Model Context Protocol (MCP) to provide additional capabilities to AI models.

To start the server:

```bash
pnpm start
```

## Development

To build the project:

```bash
pnpm build
```

To run tests:

```bash
pnpm test
```

## License

MIT © TELUS
