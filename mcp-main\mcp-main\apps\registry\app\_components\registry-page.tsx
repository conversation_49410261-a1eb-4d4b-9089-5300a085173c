"use client";

import { ServerList } from "@/components/server-list";
import { ServerSearch } from "@/components/server-search";
import { MCPServer } from "@/types/servers";
import { Suspense } from "react";

// The main component doesn't directly use useSearchParams anymore
export default function RegistryPage({
  initialServers,
}: {
  initialServers: MCPServer[];
}) {
  return (
    <div className="container mx-auto py-8 px-4">
      <header className="flex items-center justify-between gap-6 mb-8">
        <div className="flex flex-col gap-2">
          <h1 className="text-3xl text-purple-900 dark:text-white font-bold">
            MCP Servers Registry
          </h1>
          <p className="text-gray-600 dark:text-gray-400 text-lg">
            This is a registry of MCP servers that are available to use in your
            projects.
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Suspense fallback={<div>Loading search...</div>}>
            <ServerSearch />
          </Suspense>
        </div>
      </header>

      {/* Info about experimental MCP servers */}
      <div className="bg-orange-50 border-l-4 border-orange-500 text-orange-500 p-4 mb-8 rounded-md shadow-sm" role="alert">
        <p className="font-bold">⚠️ Experimental MCP Servers</p>
        <p>MCP servers with versions below 1.0.0 are considered experimental or beta. While you can safely experiment with these servers:</p>
        <ul className="list-disc list-inside mt-2 space-y-1">
          <li>They may have incomplete features or documentation</li>
          <li>APIs might change between minor versions</li>
          <li>Bugs or unexpected behavior may occur</li>
        </ul>
        <p className="mt-2 font-medium">
          We strongly encourage you to try these experimental servers, provide feedback and contribute to help improve them! Your testing and input are valuable for making these tools production-ready.
        </p>
      </div>

      {/* GitHub Packages Access Info */}
      <div className="bg-blue-50 border-l-4 border-blue-500 text-blue-700 p-4 mb-8 rounded-md shadow-sm" role="alert">
        <p>
          ℹ️ <span className="font-medium">Don&apos;t forget:</span> Configure GitHub access for TELUS packages before using MCP servers.{" "}
          <a href="https://github.com/telus/mcp?tab=readme-ov-file#1-access-telus-github-packages" 
             className="underline ml-1 font-medium" 
             target="_blank" 
             rel="noopener noreferrer">
            Learn how
          </a>
        </p>
      </div>

      <Suspense fallback={<div>Loading servers...</div>}>
        <ServerList initialServers={initialServers} />
      </Suspense>
    </div>
  );
}
