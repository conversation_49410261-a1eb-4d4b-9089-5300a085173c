# @telus/mcp-github-pr

An MCP server that provides tools for managing GitHub Pull Requests.

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-github-pr
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for Claude Desktop):

   ```json
   {
     "mcpServers": {
       "github-pr": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-github-pr"],
         "env": {
           "GITHUB_TOKEN": "your_github_token_here"
         }
       }
     }
   }
   ```

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-github-pr
   pnpm install
   ```

2. Create a file named `.env` in the project directory and add your GitHub Personal Access Token:

   ```sh
   GITHUB_TOKEN=your_github_token_here
   ```

3. Build the server:

   ```bash
   pnpm build
   ```

4. Start the server:

   ```bash
   pnpm start
   ```

   Example with custom .env path:

   ```bash
   DOTENV_PATH=/path/to/custom/.env pnpm start
   ```

## Configuration

The server requires the following environment variables:

- `GITHUB_TOKEN`: Your GitHub personal access token (required)
- `DOTENV_PATH`: Full path to your .env file (optional, defaults to .env in the current directory)

You can set these variables in your .env file or directly in your environment.

## Available Tools

[Add description of available tools and their usage]

## License

MIT © TELUS
