import { blazeMeterService } from "../services/blazemeter.js";
import type {
  GetWorkspaceArgs,
  GetWorkspaceTestsArgs,
  RunTestArgs,
  GetTestExecutionResultsSummaryArgs,
  GetTestDetailsArgs,
  GetTestExecutionStatusArgs,
  UpdateMultiTestArgs,
  StartMultiTestArgs,
  GetTestExecutionDetailsArgs,
  GetMultiTestDetailsArgs,
} from "../types/blazemeter.js";

export const blazeMeterTools = [
  {
    name: "get_workspace",
    description: "Get a BlazeMeter workspace",
    inputSchema: {
      type: "object",
      properties: {
        workspaceId: {
          type: "string",
          description: "ID of the workspace to retrieve",
        },
      },
      required: ["workspaceId"],
    },
  },
  {
    name: "update_multi_test",
    description: "Update configuration of a multi-test by id",
    inputSchema: {
      type: "object",
      properties: {
        collectionId: {
          type: "string",
          description: "ID of the multi-test to update",
        },
        index: {
          type: "integer",
          description: "Index of the test to update within the multi-test",
        },
        overrideExecutions: {
          type: "array",
          description: "Array of execution overrides",
          items: {
            type: "object",
            properties: {
              concurrency: { type: "integer" },
              holdFor: { type: "string" },
              rampUp: { type: "string" },
              steps: { type: "integer" },
              executor: { type: "string" },
              locations: { type: "object" },
              locationsPercents: { type: "object" },
            },
          },
        },
      },
      required: ["collectionId", "index", "overrideExecutions"],
    },
  },
  {
    name: "start_multi_test",
    description: "Start a multi-test",
    inputSchema: {
      type: "object",
      properties: {
        collectionId: {
          type: "string",
          description: "ID of the multi-test to start",
        },
        delayedStart: {
          type: "boolean",
          description: "Whether to use delayed start",
          default: false,
        },
      },
      required: ["collectionId"],
    },
  },
  {
    name: "get_test_details",
    description: "Get information about a specific BlazeMeter test",
    inputSchema: {
      type: "object",
      properties: {
        test_id: {
          type: "string",
          description: "ID of the test to retrieve information for",
        },
      },
      required: ["test_id"],
    },
  },
  {
    name: "get_workspace_tests",
    description: "Get tests for a BlazeMeter workspace",
    inputSchema: {
      type: "object",
      properties: {
        workspaceId: {
          type: "string",
          description: "ID of the workspace to retrieve tests for",
        },
        type: {
          type: "string",
          description: 'Type of tests to retrieve (e.g., "taurus")',
          default: "taurus",
        },
      },
      required: ["workspaceId"],
    },
  },
  {
    name: "run_performance_test",
    description: "Run a BlazeMeter performance test",
    inputSchema: {
      type: "object",
      properties: {
        test_id: {
          type: "string",
          description: "ID of the test to run",
        },
        virtual_users: {
          type: "number",
          description: "Number of virtual users for the test",
          minimum: 1,
        },
        duration: {
          type: "number",
          description: "Test duration in minutes",
          minimum: 1,
        },
      },
      required: ["test_id", "virtual_users", "duration"],
    },
  },
  {
    name: "get_test_execution_results_summary",
    description: "Get detailed summary results of a BlazeMeter test execution",
    inputSchema: {
      type: "object",
      properties: {
        test_execution_id: {
          type: "string",
          description: "ID of the test execution",
        },
        scenarios: {
          type: "array",
          items: {
            type: "string",
          },
          description:
            "Array of scenario IDs to filter results. If not provided, all scenarios will be included.",
        },
        filter: {
          type: "string",
          description:
            "Filter option for the results. Options:\n" +
            '- "ALL" (default): Provides a high-level summary for each scenario\n' +
            '- "FULL": Provides detailed results for each scenario, including per-request statistics\n' +
            "- [label name]: Filters results to include only requests with the specified label",
          default: "ALL",
        },
      },
      required: ["test_execution_id"],
    },
  },
  {
    name: "get_test_execution_status",
    description: "Get the current status of a test execution",
    inputSchema: {
      type: "object",
      properties: {
        test_execution_id: {
          type: "string",
          description: "ID of the test execution to check status for",
        },
      },
      required: ["test_execution_id"],
    },
  },
  {
    name: "get_test_execution_details",
    description:
      "Get detailed information about a test execution including scenarios and configuration",
    inputSchema: {
      type: "object",
      properties: {
        masterId: {
          type: "string",
          description:
            "ID of the test execution (master) to retrieve details for",
        },
      },
      required: ["masterId"],
    },
  },
  {
    name: "get_multi_test_details",
    description: "Get details of a specific multi-test",
    inputSchema: {
      type: "object",
      properties: {
        collectionId: {
          type: "string",
          description: "ID of the multi-test to retrieve details for",
        },
        populateTests: {
          type: "boolean",
          description: "Whether to include test details in the response",
          default: true,
        },
      },
      required: ["collectionId"],
    },
  },
];

export async function handleBlazeMeterTool(toolName: string, args: any) {
  switch (toolName) {
    case "get_workspace":
      return await blazeMeterService.getWorkspace(args as GetWorkspaceArgs);
    case "get_workspace_tests":
      return await blazeMeterService.getWorkspaceTests(
        args as GetWorkspaceTestsArgs,
      );
    case "run_performance_test":
      return await blazeMeterService.runPerformanceTest(args as RunTestArgs);
    case "get_test_execution_results_summary":
      return await blazeMeterService.getTestExecutionResultsSummary(
        args as GetTestExecutionResultsSummaryArgs,
      );
    case "get_test_details":
      return await blazeMeterService.getTestDetails(args as GetTestDetailsArgs);
    case "get_test_execution_status":
      return await blazeMeterService.getTestExecutionStatus(
        args as GetTestExecutionStatusArgs,
      );
    case "update_multi_test":
      return await blazeMeterService.updateMultiTest(
        args as UpdateMultiTestArgs,
      );
    case "start_multi_test":
      return await blazeMeterService.startMultiTest(args as StartMultiTestArgs);
    case "get_test_execution_details":
      return await blazeMeterService.getTestExecutionDetails(
        args as GetTestExecutionDetailsArgs,
      );
    case "get_multi_test_details":
      return await blazeMeterService.getMultiTestDetails(
        args as GetMultiTestDetailsArgs,
      );
    default:
      throw new Error(`Unknown tool: ${toolName}`);
  }
}
