import type { Config } from "tailwindcss";
import colors from "tailwindcss/colors";
import typography from "@tailwindcss/typography";

export default {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        gray: colors.neutral,
        purple: {
          DEFAULT: "#4b286d",
          "50": "#faf6fe",
          "100": "#f2ebfc",
          "200": "#e7dbf9",
          "300": "#d5bff3",
          "400": "#bc96ea",
          "500": "#a16ddf",
          "600": "#8b4ecf",
          "700": "#753bb5",
          "800": "#643594",
          "900": "#4b286d",
          "950": "#361556",
        },
        green: {
          DEFAULT: "#66cc00",
          "50": "#f5ffe4",
          "100": "#e8ffc5",
          "200": "#d1ff92",
          "300": "#b1ff53",
          "400": "#91fb20",
          "500": "#66cc00",
          "600": "#55b500",
          "700": "#418902",
          "800": "#366c08",
          "900": "#2f5b0c",
          "950": "#153300",
        },
      },
    },
  },
  plugins: [typography],
} satisfies Config;
