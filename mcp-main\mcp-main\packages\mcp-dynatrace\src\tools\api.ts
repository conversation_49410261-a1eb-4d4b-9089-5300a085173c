import { z } from 'zod';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import fetch from 'node-fetch';

export const dynatraceApiBase = process.env.DYNATRACE_API_BASE || '';
export const dynatraceApiToken = process.env.DYNATRACE_API_TOKEN || '';

if (!dynatraceApiBase || !dynatraceApiToken) {
    throw new Error('Missing required environment variables.');
}

export async function makeDynatraceRequest<T>(url: string): Promise<T | null> {
    const headers = {
        "Authorization": `Api-Token ${dynatraceApiToken}`,
        "Content-Type": "application/json",
    };

    try {
        const response = await fetch(url, { headers });
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Error response body:', errorText);
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
        const responseData = await response.json() as T;
        return responseData;
    } catch (error) {
        if (error instanceof Error) {
            console.error("Error making Dynatrace request:", {
                message: error.message,
                stack: error.stack,
                url: url
            });
        } else {
            console.error("Unknown error making Dynatrace request:", error);
        }
        return null;
    }
}

export function formatTimestamp(timestamp: number): string {
    if (timestamp === -1) return 'Ongoing';
    return new Date(timestamp).toLocaleString();
}

export async function getEntities(
    entitySelector?: string,
    from?: string,
    to?: string,
    fields?: string,
    sort?: string,
    pageSize?: number,
    nextPageKey?: string
) {
    let entitiesUrl = `${dynatraceApiBase}/api/v2/entities`;
    let params = [];
    
    if (entitySelector) {
        if (entitySelector.includes('entityId(') && !entitySelector.includes('entityId("')) {
            const modifiedSelector = entitySelector.replace(/entityId\(([^)]+)\)/, 'entityId("$1")');
            params.push(`entitySelector=${encodeURIComponent(modifiedSelector)}`);
        } else {
            params.push(`entitySelector=${encodeURIComponent(entitySelector)}`);
        }
    }
    if (from) params.push(`from=${encodeURIComponent(from)}`);
    if (to) params.push(`to=${encodeURIComponent(to)}`);
    if (fields) params.push(`fields=${encodeURIComponent(fields)}`);
    if (sort) params.push(`sort=${encodeURIComponent(sort)}`);
    if (pageSize !== undefined) params.push(`pageSize=${encodeURIComponent(pageSize.toString())}`);
    if (nextPageKey) params.push(`nextPageKey=${encodeURIComponent(nextPageKey)}`);
    
    if (params.length > 0) {
        entitiesUrl += `?${params.join('&')}`;
    }
    
    return await makeDynatraceRequest<{ entities: any[] }>(entitiesUrl);
}

export async function getProblems(
    problemSelector?: string,
    entitySelector?: string,
    from?: string,
    to?: string,
    fields?: string,
    sort?: string,
    pageSize?: number,
    nextPageKey?: string
) {
    let problemsUrl = `${dynatraceApiBase}/api/v2/problems`;
    let params = [];
    
    if (problemSelector) params.push(`problemSelector=${encodeURIComponent(problemSelector)}`);
    if (entitySelector) params.push(`entitySelector=${encodeURIComponent(entitySelector)}`);
    if (from) params.push(`from=${encodeURIComponent(from)}`);
    if (to) params.push(`to=${encodeURIComponent(to)}`);
    if (fields) params.push(`fields=${encodeURIComponent(fields)}`);
    if (sort) params.push(`sort=${encodeURIComponent(sort)}`);
    if (pageSize !== undefined) params.push(`pageSize=${encodeURIComponent(pageSize.toString())}`);
    if (nextPageKey) params.push(`nextPageKey=${encodeURIComponent(nextPageKey)}`);
    
    if (params.length > 0) {
        problemsUrl += `?${params.join('&')}`;
    }
    
    return await makeDynatraceRequest<{ problems: any[] }>(problemsUrl);
}

export async function getProblemById(problemId: string, fields?: string) {
    if (!problemId) {
        throw new Error('problemId is required for getProblemById');
    }

    let problemUrl = `${dynatraceApiBase}/api/v2/problems/${problemId}`;
    let params = [];
    
    if (fields) params.push(`fields=${encodeURIComponent(fields)}`);
    
    if (params.length > 0) {
        problemUrl += `?${params.join('&')}`;
    }
    
    return await makeDynatraceRequest<any>(problemUrl);
}

export async function getMetrics(
    metricSelector?: string,
    entitySelector?: string,
    from?: string,
    to?: string,
    nextPageKey?: string,
    pageSize?: number,
    resolution?: string
) {
    if (!metricSelector) {
        throw new Error('metricSelector is required for getMetrics');
    }

    let metricsUrl = `${dynatraceApiBase}/api/v2/metrics/query`;
    let params = new URLSearchParams();
    
    // Add parameters directly to URLSearchParams which handles encoding properly
    params.append('metricSelector', metricSelector);
    
    if (entitySelector) {
        // Pass the entitySelector as-is without modifying it
        params.append('entitySelector', entitySelector);
    }
    
    if (from) params.append('from', from);
    if (to) params.append('to', to);
    if (nextPageKey) params.append('nextPageKey', nextPageKey);
    if (pageSize !== undefined) params.append('pageSize', pageSize.toString());
    if (resolution) params.append('resolution', resolution);
    
    const url = `${metricsUrl}?${params.toString()}`;
    console.log(`Making request to: ${url}`);
    return await makeDynatraceRequest<any>(url);
}

export async function getEntityById(entityId: string, fields?: string, from?: string, to?: string) {
    if (!entityId) {
        throw new Error('entityId is required for getEntityById');
    }

    let entityUrl = `${dynatraceApiBase}/api/v2/entities/${entityId}`;
    let params = [];
    
    if (fields) params.push(`fields=${encodeURIComponent(fields)}`);
    if (from) params.push(`from=${encodeURIComponent(from)}`);
    if (to) params.push(`to=${encodeURIComponent(to)}`);
    
    if (params.length > 0) {
        entityUrl += `?${params.join('&')}`;
    }
    
    return await makeDynatraceRequest<any>(entityUrl);
}

export function formatEntityDetails(entityData: any): string {
    if (!entityData) {
        return "Failed to retrieve entity details";
    }

    let responseText = `Entity Details for ID ${entityData.entityId}:\n\n`;
    responseText += `Display Name: ${entityData.displayName}\n`;
    responseText += `Entity ID: ${entityData.entityId}\n`;
    responseText += `Type: ${entityData.type}\n`;
    responseText += `First Seen: ${formatTimestamp(entityData.firstSeenTms)}\n`;
    responseText += `Last Seen: ${formatTimestamp(entityData.lastSeenTms)}\n\n`;

    if (entityData.properties) {
        responseText += "Properties:\n";
        for (const [key, value] of Object.entries(entityData.properties)) {
            responseText += `  ${key}: ${value}\n`;
        }
        responseText += "\n";
    }

    if (entityData.tags && entityData.tags.length > 0) {
        responseText += "Tags:\n";
        entityData.tags.forEach((tag: any) => {
            responseText += `  - ${tag.key}: ${tag.value || ''} (${tag.context})\n`;
        });
        responseText += "\n";
    }

    if (entityData.managementZones && entityData.managementZones.length > 0) {
        responseText += "Management Zones:\n";
        entityData.managementZones.forEach((zone: any) => {
            responseText += `  - ${zone.name} (ID: ${zone.id})\n`;
        });
        responseText += "\n";
    }

    // Add fromRelationships section
    if (entityData.fromRelationships) {
        responseText += "From Relationships:\n";
        for (const [relationType, entities] of Object.entries(entityData.fromRelationships)) {
            responseText += `  ${relationType}:\n`;
            (entities as any[]).forEach((entity: any) => {
                responseText += `    - ${entity.id} (${entity.type})\n`;
            });
        }
        responseText += "\n";
    }

    // Add toRelationships section
    if (entityData.toRelationships) {
        responseText += "To Relationships:\n";
        for (const [relationType, entities] of Object.entries(entityData.toRelationships)) {
            responseText += `  ${relationType}:\n`;
            (entities as any[]).forEach((entity: any) => {
                responseText += `    - ${entity.id} (${entity.type})\n`;
            });
        }
        responseText += "\n";
    }

    return responseText;
}

export const getEntitiesApi = (server: McpServer) => {
    return server.tool(
        "get-entities",
        "Get entities based on various criteria",
        {
            entitySelector: z.string().optional().describe("Selects entities based on their properties"),
            from: z.string().optional().describe("Start time of the requested timeframe (ISO 8601 format)"),
            to: z.string().optional().describe("End time of the requested timeframe (ISO 8601 format)"),
            fields: z.string().optional().describe("Defines fields to be included in the response"),
            sort: z.string().optional().describe("Defines the ordering of the entities list"),
            pageSize: z.number().optional().describe("Specifies the pageSize of results to retrieve"),
            nextPageKey: z.string().optional().describe("Specifies next page key to retrieve")
        },
        async ({ entitySelector, from, to, fields, sort, pageSize, nextPageKey }) => {
            const entitiesData = await getEntities(entitySelector, from, to, fields, sort, pageSize, nextPageKey);
            
            if (!entitiesData) {
                return {
                    content: [{ type: "text", text: "Failed to retrieve entities data" }],
                };
            }

            let responseText = "Entities:\n\n";
            if (entitiesData.entities.length > 0) {
                entitiesData.entities.forEach(entity => {
                    responseText += `Entity ID: ${entity.entityId}\n`;
                    responseText += `Display Name: ${entity.displayName}\n`;
                    responseText += `Type: ${entity.type}\n`;
                    if (entity.properties) {
                        responseText += "Properties:\n";
                        for (const [key, value] of Object.entries(entity.properties)) {
                            responseText += `  ${key}: ${value}\n`;
                        }
                    }
                    responseText += "\n";
                });
            } else {
                responseText += "No entities found for the specified criteria.\n";
            }

            return {
                content: [{ type: "text", text: responseText }],
            };
        }
    );
};

export const getProblemsApi = (server: McpServer) => {
    return server.tool(
        "get-problems",
        "Get problems based on various criteria",
        {
            problemSelector: z.string().optional().describe("Selects problems based on their properties"),
            entitySelector: z.string().optional().describe("Selects entities based on their properties"),
            from: z.string().optional().describe("Start time of the requested timeframe (ISO 8601 format)"),
            to: z.string().optional().describe("End time of the requested timeframe (ISO 8601 format)"),
            fields: z.string().optional().describe("Defines fields to be included in the response"),
            sort: z.string().optional().describe("Defines the ordering of the problems list"),
            pageSize: z.number().optional().describe("Specifies the pageSize of results to retrieve"),
            nextPageKey: z.string().optional().describe("Specifies next page key to retrieve")
        },
        async ({ problemSelector, entitySelector, from, to, fields, sort, pageSize, nextPageKey }) => {
            const problemsData = await getProblems(problemSelector, entitySelector, from, to, fields, sort, pageSize, nextPageKey);
            
            if (!problemsData) {
                return {
                    content: [{ type: "text", text: "Failed to retrieve problems data" }],
                };
            }

            let responseText = "Problems:\n\n";
            if (problemsData.problems.length > 0) {
                problemsData.problems.forEach(problem => {
                    responseText += `Problem ID: ${problem.displayId}\n`;
                    responseText += `Title: ${problem.title}\n`;
                    responseText += `Impact Level: ${problem.impactLevel}\n`;
                    responseText += `Severity Level: ${problem.severityLevel}\n`;
                    responseText += `Duration: ${formatTimestamp(problem.startTime)} - ${formatTimestamp(problem.endTime)}\n`;
                    if (problem.affectedEntities) {
                        responseText += "Affected Entities:\n";
                        problem.affectedEntities.forEach((entity: any) => {
                            responseText += `  - ${entity.name} (${entity.entityId.id})\n`;
                        });
                    }
                    if (problem.impactedEntities) {
                        responseText += "Impacted Entities:\n";
                        problem.impactedEntities.forEach((entity: any) => {
                            responseText += `  - ${entity.name} (${entity.entityId.id})\n`;
                        });
                    }
                    if (problem.rootCauseEntity) {
                        responseText += "Root Cause:\n";
                        responseText += `  - ${problem.rootCauseEntity.name} (${problem.rootCauseEntity.entityId.id})\n`;
                    }
                    responseText += "\n";
                });
            } else {
                responseText += "No problems found for the specified criteria.\n";
            }

            return {
                content: [{ type: "text", text: responseText }],
            };
        }
    );
};

export const getMetricsApi = (server: McpServer) => {
    return server.tool(
        "get-metrics",
        "Get metrics based on various criteria",
        {
            metricSelector: z.string().describe("Selects metrics for the query. Example: builtin:host.cpu.idle"),
            entitySelector: z.string().optional().describe("Selects entities for the query"),
            from: z.string().optional().describe("Start time of the requested timeframe (ISO 8601 format)"),
            to: z.string().optional().describe("End time of the requested timeframe (ISO 8601 format)"),
            nextPageKey: z.string().optional().describe("The cursor for the next page of results"),
            pageSize: z.number().optional().describe("The number of results per result page"),
            resolution: z.string().optional().default("5m").describe("Resolution of the metrics data (e.g., 10m, 1h). Valid units: m,h,d,w,M,q,y")
        },
        async ({ metricSelector, entitySelector, from, to, nextPageKey, pageSize, resolution }) => {
            const metricsData = await getMetrics(metricSelector, entitySelector, from, to, nextPageKey, pageSize, resolution);
            
            if (!metricsData) {
                return {
                    content: [{ type: "text", text: "Failed to retrieve metrics data" }],
                };
            }

            let responseText = "Metrics:\n\n";
            if (metricsData.result && metricsData.result.length > 0) {
                metricsData.result.forEach((metric: { metricId: string; data?: Array<{ values?: number[], dimensions?: string[] }> }) => {
                    responseText += `Metric ID: ${metric.metricId}\n`;
                    if (metric.data && metric.data.length > 0) {
                        metric.data.forEach((dataPoint: { values?: number[], dimensions?: string[] }) => {
                            if (dataPoint.dimensions && dataPoint.dimensions.length > 0) {
                                responseText += `  Entity: ${dataPoint.dimensions[0]}\n`;
                            }
                            if (dataPoint.values && dataPoint.values.length > 0) {
                                responseText += `  Latest Value: ${dataPoint.values[dataPoint.values.length - 1]}\n`;
                                responseText += `  Average Value: ${calculateAverage(dataPoint.values).toFixed(2)}\n`;
                            }
                        });
                    }
                    responseText += "\n";
                });
            } else {
                responseText += "No metrics found for the specified criteria.\n";
            }

            return {
                content: [{ type: "text", text: responseText }],
            };
        }
    );
};

// Helper function to calculate average of an array of numbers
export function calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    const sum = values.reduce((a, b) => a + b, 0);
    return sum / values.length;
}

export const getEntityByIdApi = (server: McpServer) => {
    return server.tool(
        "get-entity-by-id",
        "Get details of an entity by its ID",
        {
            entityId: z.string().describe("Entity ID to query"),
            from: z.string().optional().describe("Start time of the requested timeframe (ISO 8601 format)"),
            to: z.string().optional().describe("End time of the requested timeframe (ISO 8601 format)"),
            fields: z.string().optional().describe("Defines fields to be included in the response")
        },
        async ({ entityId, fields, from, to }) => {
            const entityData = await getEntityById(entityId, fields, from, to);
            const result = formatEntityDetails(entityData);
            return {
                content: [{ type: "text", text: result }],
            };
        }
    );
};

export const getProblemByIdApi = (server: McpServer) => {
    return server.tool(
        "get-problem-by-id",
        "Get details of a problem by its ID",
        {
            problemId: z.string().describe("The ID of the required problem"),
            fields: z.string().optional().describe("A list of additional problem properties to add to the response")
        },
        async ({ problemId, fields }) => {
            const problemData = await getProblemById(problemId, fields);
            
            if (!problemData) {
                return {
                    content: [{ type: "text", text: "Failed to retrieve problem data" }],
                };
            }

            let responseText = `Problem Details for ID ${problemData.problemId}:\n\n`;
            responseText += `Display ID: ${problemData.displayId}\n`;
            responseText += `Title: ${problemData.title}\n`;
            responseText += `Status: ${problemData.status}\n`;
            responseText += `Severity Level: ${problemData.severityLevel}\n`;
            responseText += `Impact Level: ${problemData.impactLevel}\n`;
            responseText += `Start Time: ${formatTimestamp(problemData.startTime)}\n`;
            responseText += `End Time: ${formatTimestamp(problemData.endTime)}\n\n`;

            if (problemData.affectedEntities && problemData.affectedEntities.length > 0) {
                responseText += "Affected Entities:\n";
                problemData.affectedEntities.forEach((entity: any) => {
                    responseText += `  - ${entity.name} (${entity.entityId.id})\n`;
                });
                responseText += "\n";
            }

            if (problemData.impactedEntities && problemData.impactedEntities.length > 0) {
                responseText += "Impacted Entities:\n";
                problemData.impactedEntities.forEach((entity: any) => {
                    responseText += `  - ${entity.name} (${entity.entityId.id})\n`;
                });
                responseText += "\n";
            }

            if (problemData.rootCauseEntity) {
                responseText += "Root Cause Entity:\n";
                responseText += `  - ${problemData.rootCauseEntity.name} (${problemData.rootCauseEntity.entityId.id})\n\n`;
            }

            if (problemData.evidenceDetails && problemData.evidenceDetails.details) {
                responseText += "Evidence Details:\n";
                problemData.evidenceDetails.details.forEach((evidence: any) => {
                    responseText += `  - ${evidence.displayName}\n`;
                    responseText += `    Type: ${evidence.evidenceType}\n`;
                    responseText += `    Start Time: ${formatTimestamp(evidence.startTime)}\n`;
                    responseText += `    Root Cause Relevant: ${evidence.rootCauseRelevant}\n\n`;
                });
            }

            if (problemData.recentComments && problemData.recentComments.comments) {
                responseText += "Recent Comments:\n";
                problemData.recentComments.comments.forEach((comment: any) => {
                    responseText += `  - ${comment.content}\n`;
                    responseText += `    Author: ${comment.authorName}\n`;
                    responseText += `    Created At: ${formatTimestamp(comment.createdAtTimestamp)}\n\n`;
                });
            }

            return {
                content: [{ type: "text", text: responseText }],
            };
        }
    );
};
