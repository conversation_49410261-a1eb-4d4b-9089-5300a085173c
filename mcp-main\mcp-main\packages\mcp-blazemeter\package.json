{"name": "@telus/mcp-blazemeter", "version": "0.1.0", "description": "MCP server focus on running and gathering blazemeter test data", "keywords": ["telus", "mcp", "blazemeter"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-blazemeter"}, "license": "MIT", "author": "<PERSON> (@szchow89)", "type": "module", "main": "dist/index.js", "bin": {"mcp-blazemeter": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "prepare": "pnpm run build", "start": "node dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "axios": "^1.8.4", "dotenv": "~16.4.7"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}