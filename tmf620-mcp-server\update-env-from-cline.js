#!/usr/bin/env node
/**
 * TMF620 Environment Update Script
 * 
 * This script updates the .env file with the OAuth client ID and client secret
 * from the Cline MCP settings file.
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const dotenv = require('dotenv');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// Define paths
const clineConfigDir = path.join(os.homedir(), 'AppData', 'Roaming', 'Code', 'User', 'globalStorage', 'saoudrizwan.claude-dev', 'settings');
const clineConfigFile = path.join(clineConfigDir, 'cline_mcp_settings.json');
const envFilePath = path.join(__dirname, '.env');
const envBackupPath = path.join(__dirname, '.env.backup');

console.log(`${colors.blue}TMF620 Environment Update Script${colors.reset}`);
console.log(`${colors.blue}==============================${colors.reset}`);

// Check if the Cline config file exists
if (!fs.existsSync(clineConfigFile)) {
  console.error(`${colors.red}Error: Cline config file not found at ${clineConfigFile}${colors.reset}`);
  console.error(`${colors.red}Please make sure Cline is installed and has been run at least once.${colors.reset}`);
  process.exit(1);
}

// Check if the .env file exists
if (!fs.existsSync(envFilePath)) {
  console.error(`${colors.red}Error: .env file not found at ${envFilePath}${colors.reset}`);
  process.exit(1);
}

// Load Cline config
let clineConfig;
try {
  const configData = fs.readFileSync(clineConfigFile, 'utf8');
  clineConfig = JSON.parse(configData);
  if (!clineConfig.mcpServers || !clineConfig.mcpServers.tmf620) {
    console.error(`${colors.red}Error: TMF620 server configuration not found in Cline config file${colors.reset}`);
    console.error(`${colors.red}Please run update-cline-mcp-settings.js first.${colors.reset}`);
    process.exit(1);
  }
} catch (error) {
  console.error(`${colors.red}Error reading Cline config file: ${error.message}${colors.reset}`);
  process.exit(1);
}

// Get the OAuth client ID and client secret from the TMF620 server configuration
const clientId = clineConfig.mcpServers.tmf620.env.OAUTH_CLIENT_ID;
const clientSecret = clineConfig.mcpServers.tmf620.env.OAUTH_CLIENT_SECRET;

if (!clientId || !clientSecret) {
  console.error(`${colors.red}Error: OAuth client ID or client secret not found in TMF620 server configuration${colors.reset}`);
  process.exit(1);
}

// Create a backup of the .env file
try {
  fs.copyFileSync(envFilePath, envBackupPath);
  console.log(`${colors.green}Created backup of .env file at ${envBackupPath}${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Error creating backup of .env file: ${error.message}${colors.reset}`);
  process.exit(1);
}

// Read the .env file
let envContent;
try {
  envContent = fs.readFileSync(envFilePath, 'utf8');
  console.log(`${colors.green}Successfully read .env file${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Error reading .env file: ${error.message}${colors.reset}`);
  process.exit(1);
}

// Parse the .env file
const env = dotenv.parse(envContent);

// Update the OAuth client ID and client secret
env.OAUTH_CLIENT_ID = clientId;
env.OAUTH_CLIENT_SECRET = clientSecret;

// Add OAuth token URL if it doesn't exist
if (!env.OAUTH_TOKEN_URL) {
  env.OAUTH_TOKEN_URL = 'https://apigw-st.telus.com/st/token';
  console.log(`${colors.green}Added OAuth token URL${colors.reset}`);
}

// Add OAuth scope if it doesn't exist
if (!env.OAUTH_SCOPE) {
  env.OAUTH_SCOPE = '4823';
  console.log(`${colors.green}Added OAuth scope${colors.reset}`);
}

// Generate the new .env file content
let newEnvContent = '';
for (const key in env) {
  newEnvContent += `${key}=${env[key]}\n`;
}

// Write the updated .env file
try {
  fs.writeFileSync(envFilePath, newEnvContent);
  console.log(`${colors.green}Successfully updated .env file${colors.reset}`);
  
  // Log the changes made
  console.log(`${colors.green}✓ Updated OAuth client ID${colors.reset}`);
  console.log(`${colors.green}✓ Updated OAuth client secret${colors.reset}`);
  
  console.log(`\n${colors.blue}Updated .env file:${colors.reset}`);
  const envToDisplay = { ...env };
  envToDisplay.OAUTH_CLIENT_ID = '[REDACTED]';
  envToDisplay.OAUTH_CLIENT_SECRET = '[REDACTED]';
  for (const key in envToDisplay) {
    console.log(`${colors.blue}${key}=${envToDisplay[key]}${colors.reset}`);
  }
  
  console.log(`\n${colors.green}TMF620 environment update complete!${colors.reset}`);
  console.log(`${colors.green}You can now run test-api-connection.js to test the API connection.${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Error writing .env file: ${error.message}${colors.reset}`);
  console.error(`${colors.red}Restoring backup...${colors.reset}`);
  
  try {
    fs.copyFileSync(envBackupPath, envFilePath);
    console.log(`${colors.green}Successfully restored backup of .env file${colors.reset}`);
  } catch (restoreError) {
    console.error(`${colors.red}Error restoring backup of .env file: ${restoreError.message}${colors.reset}`);
    console.error(`${colors.red}Backup file is available at ${envBackupPath}${colors.reset}`);
  }
  
  process.exit(1);
}
