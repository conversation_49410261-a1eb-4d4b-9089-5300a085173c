import { NiFiClient } from '../nifi-client.js';
import { z } from 'zod';

type ToolRequest = {
  method: string;
  params?: {
    arguments?: Record<string, unknown>;
  };
};

type ToolResponse = {
  content: Array<{ type: string; text: string }>;
  isError?: boolean;
};

const toolSchema = z.object({
  method: z.string(),
  params: z.object({
    arguments: z.record(z.unknown())
  }).optional()
});

export const tools = {
  get_system_diagnostics: {
    description: 'Retrieve system diagnostics information',
    inputSchema: {
      type: 'object',
      properties: {
        nodeId: {
          type: 'string',
          description: 'Optional: Specific node ID for cluster deployments'
        },
        includeDetails: {
          type: 'boolean',
          description: 'Include detailed metrics',
          default: false
        }
      }
    }
  },
  get_controller_config: {
    description: 'Get controller configuration',
    inputSchema: {
      type: 'object',
      properties: {}
    }
  },
  get_jmx_metrics: {
    description: 'Retrieve available JMX metrics',
    inputSchema: {
      type: 'object',
      properties: {
        beanNameFilter: {
          type: 'string',
          description: 'Regular Expression Pattern to be applied against the ObjectName'
        }
      }
    }
  }
};

export const getSystemDiagnostics = (client: NiFiClient) => async (request: ToolRequest): Promise<ToolResponse> => {
  const { nodeId, includeDetails = false } = (request.params?.arguments || {}) as { nodeId?: string; includeDetails?: boolean };
  try {
    const url = `/system-diagnostics`;
    const response = await client.get(url);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2)
        }
      ]
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      content: [
        {
          type: 'text',
          text: `Error retrieving system diagnostics: ${errorMessage}`
        }
      ],
      isError: true
    };
  }
};

export const getControllerConfig = (client: NiFiClient) => async (request: ToolRequest): Promise<ToolResponse> => {
  try {
    const response = await client.get('/controller/config');
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2)
        }
      ]
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      content: [
        {
          type: 'text',
          text: `Error retrieving controller configuration: ${errorMessage}`
        }
      ],
      isError: true
    };
  }
};

export const getJmxMetrics = (client: NiFiClient) => async (request: ToolRequest): Promise<ToolResponse> => {
  const { beanNameFilter } = (request.params?.arguments || {}) as { beanNameFilter?: string };
  try {
    const url = `/system-diagnostics/jmx-metrics${beanNameFilter ? `?beanNameFilter=${encodeURIComponent(beanNameFilter)}` : ''}`;
    const response = await client.get(url);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2)
        }
      ]
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return {
      content: [
        {
          type: 'text',
          text: `Error retrieving JMX metrics: ${errorMessage}`
        }
      ],
      isError: true
    };
  }
};
