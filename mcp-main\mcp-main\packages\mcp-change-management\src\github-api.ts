import { Octokit } from '@octokit/rest';

export class GitHubAPI {
  private octokit: Octokit;

  constructor() {
    const token = process.env.GITHUB_TOKEN;
    if (!token || !token.startsWith('github_pat_')) {
      throw new Error('Invalid GITHUB_TOKEN. Please provide a valid personal access token starting with "github_pat_"');
    }
    this.octokit = new Octokit({ auth: token });
  }

  async getChanges(startDate: Date, endDate: Date): Promise<any[]> {
    try {
      const issues = await this.fetchAllIssues(startDate, endDate);
      
      return issues.map(issue => ({
        date: issue.created_at.split('T')[0],
        time: issue.created_at.split('T')[1].split('Z')[0],
        changeNumber: issue.number,
        description: issue.title,
        githubId: issue.user?.login || 'Unknown',
      }));
    } catch (error: any) {
      // Instead of throwing, we'll log the error and return an empty array
      console.error('Error fetching GitHub changes:', error);
      return [];
    }
  }

  private async fetchAllIssues(startDate: Date, endDate: Date) {
    const owner = process.env.GITHUB_OWNER;
    const repo = process.env.GITHUB_REPO;
    
    if (!owner || !repo) {
      throw new Error('GITHUB_OWNER and GITHUB_REPO environment variables are required');
    }

    const allIssues = [];
    let page = 1;
    
    while (true) {
      const response = await this.octokit.request('GET /repos/{owner}/{repo}/issues', {
        owner,
        repo,
        state: 'all',
        sort: 'created',
        direction: 'desc',
        per_page: 100,
        page,
        headers: {
          'X-GitHub-Api-Version': '2022-11-28'
        }
      });

      const issues = response.data;
      
      // No more issues to fetch
      if (issues.length === 0) break;

      // Filter issues within date range
      const relevantIssues = issues.filter(issue => {
        const createdAt = new Date(issue.created_at);
        return createdAt >= startDate && createdAt <= endDate;
      });

      allIssues.push(...relevantIssues);

      // If we got less than the requested amount, we've reached the end
      if (issues.length < 100) break;

      // If the last issue in this batch is before our start date, we can stop
      const lastIssueDate = new Date(issues[issues.length - 1].created_at);
      if (lastIssueDate < startDate) break;

      page++;
    }

    return allIssues;
  }

  private handleError(error: any) {
    if (error.status === 401) {
      throw new Error('Invalid GitHub token. Please check your Personal Access Token.');
    }
    if (error.status === 403) {
      if (error.response?.headers?.['x-ratelimit-remaining'] === '0') {
        const resetTime = new Date(Number(error.response.headers['x-ratelimit-reset']) * 1000);
        throw new Error(`Rate limit exceeded. Resets at ${resetTime.toLocaleString()}`);
      }
      throw new Error('Insufficient permissions. Token requires appropriate scopes.');
    }
    if (error.status === 404) {
      throw new Error('Repository or resource not found. Please check the owner and repo names.');
    }
    throw new Error(`GitHub API error: ${error.message}`);
  }
}
