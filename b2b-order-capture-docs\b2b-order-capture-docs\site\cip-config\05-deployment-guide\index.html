
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/cip-config/05-deployment-guide/">
      
      
        <link rel="prev" href="../04-service-catalog/">
      
      
        <link rel="next" href="../06-development-guide/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Deployment Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#deployment-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Deployment Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" checked>
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#deployment-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Deployment Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#multi-environment-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Multi-Environment Strategy
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#build-process" class="md-nav__link">
    <span class="md-ellipsis">
      Build Process
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Build Process">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-build-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Build Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Build Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-artifacts" class="md-nav__link">
    <span class="md-ellipsis">
      Build Artifacts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Environment Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#environment-specific-deployment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment-Specific Deployment Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-variables" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Variables
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#kubernetes-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Kubernetes Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Kubernetes Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#helm-chart-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Helm Chart Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-template" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Template
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#cicd-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      CI/CD Pipeline
    </span>
  </a>
  
    <nav class="md-nav" aria-label="CI/CD Pipeline">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#pipeline-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Pipeline Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#configuration-package-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Package Deployment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-script" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Script
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#blue-green-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Blue-Green Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Blue-Green Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#blue-green-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Blue-Green Strategy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#blue-green-deployment-script" class="md-nav__link">
    <span class="md-ellipsis">
      Blue-Green Deployment Script
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#monitoring-and-health-checks" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring and Health Checks
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Monitoring and Health Checks">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#health-check-endpoints" class="md-nav__link">
    <span class="md-ellipsis">
      Health Check Endpoints
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#monitoring-dashboard" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring Dashboard
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#rollback-procedures" class="md-nav__link">
    <span class="md-ellipsis">
      Rollback Procedures
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Rollback Procedures">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#automated-rollback" class="md-nav__link">
    <span class="md-ellipsis">
      Automated Rollback
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#rollback-triggers" class="md-nav__link">
    <span class="md-ellipsis">
      Rollback Triggers
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#deployment-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Deployment Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-pre-deployment-validation" class="md-nav__link">
    <span class="md-ellipsis">
      1. Pre-Deployment Validation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-deployment-safety" class="md-nav__link">
    <span class="md-ellipsis">
      2. Deployment Safety
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-monitoring-and-observability" class="md-nav__link">
    <span class="md-ellipsis">
      3. Monitoring and Observability
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#4-security" class="md-nav__link">
    <span class="md-ellipsis">
      4. Security
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#deployment-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Deployment Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#multi-environment-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Multi-Environment Strategy
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#build-process" class="md-nav__link">
    <span class="md-ellipsis">
      Build Process
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Build Process">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-build-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Build Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Build Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-artifacts" class="md-nav__link">
    <span class="md-ellipsis">
      Build Artifacts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Environment Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#environment-specific-deployment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment-Specific Deployment Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-variables" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Variables
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#kubernetes-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Kubernetes Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Kubernetes Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#helm-chart-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Helm Chart Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-template" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Template
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#cicd-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      CI/CD Pipeline
    </span>
  </a>
  
    <nav class="md-nav" aria-label="CI/CD Pipeline">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#pipeline-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Pipeline Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#configuration-package-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Package Deployment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-script" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Script
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#blue-green-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Blue-Green Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Blue-Green Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#blue-green-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Blue-Green Strategy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#blue-green-deployment-script" class="md-nav__link">
    <span class="md-ellipsis">
      Blue-Green Deployment Script
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#monitoring-and-health-checks" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring and Health Checks
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Monitoring and Health Checks">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#health-check-endpoints" class="md-nav__link">
    <span class="md-ellipsis">
      Health Check Endpoints
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#monitoring-dashboard" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring Dashboard
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#rollback-procedures" class="md-nav__link">
    <span class="md-ellipsis">
      Rollback Procedures
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Rollback Procedures">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#automated-rollback" class="md-nav__link">
    <span class="md-ellipsis">
      Automated Rollback
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#rollback-triggers" class="md-nav__link">
    <span class="md-ellipsis">
      Rollback Triggers
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#deployment-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Deployment Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#1-pre-deployment-validation" class="md-nav__link">
    <span class="md-ellipsis">
      1. Pre-Deployment Validation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#2-deployment-safety" class="md-nav__link">
    <span class="md-ellipsis">
      2. Deployment Safety
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#3-monitoring-and-observability" class="md-nav__link">
    <span class="md-ellipsis">
      3. Monitoring and Observability
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#4-security" class="md-nav__link">
    <span class="md-ellipsis">
      4. Security
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="deployment-guide">Deployment Guide<a class="headerlink" href="#deployment-guide" title="Permanent link">&para;</a></h1>
<h2 id="overview">Overview<a class="headerlink" href="#overview" title="Permanent link">&para;</a></h2>
<p>This guide covers the deployment procedures for the CIP Configuration Package across different environments, including build processes, Kubernetes deployment, configuration management, and monitoring setup.</p>
<h2 id="deployment-architecture">Deployment Architecture<a class="headerlink" href="#deployment-architecture" title="Permanent link">&para;</a></h2>
<h3 id="multi-environment-strategy">Multi-Environment Strategy<a class="headerlink" href="#multi-environment-strategy" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Source Control&quot;
        Git[Git Repository]
        Branches[Feature Branches]
        Main[Main Branch]
        Tags[Release Tags]
    end

    subgraph &quot;CI/CD Pipeline&quot;
        Build[Build Stage]
        Test[Test Stage]
        Package[Package Stage]
        Deploy[Deploy Stage]
    end

    subgraph &quot;Environments&quot;
        Dev[Development]
        QA[QA Environment]
        Staging[Staging Environment]
        Prod[Production Environment]
    end

    subgraph &quot;Deployment Tools&quot;
        Maven[Maven Build]
        Docker[Docker Images]
        Helm[Helm Charts]
        K8s[Kubernetes]
    end

    Git --&gt; Build
    Branches --&gt; Build
    Main --&gt; Test
    Tags --&gt; Package

    Build --&gt; Test
    Test --&gt; Package
    Package --&gt; Deploy

    Deploy --&gt; Dev
    Deploy --&gt; QA
    Deploy --&gt; Staging
    Deploy --&gt; Prod

    Maven --&gt; Docker
    Docker --&gt; Helm
    Helm --&gt; K8s
</code></pre></div>
<h2 id="build-process">Build Process<a class="headerlink" href="#build-process" title="Permanent link">&para;</a></h2>
<h3 id="maven-build-configuration">Maven Build Configuration<a class="headerlink" href="#maven-build-configuration" title="Permanent link">&para;</a></h3>
<p><augment_code_snippet path="nc-cloud-bss-cip-config-b2b/pom.xml" mode="EXCERPT">
<div class="highlight"><pre><span></span><code><span class="nt">&lt;project&gt;</span>
<span class="w">    </span><span class="nt">&lt;modelVersion&gt;</span>4.0.0<span class="nt">&lt;/modelVersion&gt;</span>
<span class="w">    </span><span class="nt">&lt;groupId&gt;</span>com.telus.bss.cip<span class="nt">&lt;/groupId&gt;</span>
<span class="w">    </span><span class="nt">&lt;artifactId&gt;</span>nc-cloud-bss-cip-config-b2b<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">    </span><span class="nt">&lt;version&gt;</span>1.0.0-SNAPSHOT<span class="nt">&lt;/version&gt;</span>
<span class="w">    </span><span class="nt">&lt;packaging&gt;</span>jar<span class="nt">&lt;/packaging&gt;</span>

<span class="w">    </span><span class="nt">&lt;properties&gt;</span>
<span class="w">        </span><span class="nt">&lt;maven.compiler.source&gt;</span>17<span class="nt">&lt;/maven.compiler.source&gt;</span>
<span class="w">        </span><span class="nt">&lt;maven.compiler.target&gt;</span>17<span class="nt">&lt;/maven.compiler.target&gt;</span>
<span class="w">        </span><span class="nt">&lt;groovy.version&gt;</span>3.0.10<span class="nt">&lt;/groovy.version&gt;</span>
<span class="w">        </span><span class="nt">&lt;spock.version&gt;</span>2.1-groovy-3.0<span class="nt">&lt;/spock.version&gt;</span>
<span class="w">    </span><span class="nt">&lt;/properties&gt;</span>

<span class="w">    </span><span class="nt">&lt;build&gt;</span>
<span class="w">        </span><span class="nt">&lt;plugins&gt;</span>
<span class="w">            </span><span class="nt">&lt;plugin&gt;</span>
<span class="w">                </span><span class="nt">&lt;groupId&gt;</span>org.apache.maven.plugins<span class="nt">&lt;/groupId&gt;</span>
<span class="w">                </span><span class="nt">&lt;artifactId&gt;</span>maven-assembly-plugin<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">                </span><span class="nt">&lt;version&gt;</span>3.3.0<span class="nt">&lt;/version&gt;</span>
<span class="w">                </span><span class="nt">&lt;configuration&gt;</span>
<span class="w">                    </span><span class="nt">&lt;descriptors&gt;</span>
<span class="w">                        </span><span class="nt">&lt;descriptor&gt;</span>descriptors/config-package.xml<span class="nt">&lt;/descriptor&gt;</span>
<span class="w">                    </span><span class="nt">&lt;/descriptors&gt;</span>
<span class="w">                </span><span class="nt">&lt;/configuration&gt;</span>
<span class="w">            </span><span class="nt">&lt;/plugin&gt;</span>
<span class="w">        </span><span class="nt">&lt;/plugins&gt;</span>
<span class="w">    </span><span class="nt">&lt;/build&gt;</span>
<span class="nt">&lt;/project&gt;</span>
</code></pre></div>
</augment_code_snippet></p>
<h3 id="build-commands">Build Commands<a class="headerlink" href="#build-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Clean and compile</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile

<span class="c1"># Run tests</span>
mvn<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Package configuration</span>
mvn<span class="w"> </span>package

<span class="c1"># Install to local repository</span>
mvn<span class="w"> </span>install

<span class="c1"># Deploy to remote repository</span>
mvn<span class="w"> </span>deploy
</code></pre></div>
<h3 id="build-artifacts">Build Artifacts<a class="headerlink" href="#build-artifacts" title="Permanent link">&para;</a></h3>
<p>The build process generates several artifacts:</p>
<div class="highlight"><pre><span></span><code>target/
├── nc-cloud-bss-cip-config-b2b-1.0.0.jar          # Main JAR
├── nc-cloud-bss-cip-config-b2b-1.0.0-config.zip   # Configuration package
├── nc-cloud-bss-cip-config-b2b-1.0.0-sources.jar  # Source code
└── nc-cloud-bss-cip-config-b2b-1.0.0-tests.jar    # Test classes
</code></pre></div>
<h2 id="environment-configuration">Environment Configuration<a class="headerlink" href="#environment-configuration" title="Permanent link">&para;</a></h2>
<h3 id="environment-specific-deployment-configuration">Environment-Specific Deployment Configuration<a class="headerlink" href="#environment-specific-deployment-configuration" title="Permanent link">&para;</a></h3>
<p><augment_code_snippet path="nc-cloud-bss-cip-config-b2b/deployments/deployment-configuration.json" mode="EXCERPT">
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;environments&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;qa&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;namespace&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;cip-config-qa&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;replicas&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;resources&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;requests&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;cpu&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;100m&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;memory&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;256Mi&quot;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;limits&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;cpu&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;500m&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;memory&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;512Mi&quot;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;ingress&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;host&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;cip-config-qa.telus.com&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;tls&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">    </span><span class="nt">&quot;production&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;namespace&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;cip-config-prod&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;replicas&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;resources&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;requests&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;cpu&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;200m&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;memory&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;512Mi&quot;</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">        </span><span class="nt">&quot;limits&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nt">&quot;cpu&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;1000m&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nt">&quot;memory&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;1Gi&quot;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;ingress&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nt">&quot;enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;host&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;cip-config.telus.com&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nt">&quot;tls&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
</augment_code_snippet></p>
<h3 id="environment-variables">Environment Variables<a class="headerlink" href="#environment-variables" title="Permanent link">&para;</a></h3>
<p>Each environment requires specific configuration variables:</p>
<div class="highlight"><pre><span></span><code><span class="c1"># QA Environment Variables</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">v1</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ConfigMap</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cip-config-qa-variables</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cip-config-qa</span>
<span class="nt">data</span><span class="p">:</span>
<span class="w">  </span><span class="nt">ENVIRONMENT</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;qa&quot;</span>
<span class="w">  </span><span class="nt">LOG_LEVEL</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;DEBUG&quot;</span>
<span class="w">  </span><span class="nt">CIP_PLATFORM_URL</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://cip-qa.telus.com&quot;</span>
<span class="w">  </span><span class="nt">SFDC_ENDPOINT</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://telus--qa.sandbox.my.salesforce.com&quot;</span>
<span class="w">  </span><span class="nt">TMF_BASE_URL</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://tmf-api-qa.telus.com&quot;</span>

<span class="nn">---</span>
<span class="c1"># Production Environment Variables</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">v1</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ConfigMap</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cip-config-prod-variables</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">cip-config-prod</span>
<span class="nt">data</span><span class="p">:</span>
<span class="w">  </span><span class="nt">ENVIRONMENT</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;production&quot;</span>
<span class="w">  </span><span class="nt">LOG_LEVEL</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;INFO&quot;</span>
<span class="w">  </span><span class="nt">CIP_PLATFORM_URL</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://cip.telus.com&quot;</span>
<span class="w">  </span><span class="nt">SFDC_ENDPOINT</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://telus.my.salesforce.com&quot;</span>
<span class="w">  </span><span class="nt">TMF_BASE_URL</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;https://tmf-api.telus.com&quot;</span>
</code></pre></div>
<h2 id="kubernetes-deployment">Kubernetes Deployment<a class="headerlink" href="#kubernetes-deployment" title="Permanent link">&para;</a></h2>
<h3 id="helm-chart-structure">Helm Chart Structure<a class="headerlink" href="#helm-chart-structure" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>deployments/charts/cip-config/
├── Chart.yaml                    # Chart metadata
├── values.yaml                   # Default values
├── values-qa.yaml               # QA environment values
├── values-staging.yaml          # Staging environment values
├── values-production.yaml       # Production environment values
├── templates/
│   ├── deployment.yaml          # Kubernetes deployment
│   ├── service.yaml             # Kubernetes service
│   ├── ingress.yaml             # Ingress configuration
│   ├── configmap.yaml           # Configuration maps
│   ├── secret.yaml              # Secrets
│   └── hpa.yaml                 # Horizontal Pod Autoscaler
└── charts/                      # Dependency charts
</code></pre></div>
<h3 id="deployment-template">Deployment Template<a class="headerlink" href="#deployment-template" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># templates/deployment.yaml</span>
<span class="nt">apiVersion</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">apps/v1</span>
<span class="nt">kind</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deployment</span>
<span class="nt">metadata</span><span class="p">:</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">include &quot;cip-config.fullname&quot; .</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">namespace</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.namespace</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">labels</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">{{</span><span class="nv">- include &quot;cip-config.labels&quot; . | nindent 4</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="nt">spec</span><span class="p">:</span>
<span class="w">  </span><span class="nt">replicas</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.replicaCount</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">selector</span><span class="p">:</span>
<span class="w">    </span><span class="nt">matchLabels</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">{{</span><span class="nv">- include &quot;cip-config.selectorLabels&quot; . | nindent 6</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">  </span><span class="nt">template</span><span class="p">:</span>
<span class="w">    </span><span class="nt">metadata</span><span class="p">:</span>
<span class="w">      </span><span class="nt">labels</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">{{</span><span class="nv">- include &quot;cip-config.selectorLabels&quot; . | nindent 8</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">    </span><span class="nt">spec</span><span class="p">:</span>
<span class="w">      </span><span class="nt">containers</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Chart.Name</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">        </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;{{</span><span class="nv"> </span><span class="s">.Values.image.repository</span><span class="nv"> </span><span class="s">}}:{{</span><span class="nv"> </span><span class="s">.Values.image.tag</span><span class="nv"> </span><span class="s">}}&quot;</span>
<span class="w">        </span><span class="nt">imagePullPolicy</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.image.pullPolicy</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">        </span><span class="nt">ports</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">http</span>
<span class="w">          </span><span class="nt">containerPort</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8080</span>
<span class="w">          </span><span class="nt">protocol</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">TCP</span>
<span class="w">        </span><span class="nt">env</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ENVIRONMENT</span>
<span class="w">          </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.environment</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">LOG_LEVEL</span>
<span class="w">          </span><span class="nt">value</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">.Values.logLevel</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">        </span><span class="nt">envFrom</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">configMapRef</span><span class="p">:</span>
<span class="w">            </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">include &quot;cip-config.fullname&quot; .</span><span class="w"> </span><span class="p p-Indicator">}}</span><span class="l l-Scalar l-Scalar-Plain">-config</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">secretRef</span><span class="p">:</span>
<span class="w">            </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">{{</span><span class="w"> </span><span class="nv">include &quot;cip-config.fullname&quot; .</span><span class="w"> </span><span class="p p-Indicator">}}</span><span class="l l-Scalar l-Scalar-Plain">-secrets</span>
<span class="w">        </span><span class="nt">resources</span><span class="p">:</span>
<span class="w">          </span><span class="p p-Indicator">{{</span><span class="nv">- toYaml .Values.resources | nindent 12</span><span class="w"> </span><span class="p p-Indicator">}}</span>
<span class="w">        </span><span class="nt">livenessProbe</span><span class="p">:</span>
<span class="w">          </span><span class="nt">httpGet</span><span class="p">:</span>
<span class="w">            </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/health</span>
<span class="w">            </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">http</span>
<span class="w">          </span><span class="nt">initialDelaySeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30</span>
<span class="w">          </span><span class="nt">periodSeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">        </span><span class="nt">readinessProbe</span><span class="p">:</span>
<span class="w">          </span><span class="nt">httpGet</span><span class="p">:</span>
<span class="w">            </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/ready</span>
<span class="w">            </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">http</span>
<span class="w">          </span><span class="nt">initialDelaySeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">          </span><span class="nt">periodSeconds</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
</code></pre></div>
<h3 id="deployment-commands">Deployment Commands<a class="headerlink" href="#deployment-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Install Helm chart for QA environment</span>
helm<span class="w"> </span>install<span class="w"> </span>cip-config-qa<span class="w"> </span>./deployments/charts/cip-config<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-f<span class="w"> </span>./deployments/charts/cip-config/values-qa.yaml<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--namespace<span class="w"> </span>cip-config-qa<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--create-namespace

<span class="c1"># Upgrade existing deployment</span>
helm<span class="w"> </span>upgrade<span class="w"> </span>cip-config-qa<span class="w"> </span>./deployments/charts/cip-config<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-f<span class="w"> </span>./deployments/charts/cip-config/values-qa.yaml<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--namespace<span class="w"> </span>cip-config-qa

<span class="c1"># Rollback to previous version</span>
helm<span class="w"> </span>rollback<span class="w"> </span>cip-config-qa<span class="w"> </span><span class="m">1</span><span class="w"> </span>--namespace<span class="w"> </span>cip-config-qa

<span class="c1"># Uninstall deployment</span>
helm<span class="w"> </span>uninstall<span class="w"> </span>cip-config-qa<span class="w"> </span>--namespace<span class="w"> </span>cip-config-qa
</code></pre></div>
<h2 id="cicd-pipeline">CI/CD Pipeline<a class="headerlink" href="#cicd-pipeline" title="Permanent link">&para;</a></h2>
<h3 id="pipeline-configuration">Pipeline Configuration<a class="headerlink" href="#pipeline-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># .github/workflows/deploy.yml</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy CIP Configuration</span>

<span class="nt">on</span><span class="p">:</span>
<span class="w">  </span><span class="nt">push</span><span class="p">:</span>
<span class="w">    </span><span class="nt">branches</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">main</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">develop</span><span class="p p-Indicator">]</span>
<span class="w">    </span><span class="nt">tags</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&#39;v*&#39;</span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">pull_request</span><span class="p">:</span>
<span class="w">    </span><span class="nt">branches</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">main</span><span class="p p-Indicator">]</span>

<span class="nt">jobs</span><span class="p">:</span>
<span class="w">  </span><span class="nt">build</span><span class="p">:</span>
<span class="w">    </span><span class="nt">runs-on</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ubuntu-latest</span>
<span class="w">    </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/checkout@v3</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Set up JDK 17</span>
<span class="w">      </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/setup-java@v3</span>
<span class="w">      </span><span class="nt">with</span><span class="p">:</span>
<span class="w">        </span><span class="nt">java-version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;17&#39;</span>
<span class="w">        </span><span class="nt">distribution</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;temurin&#39;</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Cache Maven dependencies</span>
<span class="w">      </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/cache@v3</span>
<span class="w">      </span><span class="nt">with</span><span class="p">:</span>
<span class="w">        </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">~/.m2</span>
<span class="w">        </span><span class="nt">key</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${{ runner.os }}-m2-${{ hashFiles(&#39;**/pom.xml&#39;) }}</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Run tests</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mvn clean test</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Build package</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mvn clean package</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Build Docker image</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">        </span><span class="no">docker build -t telus/cip-config:${{ github.sha }} .</span>
<span class="w">        </span><span class="no">docker tag telus/cip-config:${{ github.sha }} telus/cip-config:latest</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Push to registry</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">        </span><span class="no">echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin</span>
<span class="w">        </span><span class="no">docker push telus/cip-config:${{ github.sha }}</span>
<span class="w">        </span><span class="no">docker push telus/cip-config:latest</span>

<span class="w">  </span><span class="nt">deploy-qa</span><span class="p">:</span>
<span class="w">    </span><span class="nt">needs</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">build</span>
<span class="w">    </span><span class="nt">runs-on</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ubuntu-latest</span>
<span class="w">    </span><span class="nt">if</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">github.ref == &#39;refs/heads/develop&#39;</span>
<span class="w">    </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/checkout@v3</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy to QA</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">        </span><span class="no">helm upgrade --install cip-config-qa ./deployments/charts/cip-config \</span>
<span class="w">          </span><span class="no">-f ./deployments/charts/cip-config/values-qa.yaml \</span>
<span class="w">          </span><span class="no">--set image.tag=${{ github.sha }} \</span>
<span class="w">          </span><span class="no">--namespace cip-config-qa</span>

<span class="w">  </span><span class="nt">deploy-production</span><span class="p">:</span>
<span class="w">    </span><span class="nt">needs</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">build</span>
<span class="w">    </span><span class="nt">runs-on</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ubuntu-latest</span>
<span class="w">    </span><span class="nt">if</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">startsWith(github.ref, &#39;refs/tags/v&#39;)</span>
<span class="w">    </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/checkout@v3</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Deploy to Production</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">        </span><span class="no">helm upgrade --install cip-config-prod ./deployments/charts/cip-config \</span>
<span class="w">          </span><span class="no">-f ./deployments/charts/cip-config/values-production.yaml \</span>
<span class="w">          </span><span class="no">--set image.tag=${{ github.sha }} \</span>
<span class="w">          </span><span class="no">--namespace cip-config-prod</span>
</code></pre></div>
<h3 id="deployment-flow">Deployment Flow<a class="headerlink" href="#deployment-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Dev as Developer
    participant Git as Git Repository
    participant CI as CI/CD Pipeline
    participant Registry as Container Registry
    participant K8s as Kubernetes Cluster
    participant Monitor as Monitoring

    Dev-&gt;&gt;Git: Push code changes
    Git-&gt;&gt;CI: Trigger pipeline
    CI-&gt;&gt;CI: Run tests
    CI-&gt;&gt;CI: Build package
    CI-&gt;&gt;Registry: Push container image
    CI-&gt;&gt;K8s: Deploy to environment
    K8s-&gt;&gt;K8s: Rolling update
    K8s-&gt;&gt;Monitor: Health check
    Monitor-&gt;&gt;CI: Deployment status
    CI-&gt;&gt;Dev: Deployment notification
</code></pre></div>
<h2 id="configuration-deployment">Configuration Deployment<a class="headerlink" href="#configuration-deployment" title="Permanent link">&para;</a></h2>
<h3 id="configuration-package-deployment">Configuration Package Deployment<a class="headerlink" href="#configuration-package-deployment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Configuration Deployment Process&quot;
        Package[Configuration Package]
        Validate[Validate Configuration]
        Deploy[Deploy to CIP Platform]
        Import[Import Services/Chains]
        Verify[Verify Deployment]
    end

    subgraph &quot;Configuration Components&quot;
        Variables[Variables]
        Services[Services]
        Chains[Chains]
        TLS[TLS Certificates]
    end

    Package --&gt; Validate
    Validate --&gt; Deploy
    Deploy --&gt; Import
    Import --&gt; Verify

    Package --&gt; Variables
    Package --&gt; Services
    Package --&gt; Chains
    Package --&gt; TLS
</code></pre></div>
<h3 id="deployment-script">Deployment Script<a class="headerlink" href="#deployment-script" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="ch">#!/bin/bash</span>
<span class="c1"># deploy-config.sh</span>

<span class="nb">set</span><span class="w"> </span>-e

<span class="nv">ENVIRONMENT</span><span class="o">=</span><span class="si">${</span><span class="nv">1</span><span class="k">:-</span><span class="nv">qa</span><span class="si">}</span>
<span class="nv">CONFIG_PACKAGE</span><span class="o">=</span><span class="si">${</span><span class="nv">2</span><span class="k">:-</span><span class="nv">target</span><span class="p">/nc-cloud-bss-cip-config-b2b-1.0.0-config.zip</span><span class="si">}</span>
<span class="nv">CIP_PLATFORM_URL</span><span class="o">=</span><span class="si">${</span><span class="nv">3</span><span class="k">:-</span><span class="nv">https</span><span class="p">://cip-</span><span class="si">${</span><span class="nv">ENVIRONMENT</span><span class="si">}</span><span class="p">.telus.com</span><span class="si">}</span>

<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Deploying configuration package to </span><span class="si">${</span><span class="nv">ENVIRONMENT</span><span class="si">}</span><span class="s2"> environment...&quot;</span>

<span class="c1"># Validate configuration package</span>
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Validating configuration package...&quot;</span>
unzip<span class="w"> </span>-t<span class="w"> </span><span class="si">${</span><span class="nv">CONFIG_PACKAGE</span><span class="si">}</span>

<span class="c1"># Upload configuration package</span>
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Uploading configuration package...&quot;</span>
curl<span class="w"> </span>-X<span class="w"> </span>POST<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="si">${</span><span class="nv">CIP_AUTH_TOKEN</span><span class="si">}</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Content-Type: application/zip&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--data-binary<span class="w"> </span>@<span class="si">${</span><span class="nv">CONFIG_PACKAGE</span><span class="si">}</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span><span class="si">${</span><span class="nv">CIP_PLATFORM_URL</span><span class="si">}</span>/api/v1/configuration/deploy

<span class="c1"># Wait for deployment completion</span>
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Waiting for deployment completion...&quot;</span>
sleep<span class="w"> </span><span class="m">30</span>

<span class="c1"># Verify deployment</span>
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Verifying deployment...&quot;</span>
curl<span class="w"> </span>-X<span class="w"> </span>GET<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-H<span class="w"> </span><span class="s2">&quot;Authorization: Bearer </span><span class="si">${</span><span class="nv">CIP_AUTH_TOKEN</span><span class="si">}</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span><span class="si">${</span><span class="nv">CIP_PLATFORM_URL</span><span class="si">}</span>/api/v1/configuration/status

<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Configuration deployment completed successfully!&quot;</span>
</code></pre></div>
<h2 id="blue-green-deployment">Blue-Green Deployment<a class="headerlink" href="#blue-green-deployment" title="Permanent link">&para;</a></h2>
<h3 id="blue-green-strategy">Blue-Green Strategy<a class="headerlink" href="#blue-green-strategy" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Blue-Green Deployment&quot;
        LB[Load Balancer]
        Blue[Blue Environment]
        Green[Green Environment]
        Switch[Traffic Switch]
    end

    subgraph &quot;Deployment Process&quot;
        Deploy[Deploy to Green]
        Test[Test Green Environment]
        Validate[Validate Green Environment]
        Cutover[Switch Traffic to Green]
        Monitor[Monitor Green Environment]
        Cleanup[Cleanup Blue Environment]
    end

    LB --&gt; Blue
    LB --&gt; Green
    LB --&gt; Switch

    Deploy --&gt; Test
    Test --&gt; Validate
    Validate --&gt; Cutover
    Cutover --&gt; Monitor
    Monitor --&gt; Cleanup
</code></pre></div>
<h3 id="blue-green-deployment-script">Blue-Green Deployment Script<a class="headerlink" href="#blue-green-deployment-script" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="ch">#!/bin/bash</span>
<span class="c1"># blue-green-deploy.sh</span>

<span class="nv">ENVIRONMENT</span><span class="o">=</span><span class="si">${</span><span class="nv">1</span><span class="k">:-</span><span class="nv">production</span><span class="si">}</span>
<span class="nv">NEW_VERSION</span><span class="o">=</span><span class="si">${</span><span class="nv">2</span><span class="si">}</span>
<span class="nv">NAMESPACE</span><span class="o">=</span><span class="s2">&quot;cip-config-</span><span class="si">${</span><span class="nv">ENVIRONMENT</span><span class="si">}</span><span class="s2">&quot;</span>

<span class="c1"># Determine current and new environments</span>
<span class="nv">CURRENT_ENV</span><span class="o">=</span><span class="k">$(</span>kubectl<span class="w"> </span>get<span class="w"> </span>service<span class="w"> </span>cip-config-active<span class="w"> </span>-n<span class="w"> </span><span class="si">${</span><span class="nv">NAMESPACE</span><span class="si">}</span><span class="w"> </span>-o<span class="w"> </span><span class="nv">jsonpath</span><span class="o">=</span><span class="s1">&#39;{.spec.selector.version}&#39;</span><span class="k">)</span>
<span class="k">if</span><span class="w"> </span><span class="o">[</span><span class="w"> </span><span class="s2">&quot;</span><span class="si">${</span><span class="nv">CURRENT_ENV</span><span class="si">}</span><span class="s2">&quot;</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="s2">&quot;blue&quot;</span><span class="w"> </span><span class="o">]</span><span class="p">;</span><span class="w"> </span><span class="k">then</span>
<span class="w">    </span><span class="nv">NEW_ENV</span><span class="o">=</span><span class="s2">&quot;green&quot;</span>
<span class="k">else</span>
<span class="w">    </span><span class="nv">NEW_ENV</span><span class="o">=</span><span class="s2">&quot;blue&quot;</span>
<span class="k">fi</span>

<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Current environment: </span><span class="si">${</span><span class="nv">CURRENT_ENV</span><span class="si">}</span><span class="s2">&quot;</span>
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Deploying to: </span><span class="si">${</span><span class="nv">NEW_ENV</span><span class="si">}</span><span class="s2">&quot;</span>

<span class="c1"># Deploy to new environment</span>
helm<span class="w"> </span>upgrade<span class="w"> </span>--install<span class="w"> </span>cip-config-<span class="si">${</span><span class="nv">NEW_ENV</span><span class="si">}</span><span class="w"> </span>./deployments/charts/cip-config<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-f<span class="w"> </span>./deployments/charts/cip-config/values-<span class="si">${</span><span class="nv">ENVIRONMENT</span><span class="si">}</span>.yaml<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--set<span class="w"> </span>image.tag<span class="o">=</span><span class="si">${</span><span class="nv">NEW_VERSION</span><span class="si">}</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--set<span class="w"> </span>environment.color<span class="o">=</span><span class="si">${</span><span class="nv">NEW_ENV</span><span class="si">}</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--namespace<span class="w"> </span><span class="si">${</span><span class="nv">NAMESPACE</span><span class="si">}</span>

<span class="c1"># Wait for deployment to be ready</span>
kubectl<span class="w"> </span><span class="nb">wait</span><span class="w"> </span>--for<span class="o">=</span><span class="nv">condition</span><span class="o">=</span>available<span class="w"> </span>--timeout<span class="o">=</span>300s<span class="w"> </span>deployment/cip-config-<span class="si">${</span><span class="nv">NEW_ENV</span><span class="si">}</span><span class="w"> </span>-n<span class="w"> </span><span class="si">${</span><span class="nv">NAMESPACE</span><span class="si">}</span>

<span class="c1"># Run health checks</span>
<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Running health checks...&quot;</span>
kubectl<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-n<span class="w"> </span><span class="si">${</span><span class="nv">NAMESPACE</span><span class="si">}</span><span class="w"> </span>deployment/cip-config-<span class="si">${</span><span class="nv">NEW_ENV</span><span class="si">}</span><span class="w"> </span>--<span class="w"> </span>curl<span class="w"> </span>-f<span class="w"> </span>http://localhost:8080/health

<span class="c1"># Switch traffic to new environment</span>
kubectl<span class="w"> </span>patch<span class="w"> </span>service<span class="w"> </span>cip-config-active<span class="w"> </span>-n<span class="w"> </span><span class="si">${</span><span class="nv">NAMESPACE</span><span class="si">}</span><span class="w"> </span>-p<span class="w"> </span><span class="s1">&#39;{&quot;spec&quot;:{&quot;selector&quot;:{&quot;version&quot;:&quot;&#39;</span><span class="si">${</span><span class="nv">NEW_ENV</span><span class="si">}</span><span class="s1">&#39;&quot;}}}&#39;</span>

<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Traffic switched to </span><span class="si">${</span><span class="nv">NEW_ENV</span><span class="si">}</span><span class="s2"> environment&quot;</span>

<span class="c1"># Monitor new environment</span>
sleep<span class="w"> </span><span class="m">60</span>

<span class="c1"># Cleanup old environment</span>
helm<span class="w"> </span>uninstall<span class="w"> </span>cip-config-<span class="si">${</span><span class="nv">CURRENT_ENV</span><span class="si">}</span><span class="w"> </span>-n<span class="w"> </span><span class="si">${</span><span class="nv">NAMESPACE</span><span class="si">}</span>

<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Blue-green deployment completed successfully!&quot;</span>
</code></pre></div>
<h2 id="monitoring-and-health-checks">Monitoring and Health Checks<a class="headerlink" href="#monitoring-and-health-checks" title="Permanent link">&para;</a></h2>
<h3 id="health-check-endpoints">Health Check Endpoints<a class="headerlink" href="#health-check-endpoints" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Health check configuration</span>
<span class="nt">health</span><span class="p">:</span>
<span class="w">  </span><span class="nt">endpoints</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;liveness&quot;</span>
<span class="w">      </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/health&quot;</span>
<span class="w">      </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8080</span>
<span class="w">      </span><span class="nt">checks</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;database-connection&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;cip-platform-connectivity&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;memory-usage&quot;</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;readiness&quot;</span>
<span class="w">      </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/ready&quot;</span>
<span class="w">      </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8080</span>
<span class="w">      </span><span class="nt">checks</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;configuration-loaded&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;services-imported&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;chains-imported&quot;</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;startup&quot;</span>
<span class="w">      </span><span class="nt">path</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/startup&quot;</span>
<span class="w">      </span><span class="nt">port</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8080</span>
<span class="w">      </span><span class="nt">checks</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;application-started&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;dependencies-initialized&quot;</span>
</code></pre></div>
<h3 id="monitoring-dashboard">Monitoring Dashboard<a class="headerlink" href="#monitoring-dashboard" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Monitoring Stack&quot;
        Prometheus[Prometheus]
        Grafana[Grafana]
        AlertManager[Alert Manager]
        ELK[ELK Stack]
    end

    subgraph &quot;Metrics Sources&quot;
        App[Application Metrics]
        K8s[Kubernetes Metrics]
        Infra[Infrastructure Metrics]
        Business[Business Metrics]
    end

    subgraph &quot;Alerting&quot;
        Email[Email Alerts]
        Slack[Slack Notifications]
        PagerDuty[PagerDuty]
        SMS[SMS Alerts]
    end

    App --&gt; Prometheus
    K8s --&gt; Prometheus
    Infra --&gt; Prometheus
    Business --&gt; Prometheus

    Prometheus --&gt; Grafana
    Prometheus --&gt; AlertManager
    App --&gt; ELK

    AlertManager --&gt; Email
    AlertManager --&gt; Slack
    AlertManager --&gt; PagerDuty
    AlertManager --&gt; SMS
</code></pre></div>
<h2 id="rollback-procedures">Rollback Procedures<a class="headerlink" href="#rollback-procedures" title="Permanent link">&para;</a></h2>
<h3 id="automated-rollback">Automated Rollback<a class="headerlink" href="#automated-rollback" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="ch">#!/bin/bash</span>
<span class="c1"># rollback.sh</span>

<span class="nv">ENVIRONMENT</span><span class="o">=</span><span class="si">${</span><span class="nv">1</span><span class="k">:-</span><span class="nv">qa</span><span class="si">}</span>
<span class="nv">NAMESPACE</span><span class="o">=</span><span class="s2">&quot;cip-config-</span><span class="si">${</span><span class="nv">ENVIRONMENT</span><span class="si">}</span><span class="s2">&quot;</span>
<span class="nv">REVISION</span><span class="o">=</span><span class="si">${</span><span class="nv">2</span><span class="k">:-</span><span class="nv">1</span><span class="si">}</span>

<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Rolling back to revision </span><span class="si">${</span><span class="nv">REVISION</span><span class="si">}</span><span class="s2"> in </span><span class="si">${</span><span class="nv">ENVIRONMENT</span><span class="si">}</span><span class="s2"> environment...&quot;</span>

<span class="c1"># Rollback Helm deployment</span>
helm<span class="w"> </span>rollback<span class="w"> </span>cip-config-<span class="si">${</span><span class="nv">ENVIRONMENT</span><span class="si">}</span><span class="w"> </span><span class="si">${</span><span class="nv">REVISION</span><span class="si">}</span><span class="w"> </span>--namespace<span class="w"> </span><span class="si">${</span><span class="nv">NAMESPACE</span><span class="si">}</span>

<span class="c1"># Wait for rollback completion</span>
kubectl<span class="w"> </span>rollout<span class="w"> </span>status<span class="w"> </span>deployment/cip-config-<span class="si">${</span><span class="nv">ENVIRONMENT</span><span class="si">}</span><span class="w"> </span>-n<span class="w"> </span><span class="si">${</span><span class="nv">NAMESPACE</span><span class="si">}</span>

<span class="c1"># Verify rollback</span>
kubectl<span class="w"> </span>get<span class="w"> </span>pods<span class="w"> </span>-n<span class="w"> </span><span class="si">${</span><span class="nv">NAMESPACE</span><span class="si">}</span><span class="w"> </span>-l<span class="w"> </span><span class="nv">app</span><span class="o">=</span>cip-config

<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;Rollback completed successfully!&quot;</span>
</code></pre></div>
<h3 id="rollback-triggers">Rollback Triggers<a class="headerlink" href="#rollback-triggers" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Health Check Failures</strong>: Automatic rollback on consecutive health check failures</li>
<li><strong>Error Rate Threshold</strong>: Rollback when error rate exceeds threshold</li>
<li><strong>Performance Degradation</strong>: Rollback on significant performance degradation</li>
<li><strong>Manual Trigger</strong>: Manual rollback initiated by operations team</li>
</ul>
<h2 id="deployment-best-practices">Deployment Best Practices<a class="headerlink" href="#deployment-best-practices" title="Permanent link">&para;</a></h2>
<h3 id="1-pre-deployment-validation">1. Pre-Deployment Validation<a class="headerlink" href="#1-pre-deployment-validation" title="Permanent link">&para;</a></h3>
<ul>
<li>Configuration syntax validation</li>
<li>Schema compliance checking</li>
<li>Dependency verification</li>
<li>Security scanning</li>
</ul>
<h3 id="2-deployment-safety">2. Deployment Safety<a class="headerlink" href="#2-deployment-safety" title="Permanent link">&para;</a></h3>
<ul>
<li>Blue-green deployments for zero downtime</li>
<li>Canary deployments for gradual rollout</li>
<li>Automated rollback on failure</li>
<li>Comprehensive health checks</li>
</ul>
<h3 id="3-monitoring-and-observability">3. Monitoring and Observability<a class="headerlink" href="#3-monitoring-and-observability" title="Permanent link">&para;</a></h3>
<ul>
<li>Real-time deployment monitoring</li>
<li>Performance metrics tracking</li>
<li>Error rate monitoring</li>
<li>Business metrics validation</li>
</ul>
<h3 id="4-security">4. Security<a class="headerlink" href="#4-security" title="Permanent link">&para;</a></h3>
<ul>
<li>Secure credential management</li>
<li>Network policy enforcement</li>
<li>Container image scanning</li>
<li>Runtime security monitoring</li>
</ul>
<p>This deployment guide ensures reliable, secure, and efficient deployment of the CIP Configuration Package across all TELUS B2B environments.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>