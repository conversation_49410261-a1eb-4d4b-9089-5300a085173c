
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/extensions/05-development-guide/">
      
      
        <link rel="prev" href="../04-state-validators/">
      
      
        <link rel="next" href="../../qss-extension/00-overview/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Development Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#extensions-development-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Development Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" checked>
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#qes-development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      QES Development Environment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#ide-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      IDE Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="IDE Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#intellij-idea-setup" class="md-nav__link">
    <span class="md-ellipsis">
      IntelliJ IDEA Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-development-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Development Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#plugin-development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Development Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Plugin Development Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-process" class="md-nav__link">
    <span class="md-ellipsis">
      Development Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-component-creation" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Component Creation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-updates" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Updates
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategy
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Strategy">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-testing-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Testing Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#build-and-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Build and Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Build and Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-build-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Build Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Build Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-process" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#docker-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Docker Deployment
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#debugging-and-troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging and Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Debugging and Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#debug-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Debug Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#common-issues-and-solutions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues and Solutions
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Common Issues and Solutions">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-loading-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Loading Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#runtime-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Runtime Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#code-quality-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Code Quality Guidelines
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Optimization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Best Practices
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#development-environment-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Development Environment Setup
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Development Environment Setup">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#qes-development-environment" class="md-nav__link">
    <span class="md-ellipsis">
      QES Development Environment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#ide-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      IDE Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="IDE Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#intellij-idea-setup" class="md-nav__link">
    <span class="md-ellipsis">
      IntelliJ IDEA Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-development-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Development Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#plugin-development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Development Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Plugin Development Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development-process" class="md-nav__link">
    <span class="md-ellipsis">
      Development Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-component-creation" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Component Creation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-updates" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration Updates
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#testing-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Strategy
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Testing Strategy">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#unit-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Unit Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-testing" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Testing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#plugin-testing-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Testing Commands
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#build-and-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Build and Deployment
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Build and Deployment">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maven-build-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Maven Build Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#build-commands" class="md-nav__link">
    <span class="md-ellipsis">
      Build Commands
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#deployment-process" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Process
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#docker-deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Docker Deployment
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#debugging-and-troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging and Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Debugging and Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#debug-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Debug Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#common-issues-and-solutions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues and Solutions
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Common Issues and Solutions">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-loading-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Loading Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#runtime-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Runtime Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#code-quality-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Code Quality Guidelines
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-optimization" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Optimization
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#testing-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Testing Best Practices
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="extensions-development-guide">Extensions Development Guide<a class="headerlink" href="#extensions-development-guide" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#development-environment-setup">Development Environment Setup</a></li>
<li><a href="#plugin-development-workflow">Plugin Development Workflow</a></li>
<li><a href="#testing-strategy">Testing Strategy</a></li>
<li><a href="#build-and-deployment">Build and Deployment</a></li>
<li><a href="#debugging-and-troubleshooting">Debugging and Troubleshooting</a></li>
<li><a href="#best-practices">Best Practices</a></li>
</ul>
<h2 id="development-environment-setup">Development Environment Setup<a class="headerlink" href="#development-environment-setup" title="Permanent link">&para;</a></h2>
<h3 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Required Software Versions</span>
Java:<span class="w"> </span>OpenJDK<span class="w"> </span><span class="m">17</span><span class="w"> </span>or<span class="w"> </span>higher
Maven:<span class="w"> </span><span class="m">3</span>.8.x<span class="w"> </span>or<span class="w"> </span>higher
QES<span class="w"> </span>Plugin<span class="w"> </span>Framework:<span class="w"> </span><span class="m">2023</span>.3.2.0
Order<span class="w"> </span>Capture<span class="w"> </span>Product:<span class="w"> </span><span class="m">2023</span>.3.2.63
Git:<span class="w"> </span><span class="m">2</span>.x<span class="w"> </span>or<span class="w"> </span>higher
IDE:<span class="w"> </span>IntelliJ<span class="w"> </span>IDEA<span class="w"> </span>or<span class="w"> </span>Eclipse<span class="w"> </span><span class="o">(</span>recommended<span class="o">)</span>
</code></pre></div>
<h3 id="qes-development-environment">QES Development Environment<a class="headerlink" href="#qes-development-environment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Clone the extensions repository</span>
git<span class="w"> </span>clone<span class="w"> </span>&lt;repository-url&gt;
<span class="nb">cd</span><span class="w"> </span>nc-cloud-bss-oc-ui-be-extension-b2b

<span class="c1"># 2. Verify Java and Maven versions</span>
java<span class="w"> </span>-version
mvn<span class="w"> </span>-version

<span class="c1"># 3. Install QES Plugin Framework dependencies</span>
mvn<span class="w"> </span>dependency:resolve

<span class="c1"># 4. Build the plugin</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile

<span class="c1"># 5. Run unit tests</span>
mvn<span class="w"> </span><span class="nb">test</span>

<span class="c1"># 6. Package the plugin</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package
</code></pre></div>
<h3 id="ide-configuration">IDE Configuration<a class="headerlink" href="#ide-configuration" title="Permanent link">&para;</a></h3>
<h4 id="intellij-idea-setup">IntelliJ IDEA Setup<a class="headerlink" href="#intellij-idea-setup" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="cm">&lt;!-- .idea/compiler.xml --&gt;</span>
<span class="cp">&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;</span>
<span class="nt">&lt;project</span><span class="w"> </span><span class="na">version=</span><span class="s">&quot;4&quot;</span><span class="nt">&gt;</span>
<span class="w">  </span><span class="nt">&lt;component</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;CompilerConfiguration&quot;</span><span class="nt">&gt;</span>
<span class="w">    </span><span class="nt">&lt;annotationProcessing&gt;</span>
<span class="w">      </span><span class="nt">&lt;profile</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;Maven default annotation processors profile&quot;</span><span class="w"> </span><span class="na">enabled=</span><span class="s">&quot;true&quot;</span><span class="nt">&gt;</span>
<span class="w">        </span><span class="nt">&lt;sourceOutputDir</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;target/generated-sources/annotations&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">        </span><span class="nt">&lt;sourceTestOutputDir</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;target/generated-test-sources/test-annotations&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">        </span><span class="nt">&lt;outputRelativeToContentRoot</span><span class="w"> </span><span class="na">value=</span><span class="s">&quot;true&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">        </span><span class="nt">&lt;module</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;b2b-qes-plugin&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">      </span><span class="nt">&lt;/profile&gt;</span>
<span class="w">    </span><span class="nt">&lt;/annotationProcessing&gt;</span>
<span class="w">    </span><span class="nt">&lt;bytecodeTargetLevel&gt;</span>
<span class="w">      </span><span class="nt">&lt;module</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;b2b-qes-plugin&quot;</span><span class="w"> </span><span class="na">target=</span><span class="s">&quot;17&quot;</span><span class="w"> </span><span class="nt">/&gt;</span>
<span class="w">    </span><span class="nt">&lt;/bytecodeTargetLevel&gt;</span>
<span class="w">  </span><span class="nt">&lt;/component&gt;</span>
<span class="nt">&lt;/project&gt;</span>
</code></pre></div>
<h4 id="plugin-development-configuration">Plugin Development Configuration<a class="headerlink" href="#plugin-development-configuration" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># .idea/runConfigurations/QES_Plugin_Development.xml</span>
<span class="na">&lt;component</span><span class="w"> </span><span class="s">name=&quot;ProjectRunConfigurationManager&quot;&gt;</span>
<span class="w">  </span><span class="na">&lt;configuration</span><span class="w"> </span><span class="s">default=&quot;false&quot; name=&quot;QES Plugin Development&quot; type=&quot;Application&quot;&gt;</span>
<span class="w">    </span><span class="na">&lt;option</span><span class="w"> </span><span class="s">name=&quot;MAIN_CLASS_NAME&quot; value=&quot;com.netcracker.qes.engine.QesEngineApplication&quot; /&gt;</span>
<span class="w">    </span><span class="na">&lt;option</span><span class="w"> </span><span class="s">name=&quot;VM_PARAMETERS&quot; value=&quot;-Dspring.profiles.active=dev -Dqes.plugin.path=target/b2b-qes-plugin-1.2.0.jar&quot; /&gt;</span>
<span class="w">    </span><span class="na">&lt;option</span><span class="w"> </span><span class="s">name=&quot;PROGRAM_PARAMETERS&quot; value=&quot;&quot; /&gt;</span>
<span class="w">    </span><span class="na">&lt;option</span><span class="w"> </span><span class="s">name=&quot;WORKING_DIRECTORY&quot; value=&quot;$PROJECT_DIR$&quot; /&gt;</span>
<span class="w">    </span><span class="na">&lt;module</span><span class="w"> </span><span class="s">name=&quot;b2b-qes-plugin&quot; /&gt;</span>
<span class="w">    </span><span class="na">&lt;method</span><span class="w"> </span><span class="s">v=&quot;2&quot;&gt;</span>
<span class="w">      </span><span class="na">&lt;option</span><span class="w"> </span><span class="s">name=&quot;Make&quot; enabled=&quot;true&quot; /&gt;</span>
<span class="w">      </span><span class="na">&lt;option</span><span class="w"> </span><span class="s">name=&quot;Maven.BeforeRunTask&quot; enabled=&quot;true&quot; file=&quot;$PROJECT_DIR$/pom.xml&quot; goal=&quot;clean package&quot; /&gt;</span>
<span class="w">    </span><span class="na">&lt;/method&gt;</span>
<span class="w">  </span><span class="na">&lt;/configuration&gt;</span>
<span class="na">&lt;/component&gt;</span>
</code></pre></div>
<h3 id="plugin-configuration">Plugin Configuration<a class="headerlink" href="#plugin-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># src/main/resources/application-dev.yml</span>
<span class="nt">telus</span><span class="p">:</span>
<span class="w">  </span><span class="nt">plugin</span><span class="p">:</span>
<span class="w">    </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">telus-b2b-qes-plugin</span>
<span class="w">    </span><span class="nt">version</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1.2.0</span>
<span class="w">    </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">    </span><span class="nt">debug</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">    </span><span class="nt">quote-modificators</span><span class="p">:</span>
<span class="w">      </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">debug</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">components</span><span class="p">:</span>
<span class="w">        </span><span class="nt">delta-modificator</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">priority</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span>
<span class="w">          </span><span class="nt">debug</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">resulting-modificator</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">priority</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">200</span>
<span class="w">          </span><span class="nt">debug</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">    </span><span class="nt">bandwidth-validators</span><span class="p">:</span>
<span class="w">      </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">strict-mode</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span><span class="w">  </span><span class="c1"># Relaxed for development</span>
<span class="w">      </span><span class="nt">debug</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">components</span><span class="p">:</span>
<span class="w">        </span><span class="nt">l2-cir-validator</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">debug</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">l2-evc-validator</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">debug</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">l3-qos-validator</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">debug</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">    </span><span class="nt">state-validators</span><span class="p">:</span>
<span class="w">      </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">debug</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">components</span><span class="p">:</span>
<span class="w">        </span><span class="nt">state-machine-validator</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">strict-transitions</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">false</span><span class="w">  </span><span class="c1"># Relaxed for development</span>
<span class="w">          </span><span class="nt">debug</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="nt">logging</span><span class="p">:</span>
<span class="w">  </span><span class="nt">level</span><span class="p">:</span>
<span class="w">    </span><span class="nt">com.netcracker.solutions.telus.qes.extension.plugin</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DEBUG</span>
<span class="w">    </span><span class="nt">com.netcracker.qes</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">INFO</span>
<span class="w">    </span><span class="nt">org.springframework</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">INFO</span>
<span class="w">  </span><span class="nt">pattern</span><span class="p">:</span>
<span class="w">    </span><span class="nt">console</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;%d{yyyy-MM-dd</span><span class="nv"> </span><span class="s">HH:mm:ss}</span><span class="nv"> </span><span class="s">[%thread]</span><span class="nv"> </span><span class="s">%-5level</span><span class="nv"> </span><span class="s">%logger{36}</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">%msg%n&quot;</span>
</code></pre></div>
<h2 id="plugin-development-workflow">Plugin Development Workflow<a class="headerlink" href="#plugin-development-workflow" title="Permanent link">&para;</a></h2>
<h3 id="development-process">Development Process<a class="headerlink" href="#development-process" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Create feature branch</span>
git<span class="w"> </span>checkout<span class="w"> </span>-b<span class="w"> </span>feature/new-bandwidth-validator

<span class="c1"># 2. Implement plugin component</span>
<span class="c1"># Edit source files in src/main/java/</span>

<span class="c1"># 3. Write unit tests</span>
<span class="c1"># Create tests in src/test/java/</span>

<span class="c1"># 4. Run tests</span>
mvn<span class="w"> </span><span class="nb">test</span>

<span class="c1"># 5. Build plugin</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package

<span class="c1"># 6. Test plugin integration</span>
mvn<span class="w"> </span>qes:test-plugin

<span class="c1"># 7. Commit changes</span>
git<span class="w"> </span>add<span class="w"> </span>.
git<span class="w"> </span>commit<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;feat: add new bandwidth validator for fiber services&quot;</span>

<span class="c1"># 8. Push and create pull request</span>
git<span class="w"> </span>push<span class="w"> </span>origin<span class="w"> </span>feature/new-bandwidth-validator
</code></pre></div>
<h3 id="plugin-component-creation">Plugin Component Creation<a class="headerlink" href="#plugin-component-creation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Template for new bandwidth validator</span>
<span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusNewServiceBandwidthValidator</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">BandwidthValidator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">SERVICE_TYPE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;NEW_SERVICE_TYPE&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">SUPPORTED_SERVICE_TYPES</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="n">SERVICE_TYPE</span><span class="p">);</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">BandwidthValidatorUtils</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ValidationUtils</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">PluginConfigurationProperties</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">TelusNewServiceBandwidthValidator</span><span class="p">(</span>
<span class="w">            </span><span class="n">BandwidthValidatorUtils</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">,</span>
<span class="w">            </span><span class="n">ValidationUtils</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">,</span>
<span class="w">            </span><span class="n">PluginConfigurationProperties</span><span class="w"> </span><span class="n">config</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">bandwidthUtils</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">validationUtils</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">config</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validate</span><span class="p">(</span><span class="n">BandwidthValidationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Validating {} bandwidth for service item: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">SERVICE_TYPE</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Implementation here</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">serviceItemId</span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">validatorName</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="na">getClass</span><span class="p">().</span><span class="na">getSimpleName</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">valid</span><span class="p">(</span><span class="kc">true</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">getSupportedServiceTypes</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">SUPPORTED_SERVICE_TYPES</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="nf">getPriority</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="mi">100</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="configuration-updates">Configuration Updates<a class="headerlink" href="#configuration-updates" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Add new validator configuration</span>
<span class="nt">telus</span><span class="p">:</span>
<span class="w">  </span><span class="nt">plugin</span><span class="p">:</span>
<span class="w">    </span><span class="nt">bandwidth-validators</span><span class="p">:</span>
<span class="w">      </span><span class="nt">components</span><span class="p">:</span>
<span class="w">        </span><span class="nt">new-service-validator</span><span class="p">:</span>
<span class="w">          </span><span class="nt">enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">priority</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span>
<span class="w">          </span><span class="nt">min-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">          </span><span class="nt">max-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
<span class="w">          </span><span class="nt">unit</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Mbps</span>
<span class="w">          </span><span class="nt">service-types</span><span class="p">:</span>
<span class="w">            </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">NEW_SERVICE_TYPE</span>
</code></pre></div>
<h2 id="testing-strategy">Testing Strategy<a class="headerlink" href="#testing-strategy" title="Permanent link">&para;</a></h2>
<h3 id="unit-testing">Unit Testing<a class="headerlink" href="#unit-testing" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Unit test template</span>
<span class="nd">@ExtendWith</span><span class="p">(</span><span class="n">MockitoExtension</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="kd">class</span> <span class="nc">TelusNewServiceBandwidthValidatorTest</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Mock</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">BandwidthValidatorUtils</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Mock</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">ValidationUtils</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Mock</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">PluginConfigurationProperties</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@InjectMocks</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">TelusNewServiceBandwidthValidator</span><span class="w"> </span><span class="n">validator</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldValidateBandwidthSuccessfully</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">BandwidthValidationContext</span><span class="w"> </span><span class="n">context</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createValidContext</span><span class="p">();</span>

<span class="w">        </span><span class="n">when</span><span class="p">(</span><span class="n">config</span><span class="p">.</span><span class="na">getBandwidthValidators</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">thenReturn</span><span class="p">(</span><span class="n">createValidatorConfig</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validator</span><span class="p">.</span><span class="na">validate</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">.</span><span class="na">isValid</span><span class="p">()).</span><span class="na">isTrue</span><span class="p">();</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">.</span><span class="na">getErrors</span><span class="p">()).</span><span class="na">isEmpty</span><span class="p">();</span>

<span class="w">        </span><span class="n">verify</span><span class="p">(</span><span class="n">bandwidthUtils</span><span class="p">).</span><span class="na">validateBandwidth</span><span class="p">(</span><span class="n">any</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldFailValidationForInvalidBandwidth</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">BandwidthValidationContext</span><span class="w"> </span><span class="n">context</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createInvalidContext</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validator</span><span class="p">.</span><span class="na">validate</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">.</span><span class="na">isValid</span><span class="p">()).</span><span class="na">isFalse</span><span class="p">();</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">.</span><span class="na">getErrors</span><span class="p">()).</span><span class="na">isNotEmpty</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">BandwidthValidationContext</span><span class="w"> </span><span class="nf">createValidContext</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">serviceItem</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ServiceItem</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">serviceType</span><span class="p">(</span><span class="s">&quot;NEW_SERVICE_TYPE&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">serviceCharacteristics</span><span class="p">(</span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;BANDWIDTH&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;100&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;UNIT&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Mbps&quot;</span>
<span class="w">            </span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">BandwidthValidationContext</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">serviceItemId</span><span class="p">(</span><span class="s">&quot;ITEM-001&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">serviceItem</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="integration-testing">Integration Testing<a class="headerlink" href="#integration-testing" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Integration test template</span>
<span class="nd">@SpringBootTest</span>
<span class="nd">@TestPropertySource</span><span class="p">(</span><span class="n">properties</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="s">&quot;telus.plugin.enabled=true&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s">&quot;telus.plugin.bandwidth-validators.enabled=true&quot;</span>
<span class="p">})</span>
<span class="kd">class</span> <span class="nc">PluginIntegrationTest</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">PluginRegistry</span><span class="w"> </span><span class="n">pluginRegistry</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Autowired</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">BandwidthValidator</span><span class="o">&gt;</span><span class="w"> </span><span class="n">bandwidthValidators</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldLoadAllValidators</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">bandwidthValidators</span><span class="p">).</span><span class="na">hasSize</span><span class="p">(</span><span class="mi">4</span><span class="p">);</span><span class="w"> </span><span class="c1">// Including new validator</span>

<span class="w">        </span><span class="n">Optional</span><span class="o">&lt;</span><span class="n">BandwidthValidator</span><span class="o">&gt;</span><span class="w"> </span><span class="n">newValidator</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bandwidthValidators</span><span class="p">.</span><span class="na">stream</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">filter</span><span class="p">(</span><span class="n">v</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">v</span><span class="w"> </span><span class="k">instanceof</span><span class="w"> </span><span class="n">TelusNewServiceBandwidthValidator</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">findFirst</span><span class="p">();</span>

<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">newValidator</span><span class="p">).</span><span class="na">isPresent</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Test</span>
<span class="w">    </span><span class="kt">void</span><span class="w"> </span><span class="nf">shouldValidateNewServiceType</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Given</span>
<span class="w">        </span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">createQuoteContext</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// When</span>
<span class="w">        </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">pluginRegistry</span><span class="p">.</span><span class="na">modifyQuote</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Then</span>
<span class="w">        </span><span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">.</span><span class="na">getValidationErrors</span><span class="p">()).</span><span class="na">isEmpty</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="plugin-testing-commands">Plugin Testing Commands<a class="headerlink" href="#plugin-testing-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Run unit tests</span>
mvn<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Run integration tests</span>
mvn<span class="w"> </span>verify

<span class="c1"># Run specific test class</span>
mvn<span class="w"> </span><span class="nb">test</span><span class="w"> </span>-Dtest<span class="o">=</span>TelusNewServiceBandwidthValidatorTest

<span class="c1"># Run tests with coverage</span>
mvn<span class="w"> </span>clean<span class="w"> </span><span class="nb">test</span><span class="w"> </span>jacoco:report

<span class="c1"># Run plugin validation tests</span>
mvn<span class="w"> </span>qes:validate-plugin

<span class="c1"># Run end-to-end plugin tests</span>
mvn<span class="w"> </span>qes:test-plugin<span class="w"> </span>-Dqes.test.environment<span class="o">=</span>dev
</code></pre></div>
<h2 id="build-and-deployment">Build and Deployment<a class="headerlink" href="#build-and-deployment" title="Permanent link">&para;</a></h2>
<h3 id="maven-build-configuration">Maven Build Configuration<a class="headerlink" href="#maven-build-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="cm">&lt;!-- pom.xml build configuration --&gt;</span>
<span class="nt">&lt;build&gt;</span>
<span class="w">    </span><span class="nt">&lt;plugins&gt;</span>
<span class="w">        </span><span class="nt">&lt;plugin&gt;</span>
<span class="w">            </span><span class="nt">&lt;groupId&gt;</span>org.apache.maven.plugins<span class="nt">&lt;/groupId&gt;</span>
<span class="w">            </span><span class="nt">&lt;artifactId&gt;</span>maven-compiler-plugin<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">            </span><span class="nt">&lt;version&gt;</span>3.11.0<span class="nt">&lt;/version&gt;</span>
<span class="w">            </span><span class="nt">&lt;configuration&gt;</span>
<span class="w">                </span><span class="nt">&lt;source&gt;</span>17<span class="nt">&lt;/source&gt;</span>
<span class="w">                </span><span class="nt">&lt;target&gt;</span>17<span class="nt">&lt;/target&gt;</span>
<span class="w">                </span><span class="nt">&lt;annotationProcessorPaths&gt;</span>
<span class="w">                    </span><span class="nt">&lt;path&gt;</span>
<span class="w">                        </span><span class="nt">&lt;groupId&gt;</span>org.projectlombok<span class="nt">&lt;/groupId&gt;</span>
<span class="w">                        </span><span class="nt">&lt;artifactId&gt;</span>lombok<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">                        </span><span class="nt">&lt;version&gt;</span>1.18.30<span class="nt">&lt;/version&gt;</span>
<span class="w">                    </span><span class="nt">&lt;/path&gt;</span>
<span class="w">                </span><span class="nt">&lt;/annotationProcessorPaths&gt;</span>
<span class="w">            </span><span class="nt">&lt;/configuration&gt;</span>
<span class="w">        </span><span class="nt">&lt;/plugin&gt;</span>

<span class="w">        </span><span class="nt">&lt;plugin&gt;</span>
<span class="w">            </span><span class="nt">&lt;groupId&gt;</span>com.netcracker.qes<span class="nt">&lt;/groupId&gt;</span>
<span class="w">            </span><span class="nt">&lt;artifactId&gt;</span>qes-plugin-maven-plugin<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">            </span><span class="nt">&lt;version&gt;</span>2023.3.2.0<span class="nt">&lt;/version&gt;</span>
<span class="w">            </span><span class="nt">&lt;executions&gt;</span>
<span class="w">                </span><span class="nt">&lt;execution&gt;</span>
<span class="w">                    </span><span class="nt">&lt;goals&gt;</span>
<span class="w">                        </span><span class="nt">&lt;goal&gt;</span>validate-plugin<span class="nt">&lt;/goal&gt;</span>
<span class="w">                        </span><span class="nt">&lt;goal&gt;</span>generate-metadata<span class="nt">&lt;/goal&gt;</span>
<span class="w">                        </span><span class="nt">&lt;goal&gt;</span>package-plugin<span class="nt">&lt;/goal&gt;</span>
<span class="w">                    </span><span class="nt">&lt;/goals&gt;</span>
<span class="w">                </span><span class="nt">&lt;/execution&gt;</span>
<span class="w">            </span><span class="nt">&lt;/executions&gt;</span>
<span class="w">        </span><span class="nt">&lt;/plugin&gt;</span>

<span class="w">        </span><span class="nt">&lt;plugin&gt;</span>
<span class="w">            </span><span class="nt">&lt;groupId&gt;</span>org.springframework.boot<span class="nt">&lt;/groupId&gt;</span>
<span class="w">            </span><span class="nt">&lt;artifactId&gt;</span>spring-boot-maven-plugin<span class="nt">&lt;/artifactId&gt;</span>
<span class="w">            </span><span class="nt">&lt;configuration&gt;</span>
<span class="w">                </span><span class="nt">&lt;classifier&gt;</span>plugin<span class="nt">&lt;/classifier&gt;</span>
<span class="w">                </span><span class="nt">&lt;layout&gt;</span>JAR<span class="nt">&lt;/layout&gt;</span>
<span class="w">            </span><span class="nt">&lt;/configuration&gt;</span>
<span class="w">        </span><span class="nt">&lt;/plugin&gt;</span>
<span class="w">    </span><span class="nt">&lt;/plugins&gt;</span>
<span class="nt">&lt;/build&gt;</span>
</code></pre></div>
<h3 id="build-commands">Build Commands<a class="headerlink" href="#build-commands" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Development build</span>
mvn<span class="w"> </span>clean<span class="w"> </span>compile

<span class="c1"># Full build with tests</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package

<span class="c1"># Build without tests</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w"> </span>-DskipTests

<span class="c1"># Build with plugin validation</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w"> </span>qes:validate-plugin

<span class="c1"># Build for specific environment</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w"> </span>-Pdev
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w"> </span>-Pprod

<span class="c1"># Generate plugin metadata</span>
mvn<span class="w"> </span>qes:generate-metadata

<span class="c1"># Package for deployment</span>
mvn<span class="w"> </span>qes:package-plugin
</code></pre></div>
<h3 id="deployment-process">Deployment Process<a class="headerlink" href="#deployment-process" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># 1. Build plugin for target environment</span>
mvn<span class="w"> </span>clean<span class="w"> </span>package<span class="w"> </span>-Pprod

<span class="c1"># 2. Validate plugin structure</span>
mvn<span class="w"> </span>qes:validate-plugin

<span class="c1"># 3. Deploy to QES development environment</span>
mvn<span class="w"> </span>qes:deploy-plugin<span class="w"> </span>-Dqes.url<span class="o">=</span>https://qes-dev.telus.com

<span class="c1"># 4. Run deployment tests</span>
mvn<span class="w"> </span>qes:test-deployment<span class="w"> </span>-Dqes.url<span class="o">=</span>https://qes-dev.telus.com

<span class="c1"># 5. Deploy to production (with approval)</span>
mvn<span class="w"> </span>qes:deploy-plugin<span class="w"> </span>-Dqes.url<span class="o">=</span>https://qes-prod.telus.com<span class="w"> </span>-Dprofile<span class="o">=</span>production
</code></pre></div>
<h3 id="docker-deployment">Docker Deployment<a class="headerlink" href="#docker-deployment" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c"># Dockerfile for plugin development</span>
<span class="k">FROM</span><span class="w"> </span><span class="s">openjdk:17-jre-slim</span>

<span class="k">LABEL</span><span class="w"> </span><span class="nv">maintainer</span><span class="o">=</span><span class="s2">&quot;TELUS Development Team&quot;</span>
<span class="k">LABEL</span><span class="w"> </span><span class="nv">version</span><span class="o">=</span><span class="s2">&quot;1.2.0&quot;</span>
<span class="k">LABEL</span><span class="w"> </span><span class="nv">description</span><span class="o">=</span><span class="s2">&quot;TELUS B2B QES Plugin&quot;</span>

<span class="c"># Create plugin user</span>
<span class="k">RUN</span><span class="w"> </span>groupadd<span class="w"> </span>-r<span class="w"> </span>qesplugin<span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span>useradd<span class="w"> </span>-r<span class="w"> </span>-g<span class="w"> </span>qesplugin<span class="w"> </span>qesplugin

<span class="c"># Set working directory</span>
<span class="k">WORKDIR</span><span class="w"> </span><span class="s">/opt/qes/plugins</span>

<span class="c"># Copy plugin JAR</span>
<span class="k">COPY</span><span class="w"> </span>target/b2b-qes-plugin-*.jar<span class="w"> </span>telus-b2b-qes-plugin.jar

<span class="c"># Copy configuration</span>
<span class="k">COPY</span><span class="w"> </span>src/main/resources/plugin.properties<span class="w"> </span>.
<span class="k">COPY</span><span class="w"> </span>src/main/resources/validation-rules.yml<span class="w"> </span>.
<span class="k">COPY</span><span class="w"> </span>src/main/resources/business-rules.yml<span class="w"> </span>.

<span class="c"># Change ownership</span>
<span class="k">RUN</span><span class="w"> </span>chown<span class="w"> </span>-R<span class="w"> </span>qesplugin:qesplugin<span class="w"> </span>/opt/qes/plugins

<span class="c"># Switch to plugin user</span>
<span class="k">USER</span><span class="w"> </span><span class="s">qesplugin</span>

<span class="c"># Plugin metadata</span>
<span class="k">ENV</span><span class="w"> </span><span class="nv">PLUGIN_NAME</span><span class="o">=</span><span class="s2">&quot;telus-b2b-qes-plugin&quot;</span>
<span class="k">ENV</span><span class="w"> </span><span class="nv">PLUGIN_VERSION</span><span class="o">=</span><span class="s2">&quot;1.2.0&quot;</span>

<span class="c"># Health check</span>
<span class="k">HEALTHCHECK</span><span class="w"> </span>--interval<span class="o">=</span>30s<span class="w"> </span>--timeout<span class="o">=</span>3s<span class="w"> </span>--start-period<span class="o">=</span>5s<span class="w"> </span>--retries<span class="o">=</span><span class="m">3</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>CMD<span class="w"> </span><span class="nb">test</span><span class="w"> </span>-f<span class="w"> </span>telus-b2b-qes-plugin.jar<span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="nb">exit</span><span class="w"> </span><span class="m">1</span>

<span class="c"># Entry point</span>
<span class="k">ENTRYPOINT</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;java&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;-jar&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;telus-b2b-qes-plugin.jar&quot;</span><span class="p">]</span>
</code></pre></div>
<h2 id="debugging-and-troubleshooting">Debugging and Troubleshooting<a class="headerlink" href="#debugging-and-troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="debug-configuration">Debug Configuration<a class="headerlink" href="#debug-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Debug logging configuration</span>
<span class="nt">logging</span><span class="p">:</span>
<span class="w">  </span><span class="nt">level</span><span class="p">:</span>
<span class="w">    </span><span class="nt">com.netcracker.solutions.telus.qes.extension.plugin</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">TRACE</span>
<span class="w">    </span><span class="nt">com.netcracker.qes.plugin.framework</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DEBUG</span>
<span class="w">    </span><span class="nt">org.springframework.context</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">DEBUG</span>

<span class="w">  </span><span class="nt">appenders</span><span class="p">:</span>
<span class="w">    </span><span class="nt">console</span><span class="p">:</span>
<span class="w">      </span><span class="nt">pattern</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;%d{yyyy-MM-dd</span><span class="nv"> </span><span class="s">HH:mm:ss.SSS}</span><span class="nv"> </span><span class="s">[%thread]</span><span class="nv"> </span><span class="s">%-5level</span><span class="nv"> </span><span class="s">%logger{50}</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">%msg%n&quot;</span>

<span class="w">    </span><span class="nt">file</span><span class="p">:</span>
<span class="w">      </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">logs/plugin-debug.log</span>
<span class="w">      </span><span class="nt">pattern</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;%d{yyyy-MM-dd</span><span class="nv"> </span><span class="s">HH:mm:ss.SSS}</span><span class="nv"> </span><span class="s">[%thread]</span><span class="nv"> </span><span class="s">%-5level</span><span class="nv"> </span><span class="s">%logger{50}</span><span class="nv"> </span><span class="s">-</span><span class="nv"> </span><span class="s">%msg%n&quot;</span>
<span class="w">      </span><span class="nt">max-file-size</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10MB</span>
<span class="w">      </span><span class="nt">max-history</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30</span>
</code></pre></div>
<h3 id="common-issues-and-solutions">Common Issues and Solutions<a class="headerlink" href="#common-issues-and-solutions" title="Permanent link">&para;</a></h3>
<h4 id="plugin-loading-issues">Plugin Loading Issues<a class="headerlink" href="#plugin-loading-issues" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Check plugin structure</span>
jar<span class="w"> </span>-tf<span class="w"> </span>target/b2b-qes-plugin-*.jar<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>-E<span class="w"> </span><span class="s2">&quot;(META-INF|plugin\.properties)&quot;</span>

<span class="c1"># Validate plugin metadata</span>
mvn<span class="w"> </span>qes:validate-plugin

<span class="c1"># Check dependencies</span>
mvn<span class="w"> </span>dependency:tree
mvn<span class="w"> </span>dependency:analyze
</code></pre></div>
<h4 id="runtime-issues">Runtime Issues<a class="headerlink" href="#runtime-issues" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Enable debug logging</span>
<span class="nb">export</span><span class="w"> </span><span class="nv">QES_LOG_LEVEL</span><span class="o">=</span>DEBUG

<span class="c1"># Check plugin registration</span>
curl<span class="w"> </span>http://localhost:8081/qes/plugins

<span class="c1"># Monitor plugin metrics</span>
curl<span class="w"> </span>http://localhost:8081/qes/metrics/plugins

<span class="c1"># Check plugin health</span>
curl<span class="w"> </span>http://localhost:8081/qes/health/plugins
</code></pre></div>
<h4 id="performance-issues">Performance Issues<a class="headerlink" href="#performance-issues" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// Add performance monitoring</span>
<span class="nd">@Component</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">PluginPerformanceMonitor</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">MeterRegistry</span><span class="w"> </span><span class="n">meterRegistry</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@EventListener</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">onValidationEvent</span><span class="p">(</span><span class="n">ValidationEvent</span><span class="w"> </span><span class="n">event</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Timer</span><span class="p">.</span><span class="na">Sample</span><span class="w"> </span><span class="n">sample</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Timer</span><span class="p">.</span><span class="na">start</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">);</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Validation logic</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">finally</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">sample</span><span class="p">.</span><span class="na">stop</span><span class="p">(</span><span class="n">Timer</span><span class="p">.</span><span class="na">builder</span><span class="p">(</span><span class="s">&quot;plugin.validation.duration&quot;</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">tag</span><span class="p">(</span><span class="s">&quot;validator&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">event</span><span class="p">.</span><span class="na">getValidatorName</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">register</span><span class="p">(</span><span class="n">meterRegistry</span><span class="p">));</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="best-practices">Best Practices<a class="headerlink" href="#best-practices" title="Permanent link">&para;</a></h2>
<h3 id="code-quality-guidelines">Code Quality Guidelines<a class="headerlink" href="#code-quality-guidelines" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// 1. Use proper error handling</span>
<span class="nd">@Override</span>
<span class="kd">public</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validate</span><span class="p">(</span><span class="n">BandwidthValidationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Validation logic</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">success</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">ValidationException</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Validation failed for {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;VALIDATION_FAILED&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Unexpected error during validation&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;UNEXPECTED_ERROR&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Validation failed due to unexpected error&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="c1">// 2. Use configuration-driven behavior</span>
<span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isFeatureEnabled</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">featureName</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">config</span><span class="p">.</span><span class="na">getFeatures</span><span class="p">().</span><span class="na">getOrDefault</span><span class="p">(</span><span class="n">featureName</span><span class="p">,</span><span class="w"> </span><span class="kc">false</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">// 3. Implement proper logging</span>
<span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Starting validation for service type: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">serviceType</span><span class="p">);</span>
<span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Validation completed for {} items in {}ms&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">itemCount</span><span class="p">,</span><span class="w"> </span><span class="n">duration</span><span class="p">);</span>
<span class="n">log</span><span class="p">.</span><span class="na">warn</span><span class="p">(</span><span class="s">&quot;Validation warning for item {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">itemId</span><span class="p">,</span><span class="w"> </span><span class="n">warning</span><span class="p">);</span>
<span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Validation error for item {}: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">itemId</span><span class="p">,</span><span class="w"> </span><span class="n">error</span><span class="p">,</span><span class="w"> </span><span class="n">exception</span><span class="p">);</span>
</code></pre></div>
<h3 id="performance-optimization">Performance Optimization<a class="headerlink" href="#performance-optimization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// 1. Cache expensive operations</span>
<span class="nd">@Cacheable</span><span class="p">(</span><span class="n">value</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;validation-rules&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">key</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;#serviceType&quot;</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="n">ValidationRules</span><span class="w"> </span><span class="nf">getValidationRules</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">serviceType</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">loadValidationRules</span><span class="p">(</span><span class="n">serviceType</span><span class="p">);</span>
<span class="p">}</span>

<span class="c1">// 2. Use lazy initialization</span>
<span class="nd">@Lazy</span>
<span class="nd">@Component</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">ExpensiveValidator</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">BandwidthValidator</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Implementation</span>
<span class="p">}</span>

<span class="c1">// 3. Optimize validation order</span>
<span class="nd">@Override</span>
<span class="kd">public</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="nf">getPriority</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="mi">50</span><span class="p">;</span><span class="w"> </span><span class="c1">// Higher priority for faster validators</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="testing-best-practices">Testing Best Practices<a class="headerlink" href="#testing-best-practices" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// 1. Use test data builders</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TestDataBuilder</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="n">ServiceItem</span><span class="p">.</span><span class="na">ServiceItemBuilder</span><span class="w"> </span><span class="nf">validServiceItem</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ServiceItem</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">serviceType</span><span class="p">(</span><span class="s">&quot;WAN_L2_CIR&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">serviceCharacteristics</span><span class="p">(</span><span class="n">Map</span><span class="p">.</span><span class="na">of</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;CIR&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;100&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;UNIT&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Mbps&quot;</span>
<span class="w">            </span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>

<span class="c1">// 2. Test edge cases</span>
<span class="nd">@ParameterizedTest</span>
<span class="nd">@ValueSource</span><span class="p">(</span><span class="n">strings</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="s">&quot;0&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;-1&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;999999&quot;</span><span class="p">})</span>
<span class="kt">void</span><span class="w"> </span><span class="nf">shouldHandleEdgeCaseBandwidthValues</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">bandwidth</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Test implementation</span>
<span class="p">}</span>

<span class="c1">// 3. Use proper assertions</span>
<span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">.</span><span class="na">isValid</span><span class="p">())</span>
<span class="w">    </span><span class="p">.</span><span class="na">as</span><span class="p">(</span><span class="s">&quot;Validation should pass for valid bandwidth&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="p">.</span><span class="na">isTrue</span><span class="p">();</span>

<span class="n">assertThat</span><span class="p">(</span><span class="n">result</span><span class="p">.</span><span class="na">getErrors</span><span class="p">())</span>
<span class="w">    </span><span class="p">.</span><span class="na">as</span><span class="p">(</span><span class="s">&quot;Should have specific error message&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="p">.</span><span class="na">containsExactly</span><span class="p">(</span><span class="s">&quot;BANDWIDTH_BELOW_MINIMUM&quot;</span><span class="p">);</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="06-plugin-reference.md">Plugin Reference →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>