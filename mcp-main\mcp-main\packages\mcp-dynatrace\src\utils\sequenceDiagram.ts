import { Span, TreeSpan } from "../types.js";

/**
 * Represents a unique participant in the sequence diagram
 */
interface Participant {
  reference: string;
  participantName: string;
}

/**
 * Converts an array of spans into a tree structure where each span knows its children
 */
function getTree(spans: Span[]): TreeSpan[] {
  const spanMap = new Map<string, TreeSpan>();
  spans.forEach((span) => {
    spanMap.set(span["span.id"], {...span, children: []});
  });

  const tree: TreeSpan[] = [];
  const rootSpan = spans.find((span) => {
    const parentId = span["span.parent_id"]
    return !parentId || !spanMap.has(parentId)
  });
  const root = rootSpan || spans[0];
  const rootTreeSpan = spanMap.get(root['span.id'])!;
  tree.push(rootTreeSpan);


  spans.forEach(span => {
    const parentId = span["span.parent_id"];
    if (parentId) {
      const parentSpan = spanMap.get(parentId);
      const childSpan = spanMap.get(span["span.id"])!;
      if (parentSpan) {
        parentSpan.children.push(childSpan);
      }
    }
  });

  return tree;
}

/**
 * Generates a Mermaid sequence diagram from a tree of spans
 */
function generateMermaidSequenceDiagram(tree: TreeSpan[]): string {
  let diagram = `sequenceDiagram\n`;
  const participants = new Map<string, Participant>();
  let participantCounter = 0;

  /**
   * Gets or creates a unique participant name for a span
   */
  function getParticipantName(span: Span): string {
    const participantName = span["service.name"] || span["server.address"] || span["host.name"] || "Unknown";
    
    if (!participants.has(participantName)) {
      participantCounter++;
      const reference = `p${participantCounter}`;
      participants.set(participantName, {
        reference,
        participantName
      });
      diagram += `    participant ${reference} as "${participantName}"\n`;
    }
    
    return participants.get(participantName)!.reference;
  }

  /**
   * Extracts a human-readable annotation from a span
   */
  function getAnnotation(span: Span): string {
    let annotation = "Unknown Operation";
    const method = span["http.request.method"] ? `${span["http.request.method"]} ` : "";

    if (span["endpoint.name"]) {
      annotation = `${method}${span["endpoint.name"]}`;
    } else if (span["url.path"]) {
      annotation = `${method}${span["url.path"]}`;
    } else if (span["db.operation.name"]) {
      annotation = span["db.operation.name"];
    } else if (span["span.name"]) {
      annotation = span["span.name"];
    }

    // Escape special characters in the annotation
    annotation = annotation.replace(/[<>]/g, "_");
    return annotation.length > 75 ? `${annotation.substring(0, 72)}...` : annotation;
  }

  /**
   * Gets the response information from a span
   */
  function getResponse(span: Span): string {
    const status = span["http.response.status_code"];
    const count = span["db.result.execution_count"];
    
    if (status) return `Response: ${status}`;
    if (count) return `Affected rows: ${count}`;
    return "";
  }

  function processSpan(span: TreeSpan, sourceParticipant?: string) {
    const participant = getParticipantName(span);
    const annotation = getAnnotation(span);
    const responseData = getResponse(span);

    if (sourceParticipant && sourceParticipant !== participant) {
      diagram += `    ${sourceParticipant}->>${participant}: ${annotation}\n`;
      if (responseData) {
        diagram += `    activate ${participant}\n`;
      }
    }

    if (span.children && span.children.length > 0) {
      span.children.forEach((child) => processSpan(child, participant));
    }

    if (sourceParticipant && sourceParticipant !== participant && responseData) {
      diagram += `    ${participant}-->>${sourceParticipant}: ${responseData}\n`;
      diagram += `    deactivate ${participant}\n`;
    }
  }

  tree.forEach((rootSpan) => processSpan(rootSpan));
  return diagram;
}

/**
 * Converts an array of spans into a Mermaid sequence diagram
 */
export const fromSpans = (spans: Span[]): string => {
  if (!spans.length) {
    throw new Error("Cannot create sequence diagram from empty spans array");
  }

  const treeSpans = getTree(spans);
  return generateMermaidSequenceDiagram(treeSpans);
};
