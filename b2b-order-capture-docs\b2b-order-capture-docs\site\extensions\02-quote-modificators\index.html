
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/extensions/02-quote-modificators/">
      
      
        <link rel="prev" href="../01-plugin-architecture/">
      
      
        <link rel="next" href="../03-bandwidth-validators/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Quote Modificators - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#quote-modificators" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Quote Modificators
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" checked>
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quote-modification-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Modification Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Modification Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#modification-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      Modification Pipeline
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#modification-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Modification Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#telusquotedeltamodificatorimpl" class="md-nav__link">
    <span class="md-ellipsis">
      TelusQuoteDeltaModificatorImpl
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusQuoteDeltaModificatorImpl">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Core Implementation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#telusquoteresultingdeltamodificator" class="md-nav__link">
    <span class="md-ellipsis">
      TelusQuoteResultingDeltaModificator
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusQuoteResultingDeltaModificator">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#final-quote-processing" class="md-nav__link">
    <span class="md-ellipsis">
      Final Quote Processing
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#telusquoteexpirationperiodprovider" class="md-nav__link">
    <span class="md-ellipsis">
      TelusQuoteExpirationPeriodProvider
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusQuoteExpirationPeriodProvider">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-lifecycle-management" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Lifecycle Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#business-rules-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Business Rules Engine
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Business Rules Engine">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rule-definition" class="md-nav__link">
    <span class="md-ellipsis">
      Rule Definition
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-and-customization" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration and Customization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration and Customization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#dynamic-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Dynamic Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quote-modification-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Modification Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Modification Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#modification-pipeline" class="md-nav__link">
    <span class="md-ellipsis">
      Modification Pipeline
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#modification-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Modification Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#telusquotedeltamodificatorimpl" class="md-nav__link">
    <span class="md-ellipsis">
      TelusQuoteDeltaModificatorImpl
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusQuoteDeltaModificatorImpl">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#core-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Core Implementation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#telusquoteresultingdeltamodificator" class="md-nav__link">
    <span class="md-ellipsis">
      TelusQuoteResultingDeltaModificator
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusQuoteResultingDeltaModificator">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#final-quote-processing" class="md-nav__link">
    <span class="md-ellipsis">
      Final Quote Processing
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#telusquoteexpirationperiodprovider" class="md-nav__link">
    <span class="md-ellipsis">
      TelusQuoteExpirationPeriodProvider
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusQuoteExpirationPeriodProvider">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-lifecycle-management" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Lifecycle Management
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#business-rules-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Business Rules Engine
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Business Rules Engine">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rule-definition" class="md-nav__link">
    <span class="md-ellipsis">
      Rule Definition
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-and-customization" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration and Customization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration and Customization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#dynamic-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Dynamic Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="quote-modificators">Quote Modificators<a class="headerlink" href="#quote-modificators" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#quote-modification-overview">Quote Modification Overview</a></li>
<li><a href="#telusquotedeltamodificatorimpl">TelusQuoteDeltaModificatorImpl</a></li>
<li><a href="#telusquoteresultingdeltamodificator">TelusQuoteResultingDeltaModificator</a></li>
<li><a href="#telusquoteexpirationperiodprovider">TelusQuoteExpirationPeriodProvider</a></li>
<li><a href="#business-rules-engine">Business Rules Engine</a></li>
<li><a href="#configuration-and-customization">Configuration and Customization</a></li>
</ul>
<h2 id="quote-modification-overview">Quote Modification Overview<a class="headerlink" href="#quote-modification-overview" title="Permanent link">&para;</a></h2>
<h3 id="modification-pipeline">Modification Pipeline<a class="headerlink" href="#modification-pipeline" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;Quote Processing Pipeline&quot;
        A[Quote Request] --&gt; B[Delta Modificator]
        B --&gt; C[Validation Engine]
        C --&gt; D[Resulting Modificator]
        D --&gt; E[Expiration Provider]
        E --&gt; F[Final Quote]
    end

    subgraph &quot;Modification Types&quot;
        G[Pricing Adjustments]
        H[Business Rules]
        I[Regulatory Compliance]
        J[Customer-Specific Logic]
    end

    subgraph &quot;Extension Points&quot;
        K[Pre-Processing]
        L[Post-Processing]
        M[Error Handling]
        N[Audit Logging]
    end

    B --&gt; G
    B --&gt; H
    D --&gt; I
    D --&gt; J

    B --&gt; K
    D --&gt; L
    C --&gt; M
    F --&gt; N

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style D fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="modification-flow">Modification Flow<a class="headerlink" href="#modification-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant QE as Quote Engine
    participant DM as Delta Modificator
    participant VE as Validation Engine
    participant RM as Resulting Modificator
    participant EP as Expiration Provider

    QE-&gt;&gt;DM: modifyQuote(context)
    DM-&gt;&gt;DM: Apply Business Rules
    DM-&gt;&gt;DM: Calculate Pricing
    DM-&gt;&gt;DM: Validate Bandwidth
    DM--&gt;&gt;QE: QuoteDelta

    QE-&gt;&gt;VE: Validate Quote
    VE--&gt;&gt;QE: Validation Result

    QE-&gt;&gt;RM: modifyResultingQuote(context)
    RM-&gt;&gt;RM: Apply Final Adjustments
    RM-&gt;&gt;RM: Regulatory Compliance
    RM--&gt;&gt;QE: Final QuoteDelta

    QE-&gt;&gt;EP: getExpirationPeriod(context)
    EP-&gt;&gt;EP: Calculate Expiration
    EP--&gt;&gt;QE: Duration

    QE-&gt;&gt;QE: Finalize Quote
</code></pre></div>
<h2 id="telusquotedeltamodificatorimpl">TelusQuoteDeltaModificatorImpl<a class="headerlink" href="#telusquotedeltamodificatorimpl" title="Permanent link">&para;</a></h2>
<h3 id="core-implementation">Core Implementation<a class="headerlink" href="#core-implementation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusQuoteDeltaModificatorImpl</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">QuoteDeltaModificator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">BandwidthValidatorUtils</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">CommonUtils</span><span class="w"> </span><span class="n">commonUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ValidationUtils</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">QuoteUtils</span><span class="w"> </span><span class="n">quoteUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">PluginConfigurationProperties</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">TelusQuoteDeltaModificatorImpl</span><span class="p">(</span>
<span class="w">            </span><span class="n">BandwidthValidatorUtils</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">,</span>
<span class="w">            </span><span class="n">CommonUtils</span><span class="w"> </span><span class="n">commonUtils</span><span class="p">,</span>
<span class="w">            </span><span class="n">ValidationUtils</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">,</span>
<span class="w">            </span><span class="n">QuoteUtils</span><span class="w"> </span><span class="n">quoteUtils</span><span class="p">,</span>
<span class="w">            </span><span class="n">PluginConfigurationProperties</span><span class="w"> </span><span class="n">config</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">bandwidthUtils</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">commonUtils</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">commonUtils</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">validationUtils</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">quoteUtils</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quoteUtils</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">config</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="nf">modifyQuote</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Starting quote modification for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">quoteId</span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">timestamp</span><span class="p">(</span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">modificatorName</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="na">getClass</span><span class="p">().</span><span class="na">getSimpleName</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Apply bandwidth validations</span>
<span class="w">            </span><span class="n">applyBandwidthValidations</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply pricing modifications</span>
<span class="w">            </span><span class="n">applyPricingModifications</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply business rule validations</span>
<span class="w">            </span><span class="n">applyBusinessRuleValidations</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply customer-specific modifications</span>
<span class="w">            </span><span class="n">applyCustomerSpecificModifications</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply service-specific modifications</span>
<span class="w">            </span><span class="n">applyServiceSpecificModifications</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="n">delta</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Quote modification completed with {} changes for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                    </span><span class="n">delta</span><span class="p">.</span><span class="na">getChanges</span><span class="p">().</span><span class="na">size</span><span class="p">(),</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">delta</span><span class="p">;</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error during quote modification for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">deltaBuilder</span>
<span class="w">                </span><span class="p">.</span><span class="na">addError</span><span class="p">(</span><span class="s">&quot;MODIFICATION_ERROR&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Quote modification failed: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">applyBandwidthValidations</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                         </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Applying bandwidth validations for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteItems</span><span class="p">().</span><span class="na">stream</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">filter</span><span class="p">(</span><span class="n">item</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">isNetworkService</span><span class="p">(</span><span class="n">item</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">forEach</span><span class="p">(</span><span class="n">item</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">.</span><span class="na">validateBandwidth</span><span class="p">(</span><span class="n">item</span><span class="p">);</span>
<span class="w">                    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">result</span><span class="p">.</span><span class="na">isValid</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">                        </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">addValidationError</span><span class="p">(</span>
<span class="w">                            </span><span class="n">item</span><span class="p">.</span><span class="na">getItemId</span><span class="p">(),</span><span class="w"> </span>
<span class="w">                            </span><span class="s">&quot;BANDWIDTH_VALIDATION_FAILED&quot;</span><span class="p">,</span>
<span class="w">                            </span><span class="n">result</span><span class="p">.</span><span class="na">getErrorMessage</span><span class="p">()</span>
<span class="w">                        </span><span class="p">);</span>
<span class="w">                        </span><span class="n">log</span><span class="p">.</span><span class="na">warn</span><span class="p">(</span><span class="s">&quot;Bandwidth validation failed for item {}: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                </span><span class="n">item</span><span class="p">.</span><span class="na">getItemId</span><span class="p">(),</span><span class="w"> </span><span class="n">result</span><span class="p">.</span><span class="na">getErrorMessage</span><span class="p">());</span>
<span class="w">                    </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">                        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Bandwidth validation passed for item: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">item</span><span class="p">.</span><span class="na">getItemId</span><span class="p">());</span>
<span class="w">                    </span><span class="p">}</span>
<span class="w">                </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error validating bandwidth for item: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">item</span><span class="p">.</span><span class="na">getItemId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">                    </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">addValidationError</span><span class="p">(</span>
<span class="w">                        </span><span class="n">item</span><span class="p">.</span><span class="na">getItemId</span><span class="p">(),</span>
<span class="w">                        </span><span class="s">&quot;BANDWIDTH_VALIDATION_ERROR&quot;</span><span class="p">,</span>
<span class="w">                        </span><span class="s">&quot;Bandwidth validation error: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">()</span>
<span class="w">                    </span><span class="p">);</span>
<span class="w">                </span><span class="p">}</span>
<span class="w">            </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">applyPricingModifications</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                         </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Applying pricing modifications for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Calculate total quote value</span>
<span class="w">            </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">totalValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calculateTotalQuoteValue</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply volume discounts</span>
<span class="w">            </span><span class="n">applyVolumeDiscounts</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">,</span><span class="w"> </span><span class="n">totalValue</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply customer tier discounts</span>
<span class="w">            </span><span class="n">applyCustomerTierDiscounts</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply promotional discounts</span>
<span class="w">            </span><span class="n">applyPromotionalDiscounts</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply regulatory fees</span>
<span class="w">            </span><span class="n">applyRegulatoryFees</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error applying pricing modifications for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span><span class="s">&quot;PRICING_ERROR&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Pricing modification failed: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">());</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">applyVolumeDiscounts</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                    </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                    </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">totalValue</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="kd">var</span><span class="w"> </span><span class="n">volumeDiscountConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">config</span><span class="p">.</span><span class="na">getQuoteModificators</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">getComponents</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;delta-modificator&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">getProperties</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;volume-discount&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">volumeDiscountConfig</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">isVolumeDiscountEnabled</span><span class="p">(</span><span class="n">volumeDiscountConfig</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">threshold</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getVolumeDiscountThreshold</span><span class="p">(</span><span class="n">volumeDiscountConfig</span><span class="p">);</span>
<span class="w">            </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">discountPercentage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getVolumeDiscountPercentage</span><span class="p">(</span><span class="n">volumeDiscountConfig</span><span class="p">);</span>

<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">totalValue</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">threshold</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">discountAmount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">totalValue</span><span class="p">.</span><span class="na">multiply</span><span class="p">(</span><span class="n">discountPercentage</span><span class="p">)</span>
<span class="w">                    </span><span class="p">.</span><span class="na">divide</span><span class="p">(</span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">valueOf</span><span class="p">(</span><span class="mi">100</span><span class="p">),</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="n">RoundingMode</span><span class="p">.</span><span class="na">HALF_UP</span><span class="p">);</span>

<span class="w">                </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">addPriceAdjustment</span><span class="p">(</span>
<span class="w">                    </span><span class="s">&quot;VOLUME_DISCOUNT&quot;</span><span class="p">,</span>
<span class="w">                    </span><span class="s">&quot;Volume discount for orders over &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">threshold</span><span class="p">,</span>
<span class="w">                    </span><span class="n">discountAmount</span><span class="p">.</span><span class="na">negate</span><span class="p">(),</span>
<span class="w">                    </span><span class="n">PriceAdjustmentType</span><span class="p">.</span><span class="na">DISCOUNT</span>
<span class="w">                </span><span class="p">);</span>

<span class="w">                </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Applied volume discount of {} for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                        </span><span class="n">discountAmount</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">applyCustomerTierDiscounts</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                          </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">CustomerInfo</span><span class="w"> </span><span class="n">customer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getCustomer</span><span class="p">();</span>
<span class="w">        </span><span class="n">CustomerTier</span><span class="w"> </span><span class="n">customerTier</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">customer</span><span class="p">.</span><span class="na">getTier</span><span class="p">();</span>

<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">discountPercentage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">customerTier</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">PLATINUM</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">valueOf</span><span class="p">(</span><span class="mf">3.0</span><span class="p">);</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">GOLD</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">valueOf</span><span class="p">(</span><span class="mf">2.0</span><span class="p">);</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">SILVER</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">valueOf</span><span class="p">(</span><span class="mf">1.0</span><span class="p">);</span>
<span class="w">            </span><span class="k">default</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">ZERO</span><span class="p">;</span>
<span class="w">        </span><span class="p">};</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">discountPercentage</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">ZERO</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">totalValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">calculateTotalQuoteValue</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>
<span class="w">            </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">discountAmount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">totalValue</span><span class="p">.</span><span class="na">multiply</span><span class="p">(</span><span class="n">discountPercentage</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">divide</span><span class="p">(</span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">valueOf</span><span class="p">(</span><span class="mi">100</span><span class="p">),</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="n">RoundingMode</span><span class="p">.</span><span class="na">HALF_UP</span><span class="p">);</span>

<span class="w">            </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">addPriceAdjustment</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;CUSTOMER_TIER_DISCOUNT&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">customerTier</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot; customer tier discount&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">discountAmount</span><span class="p">.</span><span class="na">negate</span><span class="p">(),</span>
<span class="w">                </span><span class="n">PriceAdjustmentType</span><span class="p">.</span><span class="na">DISCOUNT</span>
<span class="w">            </span><span class="p">);</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Applied {} customer tier discount of {} for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                    </span><span class="n">customerTier</span><span class="p">,</span><span class="w"> </span><span class="n">discountAmount</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">applyBusinessRuleValidations</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                            </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Applying business rule validations for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Validate quote completeness</span>
<span class="w">        </span><span class="n">validateQuoteCompleteness</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Validate service compatibility</span>
<span class="w">        </span><span class="n">validateServiceCompatibility</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Validate customer eligibility</span>
<span class="w">        </span><span class="n">validateCustomerEligibility</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Validate geographic constraints</span>
<span class="w">        </span><span class="n">validateGeographicConstraints</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateQuoteCompleteness</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                         </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteItems</span><span class="p">().</span><span class="na">isEmpty</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">addValidationError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;QUOTE_COMPLETENESS&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;EMPTY_QUOTE&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Quote must contain at least one service item&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate required customer information</span>
<span class="w">        </span><span class="n">CustomerInfo</span><span class="w"> </span><span class="n">customer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getCustomer</span><span class="p">();</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">customer</span><span class="p">.</span><span class="na">getCustomerId</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">customer</span><span class="p">.</span><span class="na">getCustomerId</span><span class="p">().</span><span class="na">trim</span><span class="p">().</span><span class="na">isEmpty</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">addValidationError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;QUOTE_COMPLETENESS&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;MISSING_CUSTOMER_ID&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Customer ID is required&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate service locations</span>
<span class="w">        </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteItems</span><span class="p">().</span><span class="na">forEach</span><span class="p">(</span><span class="n">item</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">item</span><span class="p">.</span><span class="na">getServiceLocation</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">addValidationError</span><span class="p">(</span>
<span class="w">                    </span><span class="n">item</span><span class="p">.</span><span class="na">getItemId</span><span class="p">(),</span>
<span class="w">                    </span><span class="s">&quot;MISSING_SERVICE_LOCATION&quot;</span><span class="p">,</span>
<span class="w">                    </span><span class="s">&quot;Service location is required for item: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">item</span><span class="p">.</span><span class="na">getItemId</span><span class="p">()</span>
<span class="w">                </span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateServiceCompatibility</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                            </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">List</span><span class="o">&lt;</span><span class="n">QuoteItem</span><span class="o">&gt;</span><span class="w"> </span><span class="n">networkServices</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteItems</span><span class="p">().</span><span class="na">stream</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">filter</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">isNetworkService</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">collect</span><span class="p">(</span><span class="n">Collectors</span><span class="p">.</span><span class="na">toList</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Check for conflicting service types</span>
<span class="w">        </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">serviceTypes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">networkServices</span><span class="p">.</span><span class="na">stream</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">map</span><span class="p">(</span><span class="n">QuoteItem</span><span class="p">::</span><span class="n">getServiceType</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">collect</span><span class="p">(</span><span class="n">Collectors</span><span class="p">.</span><span class="na">toSet</span><span class="p">());</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">hasConflictingServices</span><span class="p">(</span><span class="n">serviceTypes</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">addValidationError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;SERVICE_COMPATIBILITY&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;CONFLICTING_SERVICES&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Quote contains conflicting service types: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">serviceTypes</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate bandwidth consistency across related services</span>
<span class="w">        </span><span class="n">validateBandwidthConsistency</span><span class="p">(</span><span class="n">networkServices</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isNetworkService</span><span class="p">(</span><span class="n">QuoteItem</span><span class="w"> </span><span class="n">item</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">item</span><span class="p">.</span><span class="na">getServiceType</span><span class="p">().</span><span class="na">startsWith</span><span class="p">(</span><span class="s">&quot;WAN_&quot;</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span>
<span class="w">               </span><span class="n">item</span><span class="p">.</span><span class="na">getServiceType</span><span class="p">().</span><span class="na">startsWith</span><span class="p">(</span><span class="s">&quot;LAN_&quot;</span><span class="p">)</span><span class="w"> </span><span class="o">||</span>
<span class="w">               </span><span class="n">item</span><span class="p">.</span><span class="na">getServiceType</span><span class="p">().</span><span class="na">startsWith</span><span class="p">(</span><span class="s">&quot;INTERNET_&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">BigDecimal</span><span class="w"> </span><span class="nf">calculateTotalQuoteValue</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteItems</span><span class="p">().</span><span class="na">stream</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">map</span><span class="p">(</span><span class="n">QuoteItem</span><span class="p">::</span><span class="n">getTotalPrice</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">reduce</span><span class="p">(</span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">ZERO</span><span class="p">,</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">::</span><span class="n">add</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="nf">getPriority</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">config</span><span class="p">.</span><span class="na">getQuoteModificators</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">getComponents</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;delta-modificator&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">getPriority</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isApplicable</span><span class="p">(</span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">config</span><span class="p">.</span><span class="na">getQuoteModificators</span><span class="p">().</span><span class="na">isEnabled</span><span class="p">()</span><span class="w"> </span><span class="o">&amp;&amp;</span>
<span class="w">               </span><span class="n">config</span><span class="p">.</span><span class="na">getQuoteModificators</span><span class="p">()</span>
<span class="w">                   </span><span class="p">.</span><span class="na">getComponents</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;delta-modificator&quot;</span><span class="p">)</span>
<span class="w">                   </span><span class="p">.</span><span class="na">isEnabled</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="telusquoteresultingdeltamodificator">TelusQuoteResultingDeltaModificator<a class="headerlink" href="#telusquoteresultingdeltamodificator" title="Permanent link">&para;</a></h2>
<h3 id="final-quote-processing">Final Quote Processing<a class="headerlink" href="#final-quote-processing" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusQuoteResultingDeltaModificator</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">QuoteResultingDeltaModificator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ValidationUtils</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">QuoteUtils</span><span class="w"> </span><span class="n">quoteUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">PluginConfigurationProperties</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="nf">modifyResultingQuote</span><span class="p">(</span><span class="n">QuoteResultingContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Starting resulting quote modification for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">quoteId</span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">timestamp</span><span class="p">(</span><span class="n">Instant</span><span class="p">.</span><span class="na">now</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">modificatorName</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="na">getClass</span><span class="p">().</span><span class="na">getSimpleName</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Apply final pricing adjustments</span>
<span class="w">            </span><span class="n">applyFinalPricingAdjustments</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply regulatory compliance modifications</span>
<span class="w">            </span><span class="n">applyRegulatoryCompliance</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply customer-specific modifications</span>
<span class="w">            </span><span class="n">applyCustomerSpecificModifications</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Apply quote finalization rules</span>
<span class="w">            </span><span class="n">applyQuoteFinalizationRules</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="n">delta</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Resulting quote modification completed with {} changes for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                    </span><span class="n">delta</span><span class="p">.</span><span class="na">getChanges</span><span class="p">().</span><span class="na">size</span><span class="p">(),</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">delta</span><span class="p">;</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error during resulting quote modification for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                     </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">deltaBuilder</span>
<span class="w">                </span><span class="p">.</span><span class="na">addError</span><span class="p">(</span><span class="s">&quot;RESULTING_MODIFICATION_ERROR&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                         </span><span class="s">&quot;Resulting quote modification failed: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">applyFinalPricingAdjustments</span><span class="p">(</span><span class="n">QuoteResultingContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                            </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Applying final pricing adjustments for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Round pricing to appropriate precision</span>
<span class="w">        </span><span class="n">roundPricingValues</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply minimum pricing rules</span>
<span class="w">        </span><span class="n">applyMinimumPricingRules</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply maximum pricing caps</span>
<span class="w">        </span><span class="n">applyMaximumPricingCaps</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Calculate and apply taxes</span>
<span class="w">        </span><span class="n">calculateAndApplyTaxes</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">applyRegulatoryCompliance</span><span class="p">(</span><span class="n">QuoteResultingContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                         </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Applying regulatory compliance for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="c1">// Apply CRTC compliance rules</span>
<span class="w">        </span><span class="n">applyCrtcCompliance</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply provincial regulations</span>
<span class="w">        </span><span class="n">applyProvincialRegulations</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply federal regulations</span>
<span class="w">        </span><span class="n">applyFederalRegulations</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply industry standards</span>
<span class="w">        </span><span class="n">applyIndustryStandards</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">applyCrtcCompliance</span><span class="p">(</span><span class="n">QuoteResultingContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                   </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="c1">// CRTC requires specific disclosures for telecommunications services</span>
<span class="w">        </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteItems</span><span class="p">().</span><span class="na">stream</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">filter</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">isTelecommunicationsService</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">forEach</span><span class="p">(</span><span class="n">item</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">deltaBuilder</span><span class="p">.</span><span class="na">addMetadata</span><span class="p">(</span>
<span class="w">                    </span><span class="n">item</span><span class="p">.</span><span class="na">getItemId</span><span class="p">(),</span>
<span class="w">                    </span><span class="s">&quot;CRTC_DISCLOSURE&quot;</span><span class="p">,</span>
<span class="w">                    </span><span class="s">&quot;This service is subject to CRTC regulations and consumer protection rules&quot;</span>
<span class="w">                </span><span class="p">);</span>
<span class="w">            </span><span class="p">});</span>

<span class="w">        </span><span class="c1">// Apply CRTC accessibility requirements</span>
<span class="w">        </span><span class="n">applyAccessibilityRequirements</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">deltaBuilder</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="nf">getPriority</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">config</span><span class="p">.</span><span class="na">getQuoteModificators</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">getComponents</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;resulting-modificator&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">getPriority</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="telusquoteexpirationperiodprovider">TelusQuoteExpirationPeriodProvider<a class="headerlink" href="#telusquoteexpirationperiodprovider" title="Permanent link">&para;</a></h2>
<h3 id="quote-lifecycle-management">Quote Lifecycle Management<a class="headerlink" href="#quote-lifecycle-management" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusQuoteExpirationPeriodProvider</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">QuoteExpirationPeriodProvider</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">DEFAULT_EXPIRATION_DAYS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">30</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">ENTERPRISE_EXPIRATION_DAYS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">60</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">GOVERNMENT_EXPIRATION_DAYS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">90</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">COMPLEX_SOLUTION_EXTENSION_DAYS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">15</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">MAX_EXTENSIONS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="p">;</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">PluginConfigurationProperties</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">TelusQuoteExpirationPeriodProvider</span><span class="p">(</span><span class="n">PluginConfigurationProperties</span><span class="w"> </span><span class="n">config</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">config</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Duration</span><span class="w"> </span><span class="nf">getExpirationPeriod</span><span class="p">(</span><span class="n">QuoteExpirationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Calculating expiration period for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="n">CustomerType</span><span class="w"> </span><span class="n">customerType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getCustomerType</span><span class="p">();</span>
<span class="w">        </span><span class="n">QuoteType</span><span class="w"> </span><span class="n">quoteType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteType</span><span class="p">();</span>
<span class="w">        </span><span class="n">QuoteComplexity</span><span class="w"> </span><span class="n">complexity</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteComplexity</span><span class="p">();</span>

<span class="w">        </span><span class="kt">int</span><span class="w"> </span><span class="n">expirationDays</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">customerType</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">ENTERPRISE</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">ENTERPRISE_EXPIRATION_DAYS</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">GOVERNMENT</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">GOVERNMENT_EXPIRATION_DAYS</span><span class="p">;</span>
<span class="w">            </span><span class="k">default</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">DEFAULT_EXPIRATION_DAYS</span><span class="p">;</span>
<span class="w">        </span><span class="p">};</span>

<span class="w">        </span><span class="c1">// Apply quote type modifiers</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quoteType</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteType</span><span class="p">.</span><span class="na">COMPLEX_SOLUTION</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">expirationDays</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="n">COMPLEX_SOLUTION_EXTENSION_DAYS</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Apply complexity modifiers</span>
<span class="w">        </span><span class="n">expirationDays</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="n">getComplexityAdjustment</span><span class="p">(</span><span class="n">complexity</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Apply seasonal adjustments</span>
<span class="w">        </span><span class="n">expirationDays</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="n">getSeasonalAdjustment</span><span class="p">();</span>

<span class="w">        </span><span class="n">Duration</span><span class="w"> </span><span class="n">expiration</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Duration</span><span class="p">.</span><span class="na">ofDays</span><span class="p">(</span><span class="n">expirationDays</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Calculated expiration period of {} days for quote: {} (customer: {}, type: {})&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                </span><span class="n">expirationDays</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="n">customerType</span><span class="p">,</span><span class="w"> </span><span class="n">quoteType</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">expiration</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">canExtendExpiration</span><span class="p">(</span><span class="n">QuoteExpirationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kt">boolean</span><span class="w"> </span><span class="n">canExtend</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getCustomerType</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="n">CustomerType</span><span class="p">.</span><span class="na">STANDARD</span><span class="w"> </span><span class="o">&amp;&amp;</span>
<span class="w">                           </span><span class="n">context</span><span class="p">.</span><span class="na">getExtensionCount</span><span class="p">()</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="n">MAX_EXTENSIONS</span><span class="w"> </span><span class="o">&amp;&amp;</span>
<span class="w">                           </span><span class="n">isExtensionJustified</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Extension eligibility for quote {}: {} (extensions: {}/{})&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">(),</span><span class="w"> </span><span class="n">canExtend</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getExtensionCount</span><span class="p">(),</span><span class="w"> </span><span class="n">MAX_EXTENSIONS</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">canExtend</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Duration</span><span class="w"> </span><span class="nf">getExtensionPeriod</span><span class="p">(</span><span class="n">QuoteExpirationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">canExtendExpiration</span><span class="p">(</span><span class="n">context</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IllegalStateException</span><span class="p">(</span><span class="s">&quot;Quote is not eligible for extension&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="kt">int</span><span class="w"> </span><span class="n">extensionDays</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getCustomerType</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">ENTERPRISE</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="mi">30</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">GOVERNMENT</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="mi">45</span><span class="p">;</span>
<span class="w">            </span><span class="k">default</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="mi">15</span><span class="p">;</span>
<span class="w">        </span><span class="p">};</span>

<span class="w">        </span><span class="c1">// Reduce extension period for subsequent extensions</span>
<span class="w">        </span><span class="n">extensionDays</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extensionDays</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getExtensionCount</span><span class="p">()</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span>

<span class="w">        </span><span class="n">Duration</span><span class="w"> </span><span class="n">extension</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Duration</span><span class="p">.</span><span class="na">ofDays</span><span class="p">(</span><span class="n">extensionDays</span><span class="p">);</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Calculated extension period of {} days for quote: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                </span><span class="n">extensionDays</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteId</span><span class="p">());</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">extension</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="nf">getComplexityAdjustment</span><span class="p">(</span><span class="n">QuoteComplexity</span><span class="w"> </span><span class="n">complexity</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">complexity</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">SIMPLE</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">MODERATE</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="mi">7</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">COMPLEX</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="mi">14</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="n">VERY_COMPLEX</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="mi">21</span><span class="p">;</span>
<span class="w">        </span><span class="p">};</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="nf">getSeasonalAdjustment</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">LocalDate</span><span class="w"> </span><span class="n">now</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">LocalDate</span><span class="p">.</span><span class="na">now</span><span class="p">();</span>
<span class="w">        </span><span class="n">Month</span><span class="w"> </span><span class="n">month</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">now</span><span class="p">.</span><span class="na">getMonth</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Extend expiration during holiday seasons</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">month</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">Month</span><span class="p">.</span><span class="na">DECEMBER</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">month</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">Month</span><span class="p">.</span><span class="na">JANUARY</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="mi">7</span><span class="p">;</span><span class="w"> </span><span class="c1">// Holiday extension</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Extend during summer vacation period</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">month</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">Month</span><span class="p">.</span><span class="na">JULY</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">month</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">Month</span><span class="p">.</span><span class="na">AUGUST</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="mi">5</span><span class="p">;</span><span class="w"> </span><span class="c1">// Summer extension</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isExtensionJustified</span><span class="p">(</span><span class="n">QuoteExpirationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Check if quote has significant value</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">quoteValue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteValue</span><span class="p">();</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">significantValueThreshold</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">valueOf</span><span class="p">(</span><span class="mi">50000</span><span class="p">);</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">quoteValue</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">significantValueThreshold</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Check if quote involves complex services</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteComplexity</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteComplexity</span><span class="p">.</span><span class="na">COMPLEX</span><span class="w"> </span><span class="o">||</span>
<span class="w">            </span><span class="n">context</span><span class="p">.</span><span class="na">getQuoteComplexity</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">QuoteComplexity</span><span class="p">.</span><span class="na">VERY_COMPLEX</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Check if customer has good standing</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getCustomerStanding</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">CustomerStanding</span><span class="p">.</span><span class="na">EXCELLENT</span><span class="w"> </span><span class="o">||</span>
<span class="w">               </span><span class="n">context</span><span class="p">.</span><span class="na">getCustomerStanding</span><span class="p">()</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="n">CustomerStanding</span><span class="p">.</span><span class="na">GOOD</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="business-rules-engine">Business Rules Engine<a class="headerlink" href="#business-rules-engine" title="Permanent link">&para;</a></h2>
<h3 id="rule-definition">Rule Definition<a class="headerlink" href="#rule-definition" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># business-rules.yml</span>
<span class="nt">business-rules</span><span class="p">:</span>
<span class="w">  </span><span class="nt">pricing</span><span class="p">:</span>
<span class="w">    </span><span class="nt">volume-discounts</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100000</span>
<span class="w">        </span><span class="nt">discount-percentage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;5%</span><span class="nv"> </span><span class="s">discount</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">orders</span><span class="nv"> </span><span class="s">over</span><span class="nv"> </span><span class="s">$100,000&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">500000</span>
<span class="w">        </span><span class="nt">discount-percentage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">8</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;8%</span><span class="nv"> </span><span class="s">discount</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">orders</span><span class="nv"> </span><span class="s">over</span><span class="nv"> </span><span class="s">$500,000&quot;</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000000</span>
<span class="w">        </span><span class="nt">discount-percentage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">12</span>
<span class="w">        </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;12%</span><span class="nv"> </span><span class="s">discount</span><span class="nv"> </span><span class="s">for</span><span class="nv"> </span><span class="s">orders</span><span class="nv"> </span><span class="s">over</span><span class="nv"> </span><span class="s">$1,000,000&quot;</span>

<span class="w">    </span><span class="nt">customer-tier-discounts</span><span class="p">:</span>
<span class="w">      </span><span class="nt">PLATINUM</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">      </span><span class="nt">GOLD</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>
<span class="w">      </span><span class="nt">SILVER</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">      </span><span class="nt">STANDARD</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">0</span>

<span class="w">    </span><span class="nt">minimum-pricing</span><span class="p">:</span>
<span class="w">      </span><span class="nt">WAN_L2_CIR</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span>
<span class="w">      </span><span class="nt">WAN_L2_EVC</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">150</span>
<span class="w">      </span><span class="nt">WAN_L3_IP_QOS</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">200</span>

<span class="w">  </span><span class="nt">validation</span><span class="p">:</span>
<span class="w">    </span><span class="nt">service-compatibility</span><span class="p">:</span>
<span class="w">      </span><span class="nt">conflicting-services</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">WAN_L2_CIR</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">WAN_L3_IP_QOS</span><span class="p p-Indicator">]</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">LEGACY_FRAME_RELAY</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">MODERN_ETHERNET</span><span class="p p-Indicator">]</span>

<span class="w">    </span><span class="nt">geographic-constraints</span><span class="p">:</span>
<span class="w">      </span><span class="nt">rural-surcharge</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">0.15</span>
<span class="w">      </span><span class="nt">remote-locations</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Yukon&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Northwest</span><span class="nv"> </span><span class="s">Territories&quot;</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="s">&quot;Nunavut&quot;</span>

<span class="w">  </span><span class="nt">compliance</span><span class="p">:</span>
<span class="w">    </span><span class="nt">crtc</span><span class="p">:</span>
<span class="w">      </span><span class="nt">accessibility-requirements</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">consumer-protection</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">service-quality-standards</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>

<span class="w">    </span><span class="nt">provincial</span><span class="p">:</span>
<span class="w">      </span><span class="nt">quebec-language-requirements</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">ontario-accessibility-standards</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
</code></pre></div>
<h2 id="configuration-and-customization">Configuration and Customization<a class="headerlink" href="#configuration-and-customization" title="Permanent link">&para;</a></h2>
<h3 id="dynamic-configuration">Dynamic Configuration<a class="headerlink" href="#dynamic-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@ConfigurationProperties</span><span class="p">(</span><span class="n">prefix</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;telus.plugin.quote-modificators&quot;</span><span class="p">)</span>
<span class="nd">@Data</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QuoteModificatorConfiguration</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">enabled</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">ModificatorConfig</span><span class="o">&gt;</span><span class="w"> </span><span class="n">components</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HashMap</span><span class="o">&lt;&gt;</span><span class="p">();</span>

<span class="w">    </span><span class="nd">@PostConstruct</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateConfiguration</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">enabled</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">components</span><span class="p">.</span><span class="na">isEmpty</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IllegalStateException</span><span class="p">(</span><span class="s">&quot;Quote modificators enabled but no components configured&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">components</span><span class="p">.</span><span class="na">forEach</span><span class="p">((</span><span class="n">name</span><span class="p">,</span><span class="w"> </span><span class="n">config</span><span class="p">)</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">config</span><span class="p">.</span><span class="na">isEnabled</span><span class="p">()</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">config</span><span class="p">.</span><span class="na">getPriority</span><span class="p">()</span><span class="w"> </span><span class="o">&lt;=</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IllegalStateException</span><span class="p">(</span><span class="s">&quot;Invalid priority for modificator: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">name</span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">});</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Data</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">class</span> <span class="nc">ModificatorConfig</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">enabled</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">priority</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">100</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">properties</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HashMap</span><span class="o">&lt;&gt;</span><span class="p">();</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">BusinessRule</span><span class="o">&gt;</span><span class="w"> </span><span class="n">rules</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ArrayList</span><span class="o">&lt;&gt;</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Data</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">class</span> <span class="nc">BusinessRule</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">name</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">enabled</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">condition</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">action</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">parameters</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HashMap</span><span class="o">&lt;&gt;</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../03-bandwidth-validators/">Bandwidth Validators →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>