#!/usr/bin/env node
import 'dotenv/config';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  ListToolsRequestSchema,
  CallToolRequestSchema,
  ListResourcesRequestSchema,
  ListResourceTemplatesRequestSchema,
  ReadResourceRequestSchema,
  McpError,
  ErrorCode,
  CallToolRequest,
} from '@modelcontextprotocol/sdk/types.js';
import { AmsApiClient } from './ams-api.js';
import { AmsConfig, Environment, SmartSearchAddressParams } from './types.js';

// Load configuration from environment variables
const config: AmsConfig = {
  clientId: process.env.CLIENT_ID || '',
  clientSecretProd: process.env.CLIENT_SECRET_PROD || '',
  clientSecretNonProd: process.env.CLIENT_SECRET_NONPROD || '',
  environment: (process.env.ENVIRONMENT === 'PROD' ? Environment.PROD : Environment.NON_PROD),
  scope: process.env.SCOPE || '15',
};

// Validate configuration
if (!config.clientId) {
  console.error('[Setup] Error: CLIENT_ID environment variable is required');
  process.exit(1);
}

// Validate client secrets based on environment
if (config.environment === Environment.PROD && !config.clientSecretProd) {
  console.error('[Setup] Error: CLIENT_SECRET_PROD environment variable is required for PROD environment');
  process.exit(1);
}

if (config.environment === Environment.NON_PROD && !config.clientSecretNonProd) {
  console.error('[Setup] Error: CLIENT_SECRET_NONPROD environment variable is required for NON_PROD environment');
  process.exit(1);
}

// Create API client
const amsApiClient = new AmsApiClient(config);

class AmsServer {
  private server: Server;

  constructor() {
    this.server = new Server(
      {
        name: 'mcp-ams',
        version: '0.0.1',
      },
      {
        capabilities: {
          resources: {},
          tools: {},
        },
      }
    );

    this.setupResourceHandlers();
    this.setupToolHandlers();
    
    // Error handling
    this.server.onerror = (error) => console.error('[MCP Error]', error);
    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupResourceHandlers() {
    this.server.setRequestHandler(ListResourcesRequestSchema, async () => ({
      resources: [],
    }));

    this.server.setRequestHandler(ListResourceTemplatesRequestSchema, async () => ({
      resourceTemplates: [],
    }));

    this.server.setRequestHandler(ReadResourceRequestSchema, async (request) => {
      throw new McpError(
        ErrorCode.InvalidRequest,
        `Invalid URI format: ${request.params.uri}`
      );
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'ams_smart_search_address',
          description: 'Search for an address using TELUS AMS smart match',
          inputSchema: {
            type: 'object',
            properties: {
              address: {
                type: 'string',
                description: 'Formatted address (e.g., "2457 BROADMOOR BLVD, SHERWOOD PARK AB")'
              },
              environment: {
                type: 'string',
                enum: ['PROD', 'NON_PROD'],
                description: 'Optional environment override'
              }
            },
            required: ['address']
          }
        }
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request: CallToolRequest) => {
      try {
        switch (request.params.name) {
          case 'ams_smart_search_address': {
            // Safely cast arguments to any first
            const args = request.params.arguments as any;
            
            // Validate address parameter
            if (!args.address || typeof args.address !== 'string') {
              throw new McpError(
                ErrorCode.InvalidParams,
                'Address parameter is required and must be a string'
              );
            }
            
            // Create properly typed params
            const params: SmartSearchAddressParams = {
              address: args.address,
              environment: args.environment
            };
            
            // Convert environment string to enum if provided
            let environment: Environment | undefined;
            if (params.environment) {
              environment = params.environment === 'PROD' ? Environment.PROD : Environment.NON_PROD;
            }
            
            // Call the API
            const addresses = await amsApiClient.smartSearchAddress(params.address, environment);
            
            return {
              content: [{
                type: 'text',
                text: JSON.stringify(addresses, null, 2)
              }]
            };
          }

          default:
            throw new McpError(
              ErrorCode.MethodNotFound,
              `Unknown tool: ${request.params.name}`
            );
        }
      } catch (error: any) {
        console.error('[Tool] Error executing tool:', error);
        
        return {
          content: [{
            type: 'text',
            text: error.message || 'An unknown error occurred'
          }],
          isError: true
        };
      }
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('[Setup] TELUS AMS MCP server running on stdio');
    console.error('[Setup] Environment:', config.environment);
  }
}

const server = new AmsServer();
server.run().catch(console.error);
