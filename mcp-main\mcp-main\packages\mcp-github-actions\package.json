{"name": "@telus/mcp-github-actions", "version": "0.1.0", "description": "MCP server for interacting with GitHub Actions", "keywords": ["telus", "mcp", "github-actions"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-github-actions"}, "license": "MIT", "author": "<PERSON><PERSON> (@foxted)", "type": "module", "main": "dist/index.js", "bin": {"mcp-github-actions": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "prepare": "pnpm run build", "start": "node dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "@octokit/rest": "^21.1.0", "@types/adm-zip": "^0.5.7", "adm-zip": "^0.5.16", "axios": "^1.8.4"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}