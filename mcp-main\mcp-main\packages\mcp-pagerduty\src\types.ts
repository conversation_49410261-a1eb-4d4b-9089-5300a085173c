interface Reference {
    id: string;
    type: string;
    summary: string;
    self: string;
    html_url: string;
}

export interface Incident {
    id: string;
    type: string;
    summary: string;
    self: string;
    html_url: string;
    incident_number: number;
    title: string;
    created_at: string;
    updated_at: string;
    status: 'triggered' | 'acknowledged' | 'resolved';
    incident_key: string;
    service: Reference;
    assignments: Array<{
        assignee: Reference;
    }>;
    assigned_via: 'escalation_policy' | 'direct_assignment';
    last_status_change_at: string;
    resolved_at: string | null;
    first_trigger_log_entry: Reference & {
        html_url: string;
    } | null;
    alert_counts: {
        all: number;
        triggered: number;
        resolved: number;
    };
    is_mergeable: boolean;
    incident_type: {
        name: string;
    };
    escalation_policy: Reference | null;
    teams: Reference[];
    pending_actions: Array<{
        type: string;
        at: string;
        to?: string;
    }>;
    acknowledgements: Array<{
        acknowledger: Reference;
        at: string;
    }>;
    alert_grouping: {
        grouping_type: string;
        started_at: string;
        ended_at: string | null;
        alert_grouping_active: boolean;
    } | null;
    last_status_change_by: Reference | null;
    priority: Reference | null;
    resolve_reason: {
        type: string;
        condition: string;
    } | null;
    conference_bridge: {
        conference_number: string;
        conference_url: string;
    } | null;
    incidents_responders: Array<{
        state: string;
        user: Reference;
        message: string;
        incident: Reference;
    }>;
    responder_requests: Array<{
        requester: Reference;
        requested_at: string;
        message: string;
        incidents: Reference[];
    }>;
    urgency: 'high' | 'low';
    custom_fields?: Array<{
        id: string;
        type: string;
        name: string;
        display_name: string;
        description: string;
        data_type: string;
        field_type: string;
        value: string;
    }>;
}

export interface Service {
    id: string;
    name: string;
    description: string;
    created_at: string;
    status: string;
    html_url: string;
}

export interface User {
    id: string;
    name: string;
    email: string;
    role: string;
    html_url: string;
}

export interface Team {
    id: string;
    name: string;
    description: string;
    html_url: string;
}

export interface PagerDutyResponse<T> {
    data: T[];
    limit: number;
    offset: number;
    total: number | null;
    more: boolean;
}