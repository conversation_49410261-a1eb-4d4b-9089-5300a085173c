{"name": "@telus/mcp-eps", "version": "0.2.1", "description": "MCP server for EPS Payment Management and Payment Method APIs", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"mcp-eps": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "start": "node dist/index.js"}, "keywords": ["mcp", "eps"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-eps"}, "author": "<PERSON>", "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "axios": "^1.8.4", "dotenv": "~16.4.7", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.13.10", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "eslint": "^9", "typescript": "^5.8.2"}}