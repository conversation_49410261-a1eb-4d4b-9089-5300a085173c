# @telus/mcp-guideline

A Generic Guideline MCP Server that provides specific guidelines and requirements for AI to execute within defined boundaries.

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-guideline
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for Claude Desktop):

   ```json
   {
     "mcpServers": {
       "guideline": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-guideline"],
         "env": {}
       }
     }
   }
   ```

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-guideline
   pnpm install
   ```

2. Build the server:

   ```bash
   pnpm build
   ```

3. Start the server:

   ```bash
   pnpm start
   ```

   For development with auto-rebuild:

   ```bash
   pnpm watch
   ```

## Features

This server demonstrates core MCP concepts by providing:

- Prompt for generating code
- Using provided guidelines as inputs
- Prompting the user for input during execution

The sample [typescript-api-guidelines](/guidelines/typescript-api-guidelines.md) file demonstrates sample instructions and requirements for generating a simple Task List Management REST API project.

## Usage

1. Enable the 'guideline' MCP server in your AI assistant
2. Enter the following prompt to generate a new TypeScript REST API project:

   ```
   Use the guidelines MCP Server to generate a TypeScript REST API Project.
   Follow all rules and implement all required instructions.
   ```

3. The AI assistant should prompt you for a GCP Project ID. This value will be used in the configuration files to set the environment variable.

## Debugging

We use the [MCP Inspector](https://github.com/modelcontextprotocol/inspector), which is available as a package script:

```bash
pnpm inspector
```

The Inspector will provide a URL to access debugging tools in your browser.
