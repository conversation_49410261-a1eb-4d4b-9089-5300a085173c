"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimiter = void 0;
const debug_js_1 = require("./debug.js");
const logger = new debug_js_1.DebugLogger('RateLimiter');
class RateLimiter {
    constructor(maxTokens, refillRate) {
        this.maxTokens = maxTokens;
        this.refillRate = refillRate;
        this.tokens = maxTokens;
        this.lastRefill = Date.now();
        logger.info(`RateLimiter initialized with max tokens: ${maxTokens}, refill rate: ${refillRate} tokens/second`);
    }
    async waitForToken() {
        this.refillTokens();
        if (this.tokens < 1) {
            const waitTime = Math.ceil((1 - this.tokens) / this.refillRate * 1000);
            logger.debug(`Rate limit reached. Waiting for ${waitTime}ms`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
            this.refillTokens();
        }
        this.tokens -= 1;
        logger.debug(`Token consumed. Remaining tokens: ${this.tokens}`);
    }
    refillTokens() {
        const now = Date.now();
        const timePassed = (now - this.lastRefill) / 1000;
        const newTokens = timePassed * this.refillRate;
        this.tokens = Math.min(this.maxTokens, this.tokens + newTokens);
        this.lastRefill = now;
        logger.debug(`Tokens refilled. Current tokens: ${this.tokens}`);
    }
}
exports.RateLimiter = RateLimiter;
