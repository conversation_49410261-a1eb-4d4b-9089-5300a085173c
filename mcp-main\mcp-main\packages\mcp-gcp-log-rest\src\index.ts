#!/usr/bin/env node
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from "@modelcontextprotocol/sdk/types.js";
import { GoogleAuth } from "google-auth-library";
import axios from "axios";

// Environment variables will be provided in MCP settings
const RESOURCE_NAMES = process.env.RESOURCE_NAMES ? JSON.parse(process.env.RESOURCE_NAMES) : undefined;

if (!RESOURCE_NAMES || !Array.isArray(RESOURCE_NAMES) || RESOURCE_NAMES.length === 0) {
  throw new Error("RESOURCE_NAMES environment variable is required and must be a non-empty JSON array");
}

console.error("[Config] GCP Logs MCP Server Configuration:");
console.error("[Config] Resource Names:", JSON.stringify(RESOURCE_NAMES));

class GcpLogRestServer {
  private server: Server;
  private auth: GoogleAuth;

  constructor() {
    this.server = new Server(
      {
        name: "gcp-logs-rest-server",
        version: "0.1.0",
      },
      {
        capabilities: {
          tools: {},
        },
      },
    );

    this.auth = new GoogleAuth({
      scopes: ["https://www.googleapis.com/auth/logging.read"],
    });

    this.setupToolHandlers();

    // Error handling
    this.server.onerror = (error) => {
      console.error("[Error] MCP Server error:", error);
      if (error instanceof Error && error.stack) {
        console.error("[Error] Stack trace:", error.stack);
      }
    };
    process.on("SIGINT", async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: "search_logs",
          description:
            "Search GCP logs by keyword, time range, and optional filter",
          inputSchema: {
            type: "object",
            properties: {
              keyword: {
                type: "string",
                description: "Optional: Keyword or text to search for in logs",
              },
              trace_id: {
                type: "string",
                description: "Optional: Dynatrace trace ID to filter logs by",
              },
              startTime: {
                type: "string",
                description:
                  "Start time in ISO format (e.g. 2024-01-20T10:00:00Z) or relative time (e.g. -1h, -30m)",
              },
              endTime: {
                type: "string",
                description:
                  "End time in ISO format (e.g. 2024-01-20T11:00:00Z) or relative time (e.g. now)",
              },
              maxResults: {
                type: "number",
                description: "Maximum number of log entries to return",
                minimum: 1,
                maximum: 1000,
                default: 100,
              },
              filter: {
                type: "string",
                description:
                  "Filter criteria in GCP log filter syntax",
              },
              pod_name: {
                type: "string",
                description: "Optional: Pod name to filter logs by",
              },
              namespace_name: {
                type: "string",
                description: "Optional: Namespace name to filter logs by",
              },
            },
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      if (request.params.name !== "search_logs") {
        throw new McpError(
          ErrorCode.MethodNotFound,
          `Unknown tool: ${request.params.name}`,
        );
      }
      return await this.handleSearchLogs(request.params.arguments);
    });
  }

  private parseTimeInput(
    timeStr: string | undefined,
    defaultValue: Date,
  ): Date {
    if (!timeStr) return defaultValue;

    // Handle relative time formats
    if (timeStr === "now") return new Date();

    const relativeMatch = timeStr.match(/^-(\d+)(h|m)$/);
    if (relativeMatch) {
      const [, amount, unit] = relativeMatch;
      const ms =
        unit === "h" ? parseInt(amount) * 3600000 : parseInt(amount) * 60000;
      return new Date(Date.now() - ms);
    }

    // Handle ISO format
    const date = new Date(timeStr);
    if (isNaN(date.getTime())) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid time format: ${timeStr}`,
      );
    }
    return date;
  }

  private async handleSearchLogs(args: any) {
    const maxResults = args.maxResults || 100;
    const now = new Date();
    const defaultStartTime = new Date(now.getTime() - 3600000); // Default to last hour

    try {
      const startTime = this.parseTimeInput(args.startTime, defaultStartTime);
      const endTime = this.parseTimeInput(args.endTime, now);

      console.error("[Search] Starting log search with parameters:", {
        keyword: args.keyword,
        trace_id: args.trace_id,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        maxResults,
        pod_name: args.pod_name,
        namespace_name: args.namespace_name,
        filter: args.filter
      });

      // Start with empty filter
      let filterConditions = [];

      // Add user's filter if provided
      if (args.filter) {
        filterConditions.push(`(${args.filter})`);
      }

      // Add timestamp constraints
      filterConditions.push(`timestamp >= "${startTime.toISOString()}"`);
      filterConditions.push(`timestamp <= "${endTime.toISOString()}"`);

      // Add keyword search
      if (args.keyword) {
        filterConditions.push(`SEARCH("${args.keyword}")`);
      }

      // Add trace_id filter if provided
      if (args.trace_id) {
        filterConditions.push(`SEARCH("${args.trace_id}")`);
      }

      // Add pod_name filter if provided
      if (args.pod_name) {
        filterConditions.push(`resource.labels.pod_name="${args.pod_name}"`);
      }

      // Add namespace_name filter if provided
      if (args.namespace_name) {
        filterConditions.push(`resource.labels.namespace_name="${args.namespace_name}"`);
      }

      // Join all conditions with AND, or use empty string if no conditions
      const filter = filterConditions.length > 0 ? filterConditions.join(" AND ") : "";

      console.error("[Search] Executing log search with filter:", filter);
      
      const client = await this.auth.getClient();
      const token = await client.getAccessToken();

      const response = await axios.post(
        `https://logging.googleapis.com/v2/entries:list`,
        {
          resourceNames: RESOURCE_NAMES,
          filter: filter,
          orderBy: "timestamp desc",
          pageSize: maxResults
        },
        {
          headers: {
            Authorization: `Bearer ${token.token}`,
            "Content-Type": "application/json"
          }
        }
      );

      const entries = response.data.entries || [];
      console.error(`[Search] Retrieved ${entries.length} log entries`);

      const formattedLogs = entries.map((entry: any) => ({
        timestamp: entry.timestamp,
        message: entry.jsonPayload || entry.textPayload,
        severity: entry.severity,
        labels: entry.labels || {},
        resource: {
          type: entry.resource?.type,
          labels: entry.resource?.labels || {},
        },
      }));

      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(formattedLogs, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error getting logs: ${
              error instanceof Error ? error.message : String(error)
            }`,
          },
        ],
        isError: true,
      };
    }
  }

  async run() {
    try {
      console.error("[Server] Initializing StdioServerTransport");
      const transport = new StdioServerTransport();
      
      console.error("[Server] Connecting to transport");
      await this.server.connect(transport);
      
      console.error("[Server] GCP Logs REST MCP server running on stdio");
    } catch (error) {
      console.error("[Error] Failed to start server:", error);
      throw error;
    }
  }
}

const server = new GcpLogRestServer();
server.run().catch(console.error);
