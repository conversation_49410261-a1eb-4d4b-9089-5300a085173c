
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/frontend/04-api-integration/">
      
      
        <link rel="prev" href="../03-state-management/">
      
      
        <link rel="next" href="../05-development-guide/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>API Integration - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#api-integration" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              API Integration
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#http-client-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      HTTP Client Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="HTTP Client Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#base-http-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Base HTTP Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#service-layer-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Service Layer Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Layer Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#base-api-service" class="md-nav__link">
    <span class="md-ellipsis">
      Base API Service
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#quote-service-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Service Implementation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#location-service-with-google-maps-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Location Service with Google Maps Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#interceptors" class="md-nav__link">
    <span class="md-ellipsis">
      Interceptors
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Interceptors">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#authentication-interceptor" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication Interceptor
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#loading-interceptor" class="md-nav__link">
    <span class="md-ellipsis">
      Loading Interceptor
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#retry-interceptor" class="md-nav__link">
    <span class="md-ellipsis">
      Retry Interceptor
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-handling" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Handling">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#api-error-class" class="md-nav__link">
    <span class="md-ellipsis">
      API Error Class
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#global-error-handler" class="md-nav__link">
    <span class="md-ellipsis">
      Global Error Handler
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#caching-strategies" class="md-nav__link">
    <span class="md-ellipsis">
      Caching Strategies
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Caching Strategies">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#http-cache-service" class="md-nav__link">
    <span class="md-ellipsis">
      HTTP Cache Service
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#api-contracts" class="md-nav__link">
    <span class="md-ellipsis">
      API Contracts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="API Contracts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#requestresponse-models" class="md-nav__link">
    <span class="md-ellipsis">
      Request/Response Models
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#http-client-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      HTTP Client Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="HTTP Client Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#base-http-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Base HTTP Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#environment-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#service-layer-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Service Layer Architecture
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Layer Architecture">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#base-api-service" class="md-nav__link">
    <span class="md-ellipsis">
      Base API Service
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#quote-service-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Service Implementation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#location-service-with-google-maps-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Location Service with Google Maps Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#interceptors" class="md-nav__link">
    <span class="md-ellipsis">
      Interceptors
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Interceptors">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#authentication-interceptor" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication Interceptor
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#loading-interceptor" class="md-nav__link">
    <span class="md-ellipsis">
      Loading Interceptor
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#retry-interceptor" class="md-nav__link">
    <span class="md-ellipsis">
      Retry Interceptor
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-handling" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Handling">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#api-error-class" class="md-nav__link">
    <span class="md-ellipsis">
      API Error Class
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#global-error-handler" class="md-nav__link">
    <span class="md-ellipsis">
      Global Error Handler
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#caching-strategies" class="md-nav__link">
    <span class="md-ellipsis">
      Caching Strategies
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Caching Strategies">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#http-cache-service" class="md-nav__link">
    <span class="md-ellipsis">
      HTTP Cache Service
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#api-contracts" class="md-nav__link">
    <span class="md-ellipsis">
      API Contracts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="API Contracts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#requestresponse-models" class="md-nav__link">
    <span class="md-ellipsis">
      Request/Response Models
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="api-integration">API Integration<a class="headerlink" href="#api-integration" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#http-client-configuration">HTTP Client Configuration</a></li>
<li><a href="#service-layer-architecture">Service Layer Architecture</a></li>
<li><a href="#interceptors">Interceptors</a></li>
<li><a href="#error-handling">Error Handling</a></li>
<li><a href="#caching-strategies">Caching Strategies</a></li>
<li><a href="#api-contracts">API Contracts</a></li>
</ul>
<h2 id="http-client-configuration">HTTP Client Configuration<a class="headerlink" href="#http-client-configuration" title="Permanent link">&para;</a></h2>
<h3 id="base-http-configuration">Base HTTP Configuration<a class="headerlink" href="#base-http-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// http-config.service.ts</span>
<span class="kd">@Injectable</span><span class="p">({</span>
<span class="w">  </span><span class="nx">providedIn</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;root&#39;</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">HttpConfigService</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">baseUrl</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">environment</span><span class="p">.</span><span class="nx">apiBaseUrl</span><span class="p">;</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">timeout</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">environment</span><span class="p">.</span><span class="nx">httpTimeout</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="mf">30000</span><span class="p">;</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="k">private</span><span class="w"> </span><span class="nx">http</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpClient</span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="nx">createHttpOptions</span><span class="p">(</span><span class="nx">options</span><span class="o">?:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">headers?</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpHeaders</span><span class="p">;</span>
<span class="w">    </span><span class="nx">params?</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpParams</span><span class="p">;</span>
<span class="w">    </span><span class="nx">responseType</span><span class="o">?:</span><span class="w"> </span><span class="s1">&#39;json&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;text&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;blob&#39;</span><span class="p">;</span>
<span class="w">  </span><span class="p">})</span><span class="o">:</span><span class="w"> </span><span class="nx">any</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">headers</span><span class="o">:</span><span class="w"> </span><span class="kt">options?.headers</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">getDefaultHeaders</span><span class="p">(),</span>
<span class="w">      </span><span class="nx">params</span><span class="o">:</span><span class="w"> </span><span class="kt">options?.params</span><span class="p">,</span>
<span class="w">      </span><span class="nx">responseType</span><span class="o">:</span><span class="w"> </span><span class="kt">options?.responseType</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s1">&#39;json&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">timeout</span><span class="o">:</span><span class="w"> </span><span class="kt">this.timeout</span>
<span class="w">    </span><span class="p">};</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">getDefaultHeaders</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="nx">HttpHeaders</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">HttpHeaders</span><span class="p">({</span>
<span class="w">      </span><span class="s1">&#39;Content-Type&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;application/json&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="s1">&#39;Accept&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;application/json&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="s1">&#39;X-Requested-With&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;XMLHttpRequest&#39;</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">buildUrl</span><span class="p">(</span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="sb">`</span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">baseUrl</span><span class="si">}${</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">`</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="environment-configuration">Environment Configuration<a class="headerlink" href="#environment-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// environment.ts</span>
<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">environment</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">production</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="p">,</span>
<span class="w">  </span><span class="nx">apiBaseUrl</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;http://localhost:8080/api/v1&#39;</span><span class="p">,</span>
<span class="w">  </span><span class="nx">httpTimeout</span><span class="o">:</span><span class="w"> </span><span class="kt">30000</span><span class="p">,</span>
<span class="w">  </span><span class="nx">retryAttempts</span><span class="o">:</span><span class="w"> </span><span class="kt">3</span><span class="p">,</span>
<span class="w">  </span><span class="nx">retryDelay</span><span class="o">:</span><span class="w"> </span><span class="kt">1000</span><span class="p">,</span>
<span class="w">  </span><span class="nx">cacheTimeout</span><span class="o">:</span><span class="w"> </span><span class="kt">300000</span><span class="p">,</span><span class="w"> </span><span class="c1">// 5 minutes</span>
<span class="w">  </span><span class="nx">endpoints</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;/quotes&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">orders</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;/orders&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">services</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;/services&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">locations</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;/locations&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">customers</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;/customers&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">auth</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;/auth&#39;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">};</span>
</code></pre></div>
<h2 id="service-layer-architecture">Service Layer Architecture<a class="headerlink" href="#service-layer-architecture" title="Permanent link">&para;</a></h2>
<h3 id="base-api-service">Base API Service<a class="headerlink" href="#base-api-service" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// base-api.service.ts</span>
<span class="kd">@Injectable</span><span class="p">()</span>
<span class="k">export</span><span class="w"> </span><span class="k">abstract</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">BaseApiService</span><span class="o">&lt;</span><span class="nx">T</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="k">abstract</span><span class="w"> </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">protected</span><span class="w"> </span><span class="nx">http</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpClient</span><span class="p">,</span>
<span class="w">    </span><span class="k">protected</span><span class="w"> </span><span class="nx">httpConfig</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpConfigService</span><span class="p">,</span>
<span class="w">    </span><span class="k">protected</span><span class="w"> </span><span class="nx">logger</span><span class="o">:</span><span class="w"> </span><span class="kt">LoggerService</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="c1">// Generic CRUD operations</span>
<span class="w">  </span><span class="nx">getAll</span><span class="p">(</span><span class="nx">params?</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpParams</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">T</span><span class="p">[]</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">buildUrl</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">createHttpOptions</span><span class="p">({</span><span class="w"> </span><span class="nx">params</span><span class="w"> </span><span class="p">});</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">get</span><span class="o">&lt;</span><span class="nx">T</span><span class="p">[]</span><span class="o">&gt;</span><span class="p">(</span><span class="nx">url</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">tap</span><span class="p">(</span><span class="nx">data</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">logger</span><span class="p">.</span><span class="nx">debug</span><span class="p">(</span><span class="sb">`Fetched </span><span class="si">${</span><span class="nx">data</span><span class="p">.</span><span class="nx">length</span><span class="si">}</span><span class="sb"> items from </span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">`</span><span class="p">)),</span>
<span class="w">      </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;getAll&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">))</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">getById</span><span class="p">(</span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">T</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">buildUrl</span><span class="p">(</span><span class="sb">`</span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">/</span><span class="si">${</span><span class="nx">id</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">createHttpOptions</span><span class="p">();</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">get</span><span class="o">&lt;</span><span class="nx">T</span><span class="o">&gt;</span><span class="p">(</span><span class="nx">url</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">tap</span><span class="p">(</span><span class="nx">data</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">logger</span><span class="p">.</span><span class="nx">debug</span><span class="p">(</span><span class="sb">`Fetched item </span><span class="si">${</span><span class="nx">id</span><span class="si">}</span><span class="sb"> from </span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">`</span><span class="p">)),</span>
<span class="w">      </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;getById&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">))</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">create</span><span class="p">(</span><span class="nx">item</span><span class="o">:</span><span class="w"> </span><span class="kt">Partial</span><span class="o">&lt;</span><span class="nx">T</span><span class="o">&gt;</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">T</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">buildUrl</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">createHttpOptions</span><span class="p">();</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">post</span><span class="o">&lt;</span><span class="nx">T</span><span class="o">&gt;</span><span class="p">(</span><span class="nx">url</span><span class="p">,</span><span class="w"> </span><span class="nx">item</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">tap</span><span class="p">(</span><span class="nx">data</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">logger</span><span class="p">.</span><span class="nx">debug</span><span class="p">(</span><span class="sb">`Created item in </span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">`</span><span class="p">)),</span>
<span class="w">      </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;create&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">))</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">update</span><span class="p">(</span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">,</span><span class="w"> </span><span class="nx">item</span><span class="o">:</span><span class="w"> </span><span class="kt">Partial</span><span class="o">&lt;</span><span class="nx">T</span><span class="o">&gt;</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">T</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">buildUrl</span><span class="p">(</span><span class="sb">`</span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">/</span><span class="si">${</span><span class="nx">id</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">createHttpOptions</span><span class="p">();</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">put</span><span class="o">&lt;</span><span class="nx">T</span><span class="o">&gt;</span><span class="p">(</span><span class="nx">url</span><span class="p">,</span><span class="w"> </span><span class="nx">item</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">tap</span><span class="p">(</span><span class="nx">data</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">logger</span><span class="p">.</span><span class="nx">debug</span><span class="p">(</span><span class="sb">`Updated item </span><span class="si">${</span><span class="nx">id</span><span class="si">}</span><span class="sb"> in </span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">`</span><span class="p">)),</span>
<span class="w">      </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;update&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">))</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="ow">delete</span><span class="p">(</span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="ow">void</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">buildUrl</span><span class="p">(</span><span class="sb">`</span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">/</span><span class="si">${</span><span class="nx">id</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">createHttpOptions</span><span class="p">();</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="ow">delete</span><span class="o">&lt;</span><span class="ow">void</span><span class="o">&gt;</span><span class="p">(</span><span class="nx">url</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">tap</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">logger</span><span class="p">.</span><span class="nx">debug</span><span class="p">(</span><span class="sb">`Deleted item </span><span class="si">${</span><span class="nx">id</span><span class="si">}</span><span class="sb"> from </span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">`</span><span class="p">)),</span>
<span class="w">      </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;delete&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">))</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="nx">handleError</span><span class="p">(</span><span class="nx">operation</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">any</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">never</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">logger</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="sb">`</span><span class="si">${</span><span class="nx">operation</span><span class="si">}</span><span class="sb"> failed for </span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">`</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">);</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">throwError</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">ApiError</span><span class="p">(</span><span class="nx">error</span><span class="p">,</span><span class="w"> </span><span class="nx">operation</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="p">));</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="quote-service-implementation">Quote Service Implementation<a class="headerlink" href="#quote-service-implementation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// quote.service.ts</span>
<span class="kd">@Injectable</span><span class="p">({</span>
<span class="w">  </span><span class="nx">providedIn</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;root&#39;</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">QuoteService</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">BaseApiService</span><span class="o">&lt;</span><span class="nx">Quote</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="nx">endpoint</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">environment</span><span class="p">.</span><span class="nx">endpoints</span><span class="p">.</span><span class="nx">quotes</span><span class="p">;</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="nx">http</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpClient</span><span class="p">,</span>
<span class="w">    </span><span class="nx">httpConfig</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpConfigService</span><span class="p">,</span>
<span class="w">    </span><span class="nx">logger</span><span class="o">:</span><span class="w"> </span><span class="kt">LoggerService</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">cacheService</span><span class="o">:</span><span class="w"> </span><span class="kt">CacheService</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">super</span><span class="p">(</span><span class="nx">http</span><span class="p">,</span><span class="w"> </span><span class="nx">httpConfig</span><span class="p">,</span><span class="w"> </span><span class="nx">logger</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="c1">// Specialized quote operations</span>
<span class="w">  </span><span class="nx">getQuotes</span><span class="p">(</span><span class="nx">filters?</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteFilters</span><span class="p">,</span><span class="w"> </span><span class="nx">pagination?</span><span class="o">:</span><span class="w"> </span><span class="kt">Pagination</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">QuoteResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">let</span><span class="w"> </span><span class="nx">params</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">HttpParams</span><span class="p">();</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">filters</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">filters</span><span class="p">.</span><span class="nx">status</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">filters</span><span class="p">.</span><span class="nx">status</span><span class="w"> </span><span class="o">!==</span><span class="w"> </span><span class="s1">&#39;all&#39;</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">params</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">params</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;status&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">filters</span><span class="p">.</span><span class="nx">status</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">filters</span><span class="p">.</span><span class="nx">customerId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">params</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">params</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;customerId&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">filters</span><span class="p">.</span><span class="nx">customerId</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">filters</span><span class="p">.</span><span class="nx">dateRange</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">params</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">params</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;startDate&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">filters</span><span class="p">.</span><span class="nx">dateRange</span><span class="p">.</span><span class="nx">start</span><span class="p">.</span><span class="nx">toISOString</span><span class="p">());</span>
<span class="w">        </span><span class="nx">params</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">params</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;endDate&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">filters</span><span class="p">.</span><span class="nx">dateRange</span><span class="p">.</span><span class="nx">end</span><span class="p">.</span><span class="nx">toISOString</span><span class="p">());</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">pagination</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">params</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">params</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;page&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">pagination</span><span class="p">.</span><span class="nx">page</span><span class="p">.</span><span class="nx">toString</span><span class="p">());</span>
<span class="w">      </span><span class="nx">params</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">params</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;pageSize&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">pagination</span><span class="p">.</span><span class="nx">pageSize</span><span class="p">.</span><span class="nx">toString</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">cacheKey</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="sb">`quotes_</span><span class="si">${</span><span class="nx">params</span><span class="p">.</span><span class="nx">toString</span><span class="p">()</span><span class="si">}</span><span class="sb">`</span><span class="p">;</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">cacheService</span><span class="p">.</span><span class="nx">get</span><span class="p">(</span><span class="nx">cacheKey</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">switchMap</span><span class="p">(</span><span class="nx">cached</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">cached</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">return</span><span class="w"> </span><span class="k">of</span><span class="p">(</span><span class="nx">cached</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="kd">const</span><span class="w"> </span><span class="nx">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">buildUrl</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="p">);</span>
<span class="w">        </span><span class="kd">const</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">createHttpOptions</span><span class="p">({</span><span class="w"> </span><span class="nx">params</span><span class="w"> </span><span class="p">});</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">get</span><span class="o">&lt;</span><span class="nx">QuoteResponse</span><span class="o">&gt;</span><span class="p">(</span><span class="nx">url</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">          </span><span class="nx">tap</span><span class="p">(</span><span class="nx">response</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">cacheService</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="nx">cacheKey</span><span class="p">,</span><span class="w"> </span><span class="nx">response</span><span class="p">,</span><span class="w"> </span><span class="mf">300000</span><span class="p">)),</span><span class="w"> </span><span class="c1">// 5 min cache</span>
<span class="w">          </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;getQuotes&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">))</span>
<span class="w">        </span><span class="p">);</span>
<span class="w">      </span><span class="p">})</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">createQuote</span><span class="p">(</span><span class="nx">quoteRequest</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteRequest</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">Quote</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">buildUrl</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">createHttpOptions</span><span class="p">();</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">post</span><span class="o">&lt;</span><span class="nx">Quote</span><span class="o">&gt;</span><span class="p">(</span><span class="nx">url</span><span class="p">,</span><span class="w"> </span><span class="nx">quoteRequest</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">tap</span><span class="p">(</span><span class="nx">quote</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">logger</span><span class="p">.</span><span class="nx">info</span><span class="p">(</span><span class="sb">`Quote created: </span><span class="si">${</span><span class="nx">quote</span><span class="p">.</span><span class="nx">id</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">cacheService</span><span class="p">.</span><span class="nx">invalidatePattern</span><span class="p">(</span><span class="s1">&#39;quotes_*&#39;</span><span class="p">);</span>
<span class="w">      </span><span class="p">}),</span>
<span class="w">      </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;createQuote&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">))</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">approveQuote</span><span class="p">(</span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">,</span><span class="w"> </span><span class="nx">approvalData</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteApproval</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">Quote</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">buildUrl</span><span class="p">(</span><span class="sb">`</span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">/</span><span class="si">${</span><span class="nx">quoteId</span><span class="si">}</span><span class="sb">/approve`</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">createHttpOptions</span><span class="p">();</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">post</span><span class="o">&lt;</span><span class="nx">Quote</span><span class="o">&gt;</span><span class="p">(</span><span class="nx">url</span><span class="p">,</span><span class="w"> </span><span class="nx">approvalData</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">tap</span><span class="p">(</span><span class="nx">quote</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">logger</span><span class="p">.</span><span class="nx">info</span><span class="p">(</span><span class="sb">`Quote approved: </span><span class="si">${</span><span class="nx">quote</span><span class="p">.</span><span class="nx">id</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">cacheService</span><span class="p">.</span><span class="nx">invalidatePattern</span><span class="p">(</span><span class="s1">&#39;quotes_*&#39;</span><span class="p">);</span>
<span class="w">      </span><span class="p">}),</span>
<span class="w">      </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;approveQuote&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">))</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">exportQuote</span><span class="p">(</span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">,</span><span class="w"> </span><span class="nx">format</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;pdf&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;excel&#39;</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">Blob</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">buildUrl</span><span class="p">(</span><span class="sb">`</span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">/</span><span class="si">${</span><span class="nx">quoteId</span><span class="si">}</span><span class="sb">/export`</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">params</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">HttpParams</span><span class="p">().</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;format&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">format</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">createHttpOptions</span><span class="p">({</span>
<span class="w">      </span><span class="nx">params</span><span class="p">,</span>
<span class="w">      </span><span class="nx">responseType</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;blob&#39;</span><span class="w"> </span><span class="kr">as</span><span class="w"> </span><span class="s1">&#39;json&#39;</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">get</span><span class="p">(</span><span class="nx">url</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;exportQuote&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">))</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">searchQuotes</span><span class="p">(</span><span class="nx">query</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">Quote</span><span class="p">[]</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">buildUrl</span><span class="p">(</span><span class="sb">`</span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">/search`</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">params</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">HttpParams</span><span class="p">().</span><span class="nx">set</span><span class="p">(</span><span class="s1">&#39;q&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">query</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">createHttpOptions</span><span class="p">({</span><span class="w"> </span><span class="nx">params</span><span class="w"> </span><span class="p">});</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">get</span><span class="o">&lt;</span><span class="nx">Quote</span><span class="p">[]</span><span class="o">&gt;</span><span class="p">(</span><span class="nx">url</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">debounceTime</span><span class="p">(</span><span class="mf">300</span><span class="p">),</span>
<span class="w">      </span><span class="nx">distinctUntilChanged</span><span class="p">(),</span>
<span class="w">      </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;searchQuotes&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">))</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="location-service-with-google-maps-integration">Location Service with Google Maps Integration<a class="headerlink" href="#location-service-with-google-maps-integration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// location.service.ts</span>
<span class="kd">@Injectable</span><span class="p">({</span>
<span class="w">  </span><span class="nx">providedIn</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;root&#39;</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">LocationService</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">BaseApiService</span><span class="o">&lt;</span><span class="nx">Location</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">protected</span><span class="w"> </span><span class="nx">endpoint</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">environment</span><span class="p">.</span><span class="nx">endpoints</span><span class="p">.</span><span class="nx">locations</span><span class="p">;</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="nx">http</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpClient</span><span class="p">,</span>
<span class="w">    </span><span class="nx">httpConfig</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpConfigService</span><span class="p">,</span>
<span class="w">    </span><span class="nx">logger</span><span class="o">:</span><span class="w"> </span><span class="kt">LoggerService</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">mapsService</span><span class="o">:</span><span class="w"> </span><span class="kt">GoogleMapsService</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">super</span><span class="p">(</span><span class="nx">http</span><span class="p">,</span><span class="w"> </span><span class="nx">httpConfig</span><span class="p">,</span><span class="w"> </span><span class="nx">logger</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">searchAddresses</span><span class="p">(</span><span class="nx">query</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">Location</span><span class="p">[]</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Combine Google Maps API with backend validation</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">mapsService</span><span class="p">.</span><span class="nx">searchAddresses</span><span class="p">(</span><span class="nx">query</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">switchMap</span><span class="p">(</span><span class="nx">googleResults</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">const</span><span class="w"> </span><span class="nx">addresses</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">googleResults</span><span class="p">.</span><span class="nx">map</span><span class="p">(</span><span class="nx">result</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">          </span><span class="nx">address</span><span class="o">:</span><span class="w"> </span><span class="kt">this.parseGoogleAddress</span><span class="p">(</span><span class="nx">result</span><span class="p">),</span>
<span class="w">          </span><span class="nx">coordinates</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nx">latitude</span><span class="o">:</span><span class="w"> </span><span class="kt">result.geometry.location.lat</span><span class="p">(),</span>
<span class="w">            </span><span class="nx">longitude</span><span class="o">:</span><span class="w"> </span><span class="kt">result.geometry.location.lng</span><span class="p">()</span>
<span class="w">          </span><span class="p">}</span>
<span class="w">        </span><span class="p">}));</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">validateAddresses</span><span class="p">(</span><span class="nx">addresses</span><span class="p">);</span>
<span class="w">      </span><span class="p">}),</span>
<span class="w">      </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;searchAddresses&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">))</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">checkServiceAvailability</span><span class="p">(</span><span class="nx">address</span><span class="o">:</span><span class="w"> </span><span class="kt">Address</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">ServiceAvailability</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">buildUrl</span><span class="p">(</span><span class="sb">`</span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">/service-availability`</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">createHttpOptions</span><span class="p">();</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">post</span><span class="o">&lt;</span><span class="nx">ServiceAvailability</span><span class="o">&gt;</span><span class="p">(</span><span class="nx">url</span><span class="p">,</span><span class="w"> </span><span class="nx">address</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">catchError</span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">(</span><span class="s1">&#39;checkServiceAvailability&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">))</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">validateAddresses</span><span class="p">(</span><span class="nx">addresses</span><span class="o">:</span><span class="w"> </span><span class="kt">Partial</span><span class="o">&lt;</span><span class="nx">Location</span><span class="o">&gt;</span><span class="p">[])</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">Location</span><span class="p">[]</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">url</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">buildUrl</span><span class="p">(</span><span class="sb">`</span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">/validate`</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">options</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">httpConfig</span><span class="p">.</span><span class="nx">createHttpOptions</span><span class="p">();</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">post</span><span class="o">&lt;</span><span class="nx">Location</span><span class="p">[]</span><span class="o">&gt;</span><span class="p">(</span><span class="nx">url</span><span class="p">,</span><span class="w"> </span><span class="nx">addresses</span><span class="p">,</span><span class="w"> </span><span class="nx">options</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">parseGoogleAddress</span><span class="p">(</span><span class="nx">result</span><span class="o">:</span><span class="w"> </span><span class="kt">google.maps.places.PlaceResult</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Address</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">components</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">result</span><span class="p">.</span><span class="nx">address_components</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="p">[];</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">street</span><span class="o">:</span><span class="w"> </span><span class="kt">this.getAddressComponent</span><span class="p">(</span><span class="nx">components</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;street_number&#39;</span><span class="p">)</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s1">&#39; &#39;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span>
<span class="w">              </span><span class="k">this</span><span class="p">.</span><span class="nx">getAddressComponent</span><span class="p">(</span><span class="nx">components</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;route&#39;</span><span class="p">),</span>
<span class="w">      </span><span class="nx">city</span><span class="o">:</span><span class="w"> </span><span class="kt">this.getAddressComponent</span><span class="p">(</span><span class="nx">components</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;locality&#39;</span><span class="p">),</span>
<span class="w">      </span><span class="nx">province</span><span class="o">:</span><span class="w"> </span><span class="kt">this.getAddressComponent</span><span class="p">(</span><span class="nx">components</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;administrative_area_level_1&#39;</span><span class="p">),</span>
<span class="w">      </span><span class="nx">postalCode</span><span class="o">:</span><span class="w"> </span><span class="kt">this.getAddressComponent</span><span class="p">(</span><span class="nx">components</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;postal_code&#39;</span><span class="p">),</span>
<span class="w">      </span><span class="nx">country</span><span class="o">:</span><span class="w"> </span><span class="kt">this.getAddressComponent</span><span class="p">(</span><span class="nx">components</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;country&#39;</span><span class="p">)</span>
<span class="w">    </span><span class="p">};</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">getAddressComponent</span><span class="p">(</span><span class="nx">components</span><span class="o">:</span><span class="w"> </span><span class="kt">google.maps.GeocoderAddressComponent</span><span class="p">[],</span><span class="w"> </span><span class="kr">type</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">component</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">components</span><span class="p">.</span><span class="nx">find</span><span class="p">(</span><span class="nx">c</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">c</span><span class="p">.</span><span class="nx">types</span><span class="p">.</span><span class="nx">includes</span><span class="p">(</span><span class="kr">type</span><span class="p">));</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">component</span><span class="w"> </span><span class="o">?</span><span class="w"> </span><span class="nx">component</span><span class="p">.</span><span class="nx">long_name</span><span class="w"> </span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;&#39;</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="interceptors">Interceptors<a class="headerlink" href="#interceptors" title="Permanent link">&para;</a></h2>
<h3 id="authentication-interceptor">Authentication Interceptor<a class="headerlink" href="#authentication-interceptor" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// auth.interceptor.ts</span>
<span class="kd">@Injectable</span><span class="p">()</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">AuthInterceptor</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">HttpInterceptor</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">authService</span><span class="o">:</span><span class="w"> </span><span class="kt">AuthService</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">router</span><span class="o">:</span><span class="w"> </span><span class="kt">Router</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="nx">intercept</span><span class="p">(</span><span class="nx">req</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpRequest</span><span class="o">&lt;</span><span class="nx">any</span><span class="o">&gt;</span><span class="p">,</span><span class="w"> </span><span class="nx">next</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpHandler</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">HttpEvent</span><span class="o">&lt;</span><span class="nx">any</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Skip authentication for public endpoints</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">isPublicEndpoint</span><span class="p">(</span><span class="nx">req</span><span class="p">.</span><span class="nx">url</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">next</span><span class="p">.</span><span class="nx">handle</span><span class="p">(</span><span class="nx">req</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">token</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">authService</span><span class="p">.</span><span class="nx">getToken</span><span class="p">();</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">token</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">authReq</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">req</span><span class="p">.</span><span class="nx">clone</span><span class="p">({</span>
<span class="w">        </span><span class="nx">setHeaders</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nx">Authorization</span><span class="o">:</span><span class="w"> </span><span class="sb">`Bearer </span><span class="si">${</span><span class="nx">token</span><span class="si">}</span><span class="sb">`</span><span class="p">,</span>
<span class="w">          </span><span class="s1">&#39;X-User-ID&#39;</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">authService</span><span class="p">.</span><span class="nx">getCurrentUserId</span><span class="p">(),</span>
<span class="w">          </span><span class="s1">&#39;X-Session-ID&#39;</span><span class="o">:</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">authService</span><span class="p">.</span><span class="nx">getSessionId</span><span class="p">()</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">});</span>

<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">next</span><span class="p">.</span><span class="nx">handle</span><span class="p">(</span><span class="nx">authReq</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">        </span><span class="nx">catchError</span><span class="p">((</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpErrorResponse</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">.</span><span class="nx">status</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="mf">401</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">this</span><span class="p">.</span><span class="nx">handleUnauthorized</span><span class="p">();</span>
<span class="w">          </span><span class="p">}</span>
<span class="w">          </span><span class="k">return</span><span class="w"> </span><span class="nx">throwError</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">error</span><span class="p">);</span>
<span class="w">        </span><span class="p">})</span>
<span class="w">      </span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">handleUnauthorized</span><span class="p">();</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">throwError</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="ne">Error</span><span class="p">(</span><span class="s1">&#39;No authentication token&#39;</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">isPublicEndpoint</span><span class="p">(</span><span class="nx">url</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">publicEndpoints</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;/auth/login&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;/auth/refresh&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;/health&#39;</span><span class="p">];</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">publicEndpoints</span><span class="p">.</span><span class="nx">some</span><span class="p">(</span><span class="nx">endpoint</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">url</span><span class="p">.</span><span class="nx">includes</span><span class="p">(</span><span class="nx">endpoint</span><span class="p">));</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">handleUnauthorized</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">authService</span><span class="p">.</span><span class="nx">logout</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">router</span><span class="p">.</span><span class="nx">navigate</span><span class="p">([</span><span class="s1">&#39;/login&#39;</span><span class="p">]);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="loading-interceptor">Loading Interceptor<a class="headerlink" href="#loading-interceptor" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// loading.interceptor.ts</span>
<span class="kd">@Injectable</span><span class="p">()</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">LoadingInterceptor</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">HttpInterceptor</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">activeRequests</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span><span class="p">;</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="k">private</span><span class="w"> </span><span class="nx">loadingService</span><span class="o">:</span><span class="w"> </span><span class="kt">LoadingService</span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="nx">intercept</span><span class="p">(</span><span class="nx">req</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpRequest</span><span class="o">&lt;</span><span class="nx">any</span><span class="o">&gt;</span><span class="p">,</span><span class="w"> </span><span class="nx">next</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpHandler</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">HttpEvent</span><span class="o">&lt;</span><span class="nx">any</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Skip loading indicator for background requests</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">req</span><span class="p">.</span><span class="nx">headers</span><span class="p">.</span><span class="nx">has</span><span class="p">(</span><span class="s1">&#39;X-Skip-Loading&#39;</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">next</span><span class="p">.</span><span class="nx">handle</span><span class="p">(</span><span class="nx">req</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">activeRequests</span><span class="o">++</span><span class="p">;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">loadingService</span><span class="p">.</span><span class="nx">setLoading</span><span class="p">(</span><span class="kc">true</span><span class="p">);</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">next</span><span class="p">.</span><span class="nx">handle</span><span class="p">(</span><span class="nx">req</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">finalize</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">activeRequests</span><span class="o">--</span><span class="p">;</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">activeRequests</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="mf">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">this</span><span class="p">.</span><span class="nx">loadingService</span><span class="p">.</span><span class="nx">setLoading</span><span class="p">(</span><span class="kc">false</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">})</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="retry-interceptor">Retry Interceptor<a class="headerlink" href="#retry-interceptor" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// retry.interceptor.ts</span>
<span class="kd">@Injectable</span><span class="p">()</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">RetryInterceptor</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">HttpInterceptor</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="k">private</span><span class="w"> </span><span class="nx">logger</span><span class="o">:</span><span class="w"> </span><span class="kt">LoggerService</span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="nx">intercept</span><span class="p">(</span><span class="nx">req</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpRequest</span><span class="o">&lt;</span><span class="nx">any</span><span class="o">&gt;</span><span class="p">,</span><span class="w"> </span><span class="nx">next</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpHandler</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">HttpEvent</span><span class="o">&lt;</span><span class="nx">any</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">maxRetries</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">environment</span><span class="p">.</span><span class="nx">retryAttempts</span><span class="p">;</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">retryDelay</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">environment</span><span class="p">.</span><span class="nx">retryDelay</span><span class="p">;</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">next</span><span class="p">.</span><span class="nx">handle</span><span class="p">(</span><span class="nx">req</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">retryWhen</span><span class="p">(</span><span class="nx">errors</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">        </span><span class="nx">errors</span><span class="p">.</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">          </span><span class="nx">scan</span><span class="p">((</span><span class="nx">retryCount</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">retryCount</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="nx">maxRetries</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="o">!</span><span class="k">this</span><span class="p">.</span><span class="nx">shouldRetry</span><span class="p">(</span><span class="nx">error</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="k">throw</span><span class="w"> </span><span class="nx">error</span><span class="p">;</span>
<span class="w">            </span><span class="p">}</span>

<span class="w">            </span><span class="k">this</span><span class="p">.</span><span class="nx">logger</span><span class="p">.</span><span class="nx">warn</span><span class="p">(</span><span class="sb">`Retrying request (</span><span class="si">${</span><span class="nx">retryCount</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mf">1</span><span class="si">}</span><span class="sb">/</span><span class="si">${</span><span class="nx">maxRetries</span><span class="si">}</span><span class="sb">): </span><span class="si">${</span><span class="nx">req</span><span class="p">.</span><span class="nx">url</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="nx">retryCount</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mf">1</span><span class="p">;</span>
<span class="w">          </span><span class="p">},</span><span class="w"> </span><span class="mf">0</span><span class="p">),</span>
<span class="w">          </span><span class="nx">delay</span><span class="p">(</span><span class="nx">retryDelay</span><span class="p">)</span>
<span class="w">        </span><span class="p">)</span>
<span class="w">      </span><span class="p">)</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">shouldRetry</span><span class="p">(</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpErrorResponse</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Retry on network errors and 5xx server errors</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="o">!</span><span class="nx">error</span><span class="p">.</span><span class="nx">status</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">.</span><span class="nx">status</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mf">500</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">status</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">600</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="error-handling">Error Handling<a class="headerlink" href="#error-handling" title="Permanent link">&para;</a></h2>
<h3 id="api-error-class">API Error Class<a class="headerlink" href="#api-error-class" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// api-error.model.ts</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">ApiError</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="ne">Error</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">public</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">Date</span><span class="p">;</span>
<span class="w">  </span><span class="k">public</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">operation</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="k">public</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="k">public</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">statusCode?</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="k">public</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">details?</span><span class="o">:</span><span class="w"> </span><span class="kt">any</span><span class="p">;</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">any</span><span class="p">,</span>
<span class="w">    </span><span class="nx">operation</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">,</span>
<span class="w">    </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">super</span><span class="p">(</span><span class="nx">ApiError</span><span class="p">.</span><span class="nx">extractMessage</span><span class="p">(</span><span class="nx">error</span><span class="p">));</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;ApiError&#39;</span><span class="p">;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">timestamp</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">operation</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">operation</span><span class="p">;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">endpoint</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">endpoint</span><span class="p">;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">statusCode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">error</span><span class="o">?</span><span class="p">.</span><span class="nx">status</span><span class="p">;</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">details</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">error</span><span class="o">?</span><span class="p">.</span><span class="nx">error</span><span class="p">;</span>

<span class="w">    </span><span class="c1">// Maintain proper stack trace</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="ne">Error</span><span class="p">.</span><span class="nx">captureStackTrace</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="ne">Error</span><span class="p">.</span><span class="nx">captureStackTrace</span><span class="p">(</span><span class="k">this</span><span class="p">,</span><span class="w"> </span><span class="nx">ApiError</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">static</span><span class="w"> </span><span class="nx">extractMessage</span><span class="p">(</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">any</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="o">?</span><span class="p">.</span><span class="nx">error</span><span class="o">?</span><span class="p">.</span><span class="nx">message</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">error</span><span class="p">.</span><span class="nx">message</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="o">?</span><span class="p">.</span><span class="nx">message</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">message</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="o">?</span><span class="p">.</span><span class="nx">statusText</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">statusText</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="s1">&#39;An unknown error occurred&#39;</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">public</span><span class="w"> </span><span class="nx">toJSON</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">name</span><span class="o">:</span><span class="w"> </span><span class="kt">this.name</span><span class="p">,</span>
<span class="w">      </span><span class="nx">message</span><span class="o">:</span><span class="w"> </span><span class="kt">this.message</span><span class="p">,</span>
<span class="w">      </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">this.timestamp</span><span class="p">,</span>
<span class="w">      </span><span class="nx">operation</span><span class="o">:</span><span class="w"> </span><span class="kt">this.operation</span><span class="p">,</span>
<span class="w">      </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="kt">this.endpoint</span><span class="p">,</span>
<span class="w">      </span><span class="nx">statusCode</span><span class="o">:</span><span class="w"> </span><span class="kt">this.statusCode</span><span class="p">,</span>
<span class="w">      </span><span class="nx">details</span><span class="o">:</span><span class="w"> </span><span class="kt">this.details</span>
<span class="w">    </span><span class="p">};</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="global-error-handler">Global Error Handler<a class="headerlink" href="#global-error-handler" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// global-error-handler.service.ts</span>
<span class="kd">@Injectable</span><span class="p">({</span>
<span class="w">  </span><span class="nx">providedIn</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;root&#39;</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">GlobalErrorHandler</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">ErrorHandler</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">notificationService</span><span class="o">:</span><span class="w"> </span><span class="kt">NotificationService</span><span class="p">,</span>
<span class="w">    </span><span class="k">private</span><span class="w"> </span><span class="nx">logger</span><span class="o">:</span><span class="w"> </span><span class="kt">LoggerService</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="nx">handleError</span><span class="p">(</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">any</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="ow">instanceof</span><span class="w"> </span><span class="nx">ApiError</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">handleApiError</span><span class="p">(</span><span class="nx">error</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="ow">instanceof</span><span class="w"> </span><span class="nx">HttpErrorResponse</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">handleHttpError</span><span class="p">(</span><span class="nx">error</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">handleGenericError</span><span class="p">(</span><span class="nx">error</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">handleApiError</span><span class="p">(</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">ApiError</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">logger</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;API Error&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">toJSON</span><span class="p">());</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">userMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">getUserFriendlyMessage</span><span class="p">(</span><span class="nx">error</span><span class="p">);</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">notificationService</span><span class="p">.</span><span class="nx">showError</span><span class="p">(</span><span class="nx">userMessage</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">handleHttpError</span><span class="p">(</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpErrorResponse</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">logger</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;HTTP Error&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">status</span><span class="o">:</span><span class="w"> </span><span class="kt">error.status</span><span class="p">,</span>
<span class="w">      </span><span class="nx">statusText</span><span class="o">:</span><span class="w"> </span><span class="kt">error.statusText</span><span class="p">,</span>
<span class="w">      </span><span class="nx">url</span><span class="o">:</span><span class="w"> </span><span class="kt">error.url</span><span class="p">,</span>
<span class="w">      </span><span class="nx">message</span><span class="o">:</span><span class="w"> </span><span class="kt">error.message</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="kd">let</span><span class="w"> </span><span class="nx">userMessage</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>

<span class="w">    </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">.</span><span class="nx">status</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="nx">400</span><span class="o">:</span>
<span class="w">        </span><span class="kt">userMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;Invalid request. Please check your input.&#39;</span><span class="p">;</span>
<span class="w">        </span><span class="k">break</span><span class="p">;</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="nx">401</span><span class="o">:</span>
<span class="w">        </span><span class="kt">userMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;You are not authorized. Please log in again.&#39;</span><span class="p">;</span>
<span class="w">        </span><span class="k">break</span><span class="p">;</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="nx">403</span><span class="o">:</span>
<span class="w">        </span><span class="kt">userMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;You do not have permission to perform this action.&#39;</span><span class="p">;</span>
<span class="w">        </span><span class="k">break</span><span class="p">;</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="nx">404</span><span class="o">:</span>
<span class="w">        </span><span class="kt">userMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;The requested resource was not found.&#39;</span><span class="p">;</span>
<span class="w">        </span><span class="k">break</span><span class="p">;</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="nx">500</span><span class="o">:</span>
<span class="w">        </span><span class="kt">userMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;A server error occurred. Please try again later.&#39;</span><span class="p">;</span>
<span class="w">        </span><span class="k">break</span><span class="p">;</span>
<span class="w">      </span><span class="nx">default</span><span class="o">:</span>
<span class="w">        </span><span class="kt">userMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;An unexpected error occurred. Please try again.&#39;</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">notificationService</span><span class="p">.</span><span class="nx">showError</span><span class="p">(</span><span class="nx">userMessage</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">handleGenericError</span><span class="p">(</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">any</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">logger</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Unexpected Error&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">);</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">notificationService</span><span class="p">.</span><span class="nx">showError</span><span class="p">(</span><span class="s1">&#39;An unexpected error occurred.&#39;</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">getUserFriendlyMessage</span><span class="p">(</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">ApiError</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Map technical errors to user-friendly messages</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">errorMappings</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="p">[</span><span class="nx">key</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">]</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="s1">&#39;QUOTE_NOT_FOUND&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;The requested quote could not be found.&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="s1">&#39;INVALID_LOCATION&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;The specified location is not valid.&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="s1">&#39;SERVICE_UNAVAILABLE&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;The requested service is not available at this location.&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="s1">&#39;DISCOUNT_EXPIRED&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;The discount code has expired.&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="s1">&#39;INSUFFICIENT_PERMISSIONS&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;You do not have permission to perform this action.&#39;</span>
<span class="w">    </span><span class="p">};</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">errorMappings</span><span class="p">[</span><span class="nx">error</span><span class="p">.</span><span class="nx">details</span><span class="o">?</span><span class="p">.</span><span class="nx">code</span><span class="p">]</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">message</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="caching-strategies">Caching Strategies<a class="headerlink" href="#caching-strategies" title="Permanent link">&para;</a></h2>
<h3 id="http-cache-service">HTTP Cache Service<a class="headerlink" href="#http-cache-service" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// cache.service.ts</span>
<span class="kd">@Injectable</span><span class="p">({</span>
<span class="w">  </span><span class="nx">providedIn</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;root&#39;</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">CacheService</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">cache</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Map</span><span class="o">&lt;</span><span class="kt">string</span><span class="p">,</span><span class="w"> </span><span class="nx">CacheEntry</span><span class="o">&gt;</span><span class="p">();</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">defaultTtl</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">environment</span><span class="p">.</span><span class="nx">cacheTimeout</span><span class="p">;</span>

<span class="w">  </span><span class="nx">get</span><span class="o">&lt;</span><span class="nx">T</span><span class="o">&gt;</span><span class="p">(</span><span class="nx">key</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">T</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">entry</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">cache</span><span class="p">.</span><span class="nx">get</span><span class="p">(</span><span class="nx">key</span><span class="p">);</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">entry</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="k">of</span><span class="p">(</span><span class="kc">null</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">isExpired</span><span class="p">(</span><span class="nx">entry</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">cache</span><span class="p">.</span><span class="ow">delete</span><span class="p">(</span><span class="nx">key</span><span class="p">);</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="k">of</span><span class="p">(</span><span class="kc">null</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">of</span><span class="p">(</span><span class="nx">entry</span><span class="p">.</span><span class="nx">data</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">set</span><span class="o">&lt;</span><span class="nx">T</span><span class="o">&gt;</span><span class="p">(</span><span class="nx">key</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">,</span><span class="w"> </span><span class="nx">data</span><span class="o">:</span><span class="w"> </span><span class="kt">T</span><span class="p">,</span><span class="w"> </span><span class="nx">ttl</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">defaultTtl</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">entry</span><span class="o">:</span><span class="w"> </span><span class="kt">CacheEntry</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">data</span><span class="p">,</span>
<span class="w">      </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">Date.now</span><span class="p">(),</span>
<span class="w">      </span><span class="nx">ttl</span>
<span class="w">    </span><span class="p">};</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">cache</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="nx">key</span><span class="p">,</span><span class="w"> </span><span class="nx">entry</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">invalidate</span><span class="p">(</span><span class="nx">key</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">cache</span><span class="p">.</span><span class="ow">delete</span><span class="p">(</span><span class="nx">key</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">invalidatePattern</span><span class="p">(</span><span class="nx">pattern</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">regex</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">RegExp</span><span class="p">(</span><span class="nx">pattern</span><span class="p">.</span><span class="nx">replace</span><span class="p">(</span><span class="s1">&#39;*&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;.*&#39;</span><span class="p">));</span>

<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">const</span><span class="w"> </span><span class="nx">key</span><span class="w"> </span><span class="k">of</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">cache</span><span class="p">.</span><span class="nx">keys</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">regex</span><span class="p">.</span><span class="nx">test</span><span class="p">(</span><span class="nx">key</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="nx">cache</span><span class="p">.</span><span class="ow">delete</span><span class="p">(</span><span class="nx">key</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">clear</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">cache</span><span class="p">.</span><span class="nx">clear</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">isExpired</span><span class="p">(</span><span class="nx">entry</span><span class="o">:</span><span class="w"> </span><span class="kt">CacheEntry</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">()</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nx">entry</span><span class="p">.</span><span class="nx">timestamp</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="nx">entry</span><span class="p">.</span><span class="nx">ttl</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>

<span class="kd">interface</span><span class="w"> </span><span class="nx">CacheEntry</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">data</span><span class="o">:</span><span class="w"> </span><span class="kt">any</span><span class="p">;</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">ttl</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="api-contracts">API Contracts<a class="headerlink" href="#api-contracts" title="Permanent link">&para;</a></h2>
<h3 id="requestresponse-models">Request/Response Models<a class="headerlink" href="#requestresponse-models" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// API contract interfaces</span>
<span class="k">export</span><span class="w"> </span><span class="kd">interface</span><span class="w"> </span><span class="nx">QuoteRequest</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">customerId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">serviceType</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">location</span><span class="o">:</span><span class="w"> </span><span class="kt">Address</span><span class="p">;</span>
<span class="w">  </span><span class="nx">requirements</span><span class="o">:</span><span class="w"> </span><span class="kt">ServiceRequirements</span><span class="p">;</span>
<span class="w">  </span><span class="nx">timeline</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">startDate</span><span class="o">:</span><span class="w"> </span><span class="kt">Date</span><span class="p">;</span>
<span class="w">    </span><span class="nx">endDate?</span><span class="o">:</span><span class="w"> </span><span class="kt">Date</span><span class="p">;</span>
<span class="w">  </span><span class="p">};</span>
<span class="p">}</span>

<span class="k">export</span><span class="w"> </span><span class="kd">interface</span><span class="w"> </span><span class="nx">QuoteResponse</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">quotes</span><span class="o">:</span><span class="w"> </span><span class="kt">Quote</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">page</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">pageSize</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="p">}</span>

<span class="k">export</span><span class="w"> </span><span class="kd">interface</span><span class="w"> </span><span class="nx">ServiceAvailability</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">available</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="p">;</span>
<span class="w">  </span><span class="nx">services</span><span class="o">:</span><span class="w"> </span><span class="kt">AvailableService</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">limitations?</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">estimatedInstallDate?</span><span class="o">:</span><span class="w"> </span><span class="kt">Date</span><span class="p">;</span>
<span class="p">}</span>

<span class="k">export</span><span class="w"> </span><span class="kd">interface</span><span class="w"> </span><span class="nx">PriceCalculation</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">subtotal</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">discounts</span><span class="o">:</span><span class="w"> </span><span class="kt">AppliedDiscount</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">taxes</span><span class="o">:</span><span class="w"> </span><span class="kt">Tax</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">total</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">breakdown</span><span class="o">:</span><span class="w"> </span><span class="kt">PriceBreakdown</span><span class="p">[];</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../05-development-guide/">Development Guide →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>