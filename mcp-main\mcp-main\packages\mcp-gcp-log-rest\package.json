{"name": "@telus/mcp-gcp-log-rest", "version": "0.0.4", "description": "leverage gcp logging rest api endpoint, grab logs from logsink for any gcp project", "keywords": ["telus", "mcp", "gcp", "log", "gcp-log-rest"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-gcp-log-rest"}, "license": "MIT", "author": "<PERSON> (@chengli-telus)", "type": "module", "main": "dist/index.js", "bin": {"mcp-gcp-log-rest": "dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node --eval \"import('fs').then(fs => fs.chmodSync('dist/index.js', '755'))\"", "prepare": "pnpm run build", "start": "node dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "axios": "^1.8.4", "google-auth-library": "^9.15.1"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}