#!/usr/bin/env node
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
  Request,
} from "@modelcontextprotocol/sdk/types.js";
import { GoogleAuth } from "google-auth-library";
import axios from "axios";

interface QueryMetricsArgs {
  metricType: string;
  startTime: string;
  endTime: string;
  filter?: string;
  pageSize?: number;
  aggregation?: {
    alignmentPeriod?: string;
    perSeriesAligner?: string;
    crossSeriesReducer?: string;
  };
}

// Environment variables will be provided in MCP settings
const PROJECT_ID = process.env.PROJECT_ID;

if (!PROJECT_ID) {
  throw new Error("PROJECT_ID environment variable is required");
}

console.error("[Config] GCP Metrics MCP Server Configuration:");
console.error("[Config] Project ID:", PROJECT_ID);

class GcpMetricsServer {
  private server: Server;
  private auth: GoogleAuth;

  constructor() {
    this.server = new Server(
      {
        name: "gcp-metrics-server",
        version: "0.1.0",
      },
      {
        capabilities: {
          tools: {},
        },
      },
    );

    this.auth = new GoogleAuth({
      scopes: ["https://www.googleapis.com/auth/monitoring.read"],
    });

    this.setupToolHandlers();

    // Error handling
    this.server.onerror = (error: unknown) => {
      console.error("[Error] MCP Server error:", error);
      if (error instanceof Error && error.stack) {
        console.error("[Error] Stack trace:", error.stack);
      }
    };
    process.on("SIGINT", async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: "query_metrics",
          description: "Query GCP monitoring metrics",
          inputSchema: {
            type: "object",
            properties: {
              metricType: {
                type: "string",
                description: "The metric type to query (e.g., 'compute.googleapis.com/instance/cpu/utilization')",
              },
              startTime: {
                type: "string",
                description: "Start time in ISO format (e.g. 2024-01-20T10:00:00Z) or relative time (e.g. -1h, -30m)",
              },
              endTime: {
                type: "string",
                description: "End time in ISO format (e.g. 2024-01-20T11:00:00Z) or relative time (e.g. now)",
              },
              filter: {
                type: "string",
                description: "Optional: Filter expression for the metrics",
              },
              aggregation: {
                type: "object",
                description: "Optional: Aggregation parameters",
                properties: {
                  alignmentPeriod: { type: "string" },
                  perSeriesAligner: { type: "string" },
                  crossSeriesReducer: { type: "string" },
                },
              },
              pageSize: {
                type: "number",
                description: "Maximum number of log entries to return",
                minimum: 1,
                maximum: 1000,
                default: 100,
              },
            },
            required: ["metricType", "startTime", "endTime"],
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request: Request) => {
      if (!request.params || request.params.name !== "query_metrics") {
        throw new McpError(
          ErrorCode.MethodNotFound,
          `Unknown tool: ${request.params?.name || 'undefined'}`,
        );
      }
      
      const args = request.params.arguments as unknown;
      if (this.isQueryMetricsArgs(args)) {
        return await this.handleQueryMetrics(args);
      } else {
        throw new McpError(
          ErrorCode.InvalidParams,
          "Invalid arguments for query_metrics",
        );
      }
    });
  }

  private isQueryMetricsArgs(args: unknown): args is QueryMetricsArgs {
    return (
      typeof args === "object" &&
      args !== null &&
      "metricType" in args &&
      "startTime" in args &&
      "endTime" in args
    );
  }

  private parseTimeInput(
    timeStr: string | undefined,
    defaultValue: Date,
  ): Date {
    if (!timeStr) return defaultValue;

    // Handle relative time formats
    if (timeStr === "now") return new Date();

    const relativeMatch = timeStr.match(/^-(\d+)(h|m)$/);
    if (relativeMatch) {
      const [, amount, unit] = relativeMatch;
      const ms =
        unit === "h" ? parseInt(amount) * 3600000 : parseInt(amount) * 60000;
      return new Date(Date.now() - ms);
    }

    // Handle ISO format
    const date = new Date(timeStr);
    if (isNaN(date.getTime())) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid time format: ${timeStr}`,
      );
    }
    return date;
  }

  private async handleQueryMetrics(args: QueryMetricsArgs) {
    const now = new Date();
    const defaultStartTime = new Date(now.getTime() - 3600000); // Default to last hour

    try {
      const startTime = this.parseTimeInput(args.startTime, defaultStartTime);
      const endTime = this.parseTimeInput(args.endTime, now);

      console.error("[Query] Starting metrics query with parameters:", {
        metricType: args.metricType,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        filter: args.filter,
        aggregation: args.aggregation,
      });

      const filter = `metric.type="${args.metricType}"${args.filter ? ` AND ${args.filter}` : ''}` 
      
      const client = await this.auth.getClient();
      const token = await client.getAccessToken();
     
      const name = `projects/${PROJECT_ID}`;
      const url = `https://monitoring.googleapis.com/v3/${name}/timeSeries`;
      console.error("[Query] Full URL:", url);

      const params: Record<string, any> = {
        filter: filter,
        'interval.startTime': startTime.toISOString(),
        'interval.endTime': endTime.toISOString(),
        'name': name,
        'pageSize': args.pageSize || 100
      };

      if (args.aggregation) {
        if (args.aggregation.alignmentPeriod) {
          params['aggregation.alignmentPeriod'] = args.aggregation.alignmentPeriod;
        }
        if (args.aggregation.perSeriesAligner) {
          params['aggregation.perSeriesAligner'] = args.aggregation.perSeriesAligner;
        }
        if (args.aggregation.crossSeriesReducer) {
          params['aggregation.crossSeriesReducer'] = args.aggregation.crossSeriesReducer;
        }
      }

      const response = await axios.get(url, {
        params,
        headers: {
          Authorization: `Bearer ${token.token}`,
          "Content-Type": "application/json",
        },
      });

      const timeSeries = response.data.timeSeries || [];
      console.error(`[Query] Retrieved ${timeSeries.length} time series`);

      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(timeSeries, null, 2),
          },
        ],
      };
    } catch (error: unknown) {
      console.error("[Error] Failed to query metrics:", error);
      
      // Handle axios error
      if (axios.isAxiosError(error) && error.response) {
        console.error("[Error] Response data:", error.response.data);
        console.error("[Error] Response status:", error.response.status);
        return {
          content: [
            {
              type: "text",
              text: `Error querying metrics: ${error.response.data?.error?.message || error.message}`,
            },
          ],
          isError: true,
        };
      }
      
      // Handle other errors
      return {
        content: [
          {
            type: "text",
            text: `Error querying metrics: ${error instanceof Error ? error.message : String(error)}`,
          },
        ],
        isError: true,
      };
    }
  }

  async run() {
    try {
      console.error("[Server] Initializing StdioServerTransport");
      const transport = new StdioServerTransport();
      
      console.error("[Server] Connecting to transport");
      await this.server.connect(transport);
      
      console.error("[Server] GCP Metrics MCP server running on stdio");
    } catch (error) {
      console.error("[Error] Failed to start server:", error);
      throw error;
    }
  }
}

const server = new GcpMetricsServer();
server.run().catch(console.error);
