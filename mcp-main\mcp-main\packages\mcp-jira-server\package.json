{"name": "@telus/mcp-jira-server", "version": "0.5.0", "description": "Jira Server (on-prem) MCP server for TELUS", "keywords": ["telus", "mcp", "jira", "server", "on-prem"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-jira-server"}, "license": "MIT", "author": "<PERSON> (@jonathan-dunn-telus)", "type": "module", "main": "dist/index.js", "bin": {"jira-server": "./dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node --eval \"import('fs').then(fs => fs.chmodSync('dist/index.js', '755'))\"", "inspector": "pnpm dlx @modelcontextprotocol/inspector dist/index.js", "prepare": "pnpm run build", "watch": "tsc --watch"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "axios": "^1.8.4", "tunnel": "^0.0.6"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}