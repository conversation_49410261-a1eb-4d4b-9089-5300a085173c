import { AxiosInstance } from 'axios';

export interface PaymentMethodsHandlerConfig {
  axiosInstance: AxiosInstance;
  baseUrl: string;
}

export class PaymentMethodsHandler {
  private axiosInstance: AxiosInstance;
  private paymentMethodBaseUrl: string;

  constructor({ axiosInstance, baseUrl }: PaymentMethodsHandlerConfig) {
    this.axiosInstance = axiosInstance;
    this.paymentMethodBaseUrl = `${baseUrl}/common/epsPaymentMethod/v1`;
  }

  async getOnePaymentMethod(paymentMethodId: string, headers: Record<string, string>) {
    const response = await this.axiosInstance.get(
      `${this.paymentMethodBaseUrl}/paymentMethod/${paymentMethodId}`,
      { headers }
    );

    return response.data;
  }
}
