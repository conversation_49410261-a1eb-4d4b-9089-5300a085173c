# TELUS MCP: Agentic AI Tools
Welcome to the TELUS Model Context Protocol (MCP) Monorepo! This repository contains a collection of tools designed to enhance AI interactions and boost productivity.

## ℹ️ Experimental MCP Servers

MCP servers with versions below 1.0.0 are considered experimental or beta. While you can safely experiment with these servers:

- They may have incomplete features or documentation
- APIs might change between minor versions
- Bugs or unexpected behavior may occur

You can check a server's version in its package.json file or in the [MCP Servers Directory](https://mcp.ai.telus.com).

**We strongly encourage you to try these experimental servers, provide feedback and contribute to help improve them!** Your testing and input are valuable for making these tools production-ready.

## 📚 Documentation

- [Cline Installation Guide](docs/cline-installation.md) - Setup instructions for Cline in VS Code, including VPN configuration
- [Changeset Workflow](docs/changeset-workflow.md) - Detailed information about our versioning and release process
- [MCP Servers Directory](https://mcp.ai.telus.com) - Complete list of available MCP servers and documentation

## 🔑 Setup & Installation

### 1. Access TELUS GitHub Packages

1. [Create a GitHub Personal Access Token with read:packages scope](https://github.com/settings/tokens/new?scopes=read:packages&description=Github%20Packages%20Read)
2. Authorize the token with TELUS SAML provider
    1. Click "Configure SSO" next to the newly generated token
    2. Click "Authorize" on the right of the TELUS org
3. Configure your .npmrc file
```bash
npm config set @telus:registry https://npm.pkg.github.com
npm config set //npm.pkg.github.com/:_authToken YOUR_PAT_HERE
# OR with pnpm
pnpm config set @telus:registry https://npm.pkg.github.com
pnpm config set //npm.pkg.github.com/:_authToken YOUR_PAT_HERE
```

### 2. Using MCP Servers with Cline

```bash
# Install an MCP server
npx -y @telus/mcp-[package-name]
# OR with pnpm
pnpm dlx @telus/mcp-[package-name]
```

Add to your Cline/Claude Desktop MCP settings:

```json
{
  "mcpServers": {
    "[server-name]": {
      "command": "npx",
      "args": ["-y", "@telus/mcp-[package-name]"],
      "env": {
        // Any required environment variables
      }
    }
  }
}
```

## 👨‍💻 Development

### Quick Start

```bash
# Install dependencies and build
npm install -g pnpm
pnpm install
pnpm build

# Create a new MCP server
pnpm generate:mcp
```

### Available Commands

| Command | Description |
|---------|-------------|
| `pnpm build` | Builds all packages |
| `pnpm prepare` | Runs build before publishing |
| `pnpm start` | Starts development servers |
| `pnpm changeset` | Create a new changeset |
| `pnpm version-packages` | Bump versions and update changelogs |
| `pnpm release` | Publish packages to the registry |
| `pnpm generate:mcp` | Create a new MCP server in the packages directory |

## 🤝 Contributing

We welcome contributions! Please join us in #g-ai-dev to get involved.

## 📄 License

This project is licensed under the [MIT License](LICENSE).
