import { z } from 'zod';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { KongClient } from '@telus/sdk-kong-auth';
import NodeCache from 'node-cache';

const API_MARKETPLACE_URL = 'https://apigw-pr.telus.com/engagedparty/apiMarketplaceExperience/v1/';
const { TELUS_EMAIL } = process.env;
const apiCache = new NodeCache({ checkperiod: 600 });

async function searchMarketplaceApis<T> (queryString: string, client: KongClient): Promise<T | null> {
  try {
    if(apiCache.getStats().keys === 0) {
      const { data } = await client.get(
        `${API_MARKETPLACE_URL}/apis/search`,
        {
          headers: {
            'x-user-id': TELUS_EMAIL,
          }
        },
      );
      apiCache.mset(data.map((api: any) => {
        return {
          key: api.name,
          val: [
            `name: ${api.name}`,
            `id: ${api.id}`,
            `spec:  ${api.design_url}`,
            `active: ${api.is_active}`,
            `description: ${api.description}`,
            `link: https://api-marketplace.cloudapps.telus.com/v1/apis/${api.id}`,
          ].join('\n')
        }
      }));
    }

    const matchingKeys = apiCache.keys().filter(key => key.includes(queryString));

    const results = apiCache.mget(matchingKeys);

    const apiText = `${JSON.stringify(results)}`

    return (apiText as T);
  } catch (err) {
    console.error('Error making API Marketplace request:', err);
    return ({ message: (err as Error).message } as T);
  }
}

async function getMarketplaceApiSpecification<T>(id: string, client: KongClient): Promise<T | null> {
  try {
    const { data } = await client.get(`${API_MARKETPLACE_URL}/apis/${id}/design-file`,
      {
        headers: {
          'x-user-id': TELUS_EMAIL,
        }
      }
    );
    return (JSON.stringify(data) as T);
  } catch (err) {
    console.error('Error making API Marketplace request:', err);
    return ({ message: (err as Error).message } as T);
  }
}

export const searchApis = (server: McpServer, client: KongClient) => {
  return server.tool(
    'search-apis',
    'search the TELUS API marketplace for an API',
    {
      queryString: z.string(),
    },
    async ({ queryString }) => {

      const apiData = await searchMarketplaceApis(queryString, client);

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(apiData),
          }
        ]
      }
    },
  );
};

export const getApiSpecification = (server: McpServer, client: KongClient) => {
  server.tool(
    'get-api-specification',
    'get a TELUS API swagger specification from the API Marketplace',
    {
      id: z.string().uuid(),
    },
    async ({ id }) => {
      const specData = await getMarketplaceApiSpecification(id, client);

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(specData),
          }
        ],
      }
    },
  );
};
