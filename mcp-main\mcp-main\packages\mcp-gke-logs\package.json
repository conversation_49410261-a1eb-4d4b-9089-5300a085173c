{"name": "@telus/mcp-gke-logs", "version": "0.0.9", "description": "GKE logs MCP server for TELUS", "keywords": ["telus", "mcp", "gcp", "log", "gke", "kubernetes"], "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-gke-logs"}, "license": "MIT", "author": "<PERSON> (@robert-widdis)", "type": "module", "main": "dist/index.js", "bin": {"gcp-logs-server": "./dist/index.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsc && node --eval \"import('fs').then(fs => fs.chmodSync('dist/index.js', '755'))\"", "inspector": "pnpm dlx @modelcontextprotocol/inspector dist/index.js", "prepare": "pnpm run build", "watch": "tsc --watch"}, "dependencies": {"@google-cloud/logging": "^11.2.0", "@modelcontextprotocol/sdk": "^1.7.0"}, "devDependencies": {"@types/node": "^22.13.10", "typescript": "^5.8.2"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}