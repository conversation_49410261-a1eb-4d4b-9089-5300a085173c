import path from "path";
import { fileURLToPath } from "url";
import fs from "fs";
import os from "os";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Repository URLs for different protocols
const SSH_REPOSITORY_URL = "**************:telus/universal-design-system.git";
const HTTPS_REPOSITORY_URL = "https://github.com/telus/universal-design-system.git";

/**
 * Checks if SSH is likely configured by looking for common SSH key files
 * @returns {boolean} True if SSH keys are detected, false otherwise
 */
const isSSHConfigured = (): boolean => {
  try {
    // Get home directory
    const homeDir = os.homedir();
    const sshDir = path.join(homeDir, '.ssh');
    
    // If .ssh directory doesn't exist, SSH is likely not configured
    if (!fs.existsSync(sshDir)) return false;
    
    // Check for common key files
    const commonKeyFiles = ['id_rsa', 'id_ed25519', 'id_ecdsa', 'id_dsa'];
    const hasKeys = commonKeyFiles.some(file => 
      fs.existsSync(path.join(sshDir, file))
    );
    
    return hasKeys;
  } catch (error) {
    // If any error occurs during detection, assume SSH is not configured
    console.error('Error detecting SSH configuration:', error);
    return false;
  }
};

// Allow override via environment variable
const forceProtocol = process.env.GIT_PROTOCOL?.toLowerCase();

// Determine which URL to use
let REPOSITORY_URL = SSH_REPOSITORY_URL;

if (forceProtocol === 'https') {
  REPOSITORY_URL = HTTPS_REPOSITORY_URL;
} else if (forceProtocol === 'ssh') {
  REPOSITORY_URL = SSH_REPOSITORY_URL;
} else if (!isSSHConfigured()) {
  // If SSH is not configured and no override is specified, default to HTTPS
  REPOSITORY_URL = HTTPS_REPOSITORY_URL;
}
const DIRECTORY_PATH = path.join(__dirname, "universal-design-system");
const COMPONENTS_PATH = path.join(
  DIRECTORY_PATH,
  "packages",
  "docusaurus-plugin-component-docs-pages",
  "docs",
);
const DEFAULT_MIME_TYPE = "text/markdown";

export { 
  DIRECTORY_PATH, 
  REPOSITORY_URL, 
  COMPONENTS_PATH, 
  DEFAULT_MIME_TYPE,
  SSH_REPOSITORY_URL,
  HTTPS_REPOSITORY_URL
};
