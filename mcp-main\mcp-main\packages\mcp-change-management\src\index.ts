#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import { GitHubAPI } from './github-api.js';
import { ITSMAPI } from './itsm-api.js';
import { EChangeAPI } from './echange-api.js';

interface GetChangesArgs {
  startDate?: string;
  endDate?: string;
  source?: 'github' | 'itsm' | 'echange' | 'all';
}

class ChangeManagementServer {
  private server: Server;
  private github: GitHubAPI;
  private itsm: ITSMAPI;
  private echange: EChangeAPI;

  constructor() {
    this.server = new Server(
      {
        name: 'change-management',
        version: '0.3.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.github = new GitHubAPI();
    this.itsm = new ITSMAPI();
    this.echange = new EChangeAPI();
    this.setupToolHandlers();
    
    this.server.onerror = (error) => console.error('[MCP Error]', error);
    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'get_changes',
          description: 'Get changes from GitHub issues and/or ITSM within a specified time frame',
          inputSchema: {
            type: 'object',
            properties: {
              startDate: {
                type: 'string',
                description: 'Start date in ISO 8601 format (YYYY-MM-DD), required for GitHub changes',
              },
              endDate: {
                type: 'string',
                description: 'End date in ISO 8601 format (YYYY-MM-DD), required for GitHub changes',
              },
              source: {
                type: 'string',
                enum: ['github', 'itsm', 'echange', 'all'],
                description: 'Source of changes (github, itsm, echange, or all)',
                default: 'all',
              },
            },
            required: ['source'],
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      if (request.params.name !== 'get_changes') {
        throw new McpError(
          ErrorCode.MethodNotFound,
          `Unknown tool: ${request.params.name}`
        );
      }

      const args = request.params.arguments as unknown as GetChangesArgs;
      
      // Validate input
      const source = args.source || 'all';
      if (source === 'github' || source === 'all') {
        if (!args.startDate || !args.endDate) {
          throw new McpError(
            ErrorCode.InvalidParams,
            'Both startDate and endDate are required for GitHub changes'
          );
        }
      }
      return this.getChanges(args.startDate, args.endDate, source);
    });
  }

  private async getChanges(startDate?: string, endDate?: string, source: 'github' | 'itsm' | 'echange' | 'all' = 'all') {
    try {
      let githubChanges: any[] = [];
      let itsmChanges: any[] = [];
      let echangeChanges: any[] = [];

      if (source === 'github' || source === 'all') {
        if (!startDate || !endDate) {
          throw new Error('Start and end dates are required for GitHub changes');
        }
        const start = new Date(startDate);
        const end = new Date(endDate);
        console.error(`Fetching GitHub changes from ${start.toISOString()} to ${end.toISOString()}`);
        githubChanges = await this.github.getChanges(start, end);
      }

      if (source === 'itsm' || source === 'all') {
        const start = startDate ? new Date(startDate) : undefined;
        const end = endDate ? new Date(endDate) : undefined;
        console.error(`Fetching ITSM changes${start ? ` from ${start.toISOString()}` : ''}${end ? ` to ${end.toISOString()}` : ''}`);
        const startTime = Date.now();
        try {
          itsmChanges = await this.itsm.getChanges(start, end);
          console.error(`ITSM changes fetched in ${Date.now() - startTime}ms`);
        } catch (error) {
          console.error(`ITSM fetch failed after ${Date.now() - startTime}ms:`, error);
          throw error;
        }
      }

      if (source === 'echange' || source === 'all') {
        const start = startDate ? new Date(startDate) : undefined;
        const end = endDate ? new Date(endDate) : undefined;
        console.error(`Fetching eChange changes${start ? ` from ${start.toISOString()}` : ''}${end ? ` to ${end.toISOString()}` : ''}`);
        const startTime = Date.now();
        try {
          echangeChanges = await this.echange.getChanges(start, end);
          console.error(`eChange changes fetched in ${Date.now() - startTime}ms`);
        } catch (error) {
          console.error(`eChange fetch failed after ${Date.now() - startTime}ms:`, error);
          throw error;
        }
      }

      const allChanges = {
        github: githubChanges,
        itsm: itsmChanges,
        echange: echangeChanges,
      };

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(allChanges, null, 2),
          },
        ],
      };
    } catch (error: any) {
      console.error('Error fetching changes:', error);
      return {
        content: [
          {
            type: 'text',
            text: error.message,
          },
        ],
        isError: true,
      };
    }
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Change Management MCP server running on stdio (GitHub + ITSM + eChange)');
  }
}

const server = new ChangeManagementServer();
server.run().catch(console.error);
