
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/extensions/03-bandwidth-validators/">
      
      
        <link rel="prev" href="../02-quote-modificators/">
      
      
        <link rel="next" href="../04-state-validators/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Bandwidth Validators - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#bandwidth-validators" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Bandwidth Validators
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" checked>
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bandwidth-validation-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Bandwidth Validation Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Bandwidth Validation Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#validation-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#validation-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#teluswanl2cirbandwidthvalidator" class="md-nav__link">
    <span class="md-ellipsis">
      TelusWanL2CirBandwidthValidator
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusWanL2CirBandwidthValidator">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#l2-cir-service-validation" class="md-nav__link">
    <span class="md-ellipsis">
      L2 CIR Service Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#teluswanl2evcbandwidthvalidator" class="md-nav__link">
    <span class="md-ellipsis">
      TelusWanL2EvcBandwidthValidator
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusWanL2EvcBandwidthValidator">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#l2-evc-service-validation" class="md-nav__link">
    <span class="md-ellipsis">
      L2 EVC Service Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#teluswanl3ipqosbandwidthvalidator" class="md-nav__link">
    <span class="md-ellipsis">
      TelusWanL3IpQosBandwidthValidator
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusWanL3IpQosBandwidthValidator">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#l3-ip-qos-service-validation" class="md-nav__link">
    <span class="md-ellipsis">
      L3 IP QoS Service Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#validation-rules-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Rules Engine
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Validation Rules Engine">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rule-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Rule Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-and-customization" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration and Customization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration and Customization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#dynamic-validator-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Dynamic Validator Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bandwidth-validation-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Bandwidth Validation Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Bandwidth Validation Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#validation-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#validation-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#teluswanl2cirbandwidthvalidator" class="md-nav__link">
    <span class="md-ellipsis">
      TelusWanL2CirBandwidthValidator
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusWanL2CirBandwidthValidator">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#l2-cir-service-validation" class="md-nav__link">
    <span class="md-ellipsis">
      L2 CIR Service Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#teluswanl2evcbandwidthvalidator" class="md-nav__link">
    <span class="md-ellipsis">
      TelusWanL2EvcBandwidthValidator
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusWanL2EvcBandwidthValidator">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#l2-evc-service-validation" class="md-nav__link">
    <span class="md-ellipsis">
      L2 EVC Service Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#teluswanl3ipqosbandwidthvalidator" class="md-nav__link">
    <span class="md-ellipsis">
      TelusWanL3IpQosBandwidthValidator
    </span>
  </a>
  
    <nav class="md-nav" aria-label="TelusWanL3IpQosBandwidthValidator">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#l3-ip-qos-service-validation" class="md-nav__link">
    <span class="md-ellipsis">
      L3 IP QoS Service Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#validation-rules-engine" class="md-nav__link">
    <span class="md-ellipsis">
      Validation Rules Engine
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Validation Rules Engine">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rule-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Rule Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration-and-customization" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration and Customization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration and Customization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#dynamic-validator-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Dynamic Validator Configuration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="bandwidth-validators">Bandwidth Validators<a class="headerlink" href="#bandwidth-validators" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#bandwidth-validation-overview">Bandwidth Validation Overview</a></li>
<li><a href="#teluswanl2cirbandwidthvalidator">TelusWanL2CirBandwidthValidator</a></li>
<li><a href="#teluswanl2evcbandwidthvalidator">TelusWanL2EvcBandwidthValidator</a></li>
<li><a href="#teluswanl3ipqosbandwidthvalidator">TelusWanL3IpQosBandwidthValidator</a></li>
<li><a href="#validation-rules-engine">Validation Rules Engine</a></li>
<li><a href="#configuration-and-customization">Configuration and Customization</a></li>
</ul>
<h2 id="bandwidth-validation-overview">Bandwidth Validation Overview<a class="headerlink" href="#bandwidth-validation-overview" title="Permanent link">&para;</a></h2>
<h3 id="validation-architecture">Validation Architecture<a class="headerlink" href="#validation-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Bandwidth Validation Pipeline&quot;
        A[Service Item] --&gt; B[Validator Registry]
        B --&gt; C[Service Type Matcher]
        C --&gt; D[Appropriate Validator]
        D --&gt; E[Validation Logic]
        E --&gt; F[Validation Result]
    end

    subgraph &quot;Validator Types&quot;
        G[L2 CIR Validator]
        H[L2 EVC Validator]
        I[L3 QoS Validator]
        J[Custom Validators]
    end

    subgraph &quot;Validation Rules&quot;
        K[Bandwidth Limits]
        L[CIR/EIR Ratios]
        M[QoS Classes]
        N[Geographic Constraints]
    end

    subgraph &quot;Service Types&quot;
        O[WAN_L2_CIR]
        P[WAN_L2_EVC]
        Q[WAN_L3_IP_QOS]
        R[INTERNET_ACCESS]
    end

    D --&gt; G
    D --&gt; H
    D --&gt; I
    D --&gt; J

    E --&gt; K
    E --&gt; L
    E --&gt; M
    E --&gt; N

    C --&gt; O
    C --&gt; P
    C --&gt; Q
    C --&gt; R

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style D fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style E fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="validation-flow">Validation Flow<a class="headerlink" href="#validation-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant QM as Quote Modificator
    participant VR as Validator Registry
    participant BV as Bandwidth Validator
    participant VE as Validation Engine
    participant CR as Configuration Repository

    QM-&gt;&gt;VR: validateBandwidth(serviceItem)
    VR-&gt;&gt;VR: findApplicableValidator(serviceType)
    VR-&gt;&gt;BV: validate(context)

    BV-&gt;&gt;CR: getValidationRules(serviceType)
    CR--&gt;&gt;BV: ValidationRules

    BV-&gt;&gt;VE: validateBandwidthLimits(bandwidth, rules)
    VE--&gt;&gt;BV: LimitValidationResult

    BV-&gt;&gt;VE: validateCirEirRatio(cir, eir)
    VE--&gt;&gt;BV: RatioValidationResult

    BV-&gt;&gt;VE: validateQosParameters(qosClass, bandwidth)
    VE--&gt;&gt;BV: QosValidationResult

    BV-&gt;&gt;BV: aggregateResults()
    BV--&gt;&gt;VR: ValidationResult
    VR--&gt;&gt;QM: ValidationResult
</code></pre></div>
<h2 id="teluswanl2cirbandwidthvalidator">TelusWanL2CirBandwidthValidator<a class="headerlink" href="#teluswanl2cirbandwidthvalidator" title="Permanent link">&para;</a></h2>
<h3 id="l2-cir-service-validation">L2 CIR Service Validation<a class="headerlink" href="#l2-cir-service-validation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusWanL2CirBandwidthValidator</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">BandwidthValidator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">SERVICE_TYPE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;WAN_L2_CIR&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">SUPPORTED_SERVICE_TYPES</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="n">SERVICE_TYPE</span><span class="p">);</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">BandwidthValidatorUtils</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ValidationUtils</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">PluginConfigurationProperties</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="nf">TelusWanL2CirBandwidthValidator</span><span class="p">(</span>
<span class="w">            </span><span class="n">BandwidthValidatorUtils</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">,</span>
<span class="w">            </span><span class="n">ValidationUtils</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">,</span>
<span class="w">            </span><span class="n">PluginConfigurationProperties</span><span class="w"> </span><span class="n">config</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">bandwidthUtils</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">validationUtils</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">;</span>
<span class="w">        </span><span class="k">this</span><span class="p">.</span><span class="na">config</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validate</span><span class="p">(</span><span class="n">BandwidthValidationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Validating L2 CIR bandwidth for service item: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">());</span>

<span class="w">        </span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">serviceItem</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItem</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">isWanL2CirService</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">notApplicable</span><span class="p">(</span><span class="s">&quot;Service type not supported: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">serviceItem</span><span class="p">.</span><span class="na">getServiceType</span><span class="p">());</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">serviceItemId</span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">validatorName</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="na">getClass</span><span class="p">().</span><span class="na">getSimpleName</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Extract CIR and EIR parameters</span>
<span class="w">            </span><span class="n">BandwidthParameters</span><span class="w"> </span><span class="n">params</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractBandwidthParameters</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate CIR bandwidth limits</span>
<span class="w">            </span><span class="n">validateCirBandwidthLimits</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate EIR bandwidth limits</span>
<span class="w">            </span><span class="n">validateEirBandwidthLimits</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate CIR/EIR ratio</span>
<span class="w">            </span><span class="n">validateCirEirRatio</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate port speed compatibility</span>
<span class="w">            </span><span class="n">validatePortSpeedCompatibility</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate geographic constraints</span>
<span class="w">            </span><span class="n">validateGeographicConstraints</span><span class="p">(</span><span class="n">context</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate service level agreements</span>
<span class="w">            </span><span class="n">validateServiceLevelAgreements</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;L2 CIR validation completed for service item: {} - Valid: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                     </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">(),</span><span class="w"> </span><span class="n">result</span><span class="p">.</span><span class="na">isValid</span><span class="p">());</span>

<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">result</span><span class="p">;</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error validating L2 CIR bandwidth for service item: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                     </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">resultBuilder</span>
<span class="w">                </span><span class="p">.</span><span class="na">addError</span><span class="p">(</span><span class="s">&quot;VALIDATION_ERROR&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Bandwidth validation failed: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">BandwidthParameters</span><span class="w"> </span><span class="nf">extractBandwidthParameters</span><span class="p">(</span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">serviceItem</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">characteristics</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">serviceItem</span><span class="p">.</span><span class="na">getServiceCharacteristics</span><span class="p">();</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">BandwidthParameters</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">cir</span><span class="p">(</span><span class="n">extractBandwidthValue</span><span class="p">(</span><span class="n">characteristics</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;CIR&quot;</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">eir</span><span class="p">(</span><span class="n">extractBandwidthValue</span><span class="p">(</span><span class="n">characteristics</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;EIR&quot;</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">portSpeed</span><span class="p">(</span><span class="n">extractBandwidthValue</span><span class="p">(</span><span class="n">characteristics</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;PORT_SPEED&quot;</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">unit</span><span class="p">(</span><span class="n">extractBandwidthUnit</span><span class="p">(</span><span class="n">characteristics</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">qosClass</span><span class="p">(</span><span class="n">extractQosClass</span><span class="p">(</span><span class="n">characteristics</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">serviceLocation</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">.</span><span class="na">getServiceLocation</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateCirBandwidthLimits</span><span class="p">(</span><span class="n">BandwidthParameters</span><span class="w"> </span><span class="n">params</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                          </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="kd">var</span><span class="w"> </span><span class="n">validatorConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getValidatorConfig</span><span class="p">();</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">minBandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getBigDecimal</span><span class="p">(</span><span class="n">validatorConfig</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;min-bandwidth&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">maxBandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getBigDecimal</span><span class="p">(</span><span class="n">validatorConfig</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;max-bandwidth&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">cir</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getCir</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">cir</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">minBandwidth</span><span class="p">)</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;CIR_BELOW_MINIMUM&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;CIR bandwidth %s %s is below minimum %s %s&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                             </span><span class="n">cir</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="n">minBandwidth</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">())</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">cir</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">maxBandwidth</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;CIR_ABOVE_MAXIMUM&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;CIR bandwidth %s %s exceeds maximum %s %s&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                             </span><span class="n">cir</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="n">maxBandwidth</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">())</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate CIR increments</span>
<span class="w">        </span><span class="n">validateBandwidthIncrements</span><span class="p">(</span><span class="n">cir</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="s">&quot;CIR&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateEirBandwidthLimits</span><span class="p">(</span><span class="n">BandwidthParameters</span><span class="w"> </span><span class="n">params</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                          </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">eir</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getEir</span><span class="p">();</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">eir</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">eir</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">ZERO</span><span class="p">)</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// EIR is optional for L2 CIR services</span>
<span class="w">            </span><span class="k">return</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="kd">var</span><span class="w"> </span><span class="n">validatorConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getValidatorConfig</span><span class="p">();</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">maxEir</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getBigDecimal</span><span class="p">(</span><span class="n">validatorConfig</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;max-eir&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">eir</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">maxEir</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;EIR_ABOVE_MAXIMUM&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;EIR bandwidth %s %s exceeds maximum %s %s&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                             </span><span class="n">eir</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="n">maxEir</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">())</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate EIR increments</span>
<span class="w">        </span><span class="n">validateBandwidthIncrements</span><span class="p">(</span><span class="n">eir</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="s">&quot;EIR&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateCirEirRatio</span><span class="p">(</span><span class="n">BandwidthParameters</span><span class="w"> </span><span class="n">params</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                   </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">cir</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getCir</span><span class="p">();</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">eir</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getEir</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">eir</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="n">eir</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">ZERO</span><span class="p">)</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="p">;</span><span class="w"> </span><span class="c1">// No EIR specified, ratio validation not applicable</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="kd">var</span><span class="w"> </span><span class="n">validatorConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getValidatorConfig</span><span class="p">();</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">maxRatio</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getBigDecimal</span><span class="p">(</span><span class="n">validatorConfig</span><span class="p">.</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;validation-rules&quot;</span><span class="p">),</span><span class="w"> </span><span class="s">&quot;max-ratio&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">ratio</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">eir</span><span class="p">.</span><span class="na">divide</span><span class="p">(</span><span class="n">cir</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="n">RoundingMode</span><span class="p">.</span><span class="na">HALF_UP</span><span class="p">);</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">ratio</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">maxRatio</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;CIR_EIR_RATIO_EXCEEDED&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;EIR/CIR ratio %.2f exceeds maximum allowed ratio %.2f&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                             </span><span class="n">ratio</span><span class="p">,</span><span class="w"> </span><span class="n">maxRatio</span><span class="p">)</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;CIR/EIR ratio validation: CIR={}, EIR={}, Ratio={}, MaxRatio={}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                 </span><span class="n">cir</span><span class="p">,</span><span class="w"> </span><span class="n">eir</span><span class="p">,</span><span class="w"> </span><span class="n">ratio</span><span class="p">,</span><span class="w"> </span><span class="n">maxRatio</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validatePortSpeedCompatibility</span><span class="p">(</span><span class="n">BandwidthParameters</span><span class="w"> </span><span class="n">params</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                              </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">cir</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getCir</span><span class="p">();</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">eir</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getEir</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">?</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getEir</span><span class="p">()</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">ZERO</span><span class="p">;</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">portSpeed</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getPortSpeed</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">portSpeed</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addWarning</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;PORT_SPEED_NOT_SPECIFIED&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Port speed not specified, cannot validate compatibility&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">totalBandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">cir</span><span class="p">.</span><span class="na">add</span><span class="p">(</span><span class="n">eir</span><span class="p">);</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">totalBandwidth</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">portSpeed</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;BANDWIDTH_EXCEEDS_PORT_SPEED&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;Total bandwidth (CIR + EIR = %s %s) exceeds port speed %s %s&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                             </span><span class="n">totalBandwidth</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="n">portSpeed</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">())</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate port speed is a standard value</span>
<span class="w">        </span><span class="n">validateStandardPortSpeed</span><span class="p">(</span><span class="n">portSpeed</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateGeographicConstraints</span><span class="p">(</span><span class="n">BandwidthValidationContext</span><span class="w"> </span><span class="n">context</span><span class="p">,</span>
<span class="w">                                             </span><span class="n">BandwidthParameters</span><span class="w"> </span><span class="n">params</span><span class="p">,</span>
<span class="w">                                             </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">ServiceLocation</span><span class="w"> </span><span class="n">location</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getServiceLocation</span><span class="p">();</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">location</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">null</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addWarning</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;SERVICE_LOCATION_NOT_SPECIFIED&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Service location not specified, cannot validate geographic constraints&quot;</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Check if location is in a remote area with bandwidth limitations</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">isRemoteLocation</span><span class="p">(</span><span class="n">location</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">maxRemoteBandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getMaxRemoteBandwidth</span><span class="p">();</span>
<span class="w">            </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">cir</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getCir</span><span class="p">();</span>

<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">cir</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">maxRemoteBandwidth</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                    </span><span class="s">&quot;REMOTE_LOCATION_BANDWIDTH_LIMIT&quot;</span><span class="p">,</span>
<span class="w">                    </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;CIR bandwidth %s %s exceeds remote location limit %s %s for %s&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                 </span><span class="n">cir</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="n">maxRemoteBandwidth</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span>
<span class="w">                                 </span><span class="n">location</span><span class="p">.</span><span class="na">getCity</span><span class="p">())</span>
<span class="w">                </span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Check for province-specific constraints</span>
<span class="w">        </span><span class="n">validateProvinceSpecificConstraints</span><span class="p">(</span><span class="n">location</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateServiceLevelAgreements</span><span class="p">(</span><span class="n">BandwidthParameters</span><span class="w"> </span><span class="n">params</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                              </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">qosClass</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getQosClass</span><span class="p">();</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">cir</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getCir</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Validate QoS class compatibility with bandwidth</span>
<span class="w">        </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">BigDecimal</span><span class="o">&gt;</span><span class="w"> </span><span class="n">qosLimits</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getQosClassLimits</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">qosClass</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="n">qosLimits</span><span class="p">.</span><span class="na">containsKey</span><span class="p">(</span><span class="n">qosClass</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">qosLimit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">qosLimits</span><span class="p">.</span><span class="na">get</span><span class="p">(</span><span class="n">qosClass</span><span class="p">);</span>

<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">cir</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">qosLimit</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                    </span><span class="s">&quot;QOS_CLASS_BANDWIDTH_LIMIT&quot;</span><span class="p">,</span>
<span class="w">                    </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;CIR bandwidth %s %s exceeds %s QoS class limit %s %s&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                 </span><span class="n">cir</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="n">qosClass</span><span class="p">,</span><span class="w"> </span><span class="n">qosLimit</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">())</span>
<span class="w">                </span><span class="p">);</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateBandwidthIncrements</span><span class="p">(</span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">bandwidth</span><span class="p">,</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">unit</span><span class="p">,</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">type</span><span class="p">,</span>
<span class="w">                                           </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">BigDecimal</span><span class="o">&gt;</span><span class="w"> </span><span class="n">incrementRules</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getBandwidthIncrementRules</span><span class="p">(</span><span class="n">unit</span><span class="p">);</span>

<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">Map</span><span class="p">.</span><span class="na">Entry</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">BigDecimal</span><span class="o">&gt;</span><span class="w"> </span><span class="n">rule</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">incrementRules</span><span class="p">.</span><span class="na">entrySet</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">threshold</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BigDecimal</span><span class="p">(</span><span class="n">rule</span><span class="p">.</span><span class="na">getKey</span><span class="p">());</span>
<span class="w">            </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">increment</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">rule</span><span class="p">.</span><span class="na">getValue</span><span class="p">();</span>

<span class="w">            </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">bandwidth</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">threshold</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">remainder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">bandwidth</span><span class="p">.</span><span class="na">remainder</span><span class="p">(</span><span class="n">increment</span><span class="p">);</span>

<span class="w">                </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">remainder</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">BigDecimal</span><span class="p">.</span><span class="na">ZERO</span><span class="p">)</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">                    </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                        </span><span class="n">type</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="s">&quot;_INVALID_INCREMENT&quot;</span><span class="p">,</span>
<span class="w">                        </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;%s bandwidth %s %s must be in increments of %s %s for values &gt;= %s %s&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                     </span><span class="n">type</span><span class="p">,</span><span class="w"> </span><span class="n">bandwidth</span><span class="p">,</span><span class="w"> </span><span class="n">unit</span><span class="p">,</span><span class="w"> </span><span class="n">increment</span><span class="p">,</span><span class="w"> </span><span class="n">unit</span><span class="p">,</span><span class="w"> </span><span class="n">threshold</span><span class="p">,</span><span class="w"> </span><span class="n">unit</span><span class="p">)</span>
<span class="w">                    </span><span class="p">);</span>
<span class="w">                </span><span class="p">}</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span><span class="w"> </span><span class="c1">// Use the first applicable rule</span>
<span class="w">            </span><span class="p">}</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isWanL2CirService</span><span class="p">(</span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">serviceItem</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">SERVICE_TYPE</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">.</span><span class="na">getServiceType</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">getValidatorConfig</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">config</span><span class="p">.</span><span class="na">getBandwidthValidators</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">getComponents</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="s">&quot;l2-cir-validator&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">getSupportedServiceTypes</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">SUPPORTED_SERVICE_TYPES</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="nf">getPriority</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">getValidatorConfig</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">?</span><span class="w"> </span>
<span class="w">            </span><span class="p">(</span><span class="n">Integer</span><span class="p">)</span><span class="w"> </span><span class="n">getValidatorConfig</span><span class="p">().</span><span class="na">getOrDefault</span><span class="p">(</span><span class="s">&quot;priority&quot;</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">)</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="mi">100</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="teluswanl2evcbandwidthvalidator">TelusWanL2EvcBandwidthValidator<a class="headerlink" href="#teluswanl2evcbandwidthvalidator" title="Permanent link">&para;</a></h2>
<h3 id="l2-evc-service-validation">L2 EVC Service Validation<a class="headerlink" href="#l2-evc-service-validation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusWanL2EvcBandwidthValidator</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">BandwidthValidator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">SERVICE_TYPE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;WAN_L2_EVC&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">SUPPORTED_SERVICE_TYPES</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="n">SERVICE_TYPE</span><span class="p">);</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">BandwidthValidatorUtils</span><span class="w"> </span><span class="n">bandwidthUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">ValidationUtils</span><span class="w"> </span><span class="n">validationUtils</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">PluginConfigurationProperties</span><span class="w"> </span><span class="n">config</span><span class="p">;</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validate</span><span class="p">(</span><span class="n">BandwidthValidationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Validating L2 EVC bandwidth for service item: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">());</span>

<span class="w">        </span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">serviceItem</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItem</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">isWanL2EvcService</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">notApplicable</span><span class="p">(</span><span class="s">&quot;Service type not supported: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">serviceItem</span><span class="p">.</span><span class="na">getServiceType</span><span class="p">());</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">serviceItemId</span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">validatorName</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="na">getClass</span><span class="p">().</span><span class="na">getSimpleName</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Extract EVC-specific parameters</span>
<span class="w">            </span><span class="n">EvcParameters</span><span class="w"> </span><span class="n">params</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractEvcParameters</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate EVC bandwidth limits</span>
<span class="w">            </span><span class="n">validateEvcBandwidthLimits</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate EVC type-specific rules</span>
<span class="w">            </span><span class="n">validateEvcTypeSpecificRules</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate UNI (User Network Interface) parameters</span>
<span class="w">            </span><span class="n">validateUniParameters</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate EVC endpoints</span>
<span class="w">            </span><span class="n">validateEvcEndpoints</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate CoS (Class of Service) parameters</span>
<span class="w">            </span><span class="n">validateCosParameters</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;L2 EVC validation completed for service item: {} - Valid: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                     </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">(),</span><span class="w"> </span><span class="n">result</span><span class="p">.</span><span class="na">isValid</span><span class="p">());</span>

<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">result</span><span class="p">;</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error validating L2 EVC bandwidth for service item: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                     </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">resultBuilder</span>
<span class="w">                </span><span class="p">.</span><span class="na">addError</span><span class="p">(</span><span class="s">&quot;VALIDATION_ERROR&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;EVC bandwidth validation failed: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">EvcParameters</span><span class="w"> </span><span class="nf">extractEvcParameters</span><span class="p">(</span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">serviceItem</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">characteristics</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">serviceItem</span><span class="p">.</span><span class="na">getServiceCharacteristics</span><span class="p">();</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">EvcParameters</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">evcBandwidth</span><span class="p">(</span><span class="n">extractBandwidthValue</span><span class="p">(</span><span class="n">characteristics</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;EVC_BANDWIDTH&quot;</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">evcType</span><span class="p">(</span><span class="n">extractStringValue</span><span class="p">(</span><span class="n">characteristics</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;EVC_TYPE&quot;</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">uniCount</span><span class="p">(</span><span class="n">extractIntegerValue</span><span class="p">(</span><span class="n">characteristics</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;UNI_COUNT&quot;</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">cosProfile</span><span class="p">(</span><span class="n">extractStringValue</span><span class="p">(</span><span class="n">characteristics</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;COS_PROFILE&quot;</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">endpoints</span><span class="p">(</span><span class="n">extractEndpoints</span><span class="p">(</span><span class="n">characteristics</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">unit</span><span class="p">(</span><span class="n">extractBandwidthUnit</span><span class="p">(</span><span class="n">characteristics</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">serviceLocation</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">.</span><span class="na">getServiceLocation</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateEvcBandwidthLimits</span><span class="p">(</span><span class="n">EvcParameters</span><span class="w"> </span><span class="n">params</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                          </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="kd">var</span><span class="w"> </span><span class="n">validatorConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getValidatorConfig</span><span class="p">();</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">minBandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getBigDecimal</span><span class="p">(</span><span class="n">validatorConfig</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;min-bandwidth&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">maxBandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getBigDecimal</span><span class="p">(</span><span class="n">validatorConfig</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;max-bandwidth&quot;</span><span class="p">);</span>

<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">evcBandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getEvcBandwidth</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">evcBandwidth</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">minBandwidth</span><span class="p">)</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;EVC_BANDWIDTH_BELOW_MINIMUM&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;EVC bandwidth %s %s is below minimum %s %s&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                             </span><span class="n">evcBandwidth</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="n">minBandwidth</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">())</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">evcBandwidth</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">maxBandwidth</span><span class="p">)</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;EVC_BANDWIDTH_ABOVE_MAXIMUM&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;EVC bandwidth %s %s exceeds maximum %s %s&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                             </span><span class="n">evcBandwidth</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="n">maxBandwidth</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">())</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateEvcTypeSpecificRules</span><span class="p">(</span><span class="n">EvcParameters</span><span class="w"> </span><span class="n">params</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                            </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">evcType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getEvcType</span><span class="p">();</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">evcBandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getEvcBandwidth</span><span class="p">();</span>
<span class="w">        </span><span class="kt">int</span><span class="w"> </span><span class="n">uniCount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUniCount</span><span class="p">();</span>

<span class="w">        </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">evcType</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="s">&quot;POINT_TO_POINT&quot;</span><span class="p">:</span>
<span class="w">                </span><span class="n">validatePointToPointEvc</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="s">&quot;MULTIPOINT&quot;</span><span class="p">:</span>
<span class="w">                </span><span class="n">validateMultipointEvc</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">case</span><span class="w"> </span><span class="s">&quot;ROOTED_MULTIPOINT&quot;</span><span class="p">:</span>
<span class="w">                </span><span class="n">validateRootedMultipointEvc</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">                </span><span class="k">break</span><span class="p">;</span>
<span class="w">            </span><span class="k">default</span><span class="p">:</span>
<span class="w">                </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                    </span><span class="s">&quot;UNSUPPORTED_EVC_TYPE&quot;</span><span class="p">,</span>
<span class="w">                    </span><span class="s">&quot;Unsupported EVC type: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">evcType</span>
<span class="w">                </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validatePointToPointEvc</span><span class="p">(</span><span class="n">EvcParameters</span><span class="w"> </span><span class="n">params</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                       </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">params</span><span class="p">.</span><span class="na">getUniCount</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;POINT_TO_POINT_UNI_COUNT&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Point-to-point EVC must have exactly 2 UNIs, found: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUniCount</span><span class="p">()</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">params</span><span class="p">.</span><span class="na">getEndpoints</span><span class="p">().</span><span class="na">size</span><span class="p">()</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;POINT_TO_POINT_ENDPOINT_COUNT&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Point-to-point EVC must have exactly 2 endpoints, found: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getEndpoints</span><span class="p">().</span><span class="na">size</span><span class="p">()</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateMultipointEvc</span><span class="p">(</span><span class="n">EvcParameters</span><span class="w"> </span><span class="n">params</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                     </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="kt">int</span><span class="w"> </span><span class="n">uniCount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUniCount</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">uniCount</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;MULTIPOINT_MIN_UNI_COUNT&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Multipoint EVC must have at least 3 UNIs, found: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">uniCount</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">uniCount</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">100</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;MULTIPOINT_MAX_UNI_COUNT&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Multipoint EVC cannot have more than 100 UNIs, found: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">uniCount</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Validate bandwidth scaling for multipoint</span>
<span class="w">        </span><span class="n">validateMultipointBandwidthScaling</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">getSupportedServiceTypes</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">SUPPORTED_SERVICE_TYPES</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isWanL2EvcService</span><span class="p">(</span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">serviceItem</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">SERVICE_TYPE</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">.</span><span class="na">getServiceType</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="teluswanl3ipqosbandwidthvalidator">TelusWanL3IpQosBandwidthValidator<a class="headerlink" href="#teluswanl3ipqosbandwidthvalidator" title="Permanent link">&para;</a></h2>
<h3 id="l3-ip-qos-service-validation">L3 IP QoS Service Validation<a class="headerlink" href="#l3-ip-qos-service-validation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TelusWanL3IpQosBandwidthValidator</span><span class="w"> </span><span class="kd">implements</span><span class="w"> </span><span class="n">BandwidthValidator</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">SERVICE_TYPE</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;WAN_L3_IP_QOS&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">SUPPORTED_SERVICE_TYPES</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Set</span><span class="p">.</span><span class="na">of</span><span class="p">(</span><span class="n">SERVICE_TYPE</span><span class="p">);</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ValidationResult</span><span class="w"> </span><span class="nf">validate</span><span class="p">(</span><span class="n">BandwidthValidationContext</span><span class="w"> </span><span class="n">context</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;Validating L3 IP QoS bandwidth for service item: {}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">());</span>

<span class="w">        </span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">serviceItem</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItem</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">isWanL3IpQosService</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">notApplicable</span><span class="p">(</span><span class="s">&quot;Service type not supported: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">serviceItem</span><span class="p">.</span><span class="na">getServiceType</span><span class="p">());</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">serviceItemId</span><span class="p">(</span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">validatorName</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="na">getClass</span><span class="p">().</span><span class="na">getSimpleName</span><span class="p">());</span>

<span class="w">        </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Extract L3 QoS parameters</span>
<span class="w">            </span><span class="n">L3QosParameters</span><span class="w"> </span><span class="n">params</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">extractL3QosParameters</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate QoS class bandwidth limits</span>
<span class="w">            </span><span class="n">validateQosClassBandwidthLimits</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate traffic shaping parameters</span>
<span class="w">            </span><span class="n">validateTrafficShapingParameters</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate DSCP marking rules</span>
<span class="w">            </span><span class="n">validateDscpMarkingRules</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="c1">// Validate routing protocol compatibility</span>
<span class="w">            </span><span class="n">validateRoutingProtocolCompatibility</span><span class="p">(</span><span class="n">params</span><span class="p">,</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">);</span>

<span class="w">            </span><span class="n">ValidationResult</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">debug</span><span class="p">(</span><span class="s">&quot;L3 IP QoS validation completed for service item: {} - Valid: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                     </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">(),</span><span class="w"> </span><span class="n">result</span><span class="p">.</span><span class="na">isValid</span><span class="p">());</span>

<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">result</span><span class="p">;</span>

<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Error validating L3 IP QoS bandwidth for service item: {}&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                     </span><span class="n">context</span><span class="p">.</span><span class="na">getServiceItemId</span><span class="p">(),</span><span class="w"> </span><span class="n">e</span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">resultBuilder</span>
<span class="w">                </span><span class="p">.</span><span class="na">addError</span><span class="p">(</span><span class="s">&quot;VALIDATION_ERROR&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;L3 QoS bandwidth validation failed: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">e</span><span class="p">.</span><span class="na">getMessage</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">validateQosClassBandwidthLimits</span><span class="p">(</span><span class="n">L3QosParameters</span><span class="w"> </span><span class="n">params</span><span class="p">,</span><span class="w"> </span>
<span class="w">                                               </span><span class="n">ValidationResult</span><span class="p">.</span><span class="na">Builder</span><span class="w"> </span><span class="n">resultBuilder</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">qosClass</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getQosClass</span><span class="p">();</span>
<span class="w">        </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">bandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getBandwidth</span><span class="p">();</span>

<span class="w">        </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">QosClassLimits</span><span class="o">&gt;</span><span class="w"> </span><span class="n">qosLimits</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getQosClassLimits</span><span class="p">();</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="n">qosLimits</span><span class="p">.</span><span class="na">containsKey</span><span class="p">(</span><span class="n">qosClass</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;UNSUPPORTED_QOS_CLASS&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="s">&quot;Unsupported QoS class: &quot;</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">qosClass</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">            </span><span class="k">return</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">QosClassLimits</span><span class="w"> </span><span class="n">limits</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">qosLimits</span><span class="p">.</span><span class="na">get</span><span class="p">(</span><span class="n">qosClass</span><span class="p">);</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">bandwidth</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">limits</span><span class="p">.</span><span class="na">getMinBandwidth</span><span class="p">())</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;QOS_BANDWIDTH_BELOW_MINIMUM&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;Bandwidth %s %s is below minimum %s %s for QoS class %s&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                             </span><span class="n">bandwidth</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="n">limits</span><span class="p">.</span><span class="na">getMinBandwidth</span><span class="p">(),</span><span class="w"> </span>
<span class="w">                             </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="n">qosClass</span><span class="p">)</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">bandwidth</span><span class="p">.</span><span class="na">compareTo</span><span class="p">(</span><span class="n">limits</span><span class="p">.</span><span class="na">getMaxBandwidth</span><span class="p">())</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">resultBuilder</span><span class="p">.</span><span class="na">addError</span><span class="p">(</span>
<span class="w">                </span><span class="s">&quot;QOS_BANDWIDTH_ABOVE_MAXIMUM&quot;</span><span class="p">,</span>
<span class="w">                </span><span class="n">String</span><span class="p">.</span><span class="na">format</span><span class="p">(</span><span class="s">&quot;Bandwidth %s %s exceeds maximum %s %s for QoS class %s&quot;</span><span class="p">,</span><span class="w"> </span>
<span class="w">                             </span><span class="n">bandwidth</span><span class="p">,</span><span class="w"> </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="n">limits</span><span class="p">.</span><span class="na">getMaxBandwidth</span><span class="p">(),</span><span class="w"> </span>
<span class="w">                             </span><span class="n">params</span><span class="p">.</span><span class="na">getUnit</span><span class="p">(),</span><span class="w"> </span><span class="n">qosClass</span><span class="p">)</span>
<span class="w">            </span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Set</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">getSupportedServiceTypes</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">SUPPORTED_SERVICE_TYPES</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="nf">isWanL3IpQosService</span><span class="p">(</span><span class="n">ServiceItem</span><span class="w"> </span><span class="n">serviceItem</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">SERVICE_TYPE</span><span class="p">.</span><span class="na">equals</span><span class="p">(</span><span class="n">serviceItem</span><span class="p">.</span><span class="na">getServiceType</span><span class="p">());</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="validation-rules-engine">Validation Rules Engine<a class="headerlink" href="#validation-rules-engine" title="Permanent link">&para;</a></h2>
<h3 id="rule-configuration">Rule Configuration<a class="headerlink" href="#rule-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># bandwidth-limits.yml</span>
<span class="nt">bandwidth-validation</span><span class="p">:</span>
<span class="w">  </span><span class="nt">l2-cir</span><span class="p">:</span>
<span class="w">    </span><span class="nt">min-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">    </span><span class="nt">max-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10000</span>
<span class="w">    </span><span class="nt">max-eir</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5000</span>
<span class="w">    </span><span class="nt">unit</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Mbps</span>
<span class="w">    </span><span class="nt">validation-rules</span><span class="p">:</span>
<span class="w">      </span><span class="nt">cir-eir-ratio</span><span class="p">:</span>
<span class="w">        </span><span class="nt">max-ratio</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2.0</span>
<span class="w">      </span><span class="nt">increments</span><span class="p">:</span>
<span class="w">        </span><span class="s">&quot;0&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span><span class="w">      </span><span class="c1"># 1 Mbps increments for 0-100 Mbps</span>
<span class="w">        </span><span class="s">&quot;100&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span><span class="w">   </span><span class="c1"># 10 Mbps increments for 100-1000 Mbps</span>
<span class="w">        </span><span class="s">&quot;1000&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span><span class="w"> </span><span class="c1"># 100 Mbps increments for 1000+ Mbps</span>
<span class="w">    </span><span class="nt">qos-limits</span><span class="p">:</span>
<span class="w">      </span><span class="nt">PREMIUM</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
<span class="w">      </span><span class="nt">STANDARD</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">500</span>
<span class="w">      </span><span class="nt">BASIC</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span>

<span class="w">  </span><span class="nt">l2-evc</span><span class="p">:</span>
<span class="w">    </span><span class="nt">min-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">    </span><span class="nt">max-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
<span class="w">    </span><span class="nt">unit</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Mbps</span>
<span class="w">    </span><span class="nt">evc-types</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">POINT_TO_POINT</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">MULTIPOINT</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ROOTED_MULTIPOINT</span>
<span class="w">    </span><span class="nt">multipoint-limits</span><span class="p">:</span>
<span class="w">      </span><span class="nt">min-unis</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">      </span><span class="nt">max-unis</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span>
<span class="w">      </span><span class="nt">bandwidth-scaling-factor</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">0.8</span>

<span class="w">  </span><span class="nt">l3-qos</span><span class="p">:</span>
<span class="w">    </span><span class="nt">min-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">    </span><span class="nt">max-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
<span class="w">    </span><span class="nt">unit</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Mbps</span>
<span class="w">    </span><span class="nt">qos-classes</span><span class="p">:</span>
<span class="w">      </span><span class="nt">PREMIUM</span><span class="p">:</span>
<span class="w">        </span><span class="nt">min-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">10</span>
<span class="w">        </span><span class="nt">max-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000</span>
<span class="w">        </span><span class="nt">dscp-values</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">46</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">34</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">26</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="nt">STANDARD</span><span class="p">:</span>
<span class="w">        </span><span class="nt">min-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">        </span><span class="nt">max-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">500</span>
<span class="w">        </span><span class="nt">dscp-values</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">18</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">20</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">22</span><span class="p p-Indicator">]</span>
<span class="w">      </span><span class="nt">BASIC</span><span class="p">:</span>
<span class="w">        </span><span class="nt">min-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1</span>
<span class="w">        </span><span class="nt">max-bandwidth</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100</span>
<span class="w">        </span><span class="nt">dscp-values</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">0</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">8</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">10</span><span class="p p-Indicator">]</span>
</code></pre></div>
<h2 id="configuration-and-customization">Configuration and Customization<a class="headerlink" href="#configuration-and-customization" title="Permanent link">&para;</a></h2>
<h3 id="dynamic-validator-configuration">Dynamic Validator Configuration<a class="headerlink" href="#dynamic-validator-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@ConfigurationProperties</span><span class="p">(</span><span class="n">prefix</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;telus.plugin.bandwidth-validators&quot;</span><span class="p">)</span>
<span class="nd">@Data</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">BandwidthValidatorConfiguration</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">enabled</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">strictMode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">ValidatorConfig</span><span class="o">&gt;</span><span class="w"> </span><span class="n">components</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HashMap</span><span class="o">&lt;&gt;</span><span class="p">();</span>

<span class="w">    </span><span class="nd">@Data</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kd">class</span> <span class="nc">ValidatorConfig</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="n">enabled</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="kt">int</span><span class="w"> </span><span class="n">priority</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">100</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">minBandwidth</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">BigDecimal</span><span class="w"> </span><span class="n">maxBandwidth</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">unit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;Mbps&quot;</span><span class="p">;</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="n">serviceTypes</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ArrayList</span><span class="o">&lt;&gt;</span><span class="p">();</span>
<span class="w">        </span><span class="kd">private</span><span class="w"> </span><span class="n">Map</span><span class="o">&lt;</span><span class="n">String</span><span class="p">,</span><span class="w"> </span><span class="n">Object</span><span class="o">&gt;</span><span class="w"> </span><span class="n">validationRules</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">HashMap</span><span class="o">&lt;&gt;</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../04-state-validators/">State Validators →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>