#!/usr/bin/env node
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  ListToolsRequestSchema,
  CallToolRequestSchema,
  McpError,
  ErrorCode,
} from "@modelcontextprotocol/sdk/types.js";
import { getEntries, getSingleEntry } from "./lib/contentfulClient.js";

if (!process.env.CONTENTFUL_SPACE) {
  throw new Error(
    "Invalid CONTENTFUL_SPACE. Please provide a valid contentful space id",
  );
}

if (!process.env.CONTENTFUL_TOKEN) {
  throw new Error(
    "Invalid CONTENTFUL_TOKEN. Please provide a valid contentful token",
  );
}

class ContentfulServer {
  private server: Server;

  constructor() {
    this.server = new Server(
      {
        name: "mcp-contentful",
        version: "0.0.1",
      },
      {
        capabilities: {
          tools: {},
        },
      },
    );

    this.setupToolHandlers();

    // Error handling
    this.server.onerror = (error) => console.error("[MCP Error]", error);
    process.on("SIGINT", async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: "get_single_entry",
          description:
            "Gets a single entry from Contentful. Requires an entry id",
          inputSchema: {
            type: "object",
            properties: {
              entryId: {
                type: "string",
                description: "ID of the entry",
              },
              locale: {
                type: "string",
                description: "Optional locale for the entry (e.g., 'fr-CA')",
              },
            },
            required: ["entryId"],
          },
        },
        {
          name: "get_entries",
          description: "Get entries from the Contentful space",
          inputSchema: {
            type: "object",
            properties: {
              content_type: {
                type: "string",
                description: "Content type ID",
              },
              locale: {
                type: "string",
                description: "Optional locale for the entries (e.g., 'fr-CA')",
              },
              // TODO: This is returning way too much data for the MCP to handle
              // include: {
              //   type: "number",
              //   description:
              //     "Depth of linked entries to include in the query. Use 0 to not include any linked entries.",
              // },
            },
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      switch (request.params.name) {
        case "get_single_entry":
          return this.getSingleEntry(
            request.params.arguments as { entryId: string; locale?: string },
          );
        case "get_entries":
          return this.getEntries(request.params.arguments);

        default:
          throw new McpError(
            ErrorCode.MethodNotFound,
            `Unknown tool: ${request.params.name}`,
          );
      }
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error("Contentful MCP server running on stdio");
  }

  async getEntries(query: Record<string, any> = {}) {
    try {
      const data = await getEntries(query);
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(data, null, 2),
          },
        ],
      };
    } catch (error: any) {
      return {
        content: [
          {
            type: "text",
            text: `Failed to get entries: ${error.message}`,
          },
        ],
        isError: true,
      };
    }
  }

  async getSingleEntry(params: { entryId: string; locale?: string }) {
    try {
      const data = await getSingleEntry(params.entryId, params.locale);
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(data, null, 2),
          },
        ],
      };
    } catch (error: any) {
      return {
        content: [
          {
            type: "text",
            text: `Failed to get entry: ${error.message}`,
          },
        ],
        isError: true,
      };
    }
  }
}

const server = new ContentfulServer();
server.run().catch(console.error);

// server.getSingleEntry({ entryId: "5ZsszOt0s6xxVJN8piVVrH" });
// server.getEntries({
//   content_type: "appComponentFeedback",
// });
