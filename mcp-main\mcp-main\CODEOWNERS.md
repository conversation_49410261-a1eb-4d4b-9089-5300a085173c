# CODEOWNERS Guide

This document explains the purpose and usage of the `CODEOWNERS` file in this repository.

## What is CODEOWNERS?

The `CODEOWNERS` file is a GitHub feature that defines which individuals or teams are responsible for code in a repository. GitHub uses this information to automatically request reviews from the right people when a pull request changes specific files.

## How it Works

- When a pull request is opened, GitHub automatically requests reviews from the owners of the code areas that are being modified
- Code owners are notified when relevant files are changed
- This helps ensure that the right people review changes to specific parts of the codebase
- The last matching pattern in the file takes precedence

## File Format

Each line in the CODEOWNERS file follows this format:

```
file-pattern    @username1 @username2 @org/team-name
```

- **file-pattern**: A glob pattern that matches specific files or directories
- **@username**: GitHub username of an individual owner
- **@org/team-name**: GitHub team that should be assigned for review

## Maintaining CODEOWNERS

When adding new packages or components to the repository:

1. Add a new entry to the CODEOWNERS file with the appropriate file pattern
2. Assign the GitHub username(s) of the responsible individual(s) or team(s)
3. Ensure the GitHub usernames are correct and active

When changing ownership of existing code:

1. Update the corresponding entry in the CODEOWNERS file
2. Consider adding a transition period where both old and new owners are listed

## Current Structure

The CODEOWNERS file in this repository is organized by:

- Package-specific owners (each MCP package has its designated owner)
- App directory owners
- Documentation owners
- Root configuration file owners

## References

- [GitHub CODEOWNERS Documentation](https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners)
- [GitHub CODEOWNERS Syntax](https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners#codeowners-syntax)
