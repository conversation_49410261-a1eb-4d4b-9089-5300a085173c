# @telus/mcp-dynatrace

This package provides a Model Context Protocol (MCP) server for interacting with Dynatrace APIs. It allows you to search for services, select specific services, and query golden signal metrics for those services.

## Installation

### For Cline / Claude Desktop Users

1. Install and run globally via npx:

   ```bash
   npx -y @telus/mcp-dynatrace
   ```

2. Add to your Cline/Claude Desktop MCP settings (typically located at `/Users/<USER>/Library/Application Support/Code/User/globalStorage/saoudrizwan.claude-dev/settings/cline_mcp_settings.json` for Cline, or `/Users/<USER>/Library/Application Support/Claude/claude_desktop_config.json` for Claude Desktop):

   ```json
   {
     "mcpServers": {
       "dynatrace": {
         "command": "npx",
         "args": ["-y", "@telus/mcp-dynatrace"],
         "env": {
           "DYNATRACE_API_BASE": "https://your-environment-id.live.dynatrace.com",
           "DYNATRACE_API_TOKEN": "your-api-token",
           "DYNATRACE_PLATFORM_URL": "https://your-environment-id.apps.dynatrace.com/platform",
           "DYNATRACE_PLATFORM_TOKEN": "your-platform-token"
         }
       }
     }
   }
   ```

## Configuration

### Dynatrace API Token Scopes

Your Dynatrace API token needs the following scopes:

- `entities.read` - For searching and retrieving service information
- `metrics.read` - For querying metrics data
- `problems.read` - For querying problem card
- `ReadConfig` - For query configurations

To create a token with these scopes:

1. Visit https://sro-dynatrace-token-generator.cloudapps.telus.com
2. Select your target Dynatrace environment
3. Click on "Get Token"

### Interact with grail tools

To interact with Dynatrace Grail you need a platform token, please go to this [url](https://myaccount.dynatrace.com/platformTokens), click on <b>+ Platform Token</b> on top right and make sure you select all permission that follow this pattern `storage:xxx:read`

### For Development

1. Clone the repository and install dependencies:

   ```bash
   git clone [repository-url]
   cd mcp-dynatrace
   pnpm install
   ```

2. Create a `.env` file in the `packages/mcp-dynatrace` directory with the following variables:

   ```
   DYNATRACE_API_BASE=https://your-environment-id.live.dynatrace.com
   DYNATRACE_API_TOKEN=your-api-token
   DYNATRACE_PLATFORM_URL=https://your-environment-id.apps.dynatrace.com/platform
   DYNATRACE_PLATFORM_TOKEN=your-platform-token
   ```

3. Build the server:

   ```bash
   pnpm build
   ```

4. Start the server:

   ```bash
   pnpm start
   ```

## Features

- Search Dynatrace services by name
- Select a service from search results
- Execute and analyze complex DQL (Dynatrace Query Language) queries
- Comprehensive service investigation including:
  - Service details and relationships
  - Service, infrastructure, and GKE application problems
  - Multi-dimensional metrics (service, host, GKE)
  - Recent trace data
  - Problem summary
- Get and analyze entity details
- Get and analyze any metric for selected entity
- Get and analyze Problems for selected entity

## Prerequisites

- Node.js (version 14 or higher recommended)
- A Dynatrace account with API access


## Available Tools

| Tool Name | Description | Input | Output |
|-----------|-------------|-------|--------|
| `search-services-by-name` | Search Dynatrace services by name | `serviceName` (string) | List of matching services with their IDs and names |
| `select-service` | Select a service from the last search results | `serviceNumber` (number) | Details of the selected service |
| `investigate-service` | Run a comprehensive investigation on a service, analyzing its relationships, infrastructure, metrics, problems and traces | `entityId` (string), `from` (optional string, ISO format), `to` (optional string, ISO format) | Detailed analysis including service details, relationships, problems (service, infrastructure, GKE), metrics (service, host, GKE), trace data, and problem summary |
| `get-entity-by-id` | Get full details of an entity by its ID | `entityId` (string), `from` (optional string, ISO format), `to` (optional string, ISO format), `fields` (optional string) | Detailed information about the specified entity |
| `get-metrics` | Get metrics based on various criteria | `metricSelector` (string), `entitySelector` (optional string), `from` (optional string, ISO format), `to` (optional string, ISO format), `nextPageKey` (optional string), `pageSize` (optional number), `resolution` (optional string, default: "5m", valid units: m,h,d,w,M,q,y) | Metrics data based on the specified criteria |
| `get-problems` | Get problems based on various criteria | `problemSelector` (optional string), `entitySelector` (optional string), `from` (optional string, ISO format), `to` (optional string, ISO format), `fields` (optional string), `sort` (optional string), `pageSize` (optional number), `nextPageKey` (optional string) | Problems data based on the specified criteria |
| `get-entities` | Get entities based on various criteria | `entitySelector` (optional string), `from` (optional string, ISO format), `to` (optional string, ISO format), `fields` (optional string), `sort` (optional string), `pageSize` (optional number), `nextPageKey` (optional string) | Entities data based on the specified criteria |
| `verify-dql-query` | Verify a DQL query before execution | `query` (string) | Verification result for the provided DQL query |
| `execute-dql-query` | Execute a DQL query and return the results with cost estimation | `query` (string), `maxTries` (optional number, default: 5) | Results of the executed DQL query including data scan costs in USD and CAD |
| `poll-query-result` | Poll for results of a running DQL query | `requestToken` (string), `timeoutMs` (optional number) | Results of the polled DQL query |
| `get-service-endpoints` | Retrieve all endpoints for a specific service | `serviceId` (string) | List of endpoints for the specified service |
| `get-traceIds` | Retrieve all trace IDs for a specific service and endpoint | `serviceId` (string), `endpointName` (string) | List of trace IDs for the specified service and endpoint |
| `get-sequence-diagram-for-traceId` | Get sequence diagram for a specific trace ID | `traceId` (string) | Sequence diagram data for the specified trace ID |
| `network-analysis` | Analyze network connections for a specific trace with configurable time window | `traceId` (string), `timeWindow` (optional string, e.g., '-12h', '-1d') | Detailed information about hosts, IP addresses, and status codes |

## Code Structure

The codebase has been reorganized for better separation of concerns:

- `api.ts`: Contains core API functions for interacting with Dynatrace APIs (e.g., `getMetrics`, `getEntities`, `getProblems`)
- `platform.ts`: Contains core functions for interacting with Dynatrace Platform APIs (e.g., `DQL query execution`)
- `sro_api.ts`: Contains SRO defined operational scenarios build on top of Core API functions(e.g., `investigate-service`)

This structure allows for better maintainability and easier extension of functionality in the future.

## DQL Query Capabilities

The MCP server supports executing complex DQL (Dynatrace Query Language) queries. This allows for advanced analysis of your Dynatrace data, including:

- Fetching and analyzing span data
- Summarizing data by various dimensions (e.g., trace ID, request ID)
- Calculating metrics such as count of spans, database calls, etc.
- Creating time series data for specific metrics
- Sorting and limiting results for focused analysis

Example capabilities include:
- Analyzing database call activity across different endpoints
- Identifying high-activity spikes in specific services
- Examining patterns in database call frequency and volume
- Detecting potential performance issues or anomalies

Note: DQL queries may be subject to data scan limits, which can be adjusted using the `scanLimitGBytes` parameter in the fetch command.

## Error Handling

The server includes error handling for API requests and will log errors to the console. If required environment variables are missing, the server will throw an error on startup.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

[Add your license information here]
