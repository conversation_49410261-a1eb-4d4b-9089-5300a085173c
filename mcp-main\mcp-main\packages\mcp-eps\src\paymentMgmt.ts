import axios, { AxiosInstance } from 'axios';
import { McpError, ErrorCode } from '@modelcontextprotocol/sdk/types.js';

export class PaymentManagementHandler {
  private axiosInstance: AxiosInstance;
  private paymentMgmtBaseUrl: string;

  constructor(axiosInstance: AxiosInstance, baseUrl: string) {
    this.axiosInstance = axiosInstance;
    this.paymentMgmtBaseUrl = `${baseUrl}/common/epsPaymentDashboardBff/v1`;
  }

  async getPaymentById(paymentId: string): Promise<any> {
    try {
      const idToken = process.env.OAUTH2_ID_TOKEN;
      const epsPaymentClientId = process.env.EPS_PAYMENT_CLIENT_ID;
      
      if (!idToken) {
        throw new McpError(
          ErrorCode.InvalidRequest,
          'OAUTH2_ID_TOKEN environment variable is required for payment management operations'
        );
      }

      if (!epsPaymentClientId) {
        throw new McpError(
          ErrorCode.InvalidRequest,
          'EPS_PAYMENT_CLIENT_ID environment variable is required for payment management operations'
        );
      }

      const response = await this.axiosInstance.get(`${this.paymentMgmtBaseUrl}/payment/${paymentId}`, {
        headers: {
          Authorization: `Bearer ${idToken}`,
          'Content-Type': 'application/json',
          'x-payment-client-id': epsPaymentClientId
        }
      });
      return response.data;
    } catch (error: any) {
      console.error('[PaymentManagement] Error fetching payment:', error.message);
      if (axios.isAxiosError(error)) {
        throw new McpError(
          ErrorCode.InvalidRequest,
          `Failed to fetch payment: ${error.response?.data?.message || error.message}`
        );
      }
      throw new McpError(ErrorCode.InternalError, 'An unexpected error occurred');
    }
  }
}
