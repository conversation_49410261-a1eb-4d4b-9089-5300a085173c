{"name": "@telus/mcp-nifi", "version": "0.0.4", "description": "MCP server for Apache NiFi integration", "repository": {"type": "git", "url": "https://github.com/telus/mcp.git", "directory": "packages/mcp-nifi"}, "scripts": {"build": "tsc && node --eval \"import('fs').then(fs => fs.chmodSync('dist/index.js', '755'))\"", "prepare": "pnpm run build", "start": "node --use-system-ca dist/index.js"}, "keywords": ["mcp", "nifi", "apache-nifi", "automation"], "author": "<PERSON> (@jordanblum-telus)", "type": "module", "main": "dist/index.js", "bin": {"mcp-nifi": "dist/index.js"}, "files": ["dist", "README.md"], "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "axios": "^1.8.4", "dotenv": "^16.4.7", "zod": "^3.22.4"}, "devDependencies": {"@changesets/cli": "^2.26.2", "@types/node": "^22.13.10", "typescript": "^5.3.3"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}}