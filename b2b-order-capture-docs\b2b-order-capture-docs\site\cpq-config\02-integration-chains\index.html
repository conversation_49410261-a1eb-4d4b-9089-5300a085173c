
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/cpq-config/02-integration-chains/">
      
      
        <link rel="prev" href="../01-configuration-management/">
      
      
        <link rel="next" href="../03-service-catalog/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Integration Chains - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#integration-chains" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Integration Chains
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" checked>
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#integration-chain-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Chain Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chain-execution-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Execution Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#credit-assessment-request-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Credit Assessment Request Chain
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Credit Assessment Request Chain">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chain-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Overview
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chain-definition-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Definition Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#car-workflow-logic" class="md-nav__link">
    <span class="md-ellipsis">
      CAR Workflow Logic
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#key-chain-scripts" class="md-nav__link">
    <span class="md-ellipsis">
      Key Chain Scripts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Chain Scripts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#before-script-for-quote-state-update" class="md-nav__link">
    <span class="md-ellipsis">
      Before Script for Quote State Update
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#sfdc-car-creation-script" class="md-nav__link">
    <span class="md-ellipsis">
      SFDC CAR Creation Script
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#success-response-handler" class="md-nav__link">
    <span class="md-ellipsis">
      Success Response Handler
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quote-management-chains" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Management Chains
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Management Chains">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-state-management-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Quote State Management Chain
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#quote-validation-scripts" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Validation Scripts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#service-provisioning-chains" class="md-nav__link">
    <span class="md-ellipsis">
      Service Provisioning Chains
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Provisioning Chains">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#service-activation-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Service Activation Chain
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-configuration-script" class="md-nav__link">
    <span class="md-ellipsis">
      Service Configuration Script
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-development" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chain-development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Development Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chain-testing-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Testing Framework
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-handling-and-monitoring" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling and Monitoring
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Handling and Monitoring">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#error-handling-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling Strategy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#monitoring-and-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring and Metrics
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Architecture Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Architecture Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#integration-chain-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Chain Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chain-execution-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Execution Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#credit-assessment-request-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Credit Assessment Request Chain
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Credit Assessment Request Chain">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chain-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Overview
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chain-definition-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Definition Structure
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#car-workflow-logic" class="md-nav__link">
    <span class="md-ellipsis">
      CAR Workflow Logic
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#key-chain-scripts" class="md-nav__link">
    <span class="md-ellipsis">
      Key Chain Scripts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Key Chain Scripts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#before-script-for-quote-state-update" class="md-nav__link">
    <span class="md-ellipsis">
      Before Script for Quote State Update
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#sfdc-car-creation-script" class="md-nav__link">
    <span class="md-ellipsis">
      SFDC CAR Creation Script
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#success-response-handler" class="md-nav__link">
    <span class="md-ellipsis">
      Success Response Handler
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#quote-management-chains" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Management Chains
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Quote Management Chains">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-state-management-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Quote State Management Chain
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#quote-validation-scripts" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Validation Scripts
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#service-provisioning-chains" class="md-nav__link">
    <span class="md-ellipsis">
      Service Provisioning Chains
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Service Provisioning Chains">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#service-activation-chain" class="md-nav__link">
    <span class="md-ellipsis">
      Service Activation Chain
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-configuration-script" class="md-nav__link">
    <span class="md-ellipsis">
      Service Configuration Script
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-development" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Development
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Development">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#chain-development-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Development Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#chain-testing-framework" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Testing Framework
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#error-handling-and-monitoring" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling and Monitoring
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Error Handling and Monitoring">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#error-handling-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Error Handling Strategy
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#monitoring-and-metrics" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring and Metrics
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="integration-chains">Integration Chains<a class="headerlink" href="#integration-chains" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#chain-architecture-overview">Chain Architecture Overview</a></li>
<li><a href="#credit-assessment-request-chain">Credit Assessment Request Chain</a></li>
<li><a href="#quote-management-chains">Quote Management Chains</a></li>
<li><a href="#service-provisioning-chains">Service Provisioning Chains</a></li>
<li><a href="#chain-development">Chain Development</a></li>
<li><a href="#error-handling-and-monitoring">Error Handling and Monitoring</a></li>
</ul>
<h2 id="chain-architecture-overview">Chain Architecture Overview<a class="headerlink" href="#chain-architecture-overview" title="Permanent link">&para;</a></h2>
<h3 id="integration-chain-structure">Integration Chain Structure<a class="headerlink" href="#integration-chain-structure" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Integration Chain Components&quot;
        A[Chain Definition YAML]
        B[Groovy Scripts]
        C[Service Calls]
        D[Conditional Logic]
        E[Error Handlers]
    end

    subgraph &quot;Chain Types&quot;
        F[Credit Assessment]
        G[Quote Management]
        H[Service Provisioning]
        I[Billing Integration]
        J[Order Lifecycle]
    end

    subgraph &quot;Script Categories&quot;
        K[Before Scripts]
        L[Response Handlers]
        M[Error Handlers]
        N[Default Handlers]
    end

    subgraph &quot;External Integrations&quot;
        O[SFDC CRM]
        P[TMF Quote Service]
        Q[SAP Systems]
        R[Billing Systems]
    end

    A --&gt; B
    A --&gt; C
    A --&gt; D
    A --&gt; E

    A --&gt; F
    A --&gt; G
    A --&gt; H
    A --&gt; I
    A --&gt; J

    B --&gt; K
    B --&gt; L
    B --&gt; M
    B --&gt; N

    C --&gt; O
    C --&gt; P
    C --&gt; Q
    C --&gt; R

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style C fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style B fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="chain-execution-flow">Chain Execution Flow<a class="headerlink" href="#chain-execution-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant Trigger as Chain Trigger
    participant Engine as CPQ Engine
    participant Script as Groovy Script
    participant Service as External Service
    participant Handler as Response Handler

    Trigger-&gt;&gt;Engine: Initiate Chain Execution
    Engine-&gt;&gt;Engine: Load Chain Definition
    Engine-&gt;&gt;Script: Execute Before Script
    Script--&gt;&gt;Engine: Prepared Request

    Engine-&gt;&gt;Service: Service Call
    Service--&gt;&gt;Engine: Response

    alt Success Response (2xx)
        Engine-&gt;&gt;Handler: Execute Success Handler
        Handler-&gt;&gt;Handler: Process Response
        Handler--&gt;&gt;Engine: Processed Result
    else Error Response (4xx/5xx)
        Engine-&gt;&gt;Handler: Execute Error Handler
        Handler-&gt;&gt;Handler: Handle Error
        Handler--&gt;&gt;Engine: Error Result
    else Default Response
        Engine-&gt;&gt;Handler: Execute Default Handler
        Handler--&gt;&gt;Engine: Default Result
    end

    Engine--&gt;&gt;Trigger: Chain Execution Complete
</code></pre></div>
<h2 id="credit-assessment-request-chain">Credit Assessment Request Chain<a class="headerlink" href="#credit-assessment-request-chain" title="Permanent link">&para;</a></h2>
<h3 id="chain-overview">Chain Overview<a class="headerlink" href="#chain-overview" title="Permanent link">&para;</a></h3>
<p>The Credit Assessment Request (CAR) chain (<code>901ec8bd-32c0-4e48-9996-0b2100c2b79d</code>) orchestrates the complex workflow for creating or amending credit assessment requests in SFDC CRM.</p>
<h3 id="chain-definition-structure">Chain Definition Structure<a class="headerlink" href="#chain-definition-structure" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># chain-901ec8bd-32c0-4e48-9996-0b2100c2b79d.yaml</span>
<span class="nn">---</span>
<span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;901ec8bd-32c0-4e48-9996-0b2100c2b79d&quot;</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">Or</span><span class="nv"> </span><span class="s">Amend</span><span class="nv"> </span><span class="s">Credit</span><span class="nv"> </span><span class="s">Assessment</span><span class="nv"> </span><span class="s">Request&quot;</span>
<span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Orchestrates</span><span class="nv"> </span><span class="s">CAR</span><span class="nv"> </span><span class="s">creation</span><span class="nv"> </span><span class="s">and</span><span class="nv"> </span><span class="s">amendment</span><span class="nv"> </span><span class="s">workflow</span><span class="nv"> </span><span class="s">with</span><span class="nv"> </span><span class="s">SFDC</span><span class="nv"> </span><span class="s">integration&quot;</span>
<span class="nt">modifiedWhen</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1728970401732</span>

<span class="nt">elements</span><span class="p">:</span>
<span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;49f11491-80fc-4a06-bb06-1fa45c24711f&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;TL</span><span class="nv"> </span><span class="s">Number</span><span class="nv"> </span><span class="s">exists?&quot;</span>
<span class="w">  </span><span class="nt">element-type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;condition&quot;</span>
<span class="w">  </span><span class="nt">children</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;09541f5d-deb4-48fd-9043-f4bc18265fe9&quot;</span>
<span class="w">    </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Has</span><span class="nv"> </span><span class="s">TL</span><span class="nv"> </span><span class="s">Num&quot;</span>
<span class="w">    </span><span class="nt">element-type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;if&quot;</span>
<span class="w">    </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">      </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${exchangeProperty.tlNumber}&quot;</span>
<span class="w">      </span><span class="nt">priority</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">0</span>
<span class="w">    </span><span class="nt">children</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;39fee437-a814-468b-8490-85cbb2db1e9d&quot;</span>
<span class="w">      </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Update</span><span class="nv"> </span><span class="s">quote</span><span class="nv"> </span><span class="s">state</span><span class="nv"> </span><span class="s">=</span><span class="nv"> </span><span class="s">CAR</span><span class="nv"> </span><span class="s">Approved&quot;</span>
<span class="w">      </span><span class="nt">element-type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-call&quot;</span>
<span class="w">      </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">        </span><span class="nt">integrationOperationId</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;quote-tmf-service-Quote</span><span class="nv"> </span><span class="s">Management6.2-6.2-changeQuoteState&quot;</span>
<span class="w">        </span><span class="nt">integrationOperationMethod</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>
<span class="w">        </span><span class="nt">integrationOperationPath</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/quoteManagement/v6/quote/{id}/changeState&quot;</span>
<span class="w">        </span><span class="nt">integrationOperationPathParameters</span><span class="p">:</span>
<span class="w">          </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;${exchangeProperty.quoteId}&quot;</span>
</code></pre></div>
<h3 id="car-workflow-logic">CAR Workflow Logic<a class="headerlink" href="#car-workflow-logic" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;CAR Decision Flow&quot;
        A[Start: CAR Request]
        B{TL Number Exists?}
        C[Update Quote State to CAR Approved]
        D[Update Quote with TL Number]
        E{CAR Exists?}
        F[Calculate Condition]
        G[Get CAR Deal Amount Multiplier]
        H{CAR Reusable?}
        I{CAR Locked?}
        J[Create New CAR in SFDC]
        K[Amend Existing CAR]
        L[Update Quote with CAR Number]
        M[End: CAR Processed]
    end

    A --&gt; B
    B --&gt;|Yes| C
    C --&gt; D
    D --&gt; M

    B --&gt;|No| E
    E --&gt;|Yes| F
    F --&gt; G
    G --&gt; H

    H --&gt;|Yes| L
    L --&gt; C

    H --&gt;|No| I
    I --&gt;|Yes| K
    I --&gt;|No| J

    E --&gt;|No| J
    J --&gt; L
    K --&gt; L

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style B fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style E fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="key-chain-scripts">Key Chain Scripts<a class="headerlink" href="#key-chain-scripts" title="Permanent link">&para;</a></h3>
<h4 id="before-script-for-quote-state-update">Before Script for Quote State Update<a class="headerlink" href="#before-script-for-quote-state-update" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// script-before-39fee437-a814-468b-8490-85cbb2db1e9d.groovy</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">groovy.json.JsonBuilder</span>

<span class="c1">// Prepare quote state change request</span>
<span class="kt">def</span><span class="w"> </span><span class="n">quoteId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;quoteId&quot;</span><span class="o">)</span>
<span class="kt">def</span><span class="w"> </span><span class="n">newState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s2">&quot;CAR_APPROVED&quot;</span>

<span class="kt">def</span><span class="w"> </span><span class="n">requestBody</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">[</span>
<span class="w">    </span><span class="nl">state:</span><span class="w"> </span><span class="n">newState</span><span class="o">,</span>
<span class="w">    </span><span class="nl">stateChangeReason:</span><span class="w"> </span><span class="s2">&quot;TL Number exists - automatic approval&quot;</span><span class="o">,</span>
<span class="w">    </span><span class="nl">timestamp:</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Date</span><span class="o">().</span><span class="na">format</span><span class="o">(</span><span class="s2">&quot;yyyy-MM-dd&#39;T&#39;HH:mm:ss.SSS&#39;Z&#39;&quot;</span><span class="o">)</span>
<span class="o">]</span>

<span class="c1">// Set request body</span>
<span class="n">exchange</span><span class="o">.</span><span class="na">getIn</span><span class="o">().</span><span class="na">setBody</span><span class="o">(</span><span class="k">new</span><span class="w"> </span><span class="n">JsonBuilder</span><span class="o">(</span><span class="n">requestBody</span><span class="o">).</span><span class="na">toString</span><span class="o">())</span>

<span class="c1">// Set headers</span>
<span class="n">exchange</span><span class="o">.</span><span class="na">getIn</span><span class="o">().</span><span class="na">setHeader</span><span class="o">(</span><span class="s2">&quot;Content-Type&quot;</span><span class="o">,</span><span class="w"> </span><span class="s2">&quot;application/json&quot;</span><span class="o">)</span>
<span class="n">exchange</span><span class="o">.</span><span class="na">getIn</span><span class="o">().</span><span class="na">setHeader</span><span class="o">(</span><span class="s2">&quot;CamelHttpMethod&quot;</span><span class="o">,</span><span class="w"> </span><span class="s2">&quot;POST&quot;</span><span class="o">)</span>

<span class="n">log</span><span class="o">.</span><span class="na">info</span><span class="o">(</span><span class="s2">&quot;Prepared quote state change request for quote: ${quoteId} to state: ${newState}&quot;</span><span class="o">)</span>
</code></pre></div>
<h4 id="sfdc-car-creation-script">SFDC CAR Creation Script<a class="headerlink" href="#sfdc-car-creation-script" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// script-before-c9e0beb8-0ec8-42a1-9b96-87fa38bfa2bf.groovy</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">groovy.json.JsonBuilder</span>

<span class="c1">// Extract quote parameters</span>
<span class="kt">def</span><span class="w"> </span><span class="n">quoteId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;quoteId&quot;</span><span class="o">)</span>
<span class="kt">def</span><span class="w"> </span><span class="n">customerId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;customerId&quot;</span><span class="o">)</span>
<span class="kt">def</span><span class="w"> </span><span class="n">dealAmount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;dealAmount&quot;</span><span class="o">)</span>
<span class="kt">def</span><span class="w"> </span><span class="n">multiplier</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;carDealAmountMultiplier&quot;</span><span class="o">,</span><span class="w"> </span><span class="s2">&quot;1.0&quot;</span><span class="o">)</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="n">Double</span>

<span class="c1">// Calculate CAR amount</span>
<span class="kt">def</span><span class="w"> </span><span class="n">carAmount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">(</span><span class="n">dealAmount</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="n">Double</span><span class="o">)</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">multiplier</span>

<span class="c1">// Prepare SFDC CAR creation request</span>
<span class="kt">def</span><span class="w"> </span><span class="n">carRequest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">[</span>
<span class="w">    </span><span class="nl">inputs:</span><span class="w"> </span><span class="o">[</span>
<span class="w">        </span><span class="o">[</span>
<span class="w">            </span><span class="nl">QuoteId:</span><span class="w"> </span><span class="n">quoteId</span><span class="o">,</span>
<span class="w">            </span><span class="nl">CustomerId:</span><span class="w"> </span><span class="n">customerId</span><span class="o">,</span>
<span class="w">            </span><span class="nl">DealAmount:</span><span class="w"> </span><span class="n">dealAmount</span><span class="o">,</span>
<span class="w">            </span><span class="nl">CARAmount:</span><span class="w"> </span><span class="n">carAmount</span><span class="o">,</span>
<span class="w">            </span><span class="nl">RequestType:</span><span class="w"> </span><span class="s2">&quot;CREATE&quot;</span><span class="o">,</span>
<span class="w">            </span><span class="nl">Priority:</span><span class="w"> </span><span class="s2">&quot;HIGH&quot;</span><span class="o">,</span>
<span class="w">            </span><span class="nl">BusinessJustification:</span><span class="w"> </span><span class="s2">&quot;B2B Order Capture - New CAR Request&quot;</span>
<span class="w">        </span><span class="o">]</span>
<span class="w">    </span><span class="o">]</span>
<span class="o">]</span>

<span class="c1">// Set request body</span>
<span class="n">exchange</span><span class="o">.</span><span class="na">getIn</span><span class="o">().</span><span class="na">setBody</span><span class="o">(</span><span class="k">new</span><span class="w"> </span><span class="n">JsonBuilder</span><span class="o">(</span><span class="n">carRequest</span><span class="o">).</span><span class="na">toString</span><span class="o">())</span>

<span class="c1">// Set SFDC-specific headers</span>
<span class="n">exchange</span><span class="o">.</span><span class="na">getIn</span><span class="o">().</span><span class="na">setHeader</span><span class="o">(</span><span class="s2">&quot;Content-Type&quot;</span><span class="o">,</span><span class="w"> </span><span class="s2">&quot;application/json&quot;</span><span class="o">)</span>
<span class="n">exchange</span><span class="o">.</span><span class="na">getIn</span><span class="o">().</span><span class="na">setHeader</span><span class="o">(</span><span class="s2">&quot;Sforce-Call-Options&quot;</span><span class="o">,</span><span class="w"> </span><span class="s2">&quot;client=CIP_B2B_Integration&quot;</span><span class="o">)</span>

<span class="n">log</span><span class="o">.</span><span class="na">info</span><span class="o">(</span><span class="s2">&quot;Prepared SFDC CAR creation request for quote: ${quoteId}, amount: ${carAmount}&quot;</span><span class="o">)</span>
</code></pre></div>
<h4 id="success-response-handler">Success Response Handler<a class="headerlink" href="#success-response-handler" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// script-200..299-c9e0beb8-0ec8-42a1-9b96-87fa38bfa2bf.groovy</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">groovy.json.JsonSlurper</span>

<span class="c1">// Parse SFDC response</span>
<span class="kt">def</span><span class="w"> </span><span class="n">responseBody</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getIn</span><span class="o">().</span><span class="na">getBody</span><span class="o">(</span><span class="n">String</span><span class="o">.</span><span class="na">class</span><span class="o">)</span>
<span class="kt">def</span><span class="w"> </span><span class="n">jsonSlurper</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">JsonSlurper</span><span class="o">()</span>
<span class="kt">def</span><span class="w"> </span><span class="n">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">jsonSlurper</span><span class="o">.</span><span class="na">parseText</span><span class="o">(</span><span class="n">responseBody</span><span class="o">)</span>

<span class="c1">// Extract CAR details from response</span>
<span class="kt">def</span><span class="w"> </span><span class="n">carId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">response</span><span class="o">[</span><span class="mi">0</span><span class="o">]?.</span><span class="na">outputValues</span><span class="o">?.</span><span class="na">CARId</span>
<span class="kt">def</span><span class="w"> </span><span class="n">carNumber</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">response</span><span class="o">[</span><span class="mi">0</span><span class="o">]?.</span><span class="na">outputValues</span><span class="o">?.</span><span class="na">CARNumber</span>
<span class="kt">def</span><span class="w"> </span><span class="n">carStatus</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">response</span><span class="o">[</span><span class="mi">0</span><span class="o">]?.</span><span class="na">outputValues</span><span class="o">?.</span><span class="na">Status</span>

<span class="c1">// Store CAR details in exchange properties</span>
<span class="n">exchange</span><span class="o">.</span><span class="na">setProperty</span><span class="o">(</span><span class="s2">&quot;carId&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">carId</span><span class="o">)</span>
<span class="n">exchange</span><span class="o">.</span><span class="na">setProperty</span><span class="o">(</span><span class="s2">&quot;carNumber&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">carNumber</span><span class="o">)</span>
<span class="n">exchange</span><span class="o">.</span><span class="na">setProperty</span><span class="o">(</span><span class="s2">&quot;carStatus&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">carStatus</span><span class="o">)</span>

<span class="c1">// Log success</span>
<span class="n">log</span><span class="o">.</span><span class="na">info</span><span class="o">(</span><span class="s2">&quot;CAR created successfully - ID: ${carId}, Number: ${carNumber}, Status: ${carStatus}&quot;</span><span class="o">)</span>

<span class="c1">// Set success indicator</span>
<span class="n">exchange</span><span class="o">.</span><span class="na">setProperty</span><span class="o">(</span><span class="s2">&quot;carCreationSuccess&quot;</span><span class="o">,</span><span class="w"> </span><span class="kc">true</span><span class="o">)</span>
</code></pre></div>
<h2 id="quote-management-chains">Quote Management Chains<a class="headerlink" href="#quote-management-chains" title="Permanent link">&para;</a></h2>
<h3 id="quote-state-management-chain">Quote State Management Chain<a class="headerlink" href="#quote-state-management-chain" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Quote state management chain structure</span>
<span class="nt">quote-state-chain</span><span class="p">:</span>
<span class="w">  </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;314187cb-4e20-4f23-be4f-6e7c7c422d44&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Quote</span><span class="nv"> </span><span class="s">State</span><span class="nv"> </span><span class="s">Management&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Manages</span><span class="nv"> </span><span class="s">quote</span><span class="nv"> </span><span class="s">lifecycle</span><span class="nv"> </span><span class="s">states</span><span class="nv"> </span><span class="s">and</span><span class="nv"> </span><span class="s">transitions&quot;</span>

<span class="w">  </span><span class="nt">elements</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Extract</span><span class="nv"> </span><span class="s">Quote</span><span class="nv"> </span><span class="s">Parameters&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-call&quot;</span>
<span class="w">      </span><span class="nt">operation</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;getQuote&quot;</span>
<span class="w">      </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/quoteManagement/v6/quote/{id}&quot;</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Validate</span><span class="nv"> </span><span class="s">State</span><span class="nv"> </span><span class="s">Transition&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script&quot;</span>
<span class="w">      </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;validate-state-transition.groovy&quot;</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Update</span><span class="nv"> </span><span class="s">Quote</span><span class="nv"> </span><span class="s">State&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-call&quot;</span>
<span class="w">      </span><span class="nt">operation</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;changeQuoteState&quot;</span>
<span class="w">      </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/quoteManagement/v6/quote/{id}/changeState&quot;</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Notify</span><span class="nv"> </span><span class="s">Stakeholders&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-call&quot;</span>
<span class="w">      </span><span class="nt">operation</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;sendNotification&quot;</span>
<span class="w">      </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/notification/v1/send&quot;</span>
</code></pre></div>
<h3 id="quote-validation-scripts">Quote Validation Scripts<a class="headerlink" href="#quote-validation-scripts" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Quote validation before script</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">groovy.json.JsonSlurper</span>

<span class="kt">def</span><span class="w"> </span><span class="n">quoteId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;quoteId&quot;</span><span class="o">)</span>
<span class="kt">def</span><span class="w"> </span><span class="n">requestedState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;requestedState&quot;</span><span class="o">)</span>
<span class="kt">def</span><span class="w"> </span><span class="n">currentState</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;currentState&quot;</span><span class="o">)</span>

<span class="c1">// Define valid state transitions</span>
<span class="kt">def</span><span class="w"> </span><span class="n">validTransitions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">[</span>
<span class="w">    </span><span class="s2">&quot;DRAFT&quot;</span><span class="o">:</span><span class="w"> </span><span class="o">[</span><span class="s2">&quot;SUBMITTED&quot;</span><span class="o">,</span><span class="w"> </span><span class="s2">&quot;CANCELLED&quot;</span><span class="o">],</span>
<span class="w">    </span><span class="s2">&quot;SUBMITTED&quot;</span><span class="o">:</span><span class="w"> </span><span class="o">[</span><span class="s2">&quot;APPROVED&quot;</span><span class="o">,</span><span class="w"> </span><span class="s2">&quot;REJECTED&quot;</span><span class="o">,</span><span class="w"> </span><span class="s2">&quot;CANCELLED&quot;</span><span class="o">],</span>
<span class="w">    </span><span class="s2">&quot;APPROVED&quot;</span><span class="o">:</span><span class="w"> </span><span class="o">[</span><span class="s2">&quot;ORDERED&quot;</span><span class="o">,</span><span class="w"> </span><span class="s2">&quot;CANCELLED&quot;</span><span class="o">],</span>
<span class="w">    </span><span class="s2">&quot;REJECTED&quot;</span><span class="o">:</span><span class="w"> </span><span class="o">[</span><span class="s2">&quot;DRAFT&quot;</span><span class="o">,</span><span class="w"> </span><span class="s2">&quot;CANCELLED&quot;</span><span class="o">],</span>
<span class="w">    </span><span class="s2">&quot;ORDERED&quot;</span><span class="o">:</span><span class="w"> </span><span class="o">[</span><span class="s2">&quot;COMPLETED&quot;</span><span class="o">,</span><span class="w"> </span><span class="s2">&quot;CANCELLED&quot;</span><span class="o">]</span>
<span class="o">]</span>

<span class="c1">// Validate transition</span>
<span class="kt">def</span><span class="w"> </span><span class="n">allowedStates</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">validTransitions</span><span class="o">[</span><span class="n">currentState</span><span class="o">]</span>
<span class="k">if</span><span class="w"> </span><span class="o">(!</span><span class="n">allowedStates</span><span class="o">?.</span><span class="na">contains</span><span class="o">(</span><span class="n">requestedState</span><span class="o">))</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IllegalStateException</span><span class="o">(</span>
<span class="w">        </span><span class="s2">&quot;Invalid state transition from ${currentState} to ${requestedState}&quot;</span>
<span class="w">    </span><span class="o">)</span>
<span class="o">}</span>

<span class="c1">// Additional business rule validations</span>
<span class="k">if</span><span class="w"> </span><span class="o">(</span><span class="n">requestedState</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="s2">&quot;APPROVED&quot;</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="n">validateApprovalRequirements</span><span class="o">(</span><span class="n">exchange</span><span class="o">)</span>
<span class="o">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="o">(</span><span class="n">requestedState</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="s2">&quot;ORDERED&quot;</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="n">validateOrderRequirements</span><span class="o">(</span><span class="n">exchange</span><span class="o">)</span>
<span class="o">}</span>

<span class="n">log</span><span class="o">.</span><span class="na">info</span><span class="o">(</span><span class="s2">&quot;State transition validated: ${currentState} -&gt; ${requestedState}&quot;</span><span class="o">)</span>

<span class="kt">def</span><span class="w"> </span><span class="nf">validateApprovalRequirements</span><span class="o">(</span><span class="n">exchange</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="n">quoteAmount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;quoteAmount&quot;</span><span class="o">)</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="n">Double</span>
<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="n">approverLevel</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;approverLevel&quot;</span><span class="o">)</span>

<span class="w">    </span><span class="c1">// Check approval authority</span>
<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="n">maxApprovalAmount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getMaxApprovalAmount</span><span class="o">(</span><span class="n">approverLevel</span><span class="o">)</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="o">(</span><span class="n">quoteAmount</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="n">maxApprovalAmount</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IllegalStateException</span><span class="o">(</span>
<span class="w">            </span><span class="s2">&quot;Quote amount ${quoteAmount} exceeds approval authority ${maxApprovalAmount}&quot;</span>
<span class="w">        </span><span class="o">)</span>
<span class="w">    </span><span class="o">}</span>
<span class="o">}</span>

<span class="kt">def</span><span class="w"> </span><span class="nf">getMaxApprovalAmount</span><span class="o">(</span><span class="n">approverLevel</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="n">approvalLimits</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">[</span>
<span class="w">        </span><span class="s2">&quot;STANDARD&quot;</span><span class="o">:</span><span class="w"> </span><span class="mi">50000</span><span class="o">,</span>
<span class="w">        </span><span class="s2">&quot;MANAGER&quot;</span><span class="o">:</span><span class="w"> </span><span class="mi">250000</span><span class="o">,</span>
<span class="w">        </span><span class="s2">&quot;DIRECTOR&quot;</span><span class="o">:</span><span class="w"> </span><span class="mi">1000000</span><span class="o">,</span>
<span class="w">        </span><span class="s2">&quot;VP&quot;</span><span class="o">:</span><span class="w"> </span><span class="mi">5000000</span>
<span class="w">    </span><span class="o">]</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">approvalLimits</span><span class="o">[</span><span class="n">approverLevel</span><span class="o">]</span><span class="w"> </span><span class="o">?:</span><span class="w"> </span><span class="mi">0</span>
<span class="o">}</span>
</code></pre></div>
<h2 id="service-provisioning-chains">Service Provisioning Chains<a class="headerlink" href="#service-provisioning-chains" title="Permanent link">&para;</a></h2>
<h3 id="service-activation-chain">Service Activation Chain<a class="headerlink" href="#service-activation-chain" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Service activation chain structure</span>
<span class="nt">service-activation-chain</span><span class="p">:</span>
<span class="w">  </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;4b5bae82-bab0-4211-8aab-0c81692ebc88&quot;</span>
<span class="w">  </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Service</span><span class="nv"> </span><span class="s">Activation&quot;</span>
<span class="w">  </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Orchestrates</span><span class="nv"> </span><span class="s">service</span><span class="nv"> </span><span class="s">provisioning</span><span class="nv"> </span><span class="s">across</span><span class="nv"> </span><span class="s">multiple</span><span class="nv"> </span><span class="s">systems&quot;</span>

<span class="w">  </span><span class="nt">workflow</span><span class="p">:</span>
<span class="w">    </span><span class="nt">1-preparation</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Extract</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Parameters&quot;</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script&quot;</span>
<span class="w">        </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;extract-service-params.groovy&quot;</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Validate</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Configuration&quot;</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script&quot;</span>
<span class="w">        </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;validate-service-config.groovy&quot;</span>

<span class="w">    </span><span class="nt">2-provisioning</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Create</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Instance&quot;</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-call&quot;</span>
<span class="w">        </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/serviceInventory/v4/service&quot;</span>
<span class="w">        </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Configure</span><span class="nv"> </span><span class="s">Network</span><span class="nv"> </span><span class="s">Elements&quot;</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-call&quot;</span>
<span class="w">        </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/networkConfiguration/v1/configure&quot;</span>
<span class="w">        </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Update</span><span class="nv"> </span><span class="s">Billing</span><span class="nv"> </span><span class="s">System&quot;</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-call&quot;</span>
<span class="w">        </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/billing/v2/subscription&quot;</span>
<span class="w">        </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>

<span class="w">    </span><span class="nt">3-verification</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Verify</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Status&quot;</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-call&quot;</span>
<span class="w">        </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/serviceInventory/v4/service/{id}/status&quot;</span>
<span class="w">        </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;GET&quot;</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Run</span><span class="nv"> </span><span class="s">Service</span><span class="nv"> </span><span class="s">Tests&quot;</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;script&quot;</span>
<span class="w">        </span><span class="nt">script</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;run-service-tests.groovy&quot;</span>

<span class="w">    </span><span class="nt">4-completion</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Update</span><span class="nv"> </span><span class="s">Order</span><span class="nv"> </span><span class="s">Status&quot;</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-call&quot;</span>
<span class="w">        </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/orderManagement/v4/productOrder/{id}&quot;</span>
<span class="w">        </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;PATCH&quot;</span>

<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;Send</span><span class="nv"> </span><span class="s">Completion</span><span class="nv"> </span><span class="s">Notification&quot;</span>
<span class="w">        </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service-call&quot;</span>
<span class="w">        </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;/notification/v1/send&quot;</span>
<span class="w">        </span><span class="nt">method</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;POST&quot;</span>
</code></pre></div>
<h3 id="service-configuration-script">Service Configuration Script<a class="headerlink" href="#service-configuration-script" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Service configuration preparation script</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">groovy.json.JsonBuilder</span>

<span class="kt">def</span><span class="w"> </span><span class="n">serviceType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;serviceType&quot;</span><span class="o">)</span>
<span class="kt">def</span><span class="w"> </span><span class="n">customerId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;customerId&quot;</span><span class="o">)</span>
<span class="kt">def</span><span class="w"> </span><span class="n">serviceLocation</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;serviceLocation&quot;</span><span class="o">)</span>
<span class="kt">def</span><span class="w"> </span><span class="n">bandwidth</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;bandwidth&quot;</span><span class="o">)</span>

<span class="c1">// Prepare service configuration based on type</span>
<span class="kt">def</span><span class="w"> </span><span class="n">serviceConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">[:]</span>

<span class="k">switch</span><span class="w"> </span><span class="o">(</span><span class="n">serviceType</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="k">case</span><span class="w"> </span><span class="s2">&quot;WAN_L2_CIR&quot;</span><span class="o">:</span>
<span class="w">        </span><span class="n">serviceConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">prepareL2CirConfig</span><span class="o">(</span><span class="n">bandwidth</span><span class="o">,</span><span class="w"> </span><span class="n">serviceLocation</span><span class="o">)</span>
<span class="w">        </span><span class="k">break</span>
<span class="w">    </span><span class="k">case</span><span class="w"> </span><span class="s2">&quot;WAN_L2_EVC&quot;</span><span class="o">:</span>
<span class="w">        </span><span class="n">serviceConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">prepareL2EvcConfig</span><span class="o">(</span><span class="n">bandwidth</span><span class="o">,</span><span class="w"> </span><span class="n">serviceLocation</span><span class="o">)</span>
<span class="w">        </span><span class="k">break</span>
<span class="w">    </span><span class="k">case</span><span class="w"> </span><span class="s2">&quot;WAN_L3_IP_QOS&quot;</span><span class="o">:</span>
<span class="w">        </span><span class="n">serviceConfig</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">prepareL3QosConfig</span><span class="o">(</span><span class="n">bandwidth</span><span class="o">,</span><span class="w"> </span><span class="n">serviceLocation</span><span class="o">)</span>
<span class="w">        </span><span class="k">break</span>
<span class="w">    </span><span class="k">default</span><span class="o">:</span>
<span class="w">        </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">IllegalArgumentException</span><span class="o">(</span><span class="s2">&quot;Unsupported service type: ${serviceType}&quot;</span><span class="o">)</span>
<span class="o">}</span>

<span class="c1">// Add common configuration</span>
<span class="n">serviceConfig</span><span class="o">.</span><span class="na">putAll</span><span class="o">([</span>
<span class="w">    </span><span class="nl">customerId:</span><span class="w"> </span><span class="n">customerId</span><span class="o">,</span>
<span class="w">    </span><span class="nl">serviceLocation:</span><span class="w"> </span><span class="n">serviceLocation</span><span class="o">,</span>
<span class="w">    </span><span class="nl">requestedDate:</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Date</span><span class="o">().</span><span class="na">format</span><span class="o">(</span><span class="s2">&quot;yyyy-MM-dd&#39;T&#39;HH:mm:ss.SSS&#39;Z&#39;&quot;</span><span class="o">),</span>
<span class="w">    </span><span class="nl">priority:</span><span class="w"> </span><span class="s2">&quot;HIGH&quot;</span>
<span class="o">])</span>

<span class="c1">// Set request body</span>
<span class="n">exchange</span><span class="o">.</span><span class="na">getIn</span><span class="o">().</span><span class="na">setBody</span><span class="o">(</span><span class="k">new</span><span class="w"> </span><span class="n">JsonBuilder</span><span class="o">(</span><span class="n">serviceConfig</span><span class="o">).</span><span class="na">toString</span><span class="o">())</span>

<span class="n">log</span><span class="o">.</span><span class="na">info</span><span class="o">(</span><span class="s2">&quot;Prepared service configuration for type: ${serviceType}&quot;</span><span class="o">)</span>

<span class="kt">def</span><span class="w"> </span><span class="nf">prepareL2CirConfig</span><span class="o">(</span><span class="n">bandwidth</span><span class="o">,</span><span class="w"> </span><span class="n">location</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="o">[</span>
<span class="w">        </span><span class="nl">serviceType:</span><span class="w"> </span><span class="s2">&quot;WAN_L2_CIR&quot;</span><span class="o">,</span>
<span class="w">        </span><span class="nl">characteristics:</span><span class="w"> </span><span class="o">[</span>
<span class="w">            </span><span class="o">[</span><span class="nl">name:</span><span class="w"> </span><span class="s2">&quot;CIR&quot;</span><span class="o">,</span><span class="w"> </span><span class="nl">value:</span><span class="w"> </span><span class="n">bandwidth</span><span class="o">],</span>
<span class="w">            </span><span class="o">[</span><span class="nl">name:</span><span class="w"> </span><span class="s2">&quot;EIR&quot;</span><span class="o">,</span><span class="w"> </span><span class="nl">value:</span><span class="w"> </span><span class="o">(</span><span class="n">bandwidth</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="n">Integer</span><span class="o">)</span><span class="w"> </span><span class="o">/</span><span class="w"> </span><span class="mi">2</span><span class="o">],</span>
<span class="w">            </span><span class="o">[</span><span class="nl">name:</span><span class="w"> </span><span class="s2">&quot;LOCATION_A&quot;</span><span class="o">,</span><span class="w"> </span><span class="nl">value:</span><span class="w"> </span><span class="n">location</span><span class="o">.</span><span class="na">locationA</span><span class="o">],</span>
<span class="w">            </span><span class="o">[</span><span class="nl">name:</span><span class="w"> </span><span class="s2">&quot;LOCATION_Z&quot;</span><span class="o">,</span><span class="w"> </span><span class="nl">value:</span><span class="w"> </span><span class="n">location</span><span class="o">.</span><span class="na">locationZ</span><span class="o">],</span>
<span class="w">            </span><span class="o">[</span><span class="nl">name:</span><span class="w"> </span><span class="s2">&quot;VLAN_ID&quot;</span><span class="o">,</span><span class="w"> </span><span class="nl">value:</span><span class="w"> </span><span class="n">generateVlanId</span><span class="o">()]</span>
<span class="w">        </span><span class="o">]</span>
<span class="w">    </span><span class="o">]</span>
<span class="o">}</span>

<span class="kt">def</span><span class="w"> </span><span class="nf">generateVlanId</span><span class="o">()</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="o">(</span><span class="n">Math</span><span class="o">.</span><span class="na">random</span><span class="o">()</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mi">4000</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mi">100</span><span class="o">)</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="n">Integer</span>
<span class="o">}</span>
</code></pre></div>
<h2 id="chain-development">Chain Development<a class="headerlink" href="#chain-development" title="Permanent link">&para;</a></h2>
<h3 id="chain-development-workflow">Chain Development Workflow<a class="headerlink" href="#chain-development-workflow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;Development Process&quot;
        A[Design Chain] --&gt; B[Create YAML Definition]
        B --&gt; C[Develop Groovy Scripts]
        C --&gt; D[Unit Test Scripts]
        D --&gt; E[Integration Testing]
        E --&gt; F[Deploy to QA]
        F --&gt; G[End-to-End Testing]
        G --&gt; H[Production Deployment]
    end

    subgraph &quot;Testing Levels&quot;
        I[Script Unit Tests]
        J[Chain Integration Tests]
        K[System End-to-End Tests]
        L[Performance Tests]
    end

    D --&gt; I
    E --&gt; J
    G --&gt; K
    G --&gt; L

    style A fill:#673ab7,stroke:#512da8,color:#ffffff
    style C fill:#9c27b0,stroke:#7b1fa2,color:#ffffff
    style E fill:#e1bee7,stroke:#ba68c8,color:#000000
</code></pre></div>
<h3 id="chain-testing-framework">Chain Testing Framework<a class="headerlink" href="#chain-testing-framework" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Chain testing framework</span>
<span class="nd">@Grab</span><span class="o">(</span><span class="s1">&#39;org.apache.camel:camel-test:3.20.0&#39;</span><span class="o">)</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">org.apache.camel.test.junit5.CamelTestSupport</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">org.apache.camel.builder.RouteBuilder</span>

<span class="kd">class</span><span class="w"> </span><span class="nc">CreditAssessmentChainTest</span><span class="w"> </span><span class="kd">extends</span><span class="w"> </span><span class="n">CamelTestSupport</span><span class="w"> </span><span class="o">{</span>

<span class="w">    </span><span class="nd">@Override</span>
<span class="w">    </span><span class="kd">protected</span><span class="w"> </span><span class="n">RouteBuilder</span><span class="w"> </span><span class="n">createRouteBuilder</span><span class="o">()</span><span class="w"> </span><span class="kd">throws</span><span class="w"> </span><span class="n">Exception</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">RouteBuilder</span><span class="o">()</span><span class="w"> </span><span class="o">{</span>
<span class="w">            </span><span class="nd">@Override</span>
<span class="w">            </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="n">configure</span><span class="o">()</span><span class="w"> </span><span class="kd">throws</span><span class="w"> </span><span class="n">Exception</span><span class="w"> </span><span class="o">{</span>
<span class="w">                </span><span class="c1">// Mock external services</span>
<span class="w">                </span><span class="n">from</span><span class="o">(</span><span class="s2">&quot;direct:start&quot;</span><span class="o">)</span>
<span class="w">                    </span><span class="o">.</span><span class="na">to</span><span class="o">(</span><span class="s2">&quot;mock:sfdc-service&quot;</span><span class="o">)</span>
<span class="w">                    </span><span class="o">.</span><span class="na">to</span><span class="o">(</span><span class="s2">&quot;mock:quote-service&quot;</span><span class="o">)</span>
<span class="w">                    </span><span class="o">.</span><span class="na">to</span><span class="o">(</span><span class="s2">&quot;mock:result&quot;</span><span class="o">)</span>
<span class="w">            </span><span class="o">}</span>
<span class="w">        </span><span class="o">}</span>
<span class="w">    </span><span class="o">}</span>

<span class="w">    </span><span class="kt">def</span><span class="w"> </span><span class="nf">&quot;test CAR creation with valid quote&quot;</span><span class="o">()</span><span class="w"> </span><span class="o">{</span>
<span class="w">        </span><span class="nl">given:</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">mockSfdc</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getMockEndpoint</span><span class="o">(</span><span class="s2">&quot;mock:sfdc-service&quot;</span><span class="o">)</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">mockQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getMockEndpoint</span><span class="o">(</span><span class="s2">&quot;mock:quote-service&quot;</span><span class="o">)</span>
<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">mockResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">getMockEndpoint</span><span class="o">(</span><span class="s2">&quot;mock:result&quot;</span><span class="o">)</span>

<span class="w">        </span><span class="n">mockSfdc</span><span class="o">.</span><span class="na">expectedMessageCount</span><span class="o">(</span><span class="mi">1</span><span class="o">)</span>
<span class="w">        </span><span class="n">mockQuote</span><span class="o">.</span><span class="na">expectedMessageCount</span><span class="o">(</span><span class="mi">1</span><span class="o">)</span>
<span class="w">        </span><span class="n">mockResult</span><span class="o">.</span><span class="na">expectedMessageCount</span><span class="o">(</span><span class="mi">1</span><span class="o">)</span>

<span class="w">        </span><span class="nl">when:</span>
<span class="w">        </span><span class="n">template</span><span class="o">.</span><span class="na">sendBodyAndHeaders</span><span class="o">(</span><span class="s2">&quot;direct:start&quot;</span><span class="o">,</span><span class="w"> </span><span class="s2">&quot;&quot;</span><span class="o">,</span><span class="w"> </span><span class="o">[</span>
<span class="w">            </span><span class="nl">quoteId:</span><span class="w"> </span><span class="s2">&quot;test-quote-123&quot;</span><span class="o">,</span>
<span class="w">            </span><span class="nl">customerId:</span><span class="w"> </span><span class="s2">&quot;test-customer-456&quot;</span><span class="o">,</span>
<span class="w">            </span><span class="nl">dealAmount:</span><span class="w"> </span><span class="s2">&quot;100000&quot;</span>
<span class="w">        </span><span class="o">])</span>

<span class="w">        </span><span class="nl">then:</span>
<span class="w">        </span><span class="n">assertMockEndpointsSatisfied</span><span class="o">()</span>

<span class="w">        </span><span class="kt">def</span><span class="w"> </span><span class="n">resultExchange</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">mockResult</span><span class="o">.</span><span class="na">getExchanges</span><span class="o">().</span><span class="na">get</span><span class="o">(</span><span class="mi">0</span><span class="o">)</span>
<span class="w">        </span><span class="k">assert</span><span class="w"> </span><span class="n">resultExchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;carCreationSuccess&quot;</span><span class="o">)</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="kc">true</span>
<span class="w">        </span><span class="k">assert</span><span class="w"> </span><span class="n">resultExchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;carId&quot;</span><span class="o">)</span><span class="w"> </span><span class="o">!=</span><span class="w"> </span><span class="kc">null</span>
<span class="w">    </span><span class="o">}</span>
<span class="o">}</span>
</code></pre></div>
<h2 id="error-handling-and-monitoring">Error Handling and Monitoring<a class="headerlink" href="#error-handling-and-monitoring" title="Permanent link">&para;</a></h2>
<h3 id="error-handling-strategy">Error Handling Strategy<a class="headerlink" href="#error-handling-strategy" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Error handling configuration</span>
<span class="nt">error-handling</span><span class="p">:</span>
<span class="w">  </span><span class="nt">global-error-handler</span><span class="p">:</span>
<span class="w">    </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;dead-letter-channel&quot;</span>
<span class="w">    </span><span class="nt">dead-letter-uri</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;jms:queue:error-queue&quot;</span>
<span class="w">    </span><span class="nt">max-redeliveries</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">    </span><span class="nt">redelivery-delay</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5000</span>

<span class="w">  </span><span class="nt">chain-specific-handlers</span><span class="p">:</span>
<span class="w">    </span><span class="nt">credit-assessment</span><span class="p">:</span>
<span class="w">      </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30000</span>
<span class="w">      </span><span class="nt">circuit-breaker</span><span class="p">:</span>
<span class="w">        </span><span class="nt">failure-threshold</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">        </span><span class="nt">recovery-timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">60000</span>

<span class="w">    </span><span class="nt">quote-management</span><span class="p">:</span>
<span class="w">      </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">15000</span>
<span class="w">      </span><span class="nt">retry-policy</span><span class="p">:</span>
<span class="w">        </span><span class="nt">max-attempts</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">        </span><span class="nt">backoff-multiplier</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2</span>

<span class="w">    </span><span class="nt">service-provisioning</span><span class="p">:</span>
<span class="w">      </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">120000</span>
<span class="w">      </span><span class="nt">compensation-enabled</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">      </span><span class="nt">saga-timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">300000</span>
</code></pre></div>
<h3 id="monitoring-and-metrics">Monitoring and Metrics<a class="headerlink" href="#monitoring-and-metrics" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Chain monitoring script</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">io.micrometer.core.instrument.MeterRegistry</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">io.micrometer.core.instrument.Timer</span>

<span class="kt">def</span><span class="w"> </span><span class="n">meterRegistry</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getContext</span><span class="o">().</span><span class="na">getRegistry</span><span class="o">().</span><span class="na">lookupByType</span><span class="o">(</span><span class="n">MeterRegistry</span><span class="o">.</span><span class="na">class</span><span class="o">)</span>
<span class="kt">def</span><span class="w"> </span><span class="n">chainId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;chainId&quot;</span><span class="o">)</span>
<span class="kt">def</span><span class="w"> </span><span class="n">operationName</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">exchange</span><span class="o">.</span><span class="na">getProperty</span><span class="o">(</span><span class="s2">&quot;operationName&quot;</span><span class="o">)</span>

<span class="c1">// Record execution metrics</span>
<span class="kt">def</span><span class="w"> </span><span class="n">timer</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Timer</span><span class="o">.</span><span class="na">builder</span><span class="o">(</span><span class="s2">&quot;chain.execution.duration&quot;</span><span class="o">)</span>
<span class="w">    </span><span class="o">.</span><span class="na">tag</span><span class="o">(</span><span class="s2">&quot;chain.id&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">chainId</span><span class="o">)</span>
<span class="w">    </span><span class="o">.</span><span class="na">tag</span><span class="o">(</span><span class="s2">&quot;operation&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">operationName</span><span class="o">)</span>
<span class="w">    </span><span class="o">.</span><span class="na">register</span><span class="o">(</span><span class="n">meterRegistry</span><span class="o">)</span>

<span class="kt">def</span><span class="w"> </span><span class="n">sample</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">Timer</span><span class="o">.</span><span class="na">start</span><span class="o">(</span><span class="n">meterRegistry</span><span class="o">)</span>

<span class="k">try</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="c1">// Chain execution logic here</span>
<span class="w">    </span><span class="n">executeChainOperation</span><span class="o">(</span><span class="n">exchange</span><span class="o">)</span>

<span class="w">    </span><span class="c1">// Record success metric</span>
<span class="w">    </span><span class="n">meterRegistry</span><span class="o">.</span><span class="na">counter</span><span class="o">(</span><span class="s2">&quot;chain.execution.success&quot;</span><span class="o">,</span>
<span class="w">        </span><span class="s2">&quot;chain.id&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">chainId</span><span class="o">,</span>
<span class="w">        </span><span class="s2">&quot;operation&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">operationName</span>
<span class="w">    </span><span class="o">).</span><span class="na">increment</span><span class="o">()</span>

<span class="o">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="o">(</span><span class="n">Exception</span><span class="w"> </span><span class="n">e</span><span class="o">)</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="c1">// Record error metric</span>
<span class="w">    </span><span class="n">meterRegistry</span><span class="o">.</span><span class="na">counter</span><span class="o">(</span><span class="s2">&quot;chain.execution.error&quot;</span><span class="o">,</span>
<span class="w">        </span><span class="s2">&quot;chain.id&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">chainId</span><span class="o">,</span>
<span class="w">        </span><span class="s2">&quot;operation&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">operationName</span><span class="o">,</span>
<span class="w">        </span><span class="s2">&quot;error.type&quot;</span><span class="o">,</span><span class="w"> </span><span class="n">e</span><span class="o">.</span><span class="na">class</span><span class="o">.</span><span class="na">simpleName</span>
<span class="w">    </span><span class="o">).</span><span class="na">increment</span><span class="o">()</span>

<span class="w">    </span><span class="k">throw</span><span class="w"> </span><span class="n">e</span>
<span class="o">}</span><span class="w"> </span><span class="k">finally</span><span class="w"> </span><span class="o">{</span>
<span class="w">    </span><span class="n">sample</span><span class="o">.</span><span class="na">stop</span><span class="o">(</span><span class="n">timer</span><span class="o">)</span>
<span class="o">}</span>
</code></pre></div>
<hr />
<p><strong>Next</strong>: <a href="../03-service-catalog/">Service Catalog →</a></p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>