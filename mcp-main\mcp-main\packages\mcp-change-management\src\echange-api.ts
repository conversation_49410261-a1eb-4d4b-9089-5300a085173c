import axios, { AxiosInstance } from 'axios';

interface EChangeRequest {
  id: string;
  status: string;
  plannedStartTime: string;
  plannedEndTime: string;
  description: string;
}

interface TokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
}

export class EChangeAPI {
  private axiosInstance: AxiosInstance;
  private accessToken: string | null = null;
  private tokenExpiry: Date | null = null;

  constructor() {
    const apiUrl = process.env.ITSM_API_URL;
    if (!apiUrl) {
      throw new Error('ITSM_API_URL environment variable is required');
    }

    this.axiosInstance = axios.create({
      baseURL: apiUrl,
      maxBodyLength: Infinity,
    });

    // Add response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token might be expired, try to refresh and retry
          await this.authenticate();
          const originalRequest = error.config;
          originalRequest.headers.Authorization = `Bearer ${this.accessToken}`;
          return this.axiosInstance(originalRequest);
        }
        throw error;
      }
    );
  }

  private async authenticate(): Promise<void> {
    const clientId = process.env.ITSM_CLIENT_ID;
    const clientSecret = process.env.ITSM_CLIENT_SECRET;

    if (!clientId || !clientSecret) {
      throw new Error('ITSM credentials not found in environment variables');
    }

    // Check if we have a valid token
    if (this.accessToken && this.tokenExpiry && this.tokenExpiry > new Date()) {
      return;
    }

    try {
      const tokenUrl = process.env.ITSM_TOKEN_URL;
      if (!tokenUrl) {
        throw new Error('ITSM_TOKEN_URL environment variable is required');
      }

      const response = await axios.post<TokenResponse>(
        tokenUrl,
        new URLSearchParams({
          grant_type: 'client_credentials',
          scope: '90',
          client_id: clientId,
          client_secret: clientSecret,
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      this.accessToken = response.data.access_token;
      // Set token expiry with a small buffer (5 minutes)
      this.tokenExpiry = new Date(Date.now() + (response.data.expires_in - 300) * 1000);

      // Update axios instance headers
      this.axiosInstance.defaults.headers.common.Authorization = `Bearer ${this.accessToken}`;
    } catch (error: unknown) {
      console.error('Failed to authenticate with eChange API:', error);
      if (axios.isAxiosError(error)) {
        console.error('Response data:', error.response?.data);
        console.error('Response status:', error.response?.status);
        console.error('Response headers:', error.response?.headers);
        throw new Error(`Authentication failed: ${error.message}`);
      } else if (error instanceof Error) {
        throw new Error(`Authentication failed: ${error.message}`);
      } else {
        throw new Error('Authentication failed: Unknown error');
      }
    }
  }

  async getChanges(startDate?: Date, endDate?: Date): Promise<EChangeRequest[]> {
    console.error('Starting eChange getChanges method');
    const startTime = Date.now();
    try {
      await this.authenticate();
      console.error(`Authentication completed in ${Date.now() - startTime}ms`);

      let url = `${this.axiosInstance.defaults.baseURL}/changeRequests?channel=eChange&fields=description,plannedStartTime,plannedEndTime,status`;

      // Add date filters if provided
      if (startDate) {
        const startDateStr = startDate.toISOString();
        url += `&plannedStartTime>%3D${startDateStr}`;
      }
      if (endDate) {
        const endDateStr = endDate.toISOString();
        url += `&plannedStartTime<%3D${endDateStr}`;
      }

      console.error('Request URL:', url);

      console.error('Sending request to eChange API');
      const requestStartTime = Date.now();
      try {
        const response = await this.axiosInstance.get(url, {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Accept': 'application/json'
          },
          maxBodyLength: Infinity,
          timeout: 180000 // Increase timeout to 3 minutes
        });
        console.error(`Received response from eChange API in ${Date.now() - requestStartTime}ms`);
        console.error('Response status:', response.status);
        console.error('Response headers:', JSON.stringify(response.headers, null, 2));
        console.error('Response data:', JSON.stringify(response.data, null, 2));

        // Transform the response to match our interface
        const changes = response.data.changeRequest || response.data;
        const transformedData = Array.isArray(changes) ? changes.map((change: any) => ({
          id: change.id || change.changeId,
          status: change.status,
          plannedStartTime: change.plannedStartTime,
          plannedEndTime: change.plannedEndTime,
          description: change.description || '',
        })) : [];
        console.error(`Transformed ${transformedData.length} change requests`);

        console.error(`Total getChanges execution time: ${Date.now() - startTime}ms`);
        return transformedData;
      } catch (error: unknown) {
        console.error(`Failed to fetch changes from eChange after ${Date.now() - startTime}ms:`, error);
        if (axios.isAxiosError(error)) {
          console.error('Request config:', JSON.stringify(error.config, null, 2));
          console.error('Response data:', JSON.stringify(error.response?.data, null, 2));
          console.error('Response status:', error.response?.status);
          console.error('Response headers:', JSON.stringify(error.response?.headers, null, 2));
          throw new Error(`Failed to fetch changes from eChange: ${error.message} (${error.response?.status})\nResponse data: ${JSON.stringify(error.response?.data)}`);
        }
        throw new Error(`Failed to fetch changes from eChange: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } catch (error: unknown) {
      console.error(`Failed to fetch changes from eChange after ${Date.now() - startTime}ms:`, error);
      throw error;
    }
  }
}
