
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/cip-config/09-chain-catalog/">
      
      
        <link rel="prev" href="../08-reference/">
      
      
        <link rel="next" href="../../cpq-config/00-overview/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Chain Catalog - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#integration-chain-catalog" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Chain Catalog
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" checked>
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-quick-reference" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Quick Reference
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Quick Reference">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-process-chains" class="md-nav__link">
    <span class="md-ellipsis">
      Business Process Chains
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-integration-chains" class="md-nav__link">
    <span class="md-ellipsis">
      Service Integration Chains
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#address-and-location-chains" class="md-nav__link">
    <span class="md-ellipsis">
      Address and Location Chains
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#system-integration-chains" class="md-nav__link">
    <span class="md-ellipsis">
      System Integration Chains
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#utility-and-support-chains" class="md-nav__link">
    <span class="md-ellipsis">
      Utility and Support Chains
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-categories-summary" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Categories Summary
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Categories Summary">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#by-business-function" class="md-nav__link">
    <span class="md-ellipsis">
      By Business Function
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#by-trigger-type" class="md-nav__link">
    <span class="md-ellipsis">
      By Trigger Type
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#by-integration-pattern" class="md-nav__link">
    <span class="md-ellipsis">
      By Integration Pattern
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-characteristics" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Characteristics
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Characteristics">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#response-time-categories" class="md-nav__link">
    <span class="md-ellipsis">
      Response Time Categories
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#throughput-categories" class="md-nav__link">
    <span class="md-ellipsis">
      Throughput Categories
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#reliability-targets" class="md-nav__link">
    <span class="md-ellipsis">
      Reliability Targets
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-classifications" class="md-nav__link">
    <span class="md-ellipsis">
      Security Classifications
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Classifications">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#authentication-methods" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication Methods
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-sensitivity" class="md-nav__link">
    <span class="md-ellipsis">
      Data Sensitivity
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#operational-considerations" class="md-nav__link">
    <span class="md-ellipsis">
      Operational Considerations
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Operational Considerations">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#monitoring-priority" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring Priority
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#maintenance-windows" class="md-nav__link">
    <span class="md-ellipsis">
      Maintenance Windows
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../04-integration-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-quick-reference" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Quick Reference
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Quick Reference">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#business-process-chains" class="md-nav__link">
    <span class="md-ellipsis">
      Business Process Chains
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#service-integration-chains" class="md-nav__link">
    <span class="md-ellipsis">
      Service Integration Chains
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#address-and-location-chains" class="md-nav__link">
    <span class="md-ellipsis">
      Address and Location Chains
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#system-integration-chains" class="md-nav__link">
    <span class="md-ellipsis">
      System Integration Chains
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#utility-and-support-chains" class="md-nav__link">
    <span class="md-ellipsis">
      Utility and Support Chains
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#chain-categories-summary" class="md-nav__link">
    <span class="md-ellipsis">
      Chain Categories Summary
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Chain Categories Summary">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#by-business-function" class="md-nav__link">
    <span class="md-ellipsis">
      By Business Function
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#by-trigger-type" class="md-nav__link">
    <span class="md-ellipsis">
      By Trigger Type
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#by-integration-pattern" class="md-nav__link">
    <span class="md-ellipsis">
      By Integration Pattern
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#performance-characteristics" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Characteristics
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Performance Characteristics">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#response-time-categories" class="md-nav__link">
    <span class="md-ellipsis">
      Response Time Categories
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#throughput-categories" class="md-nav__link">
    <span class="md-ellipsis">
      Throughput Categories
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#reliability-targets" class="md-nav__link">
    <span class="md-ellipsis">
      Reliability Targets
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-classifications" class="md-nav__link">
    <span class="md-ellipsis">
      Security Classifications
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Security Classifications">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#authentication-methods" class="md-nav__link">
    <span class="md-ellipsis">
      Authentication Methods
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-sensitivity" class="md-nav__link">
    <span class="md-ellipsis">
      Data Sensitivity
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#operational-considerations" class="md-nav__link">
    <span class="md-ellipsis">
      Operational Considerations
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Operational Considerations">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#monitoring-priority" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring Priority
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#maintenance-windows" class="md-nav__link">
    <span class="md-ellipsis">
      Maintenance Windows
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="integration-chain-catalog">Integration Chain Catalog<a class="headerlink" href="#integration-chain-catalog" title="Permanent link">&para;</a></h1>
<h2 id="overview">Overview<a class="headerlink" href="#overview" title="Permanent link">&para;</a></h2>
<p>This document provides a quick reference catalog of all 15 integration chains in the CIP Configuration Package, organized by category and functionality.</p>
<h2 id="chain-quick-reference">Chain Quick Reference<a class="headerlink" href="#chain-quick-reference" title="Permanent link">&para;</a></h2>
<h3 id="business-process-chains">Business Process Chains<a class="headerlink" href="#business-process-chains" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Chain Name</th>
<th>ID</th>
<th>Purpose</th>
<th>Triggers</th>
<th>Key Systems</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Contract Agreement Generation</strong></td>
<td><code>8b4700f3-8527-41b1-a221-1c764516e490</code></td>
<td>Creates contract agreements in SFDC when quotes reach customer acceptance</td>
<td>Quote state changes</td>
<td>SFDC CRM, TMF 651</td>
</tr>
<tr>
<td><strong>Create Or Amend Credit Assessment</strong></td>
<td><code>901ec8bd-32c0-4e48-9996-0b2100c2b79d</code></td>
<td>Orchestrates credit evaluation workflows with Salesforce</td>
<td>Quote state changes</td>
<td>SFDC CRM, Work Log</td>
</tr>
<tr>
<td><strong>Create Financial Approval Request</strong></td>
<td><code>a576d7e9-a0be-4e2e-bc02-0377c137ac4a</code></td>
<td>Manages financial approval workflow for quotes</td>
<td>Quote state changes</td>
<td>SFDC CRM</td>
</tr>
<tr>
<td><strong>Update Opportunity</strong></td>
<td><code>7b7e29a8-0040-4d56-9be7-e7d90d872b27</code></td>
<td>Synchronizes quote changes with Salesforce opportunities</td>
<td>Quote state changes</td>
<td>SFDC CRM</td>
</tr>
</tbody>
</table>
<h3 id="service-integration-chains">Service Integration Chains<a class="headerlink" href="#service-integration-chains" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Chain Name</th>
<th>ID</th>
<th>Purpose</th>
<th>Triggers</th>
<th>Key Systems</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Get SFDC CRM Token</strong></td>
<td><code>0af070ae-20f0-4567-bbcc-6c7043e2d956</code></td>
<td>Manages OAuth2 authentication for Salesforce CRM</td>
<td>Chain calls</td>
<td>SFDC OAuth</td>
</tr>
<tr>
<td><strong>Register Billing Notification</strong></td>
<td><code>314187cb-4e20-4f23-be4f-6e7c7c422d44</code></td>
<td>Handles billing system notifications and errors</td>
<td>Event-driven</td>
<td>Billing Systems</td>
</tr>
<tr>
<td><strong>Mobility Create Memo</strong></td>
<td><code>85cbed6a-14c3-4af2-a4d1-096276df2e02</code></td>
<td>Creates memos in wireless product inventory</td>
<td>Service calls</td>
<td>TMF637 Product Inventory</td>
</tr>
<tr>
<td><strong>Get Billing Account</strong></td>
<td><code>b9b7d6ea-380a-437d-ab0c-d02f7fe3564b</code></td>
<td>Retrieves billing account information</td>
<td>HTTP API</td>
<td>Billing Systems</td>
</tr>
</tbody>
</table>
<h3 id="address-and-location-chains">Address and Location Chains<a class="headerlink" href="#address-and-location-chains" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Chain Name</th>
<th>ID</th>
<th>Purpose</th>
<th>Triggers</th>
<th>Key Systems</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>AMS Get Address Suggestions</strong></td>
<td><code>b9a99a9c-fbb1-4441-a6de-7d4da38e1942</code></td>
<td>Provides address suggestions via AMS</td>
<td>Service calls</td>
<td>Address Management</td>
</tr>
<tr>
<td><strong>SAQ Get Address Qualification</strong></td>
<td><code>93dbad2a-d7a5-4bc1-97e5-b6e1253117d3</code></td>
<td>Service address qualification processing</td>
<td>HTTP API</td>
<td>SAQ Service, Kong</td>
</tr>
<tr>
<td><strong>Market Resolver Resolve Market</strong></td>
<td><code>5028495c-60d5-48fc-9f10-57b13d42f9d4</code></td>
<td>Resolves market information and catalog data</td>
<td>HTTP API</td>
<td>Market Resolver</td>
</tr>
</tbody>
</table>
<h3 id="system-integration-chains">System Integration Chains<a class="headerlink" href="#system-integration-chains" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Chain Name</th>
<th>ID</th>
<th>Purpose</th>
<th>Triggers</th>
<th>Key Systems</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>P3MS</strong></td>
<td><code>4b5bae82-bab0-4211-8aab-0c81692ebc88</code></td>
<td>Product Management System integration</td>
<td>Service calls</td>
<td>Product Management</td>
</tr>
</tbody>
</table>
<h3 id="utility-and-support-chains">Utility and Support Chains<a class="headerlink" href="#utility-and-support-chains" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Chain Name</th>
<th>ID</th>
<th>Purpose</th>
<th>Triggers</th>
<th>Key Systems</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Service Qualification</strong></td>
<td><code>df8c9aea-1c48-4e2b-ad16-61bf05fe24a1</code></td>
<td>Validates service qualification requirements</td>
<td>Service calls</td>
<td>Qualification Services</td>
</tr>
<tr>
<td><strong>Utility Processing Chain</strong></td>
<td><code>f566c7e0-0e71-4583-a5c3-73dd3f40ae6d</code></td>
<td>General utility processing and support</td>
<td>Various</td>
<td>Support Services</td>
</tr>
<tr>
<td><strong>Support Processing Chain</strong></td>
<td><code>fcd41a8f-92b3-45a5-acdb-98573df69499</code></td>
<td>Additional support and processing functions</td>
<td>Various</td>
<td>Support Services</td>
</tr>
</tbody>
</table>
<h2 id="chain-categories-summary">Chain Categories Summary<a class="headerlink" href="#chain-categories-summary" title="Permanent link">&para;</a></h2>
<h3 id="by-business-function">By Business Function<a class="headerlink" href="#by-business-function" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Quote Management</strong>: 4 chains handling quote lifecycle and state management</li>
<li><strong>Authentication</strong>: 1 chain for secure token management</li>
<li><strong>Billing Integration</strong>: 2 chains for billing system coordination</li>
<li><strong>Address Services</strong>: 3 chains for location and address processing</li>
<li><strong>Product Management</strong>: 1 chain for product system integration</li>
<li><strong>Utility Services</strong>: 3 chains for general support functions</li>
</ul>
<h3 id="by-trigger-type">By Trigger Type<a class="headerlink" href="#by-trigger-type" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>RabbitMQ Events</strong>: 4 chains triggered by message broker events</li>
<li><strong>HTTP API</strong>: 4 chains exposed as REST endpoints</li>
<li><strong>Service Calls</strong>: 4 chains triggered by internal service calls</li>
<li><strong>Chain Calls</strong>: 1 chain triggered by other chains</li>
<li><strong>Mixed/Various</strong>: 2 chains with multiple trigger types</li>
</ul>
<h3 id="by-integration-pattern">By Integration Pattern<a class="headerlink" href="#by-integration-pattern" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>SFDC Integration</strong>: 4 chains integrating with Salesforce CRM</li>
<li><strong>TMF API Integration</strong>: 2 chains using TMF standard APIs</li>
<li><strong>Gateway Integration</strong>: 2 chains using Kong API Gateway</li>
<li><strong>Direct Service Integration</strong>: 4 chains with direct service calls</li>
<li><strong>Event-Driven Integration</strong>: 3 chains using message-based patterns</li>
</ul>
<h2 id="performance-characteristics">Performance Characteristics<a class="headerlink" href="#performance-characteristics" title="Permanent link">&para;</a></h2>
<h3 id="response-time-categories">Response Time Categories<a class="headerlink" href="#response-time-categories" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Fast (&lt; 500ms)</strong>: Address services, utility chains</li>
<li><strong>Medium (500ms-2s)</strong>: Service integration chains</li>
<li><strong>Slow (2-5s)</strong>: Business process chains with complex workflows</li>
</ul>
<h3 id="throughput-categories">Throughput Categories<a class="headerlink" href="#throughput-categories" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>High (&gt; 1000 req/min)</strong>: Utility and address services</li>
<li><strong>Medium (500-1000 req/min)</strong>: Service integration chains</li>
<li><strong>Low (&lt; 500 req/min)</strong>: Complex business process chains</li>
</ul>
<h3 id="reliability-targets">Reliability Targets<a class="headerlink" href="#reliability-targets" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Critical (99.99%)</strong>: Address and utility services</li>
<li><strong>High (99.95%)</strong>: Service integration chains</li>
<li><strong>Standard (99.9%)</strong>: Business process chains</li>
</ul>
<h2 id="security-classifications">Security Classifications<a class="headerlink" href="#security-classifications" title="Permanent link">&para;</a></h2>
<h3 id="authentication-methods">Authentication Methods<a class="headerlink" href="#authentication-methods" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>OAuth2</strong>: Salesforce CRM integration chains</li>
<li><strong>M2M</strong>: Internal service integration chains</li>
<li><strong>Bearer Token</strong>: External service integration chains</li>
<li><strong>Kong Gateway</strong>: API gateway protected chains</li>
<li><strong>None</strong>: Public utility chains</li>
</ul>
<h3 id="data-sensitivity">Data Sensitivity<a class="headerlink" href="#data-sensitivity" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>High</strong>: Billing and financial approval chains</li>
<li><strong>Medium</strong>: Quote and opportunity management chains</li>
<li><strong>Low</strong>: Address and utility service chains</li>
</ul>
<h2 id="operational-considerations">Operational Considerations<a class="headerlink" href="#operational-considerations" title="Permanent link">&para;</a></h2>
<h3 id="monitoring-priority">Monitoring Priority<a class="headerlink" href="#monitoring-priority" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>P1 (Critical)</strong>: Business process chains affecting customer experience</li>
<li><strong>P2 (High)</strong>: Service integration chains affecting system functionality</li>
<li><strong>P3 (Medium)</strong>: Utility and support chains</li>
</ul>
<h3 id="maintenance-windows">Maintenance Windows<a class="headerlink" href="#maintenance-windows" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Business Hours</strong>: Utility and support chains</li>
<li><strong>Off-Hours</strong>: Critical business process chains</li>
<li><strong>Flexible</strong>: Service integration chains</li>
</ul>
<p>This catalog serves as a quick reference for developers, operators, and business stakeholders working with the CIP Configuration Package integration chains.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": [], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>