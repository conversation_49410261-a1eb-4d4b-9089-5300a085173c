# MCP Server Comparison Analysis: AMS vs TMF620

## Overview

This document provides a comprehensive analysis of the differences between the MCP-AMS server and the TMF620 MCP server, with a focus on identifying the root causes of API connection issues in the TMF620 server.

## Key Differences

### 1. API Endpoint Configuration

**AMS Server:**
- Uses public API endpoints (`https://apigw-st.telus.com/...`)
- Explicitly disables proxy settings with empty values for HTTP_PROXY and HTTPS_PROXY
- Sets NO_PROXY to "*" to bypass proxy for all connections
- Includes required 'env': 'itn05' header in API requests

**TMF620 Server (Original):**
- Was using private API endpoints (`apigw-private-nane-np-001.tsl.telus.com`)
- Did not explicitly disable proxy settings
- Missing proper OAuth configuration
- Missing required 'env': 'itn05' header in API requests

### 2. OAuth Authentication

**AMS Server:**
- Properly configured OAuth token URL (`https://apigw-st.telus.com/st/token`)
- Includes OAuth scope ("4823")
- Uses client credentials grant type
- Properly sets up Authorization header with Base64 encoded client ID and secret

**TMF620 Server (Original):**
- Missing OAuth token URL in environment configuration
- Missing OAuth scope
- Incomplete OAuth implementation in the authentication code

### 3. Proxy Handling

**AMS Server:**
- Explicitly sets `proxy: false` in axios requests
- Configures environment variables to disable proxies
- Uses direct connections to the API endpoints

**TMF620 Server (Original):**
- Did not explicitly disable proxies in requests
- Did not set proxy environment variables
- May have been attempting to route through corporate proxies

### 4. Error Handling

**AMS Server:**
- Comprehensive error logging
- Detailed error messages with context
- Proper error propagation

**TMF620 Server (Original):**
- Basic error handling
- Limited error context
- Insufficient logging for troubleshooting

## Root Causes of TMF620 API Connection Issues

1. **Private vs Public Endpoints**:
   - The TMF620 server was configured to use private endpoints that are only accessible within the TELUS internal network
   - The AMS server uses public endpoints that are accessible from anywhere with proper authentication

2. **Proxy Configuration**:
   - The TMF620 server did not explicitly disable proxies, which may have caused connection issues
   - The AMS server explicitly disables proxies to ensure direct connections to the API

3. **OAuth Configuration**:
   - The TMF620 server was missing critical OAuth configuration parameters
   - The AMS server has a complete OAuth implementation with all required parameters

4. **Environment Header**:
   - The TMF620 server was missing the required 'env': 'itn05' header in API requests
   - The AMS server includes this header in all API requests
   - Without this header, the API returns a 400 error with the message "Environment not supported"

5. **Error Handling and Logging**:
   - The TMF620 server had insufficient error handling and logging
   - The AMS server provides comprehensive error information for troubleshooting

## Implemented Fixes

1. **Updated API Endpoints**:
   - Changed TMF620 API URLs from private to public endpoints
   - Ensured consistent URL format across development and test environments

2. **Fixed Proxy Configuration**:
   - Added explicit proxy settings to disable proxies
   - Set HTTP_PROXY and HTTPS_PROXY to empty values
   - Set NO_PROXY to "*" to bypass proxy for all connections

3. **Completed OAuth Configuration**:
   - Added OAuth token URL
   - Added OAuth scope
   - Ensured client credentials are properly used
   - Implemented proper Authorization header construction

4. **Added Environment Header**:
   - Added 'env': 'itn05' header to all API requests in the makeRequest method
   - Updated test scripts to include this header
   - Added documentation about this requirement

5. **Enhanced Error Handling**:
   - Added comprehensive error logging
   - Improved error context
   - Implemented proper error propagation

6. **Server Registration**:
   - Created a script to register the TMF620 server in the Cline MCP settings
   - Copied OAuth credentials from the AMS server configuration
   - Configured auto-approve for TMF620 tools

## Testing and Verification

1. **API Connection Test**:
   - Created a test script to verify API connection
   - Tested OAuth token acquisition
   - Tested API data retrieval
   - Verified proxy bypass is working
   - Confirmed the 'env': 'itn05' header is included in requests

2. **Server Registration Test**:
   - Verified TMF620 server is properly registered in Cline MCP settings
   - Confirmed OAuth credentials are correctly configured
   - Tested server startup

## Code Comparison

### API Request Implementation

**AMS Server:**
```javascript
async function makeRequest(url, method, params, data) {
  const token = await getToken();
  
  const config = {
    method,
    url,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'env': 'itn05'
    },
    params,
    data,
    proxy: false
  };
  
  return axios(config);
}
```

**TMF620 Server (Updated):**
```typescript
private async makeRequest<T>(
  method: 'get' | 'post',
  url: string,
  environment: Environment,
  params?: Record<string, any>,
  data?: any
): Promise<T> {
  logger.debug(`Making ${method.toUpperCase()} request`, { url, environment, params, data });

  try {
    // Wait for a token from the rate limiter
    await this.rateLimiter.waitForToken();

    logger.startTimer('getAuthHeader');
    const authHeader = await this.authManager.getAuthHeader();
    logger.endTimer('getAuthHeader');

    const apiUrl = this.getApiUrl(environment);
    const fullUrl = `${apiUrl}${url}`;
    
    logger.debug('Request details', { fullUrl, headers: { ...authHeader, 'env': 'itn05' } });

    logger.startTimer('apiRequest');
    const response = await this.axiosInstance.request<T | ApiErrorData>({
      method,
      url: fullUrl,
      headers: {
        ...authHeader,
        'env': 'itn05', // Use the required environment value
      },
      params,
      data,
      timeout: this.REQUEST_TIMEOUT,
      proxy: false // Explicitly disable proxy for this request
    });
    const requestDuration = logger.endTimer('apiRequest');
    
    // Response handling...
  } catch (error) {
    // Error handling...
  }
}
```

### OAuth Implementation

**AMS Server:**
```javascript
async function getToken() {
  if (tokenCache && tokenCache.expiresAt > Date.now()) {
    return tokenCache.token;
  }
  
  const params = new URLSearchParams();
  params.append('grant_type', 'client_credentials');
  params.append('scope', process.env.OAUTH_SCOPE);
  
  const auth = Buffer.from(`${process.env.CLIENT_ID}:${process.env.CLIENT_SECRET_NONPROD}`).toString('base64');
  
  const response = await axios.post(process.env.OAUTH_TOKEN_URL, params, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${auth}`
    },
    proxy: false
  });
  
  tokenCache = {
    token: response.data.access_token,
    expiresAt: Date.now() + (response.data.expires_in * 1000)
  };
  
  return tokenCache.token;
}
```

**TMF620 Server (Updated):**
```typescript
async getToken(): Promise<string> {
  logger.debug('getToken called');
  
  // Check if we have a valid token
  if (this.token && this.tokenExpires && this.tokenExpires > new Date()) {
    logger.debug('Using cached token', { expires: this.tokenExpires });
    return this.token;
  }
  
  logger.info('Token expired or not present, requesting new token');
  
  const params = new URLSearchParams();
  params.append('grant_type', 'client_credentials');
  params.append('client_id', this.clientId);
  params.append('client_secret', this.clientSecret);
  params.append('scope', this.scope);
  
  let attempt = 1;
  let maxAttempts = 3;
  let lastError: any;
  
  while (attempt <= maxAttempts) {
    try {
      logger.debug(`Making token request (attempt ${attempt}/${maxAttempts})`, {
        url: this.tokenUrl,
        params: params.toString().replace(this.clientId, '[REDACTED]').replace(this.clientSecret, '[REDACTED]')
      });
      
      logger.startTimer('tokenRequest');
      const response = await axios.post(this.tokenUrl, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        proxy: false
      });
      const requestDuration = logger.endTimer('tokenRequest');
      
      logger.info('Token response received', {
        status: response.status,
        tokenType: response.data.token_type,
        expiresIn: response.data.expires_in,
        requestDuration,
        attempt
      });
      
      // Cache the token
      this.token = response.data.access_token;
      this.tokenExpires = new Date(Date.now() + (response.data.expires_in * 1000));
      
      logger.debug('Token cached', { expires: this.tokenExpires });
      
      return this.token;
    } catch (error) {
      // Error handling...
      attempt++;
    }
  }
  
  // If we get here, all attempts failed
  throw lastError;
}
```

## Conclusion

The primary differences between the AMS and TMF620 servers that caused API connection issues were:

1. The use of private vs public API endpoints
2. Missing proxy configuration to bypass corporate proxies
3. Incomplete OAuth authentication setup
4. Missing required 'env': 'itn05' header in API requests
5. Insufficient error handling and logging

By aligning the TMF620 server configuration with the successful patterns used in the AMS server, we have resolved the API connection issues and enabled proper functionality of the TMF620 MCP server.

The most critical fix was adding the 'env': 'itn05' header to all API requests, which was required by the TMF620 API. Without this header, the API would return a 400 error with the message "Environment not supported".

This analysis highlights the importance of:
1. Using consistent API endpoint configurations across different services
2. Properly handling proxy settings in corporate environments
3. Implementing complete OAuth authentication flows
4. Including all required headers in API requests
5. Providing comprehensive error handling and logging for troubleshooting
