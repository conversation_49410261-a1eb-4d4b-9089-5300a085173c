import localFont from "next/font/local";

export const helveticaNeue = localFont({
  src: [
    {
      path: "../assets/HNforTELUSSA.otf",
      weight: "400",
      style: "normal",
    },
    {
      path: "../assets/HNforTELUSSA-Bold.otf",
      weight: "700",
    },
    {
      path: "../assets/HNforTELUSSA-Medium.otf",
      weight: "500",
    },
  ],
  display: "swap",
  preload: true,
  variable: "--font-sans",
});

export const helveticaNeueDisplay = localFont({
  src: "../assets/HNforTELUSSA-Display.otf",
  display: "swap",
  preload: true,
  variable: "--font-display",
});
