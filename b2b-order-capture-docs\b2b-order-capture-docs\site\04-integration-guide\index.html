
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Comprehensive documentation for TELUS B2B Order Capture ecosystem">
      
      
        <meta name="author" content="TELUS Cloud BSS Team">
      
      
        <link rel="canonical" href="http://localhost:8000/04-integration-guide/">
      
      
        <link rel="prev" href="../cpq-config/05-development-guide/">
      
      
        <link rel="next" href="../05-development-guide/">
      
      
      <link rel="icon" href="../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.14">
    
    
      
        <title>Integration Guide - TELUS B2B Order Capture Documentation</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="deep-purple" data-md-color-accent="purple">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#integration-guide" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

  

<header class="md-header md-header--shadow" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-header__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            TELUS B2B Order Capture Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Integration Guide
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    



<nav class="md-nav md-nav--primary" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="TELUS B2B Order Capture Documentation" class="md-nav__button md-logo" aria-label="TELUS B2B Order Capture Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    TELUS B2B Order Capture Documentation
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href=".." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Frontend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Frontend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/02-custom-components/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Custom Components
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/03-state-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/04-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../frontend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Backend Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Backend Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/02-rest-api-design/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    REST API Design
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/03-service-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/04-integration-layer/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Layer
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../backend/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Extensions Application
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Extensions Application
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/02-quote-modificators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quote Modificators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/03-bandwidth-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Bandwidth Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/04-state-validators/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    State Validators
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../extensions/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" >
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    QSS Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            QSS Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/01-plugin-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Plugin Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/02-snapshot-generation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Snapshot Generation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/03-storage-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Storage Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../qss-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_7" >
        
          
          <label class="md-nav__link" for="__nav_7" id="__nav_7_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Bulk Operation Extension
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_7_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_7">
            <span class="md-nav__icon md-icon"></span>
            Bulk Operation Extension
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/01-nifi-flow-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NiFi Flow Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/02-api-integration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    API Integration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/03-deployment-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../bulk-operation-extension/04-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_8" >
        
          
          <label class="md-nav__link" for="__nav_8" id="__nav_8_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CIP Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_8_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_8">
            <span class="md-nav__icon md-icon"></span>
            CIP Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/01-architecture/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Architecture
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/02-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/03-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/04-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/05-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/06-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/07-troubleshooting/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Troubleshooting
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/08-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cip-config/09-chain-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chain Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_9" >
        
          
          <label class="md-nav__link" for="__nav_9" id="__nav_9_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    CPQ Configuration
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_9_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_9">
            <span class="md-nav__icon md-icon"></span>
            CPQ Configuration
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/00-overview/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Overview
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/01-configuration-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/02-integration-chains/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Integration Chains
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/03-service-catalog/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Service Catalog
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/04-security-configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Security Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../cpq-config/05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Integration Guide
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-integration-overview" class="md-nav__link">
    <span class="md-ellipsis">
      System Integration Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="System Integration Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#integration-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#inter-repository-communication" class="md-nav__link">
    <span class="md-ellipsis">
      Inter-Repository Communication
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Inter-Repository Communication">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-to-backend-communication" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend to Backend Communication
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Frontend to Backend Communication">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#http-client-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      HTTP Client Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#api-service-pattern" class="md-nav__link">
    <span class="md-ellipsis">
      API Service Pattern
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-to-extension-communication" class="md-nav__link">
    <span class="md-ellipsis">
      Backend to Extension Communication
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Backend to Extension Communication">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-interface-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Interface Implementation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#external-system-integrations" class="md-nav__link">
    <span class="md-ellipsis">
      External System Integrations
    </span>
  </a>
  
    <nav class="md-nav" aria-label="External System Integrations">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#order-capture-product-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Order Capture Product Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Order Capture Product Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#client-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Client Implementation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#geocoding-service-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Geocoding Service Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#api-contracts" class="md-nav__link">
    <span class="md-ellipsis">
      API Contracts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="API Contracts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rest-api-specifications" class="md-nav__link">
    <span class="md-ellipsis">
      REST API Specifications
    </span>
  </a>
  
    <nav class="md-nav" aria-label="REST API Specifications">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-management-apis" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Management APIs
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#websocket-api-contracts" class="md-nav__link">
    <span class="md-ellipsis">
      WebSocket API Contracts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="WebSocket API Contracts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#real-time-quote-updates" class="md-nav__link">
    <span class="md-ellipsis">
      Real-time Quote Updates
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#data-flow-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Data Flow Patterns
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Flow Patterns">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-creation-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Creation Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#order-processing-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Order Processing Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bulk-operation-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Operation Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Bulk Operation Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#integration-with-bulk-operation-extension" class="md-nav__link">
    <span class="md-ellipsis">
      Integration with Bulk Operation Extension
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration with Bulk Operation Extension">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#address-validation-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Address Validation Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#callback-handling" class="md-nav__link">
    <span class="md-ellipsis">
      Callback Handling
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-synchronization" class="md-nav__link">
    <span class="md-ellipsis">
      Data Synchronization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Synchronization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#event-driven-updates" class="md-nav__link">
    <span class="md-ellipsis">
      Event-Driven Updates
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#cip-configuration-integration" class="md-nav__link">
    <span class="md-ellipsis">
      CIP Configuration Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="CIP Configuration Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#cloud-integration-platform-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Cloud Integration Platform Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Cloud Integration Platform Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#integration-flow-orchestration" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Flow Orchestration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cross-repository-event-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Cross-Repository Event Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-driven-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration-Driven Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cross-repository-data-synchronization" class="md-nav__link">
    <span class="md-ellipsis">
      Cross-Repository Data Synchronization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Cross-Repository Data Synchronization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#event-driven-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Event-Driven Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Patterns
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../05-development-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Development Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../06-deployment-guide/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Deployment Guide
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../07-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Reference
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../DOCUMENTATION_SUMMARY/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Documentation Summary
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../SETUP/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Setup Guide
    
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#table-of-contents" class="md-nav__link">
    <span class="md-ellipsis">
      Table of Contents
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#system-integration-overview" class="md-nav__link">
    <span class="md-ellipsis">
      System Integration Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="System Integration Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#integration-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Architecture
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#inter-repository-communication" class="md-nav__link">
    <span class="md-ellipsis">
      Inter-Repository Communication
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Inter-Repository Communication">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#frontend-to-backend-communication" class="md-nav__link">
    <span class="md-ellipsis">
      Frontend to Backend Communication
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Frontend to Backend Communication">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#http-client-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      HTTP Client Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#api-service-pattern" class="md-nav__link">
    <span class="md-ellipsis">
      API Service Pattern
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#backend-to-extension-communication" class="md-nav__link">
    <span class="md-ellipsis">
      Backend to Extension Communication
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Backend to Extension Communication">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#plugin-interface-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Plugin Interface Implementation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#external-system-integrations" class="md-nav__link">
    <span class="md-ellipsis">
      External System Integrations
    </span>
  </a>
  
    <nav class="md-nav" aria-label="External System Integrations">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#order-capture-product-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Order Capture Product Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Order Capture Product Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#client-implementation" class="md-nav__link">
    <span class="md-ellipsis">
      Client Implementation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#geocoding-service-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Geocoding Service Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#api-contracts" class="md-nav__link">
    <span class="md-ellipsis">
      API Contracts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="API Contracts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#rest-api-specifications" class="md-nav__link">
    <span class="md-ellipsis">
      REST API Specifications
    </span>
  </a>
  
    <nav class="md-nav" aria-label="REST API Specifications">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-management-apis" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Management APIs
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#websocket-api-contracts" class="md-nav__link">
    <span class="md-ellipsis">
      WebSocket API Contracts
    </span>
  </a>
  
    <nav class="md-nav" aria-label="WebSocket API Contracts">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#real-time-quote-updates" class="md-nav__link">
    <span class="md-ellipsis">
      Real-time Quote Updates
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#data-flow-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Data Flow Patterns
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Flow Patterns">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quote-creation-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Quote Creation Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#order-processing-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Order Processing Flow
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#bulk-operation-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Operation Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Bulk Operation Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#integration-with-bulk-operation-extension" class="md-nav__link">
    <span class="md-ellipsis">
      Integration with Bulk Operation Extension
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Integration with Bulk Operation Extension">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#address-validation-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Address Validation Integration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#callback-handling" class="md-nav__link">
    <span class="md-ellipsis">
      Callback Handling
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-synchronization" class="md-nav__link">
    <span class="md-ellipsis">
      Data Synchronization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Synchronization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#event-driven-updates" class="md-nav__link">
    <span class="md-ellipsis">
      Event-Driven Updates
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#cip-configuration-integration" class="md-nav__link">
    <span class="md-ellipsis">
      CIP Configuration Integration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="CIP Configuration Integration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#cloud-integration-platform-configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Cloud Integration Platform Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Cloud Integration Platform Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#integration-flow-orchestration" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Flow Orchestration
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cross-repository-event-flow" class="md-nav__link">
    <span class="md-ellipsis">
      Cross-Repository Event Flow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#configuration-driven-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration-Driven Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
          <li class="md-nav__item">
  <a href="#cross-repository-data-synchronization" class="md-nav__link">
    <span class="md-ellipsis">
      Cross-Repository Data Synchronization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Cross-Repository Data Synchronization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#event-driven-architecture" class="md-nav__link">
    <span class="md-ellipsis">
      Event-Driven Architecture
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#integration-patterns" class="md-nav__link">
    <span class="md-ellipsis">
      Integration Patterns
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="integration-guide">Integration Guide<a class="headerlink" href="#integration-guide" title="Permanent link">&para;</a></h1>
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ul>
<li><a href="#system-integration-overview">System Integration Overview</a></li>
<li><a href="#inter-repository-communication">Inter-Repository Communication</a></li>
<li><a href="#external-system-integrations">External System Integrations</a></li>
<li><a href="#api-contracts">API Contracts</a></li>
<li><a href="#data-flow-patterns">Data Flow Patterns</a></li>
<li><a href="#bulk-operation-integration">Bulk Operation Integration</a></li>
<li><a href="#cip-configuration-integration">CIP Configuration Integration</a></li>
</ul>
<h2 id="system-integration-overview">System Integration Overview<a class="headerlink" href="#system-integration-overview" title="Permanent link">&para;</a></h2>
<p>The TELUS B2B Order Capture ecosystem integrates multiple systems to provide a comprehensive order management solution. This guide covers integration patterns, API contracts, and data flow between components.</p>
<h3 id="integration-architecture">Integration Architecture<a class="headerlink" href="#integration-architecture" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Frontend Layer&quot;
        FE[Angular Frontend]
        FE_COMP[Custom TELUS Components]
        FE_STORE[NgRx Store]
    end

    subgraph &quot;Backend Layer&quot;
        BE[Spring Boot Backend]
        BE_API[REST APIs]
        BE_SVC[Business Services]
    end

    subgraph &quot;Extension Layer&quot;
        EXT[QES Plugins]
        EXT_VAL[Bandwidth Validators]
        EXT_MOD[Quote Modificators]
    end

    subgraph &quot;TELUS Core Systems&quot;
        OCP[Order Capture Product]
        BSS[Business Support Systems]
        BULK[Bulk Operation Extension]
    end

    subgraph &quot;External Services&quot;
        GEO[Geocoding Service]
        MAPS[Google Maps API]
        AUTH[Authentication Service]
    end

    FE --&gt; BE_API
    FE_COMP --&gt; FE_STORE
    FE_STORE --&gt; BE_API
    BE_API --&gt; BE_SVC
    BE_SVC --&gt; EXT
    BE_SVC --&gt; OCP
    EXT --&gt; EXT_VAL
    EXT --&gt; EXT_MOD
    OCP --&gt; BSS
    BE_SVC --&gt; BULK
    FE --&gt; MAPS
    BE_SVC --&gt; GEO
    FE --&gt; AUTH
</code></pre></div>
<h2 id="inter-repository-communication">Inter-Repository Communication<a class="headerlink" href="#inter-repository-communication" title="Permanent link">&para;</a></h2>
<h3 id="frontend-to-backend-communication">Frontend to Backend Communication<a class="headerlink" href="#frontend-to-backend-communication" title="Permanent link">&para;</a></h3>
<h4 id="http-client-configuration">HTTP Client Configuration<a class="headerlink" href="#http-client-configuration" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// Angular HTTP interceptor for API communication</span>
<span class="kd">@Injectable</span><span class="p">()</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">ApiInterceptor</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">HttpInterceptor</span><span class="w"> </span><span class="p">{</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="k">private</span><span class="w"> </span><span class="nx">authService</span><span class="o">:</span><span class="w"> </span><span class="kt">AuthService</span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="nx">intercept</span><span class="p">(</span><span class="nx">req</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpRequest</span><span class="o">&lt;</span><span class="nx">any</span><span class="o">&gt;</span><span class="p">,</span><span class="w"> </span><span class="nx">next</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpHandler</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">HttpEvent</span><span class="o">&lt;</span><span class="nx">any</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Add authentication token</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">authToken</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">authService</span><span class="p">.</span><span class="nx">getToken</span><span class="p">();</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">authReq</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">req</span><span class="p">.</span><span class="nx">clone</span><span class="p">({</span>
<span class="w">      </span><span class="nx">setHeaders</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">Authorization</span><span class="o">:</span><span class="w"> </span><span class="sb">`Bearer </span><span class="si">${</span><span class="nx">authToken</span><span class="si">}</span><span class="sb">`</span><span class="p">,</span>
<span class="w">        </span><span class="s1">&#39;Content-Type&#39;</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;application/json&#39;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Add error handling</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">next</span><span class="p">.</span><span class="nx">handle</span><span class="p">(</span><span class="nx">authReq</span><span class="p">).</span><span class="nx">pipe</span><span class="p">(</span>
<span class="w">      </span><span class="nx">catchError</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">handleError</span><span class="p">)</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">handleError</span><span class="p">(</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpErrorResponse</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">never</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">.</span><span class="nx">status</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="mf">401</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Handle authentication errors</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">authService</span><span class="p">.</span><span class="nx">logout</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">throwError</span><span class="p">(</span><span class="nx">error</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="api-service-pattern">API Service Pattern<a class="headerlink" href="#api-service-pattern" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="kd">@Injectable</span><span class="p">({</span>
<span class="w">  </span><span class="nx">providedIn</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;root&#39;</span>
<span class="p">})</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">QuoteService</span><span class="w"> </span><span class="p">{</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">baseUrl</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;/api/v1/quotes&#39;</span><span class="p">;</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="k">private</span><span class="w"> </span><span class="nx">http</span><span class="o">:</span><span class="w"> </span><span class="kt">HttpClient</span><span class="p">)</span><span class="w"> </span><span class="p">{}</span>

<span class="w">  </span><span class="nx">getQuote</span><span class="p">(</span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">QuoteResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">get</span><span class="o">&lt;</span><span class="nx">QuoteResponse</span><span class="o">&gt;</span><span class="p">(</span><span class="sb">`</span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">baseUrl</span><span class="si">}</span><span class="sb">/</span><span class="si">${</span><span class="nx">quoteId</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">createQuote</span><span class="p">(</span><span class="nx">request</span><span class="o">:</span><span class="w"> </span><span class="kt">CreateQuoteRequest</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">QuoteResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">post</span><span class="o">&lt;</span><span class="nx">QuoteResponse</span><span class="o">&gt;</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">baseUrl</span><span class="p">,</span><span class="w"> </span><span class="nx">request</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">updateQuote</span><span class="p">(</span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">,</span><span class="w"> </span><span class="nx">request</span><span class="o">:</span><span class="w"> </span><span class="kt">UpdateQuoteRequest</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">QuoteResponse</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">http</span><span class="p">.</span><span class="nx">put</span><span class="o">&lt;</span><span class="nx">QuoteResponse</span><span class="o">&gt;</span><span class="p">(</span><span class="sb">`</span><span class="si">${</span><span class="k">this</span><span class="p">.</span><span class="nx">baseUrl</span><span class="si">}</span><span class="sb">/</span><span class="si">${</span><span class="nx">quoteId</span><span class="si">}</span><span class="sb">`</span><span class="p">,</span><span class="w"> </span><span class="nx">request</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="c1">// Real-time quote updates via WebSocket</span>
<span class="w">  </span><span class="nx">getQuoteUpdates</span><span class="p">(</span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">Observable</span><span class="o">&lt;</span><span class="nx">QuoteUpdate</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">Observable</span><span class="p">(</span><span class="nx">observer</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">ws</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">WebSocket</span><span class="p">(</span><span class="sb">`ws://localhost:8080/quotes/</span><span class="si">${</span><span class="nx">quoteId</span><span class="si">}</span><span class="sb">/updates`</span><span class="p">);</span>

<span class="w">      </span><span class="nx">ws</span><span class="p">.</span><span class="nx">onmessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">event</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">observer</span><span class="p">.</span><span class="nx">next</span><span class="p">(</span><span class="nb">JSON</span><span class="p">.</span><span class="nx">parse</span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">data</span><span class="p">));</span>
<span class="w">      </span><span class="p">};</span>

<span class="w">      </span><span class="nx">ws</span><span class="p">.</span><span class="nx">onerror</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">error</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">observer</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="nx">error</span><span class="p">);</span>
<span class="w">      </span><span class="nx">ws</span><span class="p">.</span><span class="nx">onclose</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">observer</span><span class="p">.</span><span class="nx">complete</span><span class="p">();</span>

<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">ws</span><span class="p">.</span><span class="nx">close</span><span class="p">();</span>
<span class="w">    </span><span class="p">});</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="backend-to-extension-communication">Backend to Extension Communication<a class="headerlink" href="#backend-to-extension-communication" title="Permanent link">&para;</a></h3>
<h4 id="plugin-interface-implementation">Plugin Interface Implementation<a class="headerlink" href="#plugin-interface-implementation" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="nd">@Service</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">QuoteProcessingService</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">List</span><span class="o">&lt;</span><span class="n">QuoteDeltaModificator</span><span class="o">&gt;</span><span class="w"> </span><span class="n">modificators</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">QuoteRepository</span><span class="w"> </span><span class="n">quoteRepository</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">QuoteResponse</span><span class="w"> </span><span class="nf">processQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">QuoteProcessingRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">quote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quoteRepository</span><span class="p">.</span><span class="na">findById</span><span class="p">(</span><span class="n">quoteId</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">orElseThrow</span><span class="p">(()</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">QuoteNotFoundException</span><span class="p">(</span><span class="n">quoteId</span><span class="p">));</span>

<span class="w">        </span><span class="c1">// Create modification context</span>
<span class="w">        </span><span class="n">QuoteModificationContext</span><span class="w"> </span><span class="n">context</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">QuoteModificationContext</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">quote</span><span class="p">(</span><span class="n">quote</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">request</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">customer</span><span class="p">(</span><span class="n">getCustomer</span><span class="p">(</span><span class="n">quote</span><span class="p">.</span><span class="na">getCustomerId</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Apply all registered modificators</span>
<span class="w">        </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="n">aggregatedDelta</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">QuoteDelta</span><span class="p">.</span><span class="na">empty</span><span class="p">();</span>
<span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">QuoteDeltaModificator</span><span class="w"> </span><span class="n">modificator</span><span class="w"> </span><span class="p">:</span><span class="w"> </span><span class="n">modificators</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="n">QuoteDelta</span><span class="w"> </span><span class="n">delta</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">modificator</span><span class="p">.</span><span class="na">modifyQuote</span><span class="p">(</span><span class="n">context</span><span class="p">);</span>
<span class="w">            </span><span class="n">aggregatedDelta</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">aggregatedDelta</span><span class="p">.</span><span class="na">merge</span><span class="p">(</span><span class="n">delta</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="c1">// Apply modifications to quote</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">modifiedQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">applyDelta</span><span class="p">(</span><span class="n">quote</span><span class="p">,</span><span class="w"> </span><span class="n">aggregatedDelta</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Save and return</span>
<span class="w">        </span><span class="n">Quote</span><span class="w"> </span><span class="n">savedQuote</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">quoteRepository</span><span class="p">.</span><span class="na">save</span><span class="p">(</span><span class="n">modifiedQuote</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">quoteMapper</span><span class="p">.</span><span class="na">toResponse</span><span class="p">(</span><span class="n">savedQuote</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="external-system-integrations">External System Integrations<a class="headerlink" href="#external-system-integrations" title="Permanent link">&para;</a></h2>
<h3 id="order-capture-product-integration">Order Capture Product Integration<a class="headerlink" href="#order-capture-product-integration" title="Permanent link">&para;</a></h3>
<h4 id="configuration">Configuration<a class="headerlink" href="#configuration" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># application.yml</span>
<span class="nt">telus</span><span class="p">:</span>
<span class="w">  </span><span class="nt">order-capture</span><span class="p">:</span>
<span class="w">    </span><span class="nt">base-url</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">https://order-capture.telus.com/api/v2</span>
<span class="w">    </span><span class="nt">timeout</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">30s</span>
<span class="w">    </span><span class="nt">retry</span><span class="p">:</span>
<span class="w">      </span><span class="nt">max-attempts</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">3</span>
<span class="w">      </span><span class="nt">backoff-delay</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1s</span>
<span class="w">    </span><span class="nt">authentication</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">oauth2</span>
<span class="w">      </span><span class="nt">client-id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${OCP_CLIENT_ID}</span>
<span class="w">      </span><span class="nt">client-secret</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">${OCP_CLIENT_SECRET}</span>
<span class="w">      </span><span class="nt">token-url</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">https://auth.telus.com/oauth/token</span>
</code></pre></div>
<h4 id="client-implementation">Client Implementation<a class="headerlink" href="#client-implementation" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="nd">@Component</span>
<span class="nd">@Slf4j</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">OrderCaptureClient</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">WebClient</span><span class="w"> </span><span class="n">webClient</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">OrderCaptureProperties</span><span class="w"> </span><span class="n">properties</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">OAuth2TokenService</span><span class="w"> </span><span class="n">tokenService</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Mono</span><span class="o">&lt;</span><span class="n">OrderCaptureQuote</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">getQuote</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">quoteId</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">webClient</span>
<span class="w">            </span><span class="p">.</span><span class="na">get</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">uri</span><span class="p">(</span><span class="s">&quot;/quotes/{quoteId}&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">headers</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">addAuthHeaders</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">retrieve</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">onStatus</span><span class="p">(</span><span class="n">HttpStatus</span><span class="p">::</span><span class="n">isError</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">::</span><span class="n">handleError</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">bodyToMono</span><span class="p">(</span><span class="n">OrderCaptureQuote</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">retryWhen</span><span class="p">(</span><span class="n">Retry</span><span class="p">.</span><span class="na">backoff</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="n">Duration</span><span class="p">.</span><span class="na">ofSeconds</span><span class="p">(</span><span class="mi">1</span><span class="p">)))</span>
<span class="w">            </span><span class="p">.</span><span class="na">doOnSuccess</span><span class="p">(</span><span class="n">quote</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Retrieved quote {} from OCP&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">))</span>
<span class="w">            </span><span class="p">.</span><span class="na">doOnError</span><span class="p">(</span><span class="n">error</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">log</span><span class="p">.</span><span class="na">error</span><span class="p">(</span><span class="s">&quot;Failed to retrieve quote {} from OCP&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">,</span><span class="w"> </span><span class="n">error</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Mono</span><span class="o">&lt;</span><span class="n">String</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">createQuote</span><span class="p">(</span><span class="n">CreateOrderCaptureQuoteRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">webClient</span>
<span class="w">            </span><span class="p">.</span><span class="na">post</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">uri</span><span class="p">(</span><span class="s">&quot;/quotes&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">headers</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">addAuthHeaders</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">bodyValue</span><span class="p">(</span><span class="n">request</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">retrieve</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">bodyToMono</span><span class="p">(</span><span class="n">CreateQuoteResponse</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">map</span><span class="p">(</span><span class="n">CreateQuoteResponse</span><span class="p">::</span><span class="n">getQuoteId</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">doOnSuccess</span><span class="p">(</span><span class="n">quoteId</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">log</span><span class="p">.</span><span class="na">info</span><span class="p">(</span><span class="s">&quot;Created quote {} in OCP&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">quoteId</span><span class="p">));</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">addAuthHeaders</span><span class="p">(</span><span class="n">HttpHeaders</span><span class="w"> </span><span class="n">headers</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">token</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">tokenService</span><span class="p">.</span><span class="na">getAccessToken</span><span class="p">();</span>
<span class="w">        </span><span class="n">headers</span><span class="p">.</span><span class="na">setBearerAuth</span><span class="p">(</span><span class="n">token</span><span class="p">);</span>
<span class="w">        </span><span class="n">headers</span><span class="p">.</span><span class="na">setContentType</span><span class="p">(</span><span class="n">MediaType</span><span class="p">.</span><span class="na">APPLICATION_JSON</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="geocoding-service-integration">Geocoding Service Integration<a class="headerlink" href="#geocoding-service-integration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="nd">@Service</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">GeocodingService</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">WebClient</span><span class="w"> </span><span class="n">geoClient</span><span class="p">;</span>
<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">GeocodingProperties</span><span class="w"> </span><span class="n">properties</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">Mono</span><span class="o">&lt;</span><span class="n">GeocodingResult</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">geocodeAddress</span><span class="p">(</span><span class="n">String</span><span class="w"> </span><span class="n">address</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">geoClient</span>
<span class="w">            </span><span class="p">.</span><span class="na">get</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">uri</span><span class="p">(</span><span class="n">uriBuilder</span><span class="w"> </span><span class="o">-&gt;</span><span class="w"> </span><span class="n">uriBuilder</span>
<span class="w">                </span><span class="p">.</span><span class="na">path</span><span class="p">(</span><span class="s">&quot;/geocode/json&quot;</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">queryParam</span><span class="p">(</span><span class="s">&quot;address&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">address</span><span class="p">)</span>
<span class="w">                </span><span class="p">.</span><span class="na">queryParam</span><span class="p">(</span><span class="s">&quot;key&quot;</span><span class="p">,</span><span class="w"> </span><span class="n">properties</span><span class="p">.</span><span class="na">getApiKey</span><span class="p">())</span>
<span class="w">                </span><span class="p">.</span><span class="na">build</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">retrieve</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">bodyToMono</span><span class="p">(</span><span class="n">GoogleGeocodingResponse</span><span class="p">.</span><span class="na">class</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">map</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">mapToGeocodingResult</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">onErrorMap</span><span class="p">(</span><span class="k">this</span><span class="p">::</span><span class="n">mapGeocodingError</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="n">GeocodingResult</span><span class="w"> </span><span class="nf">mapToGeocodingResult</span><span class="p">(</span><span class="n">GoogleGeocodingResponse</span><span class="w"> </span><span class="n">response</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">response</span><span class="p">.</span><span class="na">getResults</span><span class="p">().</span><span class="na">isEmpty</span><span class="p">())</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="k">throw</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">AddressNotFoundException</span><span class="p">(</span><span class="s">&quot;No results found for address&quot;</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>

<span class="w">        </span><span class="n">GoogleGeocodingResult</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">response</span><span class="p">.</span><span class="na">getResults</span><span class="p">().</span><span class="na">get</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">GeocodingResult</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">formattedAddress</span><span class="p">(</span><span class="n">result</span><span class="p">.</span><span class="na">getFormattedAddress</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">latitude</span><span class="p">(</span><span class="n">result</span><span class="p">.</span><span class="na">getGeometry</span><span class="p">().</span><span class="na">getLocation</span><span class="p">().</span><span class="na">getLat</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">longitude</span><span class="p">(</span><span class="n">result</span><span class="p">.</span><span class="na">getGeometry</span><span class="p">().</span><span class="na">getLocation</span><span class="p">().</span><span class="na">getLng</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">accuracy</span><span class="p">(</span><span class="n">result</span><span class="p">.</span><span class="na">getGeometry</span><span class="p">().</span><span class="na">getLocationType</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="api-contracts">API Contracts<a class="headerlink" href="#api-contracts" title="Permanent link">&para;</a></h2>
<h3 id="rest-api-specifications">REST API Specifications<a class="headerlink" href="#rest-api-specifications" title="Permanent link">&para;</a></h3>
<h4 id="quote-management-apis">Quote Management APIs<a class="headerlink" href="#quote-management-apis" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># OpenAPI specification excerpt</span>
<span class="nt">paths</span><span class="p">:</span>
<span class="w">  </span><span class="nt">/api/v1/quotes</span><span class="p">:</span>
<span class="w">    </span><span class="nt">post</span><span class="p">:</span>
<span class="w">      </span><span class="nt">summary</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Create new quote</span>
<span class="w">      </span><span class="nt">requestBody</span><span class="p">:</span>
<span class="w">        </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">        </span><span class="nt">content</span><span class="p">:</span>
<span class="w">          </span><span class="nt">application/json</span><span class="p">:</span>
<span class="w">            </span><span class="nt">schema</span><span class="p">:</span>
<span class="w">              </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/CreateQuoteRequest&#39;</span>
<span class="w">      </span><span class="nt">responses</span><span class="p">:</span>
<span class="w">        </span><span class="s">&#39;201&#39;</span><span class="p p-Indicator">:</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Quote created successfully</span>
<span class="w">          </span><span class="nt">content</span><span class="p">:</span>
<span class="w">            </span><span class="nt">application/json</span><span class="p">:</span>
<span class="w">              </span><span class="nt">schema</span><span class="p">:</span>
<span class="w">                </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/QuoteResponse&#39;</span>
<span class="w">        </span><span class="s">&#39;400&#39;</span><span class="p p-Indicator">:</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Invalid request</span>
<span class="w">          </span><span class="nt">content</span><span class="p">:</span>
<span class="w">            </span><span class="nt">application/json</span><span class="p">:</span>
<span class="w">              </span><span class="nt">schema</span><span class="p">:</span>
<span class="w">                </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/ErrorResponse&#39;</span>

<span class="w">  </span><span class="l l-Scalar l-Scalar-Plain">/api/v1/quotes/{quoteId}</span><span class="p p-Indicator">:</span>
<span class="w">    </span><span class="nt">get</span><span class="p">:</span>
<span class="w">      </span><span class="nt">summary</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Get quote by ID</span>
<span class="w">      </span><span class="nt">parameters</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">quoteId</span>
<span class="w">          </span><span class="nt">in</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">path</span>
<span class="w">          </span><span class="nt">required</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">true</span>
<span class="w">          </span><span class="nt">schema</span><span class="p">:</span>
<span class="w">            </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">      </span><span class="nt">responses</span><span class="p">:</span>
<span class="w">        </span><span class="s">&#39;200&#39;</span><span class="p p-Indicator">:</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Quote retrieved successfully</span>
<span class="w">          </span><span class="nt">content</span><span class="p">:</span>
<span class="w">            </span><span class="nt">application/json</span><span class="p">:</span>
<span class="w">              </span><span class="nt">schema</span><span class="p">:</span>
<span class="w">                </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/QuoteResponse&#39;</span>
<span class="w">        </span><span class="s">&#39;404&#39;</span><span class="p p-Indicator">:</span>
<span class="w">          </span><span class="nt">description</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Quote not found</span>

<span class="nt">components</span><span class="p">:</span>
<span class="w">  </span><span class="nt">schemas</span><span class="p">:</span>
<span class="w">    </span><span class="nt">CreateQuoteRequest</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">object</span>
<span class="w">      </span><span class="nt">required</span><span class="p">:</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">customerId</span>
<span class="w">        </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">items</span>
<span class="w">      </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">        </span><span class="nt">customerId</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">        </span><span class="nt">items</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">array</span>
<span class="w">          </span><span class="nt">items</span><span class="p">:</span>
<span class="w">            </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/QuoteItemRequest&#39;</span>
<span class="w">        </span><span class="nt">location</span><span class="p">:</span>
<span class="w">          </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/LocationRequest&#39;</span>

<span class="w">    </span><span class="nt">QuoteResponse</span><span class="p">:</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">object</span>
<span class="w">      </span><span class="nt">properties</span><span class="p">:</span>
<span class="w">        </span><span class="nt">quoteId</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">        </span><span class="nt">customerId</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">        </span><span class="nt">status</span><span class="p">:</span>
<span class="w">          </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/QuoteStatus&#39;</span>
<span class="w">        </span><span class="nt">items</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">array</span>
<span class="w">          </span><span class="nt">items</span><span class="p">:</span>
<span class="w">            </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/QuoteItem&#39;</span>
<span class="w">        </span><span class="nt">pricing</span><span class="p">:</span>
<span class="w">          </span><span class="nt">$ref</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;#/components/schemas/PricingDetails&#39;</span>
<span class="w">        </span><span class="nt">createdDate</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">          </span><span class="nt">format</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">date-time</span>
<span class="w">        </span><span class="nt">expirationDate</span><span class="p">:</span>
<span class="w">          </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">string</span>
<span class="w">          </span><span class="nt">format</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">date-time</span>
</code></pre></div>
<h3 id="websocket-api-contracts">WebSocket API Contracts<a class="headerlink" href="#websocket-api-contracts" title="Permanent link">&para;</a></h3>
<h4 id="real-time-quote-updates">Real-time Quote Updates<a class="headerlink" href="#real-time-quote-updates" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="kd">interface</span><span class="w"> </span><span class="nx">QuoteUpdateMessage</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">type</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;QUOTE_UPDATED&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;QUOTE_EXPIRED&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;QUOTE_APPROVED&#39;</span><span class="p">;</span>
<span class="w">  </span><span class="nx">quoteId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">data</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">status?</span><span class="o">:</span><span class="w"> </span><span class="kt">QuoteStatus</span><span class="p">;</span>
<span class="w">    </span><span class="nx">pricing?</span><span class="o">:</span><span class="w"> </span><span class="kt">PricingDetails</span><span class="p">;</span>
<span class="w">    </span><span class="nx">validationErrors?</span><span class="o">:</span><span class="w"> </span><span class="kt">ValidationError</span><span class="p">[];</span>
<span class="w">  </span><span class="p">};</span>
<span class="p">}</span>

<span class="c1">// WebSocket endpoint: ws://backend-url/quotes/{quoteId}/updates</span>
</code></pre></div>
<h2 id="data-flow-patterns">Data Flow Patterns<a class="headerlink" href="#data-flow-patterns" title="Permanent link">&para;</a></h2>
<h3 id="quote-creation-flow">Quote Creation Flow<a class="headerlink" href="#quote-creation-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant FE as Frontend
    participant BE as Backend
    participant EXT as Extensions
    participant OCP as Order Capture
    participant BSS as TELUS BSS

    FE-&gt;&gt;BE: POST /api/v1/quotes
    BE-&gt;&gt;EXT: Apply validation rules
    EXT--&gt;&gt;BE: Validation results
    BE-&gt;&gt;OCP: Create quote request
    OCP-&gt;&gt;BSS: Validate services
    BSS--&gt;&gt;OCP: Service validation
    OCP--&gt;&gt;BE: Quote created
    BE-&gt;&gt;EXT: Apply quote modifications
    EXT--&gt;&gt;BE: Modified quote
    BE--&gt;&gt;FE: Quote response

    Note over FE,BSS: Real-time updates via WebSocket
    BE-&gt;&gt;FE: Quote status updates
</code></pre></div>
<h3 id="order-processing-flow">Order Processing Flow<a class="headerlink" href="#order-processing-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant FE as Frontend
    participant BE as Backend
    participant EXT as Extensions
    participant OCP as Order Capture
    participant BULK as Bulk Operations
    participant BSS as TELUS BSS

    FE-&gt;&gt;BE: Submit order
    BE-&gt;&gt;EXT: Final validations
    EXT--&gt;&gt;BE: Validation passed
    BE-&gt;&gt;OCP: Create order
    OCP-&gt;&gt;BSS: Process order

    alt Bulk Address Processing
        BSS-&gt;&gt;BULK: Bulk address validation
        BULK--&gt;&gt;BSS: Addresses validated
    end

    BSS--&gt;&gt;OCP: Order processed
    OCP--&gt;&gt;BE: Order confirmation
    BE--&gt;&gt;FE: Order status
</code></pre></div>
<h2 id="bulk-operation-integration">Bulk Operation Integration<a class="headerlink" href="#bulk-operation-integration" title="Permanent link">&para;</a></h2>
<h3 id="integration-with-bulk-operation-extension">Integration with Bulk Operation Extension<a class="headerlink" href="#integration-with-bulk-operation-extension" title="Permanent link">&para;</a></h3>
<p>The B2B Order Capture system integrates with the TELUS Bulk Operation Extension for large-scale address processing:</p>
<h4 id="address-validation-integration">Address Validation Integration<a class="headerlink" href="#address-validation-integration" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="nd">@Service</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">BulkAddressService</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="kd">private</span><span class="w"> </span><span class="kd">final</span><span class="w"> </span><span class="n">BulkOperationClient</span><span class="w"> </span><span class="n">bulkClient</span><span class="p">;</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">CompletableFuture</span><span class="o">&lt;</span><span class="n">List</span><span class="o">&lt;</span><span class="n">AddressValidationResult</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="nf">validateAddresses</span><span class="p">(</span>
<span class="w">            </span><span class="n">List</span><span class="o">&lt;</span><span class="n">Address</span><span class="o">&gt;</span><span class="w"> </span><span class="n">addresses</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">addresses</span><span class="p">.</span><span class="na">size</span><span class="p">()</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mi">100</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Use bulk operation service for large datasets</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">bulkClient</span><span class="p">.</span><span class="na">validateAddressesBulk</span><span class="p">(</span><span class="n">addresses</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Use standard validation for small datasets</span>
<span class="w">            </span><span class="k">return</span><span class="w"> </span><span class="n">validateAddressesStandard</span><span class="p">(</span><span class="n">addresses</span><span class="p">);</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">processBulkOrder</span><span class="p">(</span><span class="n">BulkOrderRequest</span><span class="w"> </span><span class="n">request</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Submit to bulk operation extension</span>
<span class="w">        </span><span class="n">BulkProcessingRequest</span><span class="w"> </span><span class="n">bulkRequest</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">BulkProcessingRequest</span><span class="p">.</span><span class="na">builder</span><span class="p">()</span>
<span class="w">            </span><span class="p">.</span><span class="na">operation</span><span class="p">(</span><span class="s">&quot;ADDRESS_VALIDATION&quot;</span><span class="p">)</span>
<span class="w">            </span><span class="p">.</span><span class="na">data</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getAddresses</span><span class="p">())</span>
<span class="w">            </span><span class="p">.</span><span class="na">callback</span><span class="p">(</span><span class="n">buildCallbackUrl</span><span class="p">(</span><span class="n">request</span><span class="p">.</span><span class="na">getOrderId</span><span class="p">()))</span>
<span class="w">            </span><span class="p">.</span><span class="na">build</span><span class="p">();</span>

<span class="w">        </span><span class="n">bulkClient</span><span class="p">.</span><span class="na">submitBulkOperation</span><span class="p">(</span><span class="n">bulkRequest</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="callback-handling">Callback Handling<a class="headerlink" href="#callback-handling" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="nd">@RestController</span>
<span class="nd">@RequestMapping</span><span class="p">(</span><span class="s">&quot;/api/v1/callbacks/bulk&quot;</span><span class="p">)</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">BulkOperationCallbackController</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@PostMapping</span><span class="p">(</span><span class="s">&quot;/address-validation/{orderId}&quot;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="n">ResponseEntity</span><span class="o">&lt;</span><span class="n">Void</span><span class="o">&gt;</span><span class="w"> </span><span class="nf">handleAddressValidationCallback</span><span class="p">(</span>
<span class="w">            </span><span class="nd">@PathVariable</span><span class="w"> </span><span class="n">String</span><span class="w"> </span><span class="n">orderId</span><span class="p">,</span>
<span class="w">            </span><span class="nd">@RequestBody</span><span class="w"> </span><span class="n">BulkValidationResult</span><span class="w"> </span><span class="n">result</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>

<span class="w">        </span><span class="c1">// Process bulk validation results</span>
<span class="w">        </span><span class="n">orderService</span><span class="p">.</span><span class="na">updateOrderWithValidationResults</span><span class="p">(</span><span class="n">orderId</span><span class="p">,</span><span class="w"> </span><span class="n">result</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Notify frontend via WebSocket</span>
<span class="w">        </span><span class="n">notificationService</span><span class="p">.</span><span class="na">notifyOrderUpdate</span><span class="p">(</span><span class="n">orderId</span><span class="p">,</span><span class="w"> </span><span class="n">result</span><span class="p">);</span>

<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="n">ResponseEntity</span><span class="p">.</span><span class="na">ok</span><span class="p">().</span><span class="na">build</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="data-synchronization">Data Synchronization<a class="headerlink" href="#data-synchronization" title="Permanent link">&para;</a></h3>
<h4 id="event-driven-updates">Event-Driven Updates<a class="headerlink" href="#event-driven-updates" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="nd">@EventListener</span>
<span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">OrderEventHandler</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="nd">@Async</span>
<span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">handleBulkOperationCompleted</span><span class="p">(</span><span class="n">BulkOperationCompletedEvent</span><span class="w"> </span><span class="n">event</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="n">String</span><span class="w"> </span><span class="n">orderId</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">event</span><span class="p">.</span><span class="na">getOrderId</span><span class="p">();</span>
<span class="w">        </span><span class="n">BulkOperationResult</span><span class="w"> </span><span class="n">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">event</span><span class="p">.</span><span class="na">getResult</span><span class="p">();</span>

<span class="w">        </span><span class="c1">// Update order status</span>
<span class="w">        </span><span class="n">orderService</span><span class="p">.</span><span class="na">updateOrderStatus</span><span class="p">(</span><span class="n">orderId</span><span class="p">,</span><span class="w"> </span><span class="n">OrderStatus</span><span class="p">.</span><span class="na">ADDRESSES_VALIDATED</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Continue order processing</span>
<span class="w">        </span><span class="n">orderProcessingService</span><span class="p">.</span><span class="na">continueProcessing</span><span class="p">(</span><span class="n">orderId</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h2 id="cip-configuration-integration">CIP Configuration Integration<a class="headerlink" href="#cip-configuration-integration" title="Permanent link">&para;</a></h2>
<h3 id="cloud-integration-platform-configuration">Cloud Integration Platform Configuration<a class="headerlink" href="#cloud-integration-platform-configuration" title="Permanent link">&para;</a></h3>
<p>The <strong>nc-cloud-bss-cip-config-b2b</strong> repository provides the Cloud Integration Platform configuration that orchestrates business processes across all B2B systems:</p>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;CIP Configuration Layer&quot;
        Config[CIP Configuration Package]
        Chains[Integration Chains]
        Services[Service Catalog]
        Variables[Environment Variables]
    end

    subgraph &quot;B2B Application Layer&quot;
        Frontend[Frontend Application]
        Backend[Backend Application]
        Extensions[Backend Extensions]
    end

    subgraph &quot;External Systems&quot;
        SFDC[Salesforce CRM]
        SAP[SAP Systems]
        TMF[TMF APIs]
        Billing[Billing Systems]
    end

    Config --&gt; Chains
    Config --&gt; Services
    Config --&gt; Variables

    Chains --&gt; Frontend
    Chains --&gt; Backend
    Chains --&gt; Extensions

    Services --&gt; SFDC
    Services --&gt; SAP
    Services --&gt; TMF
    Services --&gt; Billing

    Frontend --&gt; Backend
    Backend --&gt; Extensions
</code></pre></div>
<h4 id="integration-flow-orchestration">Integration Flow Orchestration<a class="headerlink" href="#integration-flow-orchestration" title="Permanent link">&para;</a></h4>
<p>The CIP Configuration Package orchestrates complex business flows that span multiple repositories:</p>
<ol>
<li><strong>Quote Management Flow</strong>: Frontend initiates quote → Backend processes → CIP chains orchestrate SFDC integration → Extensions handle specialized logic</li>
<li><strong>Credit Assessment Flow</strong>: CIP chains trigger on quote state changes → Coordinate between SFDC, backend services, and billing systems</li>
<li><strong>Service Provisioning Flow</strong>: CIP chains orchestrate multi-system provisioning across inventory, billing, and CRM systems</li>
</ol>
<h4 id="cross-repository-event-flow">Cross-Repository Event Flow<a class="headerlink" href="#cross-repository-event-flow" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant FE as Frontend App
    participant BE as Backend App
    participant EXT as Extensions
    participant CIP as CIP Platform
    participant SFDC as Salesforce
    participant TMF as TMF APIs

    FE-&gt;&gt;BE: Create quote request
    BE-&gt;&gt;CIP: Trigger quote.created event
    CIP-&gt;&gt;CIP: Execute quote management chain
    CIP-&gt;&gt;SFDC: Create opportunity
    SFDC--&gt;&gt;CIP: Opportunity created
    CIP-&gt;&gt;TMF: Create TMF quote
    TMF--&gt;&gt;CIP: Quote created
    CIP-&gt;&gt;BE: Quote orchestration complete
    BE-&gt;&gt;EXT: Apply business rules
    EXT--&gt;&gt;BE: Rules applied
    BE--&gt;&gt;FE: Quote response with SFDC/TMF data
</code></pre></div>
<h4 id="configuration-driven-integration">Configuration-Driven Integration<a class="headerlink" href="#configuration-driven-integration" title="Permanent link">&para;</a></h4>
<p>The CIP configuration enables dynamic integration behavior:</p>
<div class="highlight"><pre><span></span><code><span class="c1"># Example: Quote state change triggers</span>
<span class="nt">chain</span><span class="p">:</span>
<span class="w">  </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;quote-state-management&quot;</span>
<span class="w">  </span><span class="nt">triggers</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">event</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;quote.state.changed&quot;</span>
<span class="w">      </span><span class="nt">condition</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;state</span><span class="nv"> </span><span class="s">==</span><span class="nv"> </span><span class="s">&#39;SUBMITTED&#39;&quot;</span>

<span class="w">  </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;notifyFrontend&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service&quot;</span>
<span class="w">      </span><span class="nt">service</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;frontend-notification&quot;</span>
<span class="w">      </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;websocket&quot;</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;updateBackend&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service&quot;</span>
<span class="w">      </span><span class="nt">service</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;backend-api&quot;</span>
<span class="w">      </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;updateQuoteStatus&quot;</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;triggerExtensions&quot;</span>
<span class="w">      </span><span class="nt">type</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;service&quot;</span>
<span class="w">      </span><span class="nt">service</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;extensions-api&quot;</span>
<span class="w">      </span><span class="nt">endpoint</span><span class="p">:</span><span class="w"> </span><span class="s">&quot;processQuoteSubmission&quot;</span>
</code></pre></div>
<h3 id="cross-repository-data-synchronization">Cross-Repository Data Synchronization<a class="headerlink" href="#cross-repository-data-synchronization" title="Permanent link">&para;</a></h3>
<h4 id="event-driven-architecture">Event-Driven Architecture<a class="headerlink" href="#event-driven-architecture" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph &quot;Event Sources&quot;
        FE_Events[Frontend Events]
        BE_Events[Backend Events]
        EXT_Events[Extension Events]
        CIP_Events[CIP Events]
    end

    subgraph &quot;Event Bus&quot;
        EventBus[Message Queue]
    end

    subgraph &quot;Event Consumers&quot;
        FE_Consumer[Frontend Consumer]
        BE_Consumer[Backend Consumer]
        EXT_Consumer[Extension Consumer]
        CIP_Consumer[CIP Consumer]
    end

    FE_Events --&gt; EventBus
    BE_Events --&gt; EventBus
    EXT_Events --&gt; EventBus
    CIP_Events --&gt; EventBus

    EventBus --&gt; FE_Consumer
    EventBus --&gt; BE_Consumer
    EventBus --&gt; EXT_Consumer
    EventBus --&gt; CIP_Consumer
</code></pre></div>
<h4 id="integration-patterns">Integration Patterns<a class="headerlink" href="#integration-patterns" title="Permanent link">&para;</a></h4>
<ol>
<li><strong>Command Pattern</strong>: Frontend commands trigger backend processing via CIP orchestration</li>
<li><strong>Event Sourcing</strong>: All state changes captured as events for audit and replay</li>
<li><strong>CQRS</strong>: Separate read/write models optimized for different access patterns</li>
<li><strong>Saga Pattern</strong>: Long-running business processes coordinated across systems</li>
</ol>
<hr />
<p><strong>Next</strong>: Explore the <a href="../05-development-guide/">Development Guide</a> for setup and development workflows.</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "..", "features": [], "search": "../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": null}</script>
    
    
      <script src="../assets/javascripts/bundle.13a4f30d.min.js"></script>
      
    
  </body>
</html>